<template>
  <gms-dialog
    :visible="visible"
    append-to-body
    :title="title"
    :show-cancel="false"
    :show-save="false"
    @closed="send('CANCEL')"
  >
    <SelectContainerType
      v-if="machineState.matches('add.select')"
      :container-type-list="$props.options.containerTypeList"
      :type.sync="$props.record.modelInfo.id"
      :send="send"
    />
    <template v-if="showForm">
      <ContainerInfo
        ref="formRef"
        :options="$props.options"
        :record="$props.record"
        :machine-state="machineState"
        :send="send"
        @batchAdd="(data) => (state.tableData = data)"
        @removeContainers="handleRemoveContainers"
      />
      <CodeTable
        v-bind="$attrs"
        ref="formTable"
        :machine-state="machineState"
        :record="record"
        class="tw-mt-5"
        :table-data.sync="state.tableData"
      />
    </template>

    <span v-if="$props.options.containerTypeList.length" slot="footer">
      <!-- 取消 -->
      <gp-button v-if="showCancelButton" @click="handleCancel">
        {{ $t("geekplus.gms.client.commons.btn.cancel") }}
      </gp-button>
      <!-- 下一步 -->
      <gp-button v-if="showNextButton" type="primary" @click="handleNextStep">
        {{ $t("geekplus.gms.client.commons.btn.nextStep") }}
      </gp-button>
      <!-- 上一步 -->
      <gp-button v-if="machineState.matches('add.list')" @click="send('PREV')">
        {{ $t("geekplus.gms.client.commons.btn.prevStep") }}
      </gp-button>
      <!-- 保存 -->
      <gp-button
        v-if="showSaveButton"
        :loading="machineState.matches('add.list.saving')"
        type="primary"
        :disabled="state.tableData.length <= 0"
        @click="handleSave"
        >{{ $t("geekplus.gms.client.commons.btn.save") }}</gp-button
      >
      <!-- 保存并入场 -->
      <gp-button
        v-if="machineState.matches('add.list')"
        :loading="machineState.matches('add.list.saving')"
        type="primary"
        :disabled="state.tableData.length <= 0"
        @click="handleSaveAndEntry"
      >
        {{ $t("geekplus.gms.client.screen.container.btns.saveAndEntry") }}
      </gp-button>
      <!-- 入场 -->
      <gp-button
        v-if="machineState.matches('entry')"
        :loading="machineState.matches('entry.saving')"
        type="primary"
        @click="handleEntry"
      >
        {{ $t("geekplus.gms.client.screen.container.btns.containerEntry") }}
      </gp-button>
    </span>
  </gms-dialog>
</template>

<script>
import { Message } from "geekplus-ui";
import { reactive, ref, computed, onMounted } from "vue";
import { useI18n } from "gms-hooks";
import { editContainer, batchBindPoint } from "gms-apis/container";
import SelectContainerType from "./select-container-type.vue";
import ContainerInfo from "./container-info.vue";
import CodeTable from "./code-table.vue";
import { containerStatus, loadingStatus } from "@/gms/commons/constants";
import { matchesSome } from "./machine";
import { fromPairs } from "lodash";

const t = useI18n();

export default {
  name: "DialogAddContainer",
  components: { SelectContainerType, ContainerInfo, CodeTable },
  props: {
    visible: { type: Boolean, default: false },
    handleClose: { type: Function, default: () => {} },
    options: { type: Object, default: () => ({}) },
    record: { type: Object, default: () => ({}) },
    entryList: { type: Array, default: () => [] },
    machineState: { type: Object, default: null },
    send: { type: Function, default: null },
  },
  emits: [],
  setup(props, { emit }) {
    const loading = ref(false);
    const formTable = ref(null);
    const formRef = ref(null);

    const state = reactive({
      containerType: null,
      enableQueue: false,
      step: 1,
      tableData: [],
    });

    const title = computed(() => {
      if (props.machineState.matches("entry")) {
        return t("geekplus.gms.client.screen.container.tips.containerEntry");
      }

      if (props.machineState.matches("edit")) {
        return t("geekplus.gms.client.screen.container.btns.editContainer");
      }

      return t("geekplus.gms.client.screen.container.btns.addContainer");
    });

    const showNextButton = computed(() => matchesSome(props.machineState, ["add.select"]));
    const showCancelButton = computed(() => matchesSome(props.machineState, ["add.select", "entry", "edit"]));
    const showSaveButton = computed(() => matchesSome(props.machineState, ["add.list", "edit"]));
    // 是否显示表单
    const showForm = computed(() => ["add.list", "entry", "edit"].some((item) => props.machineState.matches(item)));

    onMounted(() => {
      if (props.machineState.matches("edit")) {
        state.tableData = [props.record];
      } else if (props.machineState.matches("entry")) {
        state.tableData = props.entryList;
      }
    });

    const handleNextStep = () => {
      if (!props.record.modelInfo.id) {
        Message.error(t("geekplus.gms.client.screen.container.form.pleaseSelectContainerTypeName"));
        return;
      }
      props.send("NEXT");
    };

    const handleCancel = () => {
      props.send("CANCEL");
      formTable.value?.$refs?.form.resetFields();
      emit("update:visible", false);
    };

    /** 获取当前容器状态 */
    const getContainerStatus = () => {
      if (props.machineState.matches("add")) return containerStatus.INACTIVE;
      return props.record?.status;
    };

    const saveFn = () =>
      new Promise((resolve, reject) => {
        formRef.value.baseFormGroup.$validate((valid) => {
          if (!valid) return;
          formTable.value?.$refs?.form.validate((valid) => {
            if (!valid) {
              return false;
            }

            submitRequest({
              status: getContainerStatus(),
            })
              .then(resolve)
              .catch(reject);
          });
        });
      });

    const handleSave = () => {
      return saveFn()
        .then(() => {
          emit("save", null, "save");
          props.send("SUCCESS");
        })
        .catch(() => {
          props.send("ERROR");
        });
    };

    const handleEntry = () => {
      // 入场
      formRef.value.entryFormGroup.$validate((valid) => {
        if (!valid) return;
        formTable.value?.$refs?.form.validate((valid) => {
          if (!valid) {
            return false;
          }
          const entryData = formRef.value.entryFormGroup.$getData();
          const requestData = state.tableData.map((v) => ({
            ...v,
            loadingStatus: entryData.loadingStatus,
            placementAngle: entryData.placementAngle,
            loadInfo: getLoadInfo(),
          }));
          props.send("SAVE");

          batchBindPoint(requestData).then((data) => {
            // 出错的容器编号
            const errorContainerCodes = data?.filter(({ isSuccess }) => !isSuccess).map((item) => item.loadCarrierCode);
            if (errorContainerCodes && errorContainerCodes.length > 0) {
              props.send("ERROR");
              // 只保留错误的容器编号
              state.tableData = state.tableData.filter(({ code }) => errorContainerCodes.includes(code));
            } else {
              props.send("SUCCESS");
            }
            emit("save", data, "entry");
          });
        });
      });
    };

    // 根据服务器返回的信息更新容器状态
    const updateContainerStatus = (data) => {
      if (!data || data.length < 0) return;
      const statusObj = fromPairs(data.map(({ code, status }) => [code, status]));

      state.tableData = state.tableData.map((item) => ({
        ...item,
        status: statusObj[item.code],
      }));
    };

    const handleSaveAndEntry = () => {
      saveFn()
        .then((data) => {
          updateContainerStatus(data);
          props.send("ENTRY");
        })
        .catch(() => {
          props.send("ERROR");
        });
    };

    /**
     * 获取物料类别数据
     */
    function getLoadInfo() {
      const entryData = formRef.value.entryFormGroup.$getData();

      const loadInfo = {};
      if (entryData.loadingStatus === loadingStatus.LOADED) {
        loadInfo.materialCode = entryData.materialType;
      }

      return loadInfo;
    }

    const submitRequest = async (params) => {
      const entryData = formRef.value.entryFormGroup.$getData();

      const requestData = state.tableData.map((v) => ({
        ...v,
        ...params,
        loadingStatus: entryData.loadingStatus,
        placementAngle: entryData.placementAngle,
        loadInfo: getLoadInfo(),
      }));

      props.send("SAVE");
      return await editContainer(requestData);
    };
    const handleRemoveContainers = (count) => {
      state.tableData = state.tableData.slice(0, -count);
    };

    return {
      formTable,
      title,
      formRef,
      state,
      loading,
      showNextButton,
      showCancelButton,
      showSaveButton,
      showForm,

      handleCancel,
      handleSave,
      handleEntry,
      handleSaveAndEntry,
      handleNextStep,
      handleRemoveContainers,
    };
  },
};
</script>

<style lang="scss" module="cn"></style>
