{"geekplus.gms.client.screen.flowTemplate.title": "Workflow Template", "geekplus.gms.client.screen.flowTemplate.subtitle": "A robot workflow template summarized from common businesses can be used to generate the workflow and drive the robot to perform the handling tasks.", "geekplus.gms.client.screen.flowTemplate.edgeName": "Connector name", "geekplus.gms.client.screen.flowTemplate.slamEditor.nodeSelected.rollerNodeActionCheckMsg": "The interactive action does not include picking and placement at non-roller station.", "geekplus.gms.client.screen.flowTemplate.slamEditor.currentNode": "Current point", "geekplus.gms.client.screen.flowTemplate.slamEditor.specifyNodeType": "Specified point type", "geekplus.gms.client.screen.flowTemplate.slamEditor.specifyNodeType.tooltip": "When the user selects not to specify the point type, all point types across the site are included by default. This option is applicable to the scenario where there is only an external device.", "geekplus.gms.client.screen.flowTemplate.slamEditor.nodeAction": "Interactive action at point", "geekplus.gms.client.screen.flowTemplate.slamEditor.defaultQueueArea": "Default queuing area"}