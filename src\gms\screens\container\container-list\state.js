import { columnTypes } from "@srpe-fe/uic-el-table-wrapper-vue2";
import { useI18n } from "gms-hooks";
import { containerStatus, loadingStatus, entryAngle, containerTypeShape } from "gms-constants";
import { getOptionsLabel } from "gms-utils";
const t = useI18n();

export const ADD_BTN = "add";

export const LEAVE_BTN = "leave";

export const ENTRY_BTN = "entry";

export const DELETE_BTN = "delete";

export const confirmMsg = {
  [`${ENTRY_BTN}Title`]: t("geekplus.gms.client.screen.container.tips.confirmEntry"),
  [`${LEAVE_BTN}Title`]: t("geekplus.gms.client.screen.container.tips.confirmLeave"),
  [`${LEAVE_BTN}Content`]: t("geekplus.gms.client.screen.container.tips.leaveTipMessage"),
  [`${DELETE_BTN}Title`]: t("geekplus.gms.client.commons.tips.confirmDelete"),
  [`${DELETE_BTN}Content`]: t("geekplus.gms.client.screen.container.tips.containerDeleteTipMessage"),
};

export const getKeyLabelMap = () => ({
  amount: t("geekplus.gms.client.screen.container.columns.containerAmount"),
  code: t("geekplus.gms.client.screen.container.columns.containerCode"),
  containerType: t("geekplus.gms.client.screen.container.columns.containerTypeName"),
  loadingStatus: t("geekplus.gms.client.screen.container.columns.loadingStatus"),
  materialType: t("geekplus.gms.client.screen.container.columns.materialType"),
  placementAngle: t("geekplus.gms.client.screen.container.columns.containerEntryAngle"),
  pointCode: t("geekplus.gms.client.screen.container.columns.entryEndPoint"),
  shape: t("geekplus.gms.client.screen.container.columns.containerShape"),
});

/**
 * 可入场状态
 * 离场
 */
export const enterableStatus = [containerStatus.INACTIVE];
/**
 * 可删除状态
 * 离场
 */
export const deletableStatus = [containerStatus.INACTIVE];

/**
 * @typedef {object} AreaItem
 * @property {string} name - area name
 * @property {null} parent - area's parent is always null
 *
 * @typedef {object} GroupItem
 * @property {string} name - group name
 * @property {AreaItem} parent - group's parent area
 *
 * @typedef {object} RowItem
 * @property {AreaItem|GroupItem} [area] - area or group item
 * @property {string} [areaCode] - area name, deprecated Since 3.3.0-beta.2. Use area instead.
 * @property {string} [areaGroup] - group name, deprecated Since 3.3.0-beta.2. Use area instead.
 */

/**
 * 获取区域名称
 * @param {RowItem} row
 */
function getAreaName(row) {
  let result = "-";

  if (row.area) {
    // 此处是新的接口逻辑
    if (row.area.parent) {
      result = row.area.parent.name;
    } else {
      result = row.area.name;
    }
  } else if (row.areaCode) {
    // 兼容原有接口逻辑
    result = row.areaCode;
  }

  return result;
}

/**
 * 获取区域下的分组名称
 * @param {RowItem} row
 */
function getAreaGroupName(row) {
  let result = "-";

  if (row.area && row.area.parent) {
    // 此处是新的接口逻辑
    result = row.area.name;
  } else if (row.areaGroup) {
    // 兼容原有接口逻辑
    result = row.areaGroup;
  }

  return result;
}

/**
 * 可离场状态
 * 空闲
 */
export const leavableStatus = [containerStatus.IDLE, containerStatus.OCCUPIED];

export const getTableColumns = (state, { handleEdit, handleEntry, handleDelete, handleLeave }) => [
  { type: columnTypes.SELECTION }, //
  {
    type: columnTypes.GEEK_CUSTOMIZED,
    prop: "status",
    label: t("geekplus.gms.client.screen.container.columns.status"),
    cellSlotName: "statusSlot",
    minWidth: 100,
  },
  { prop: "code", label: t("geekplus.gms.client.screen.container.columns.code"), minWidth: 110 },
  {
    prop: "modelInfo",
    label: t("geekplus.gms.client.screen.container.columns.typeName"),
    formatter(row, column, value) {
      return value?.name ? value?.name : "-";
    },
    minWidth: 120,
  },
  {
    prop: "loadInfo",
    label: t("geekplus.gms.client.screen.container.columns.materialType"),
    formatter(row, column, value) {
      return getOptionsLabel(state.materialTypeList, Number(value?.materialCode ? Number(value?.materialCode) : "-"));
    },
    minWidth: 100,
  },
  {
    prop: "placementAngle",
    label: t("geekplus.gms.client.screen.container.columns.angle"),
    formatter(row, column, value) {
      return entryAngle.getLabelByValue(value);
    },
  },
  {
    prop: "loadingStatus",
    label: t("geekplus.gms.client.screen.container.columns.loadingStatus"),
    formatter(row, column, value) {
      return loadingStatus.getLabelByValue(value);
    },
    minWidth: 100,
  },
  {
    prop: "placementPointCode",
    label: t("geekplus.gms.client.screen.container.columns.currentPosition"),
    minWidth: 120,
    formatter(row) {
      if (!row.placementPointCode) return "-";
      let code = row.placementPointCode;
      if (row.modelInfo.type === containerTypeShape.FORKLIFT_PALLET) {
        code = state.palletCodeList.find((v) => v.value === code)?.label ?? code;
        return `${t("geekplus.gms.client.commons.constants.stopPointTypes.pallet")}#${code}`;
      }
      code = state.pointCodeList.find((v) => v.value === code)?.label ?? code;
      return `${t("geekplus.gms.client.commons.constants.stopPointTypes.common")}#${code}`;
    },
  },
  {
    prop: "areaCode",
    label: t("geekplus.gms.client.screen.container.columns.belongsArea"),
    formatter(row) {
      if (getAreaGroupName(row) === "-") {
        return getAreaName(row);
      }
      return `${getAreaName(row)}(${getAreaGroupName(row)})`;
    },
    minWidth: 100,
  },
  {
    type: columnTypes.GEEK_DATETIME,
    prop: "lastModifiedTime",
    label: t("geekplus.gms.client.commons.cols.updateTime"),
  },
  // 应产品要求，暂时隐藏编辑人一列
  /*
  {
    prop: "lastModifiedBy",
    label: t("geekplus.gms.client.commons.cols.updateUser"),
    formatter(row, column, value) {
      return value?.screenName ? value.screenName : "-";
    },
  },
  */
  {
    type: columnTypes.GEEK_ACTION_BUTTONS,
    prop: "actions",
    label: t("geekplus.gms.client.screen.taskMonitoring.columns.actions"),
    fixed: "right",
    width: 180,
    options: [
      {
        text: t("geekplus.gms.client.commons.btn.edit"),
        handleClick: ({ row }) => handleEdit(row),
        isEnabled: ({ row }) => row.status !== containerStatus.ACTIVE,
      },
      {
        text: t("geekplus.gms.client.commons.btn.delete"),
        getClassName: () => "tw-text-red-400 hover:tw-text-red-400",
        isEnabled: ({ row }) => deletableStatus.includes(row.status),
        confirm: {
          title: confirmMsg[`${DELETE_BTN}Content`],
          handleConfirm: ({ row }) => handleDelete([row.id]),
        },
      },
      {
        text: t("geekplus.gms.client.screen.container.btns.containerEntry"),
        isVisible: ({ row }) => row.status === containerStatus.INACTIVE,
        handleClick: ({ row }) => handleEntry([row]),
      },
      {
        text: t("geekplus.gms.client.screen.container.btns.containerLeave"),
        isVisible: ({ row }) => row.status === containerStatus.IDLE,
        confirm: {
          title: confirmMsg[`${LEAVE_BTN}Content`],
          handleConfirm: ({ row }) => handleLeave([row.id]),
        },
      },
    ],
  },
];

/** 容器弹框的模式 */
export const CONTAINER_DIALOG_MODE = {
  /** 添加 */
  ADD: "add",
  /** 编辑 */
  EDIT: "edit",
  /** 入场 */
  ENTRY: "entry",
};
