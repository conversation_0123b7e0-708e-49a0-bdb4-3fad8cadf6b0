import { describe, expect, it } from "vitest";
import * as utils from "./utils";

describe("hook: utils", () => {
  it("queryParamToString()", () => {
    it("should able to parse paarm object", () => {
      expect(utils.queryParamToString({ a: 1 })).toBe("a=1");
      expect(utils.queryParamToString({ a: 1, b: 2 })).toBe("a=1&b=2");
    });

    it("should ignore epmty value", () => {
      expect(utils.queryParamToString({ a: 1, b: undefined })).toBe("a=1");
      expect(utils.queryParamToString({ a: 1, b: null })).toBe("a=1");
      expect(utils.queryParamToString({ a: 1, b: "" })).toBe("a=1");
      expect(utils.queryParamToString({ a: 1, b: {} })).toBe("a=1");
    });

    it("should ignore key order", () => {
      expect(utils.queryParamToString({ a: 1, b: 2, c: 3 })).toBe("a=1&b=2&c=3");
      expect(utils.queryParamToString({ c: 3, b: 2, a: 1 })).toBe("a=1&b=2&c=3");
    });
  });
});
