{"geekplus.gms.client.screen.processConfig.column.guid": "Workflow coding", "geekplus.gms.client.screen.processConfig.column.name": "Workflow name", "geekplus.gms.client.screen.processConfig.column.robotModel": "Robot model", "geekplus.gms.client.screen.processConfig.column.status": "State", "geekplus.gms.client.screen.processConfig.column.lastModifiedBy": "Editor", "geekplus.gms.client.screen.processConfig.column.lastModifiedTime": "Edit time", "geekplus.gms.client.screen.processConfig.column.priority": "Priority", "geekplus.gms.client.screen.processConfig.column.specifyRobot": "Designate a robot", "geekplus.gms.client.screen.processConfig.column.cancelStrategy": "Cancel the strategy", "geekplus.gms.client.screen.processConfig.column.robotFree": "Make a judgment whether there are idle robots", "geekplus.gms.client.screen.processConfig.column.note": "Remarks", "geekplus.gms.client.screen.processConfig.btnCreateProcess": "New workflow", "geekplus.gms.client.screen.processConfig.dialogDesign.stepConfig": "Basic configuration", "geekplus.gms.client.screen.processConfig.dialogDesign.stepDesign": "Workflow design", "geekplus.gms.client.screen.processConfig.dialogDesign.btn.release": "Released by", "geekplus.gms.client.commons.constants.processStatus.designing": "In design", "geekplus.gms.client.commons.constants.processStatus.released": "Published"}