import { beforeEach, describe, expect, it, vi } from "vitest";
import { useQueryCache } from "./use-query-cache";

const ajaxFn = vi.fn().mockImplementation((params) => Promise.resolve(Object.values(params).join(",")));
const ajaxErrorFn = vi.fn().mockImplementation(() => Promise.reject("some error"));

let requestHandler;
beforeEach(() => {
  ajaxFn.mockClear();
  ajaxErrorFn.mockClear();
  requestHandler = useQueryCache(ajaxFn);
});

describe("hook: useQueryCache", () => {
  it("should cache daat with the same param object", () => {
    const promise1 = requestHandler({ a: 1 });
    const promise2 = requestHandler({ a: 1 });
    expect(promise1).toBe(promise2);
    expect(ajaxFn).toHaveBeenCalledTimes(1);
  });

  it("should cache promise with the same param object, even with different key order", () => {
    const promise1 = requestHandler({ a: 1, b: 2 });
    const promise2 = requestHandler({ b: 2, a: 1 });
    expect(promise1).toBe(promise2);
    expect(ajaxFn).toHaveBeenCalledTimes(1);
  });

  it("should not cache with different params", async () => {
    const data1 = await requestHandler({ a: 1 });
    const data2 = await requestHandler({ a: 2 });
    const data3 = await requestHandler({ a: 3 });
    expect(data1).toBe("1");
    expect(data2).toBe("2");
    expect(data3).toBe("3");
    expect(ajaxFn).toHaveBeenCalledTimes(3);
  });

  it("should resend request if cache is cleared", async () => {
    await requestHandler({ a: 1 });
    expect(ajaxFn).toHaveBeenCalledTimes(1);
    requestHandler.clearCache();
    await requestHandler({ a: 1 });
    expect(ajaxFn).toHaveBeenCalledTimes(2);
  });

  it("should able to resend request if encounter error", async () => {
    const errorRequest = useQueryCache(ajaxErrorFn);
    await errorRequest({ a: 1 }).catch(() => {});
    expect(ajaxErrorFn).toHaveBeenCalledTimes(1);
    await errorRequest({ a: 1 }).catch(() => {});
    expect(ajaxErrorFn).toHaveBeenCalledTimes(2);
  });
});
