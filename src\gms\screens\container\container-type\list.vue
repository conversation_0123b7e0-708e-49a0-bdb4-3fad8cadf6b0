<template>
  <div class="tw-flex-grow tw-flex tw-flex-col" :class="cn.page_wrapper">
    <template v-if="state.mode === 'list'">
      <div class="tw-text-right">
        <gp-button type="primary" @click="handleAdd">
          {{ $t("geekplus.gms.client.screen.container.btns.addContainerType") }}
        </gp-button>
      </div>
      <div v-loading="state.loading">
        <section v-if="state.containerTypeList.length" class="tw-flex-grow tw-grid tw-grid-cols-4 tw-gap-4 tw-mt-4">
          <gp-card
            v-for="item in state.containerTypeList"
            :key="item.id"
            :body-style="{ padding: '0px' }"
            shadow="hover"
            class="tw-group tw-relative tw-cursor-pointer tw-border-grey-n3 tw-border tw-border-solid"
            @click.native="handleEdit(item, 'view')"
          >
            <gp-tag size="mini" :type="getTagType(item)" class="tw-absolute tw-top-0 tw-right-0">
              {{ getModelName(item) }}
            </gp-tag>

            <div class="tw-bg-grey-n1 tw-py-4">
              <div class="tw-w-40 tw-h-36 tw-mx-auto tw-flex tw-items-center tw-justify-center">
                <img :src="item.imgUrl" class="tw-max-w-full tw-max-h-full" />
              </div>
            </div>

            <div class="tw-px-4 tw-py-2">
              <TextOverflow :content="item.name" class="tw-text-base tw-font-medium" />

              <div class="tw-flex tw-justify-between tw-mt-2 tw-text-sm">
                <p class="tw-flex-1">
                  <span class="tw-text-text-secondary">
                    {{ $t("geekplus.gms.client.commons.constants.containerStatus.idle") }}:
                  </span>
                  {{ getMetricsByModelId(item.id, containerStatus.IDLE) }}
                </p>
                <p class="tw-flex-1 tw-text-center">
                  <span class="tw-text-text-secondary">
                    {{ $t("geekplus.gms.client.commons.constants.containerStatus.leave") }}:
                  </span>
                  {{ getMetricsByModelId(item.id, containerStatus.INACTIVE) }}
                </p>
                <p class="tw-flex-1 tw-text-right">
                  <span class="tw-text-text-secondary">
                    {{ $t("geekplus.gms.client.commons.constants.containerStatus.working") }}:
                  </span>
                  {{ getMetricsByModelId(item.id, containerStatus.ACTIVE) }}
                </p>
              </div>

              <div
                class="tw-flex tw-mt-3 tw-justify-end tw-items-center tw-gap-x-4 tw-opacity-0 group-hover:tw-opacity-100"
              >
                <svg-edit class="tw-w-4 hover:tw-cursor-point" @click.stop="handleEdit(item, 'edit')" />
                <svg-delete class="tw-w-4 hover:tw-cursor-point" @click.stop="handleDelete(item)" />
              </div>
            </div>
          </gp-card>
        </section>
      </div>
      <div
        v-if="!state.containerTypeList.length && !state.loading"
        id="empty-container"
        class="tw-flex tw-flex-1 tw-flex-col tw-justify-center"
      >
        <EmptyPage
          :button-name="$t('geekplus.gms.client.screen.container.btns.addContainerType')"
          @click="state.mode = 'add'"
        />
      </div>
    </template>
    <ContainerTypeEdit
      v-if="state.mode !== 'list'"
      :mode.sync="state.mode"
      :record="state.containerModelRecord"
      @update:mode="initPage()"
    />
  </div>
</template>

<script>
import { Message, MessageBox } from "geekplus-ui";
import { defineComponent, onMounted, reactive } from "vue";
import icons from "gms-assets/svg-icons";
import { useI18n } from "gms-hooks";
import { deleteContainerType, queryMetricsByName } from "gms-apis/container";
import { containerTypeShape, containerModel, palletSpeciType, containerStatus } from "gms-constants";
import ContainerTypeEdit from "./index.vue";
import { pallets } from "./state";
import { getPageContainerTypeList } from "../common";
import TextOverflow from "gms-components/text-overflow.vue";
import EmptyPage from "gms-components/empty-page";

export default defineComponent({
  name: "ContainerModel",
  components: { ContainerTypeEdit, ...icons, TextOverflow, EmptyPage },
  setup() {
    const t = useI18n();
    const state = reactive({
      containerTypeList: [],
      mode: "list",
      containerModelRecord: null,
      metricsList: [],
      loading: false,
      containerTypeId: null,
      deleteLoading: false,
      activeName: "ContainerModel",
    });

    const initPage = (params) => {
      state.loading = true;
      Promise.all([
        getPageContainerTypeList(params),
        queryMetricsByName({ name: "LOADCARRIER_DISTINCT_COUNT_BY_STATUS" }),
      ])
        .then(([{ data }, { measures }]) => {
          state.containerTypeList = data;
          state.metricsList = measures;
        })
        .finally(() => {
          state.loading = false;
        });
    };

    const getMetricsByModelId = (id, status) => {
      return state.metricsList.find((v) => v.name === String(id) && v.fact === status)?.measure ?? 0;
    };

    const getTagType = (item) => {
      let result = "";
      // 非标准容器使用 warning 标签
      if (item.modelingMethod === containerModel.NON_STANDARD) {
        result = "warning";
      }

      return result;
    };

    onMounted(() => {
      initPage();
    });

    const handleAdd = () => {
      state.mode = "add";
    };

    const handleEdit = (row, mode) => {
      state.mode = mode;
      state.containerModelRecord = row;
    };

    const handleDelete = (row) => {
      const msg = t("geekplus.gms.client.commons.tips.confirmDeleteMsg");

      MessageBox.confirm("", msg, {
        confirmButtonText: t("geekplus.gms.client.commons.btn.confirm"),
        cancelButtonText: t("geekplus.gms.client.commons.btn.cancel"),
        type: "warning",
        showClose: false,
      }).then(() => {
        state.deleteLoading = true;
        deleteContainerType({ id: row.id }).then(() => {
          Message.success(t("geekplus.gms.client.commons.tips.operationSuccess"));
          initPage();
        });
      });
    };

    const getModelName = (model) => {
      if (!model.modelingMethod) return "";
      if (!pallets.includes(model.type)) {
        return containerModel.getLabelByValue(model.modelingMethod);
      }
      return palletSpeciType.getLabelByValue(model.modelingMethod);
    };

    return {
      state,
      containerModel,
      containerTypeShape,
      containerStatus,
      getTagType,
      initPage,
      getModelName,
      getMetricsByModelId,
      handleEdit,
      handleDelete,
      handleAdd,
    };
  },
});
</script>

<style lang="scss" module="cn">
.page_wrapper {
  :global(.gp-message-box__title) {
    display: flex;
    align-items: center;
  }
}
</style>
