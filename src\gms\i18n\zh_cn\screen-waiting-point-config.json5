/**
 *   工作站页面国际化字符集
 *   key 必须以 'geekplus.gms.client.screen.waitingPointConfig.' 开头
 */
{
  "geekplus.gms.client.screen.waitingPointConfig.btns.add": "新增",
  "geekplus.gms.client.screen.waitingPointConfig.btns.edit": "编辑",
  "geekplus.gms.client.screen.waitingPointConfig.btns.delete": "删除",

  "geekplus.gms.client.screen.waitingPointConfig.columns.cellCode": "点位编码",
  "geekplus.gms.client.screen.waitingPointConfig.columns.cellType": "点位类型",
  "geekplus.gms.client.screen.waitingPointConfig.columns.nodeActionName": "交互动作",
  "geekplus.gms.client.screen.waitingPointConfig.columns.cleanWaitType": "等待状态",
  "geekplus.gms.client.screen.waitingPointConfig.columns.creatorUsername": "创建人",
  "geekplus.gms.client.screen.waitingPointConfig.columns.creationTime": "创建时间",
  "geekplus.gms.client.screen.waitingPointConfig.columns.updatorUsername": "编辑人",
  "geekplus.gms.client.screen.waitingPointConfig.columns.updateTime": "编辑时间",
  "geekplus.gms.client.screen.waitingPointConfig.columns.actions": "操作",

  "geekplus.gms.client.screen.waitingPointConfig.columns.cleanWaitType.auto": "自动清除",
  "geekplus.gms.client.screen.waitingPointConfig.columns.cleanWaitType.manual": "手动清除",

  "geekplus.gms.client.screen.waitingPointConfig.tooltip.edit": "编辑",
  "geekplus.gms.client.screen.waitingPointConfig.tooltip.delete": "删除",

  "geekplus.gms.client.screen.waitingPointConfig.dialogConfig.title.add": "新增",
  "geekplus.gms.client.screen.waitingPointConfig.dialogConfig.title.edit": "编辑",

  "geekplus.gms.client.screen.waitingPointConfig.form.label.cellType": "点位类型",
  "geekplus.gms.client.screen.waitingPointConfig.form.label.cleanWaitType": "等待状态",
  "geekplus.gms.client.screen.waitingPointConfig.form.label.cellCode": "点位编码",
  "geekplus.gms.client.screen.waitingPointConfig.form.label.nodeActionName": "交互动作",

  "geekplus.gms.client.screen.waitingPointConfig.dialogConfig.actions.save": "保存",
  "geekplus.gms.client.screen.waitingPointConfig.dialogConfig.actions.cancel": "取消",

  "geekplus.gms.client.screen.waitingPointConfig.dialogConfirm.actions.desc": "确认删除此数据吗？",
  "geekplus.gms.client.screen.waitingPointConfig.dialogConfirm.actions.cancel": "取消",
  "geekplus.gms.client.screen.waitingPointConfig.dialogConfirm.actions.confirm": "确定",

  "geekplus.gms.client.screen.waitingPointConfig.dialogConfig.actions.device.title": "外部设备",
  "geekplus.gms.client.screen.waitingPointConfig.dialogConfig.actions.device.own": "设备归属",
  "geekplus.gms.client.screen.waitingPointConfig.dialogConfig.actions.device.type": "设备类型",
  "geekplus.gms.client.screen.waitingPointConfig.dialogConfig.actions.device.name": "设备名称",
  "geekplus.gms.client.screen.waitingPointConfig.dialogConfig.actions.device.command": "设备指令",
  "geekplus.gms.client.screen.waitingPointConfig.dialogConfig.actions.device.criteria": "指令执行条件",
  "geekplus.gms.client.screen.waitingPointConfig.dialogConfig.actions.interface.title": "接口指令",
  "geekplus.gms.client.screen.waitingPointConfig.dialogConfig.actions.interface.url": "接口地址",
  "geekplus.gms.client.screen.waitingPointConfig.dialogConfig.actions.interface.commandCode": "指令代码",
  "geekplus.gms.client.screen.waitingPointConfig.dialogConfig.actions.component.title": "组件指令",
  "geekplus.gms.client.screen.waitingPointConfig.dialogConfig.actions.component.robotType": "机器人型号",
  "geekplus.gms.client.screen.waitingPointConfig.dialogConfig.actions.component.actionType": "旋转",
  "geekplus.gms.client.screen.waitingPointConfig.dialogConfig.actions.component.actionExtendedParameters": "旋转的值",
}
