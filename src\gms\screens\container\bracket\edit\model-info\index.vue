<template>
  <NoPalletModel
    v-bind="$attrs"
    @update:formValues="$emit('update:formValues', $event)"
    @update:active="$emit('update:active', $event)"
  />
</template>
<script>
import { defineComponent } from "vue";
import NoPalletModel from "./no-pallet-model";

export default defineComponent({
  name: "ModelInfo",
  components: { NoPalletModel },
  emit: ["update:active", "update:formValues"],
  setup() {},
});
</script>
