/**
 *   支架页面国际化字符集
 *   key 必须以 'geekplus.gms.client.screen.bracket.' 开头
 */
{
  "geekplus.gms.client.screen.bracket.btns.addBracket": "新增支架",
  "geekplus.gms.client.screen.bracket.btns.addBracketModel": "新增支架模型",
  "geekplus.gms.client.screen.bracket.columns.containerShape": "支架形态",
  "geekplus.gms.client.screen.bracket.columns.containerName": "支架名称",
  "geekplus.gms.client.screen.bracket.columns.containerModel": "支架类型",
  "geekplus.gms.client.screen.bracket.columns.mapCode": "关联地图",
  "geekplus.gms.client.screen.bracket.columns.cellCode": "关联点位",

  "geekplus.gms.client.screen.bracket.tips.notAllowDelete": "不允许删除",
  "geekplus.gms.client.screen.bracket.tips.notAllowDeleteMsg": "当前模型已关联了点位信息，不允许删除，请先取消点位的关联后再做删除",

  "geekplus.gms.client.screen.bracket.form.bracketTitle": "支架",
  "geekplus.gms.client.screen.bracket.form.bracketSubTitle": "支架是地面上用于取放货物的支撑结构，确保机器人能够准确计算位置并在支架上稳定地取放货物。",
  "geekplus.gms.client.screen.bracket.form.mapName": "地图名称",
  "geekplus.gms.client.screen.bracket.form.cellPoints": "点位",
  "geekplus.gms.client.screen.bracket.form.bracketCenter": "支架中心",
  "geekplus.gms.client.screen.bracket.form.bracketElements": "支架元素",
  "geekplus.gms.client.screen.bracket.form.bracketSize": "支架尺寸",
  "geekplus.gms.client.screen.bracket.form.bracketFaceLength": "支架面长",
  "geekplus.gms.client.screen.bracket.form.bracketFaceWidth": "支架面宽",
  "geekplus.gms.client.screen.bracket.form.bracketLeg": "支架腿",
  "geekplus.gms.client.screen.bracket.form.offset": "托盘位置偏离量",
  "geekplus.gms.client.screen.bracket.form.offsetY": "Y轴偏离量",
  "geekplus.gms.client.screen.bracket.example.groundBracketExplain": "地面支架：是指固定在地面不可移动的支架。",
  "geekplus.gms.client.screen.bracket.example.groundBracketDiagram": "地面支架示例图：",
  "geekplus.gms.client.screen.bracket.example.groundBracketModel": "地面支架，主要配置两部分：支架面、支架腿，配置完的地面支架模型，需要到地图编辑中找到对应点位单元格进行关联配置。",
  "geekplus.gms.client.screen.bracket.example.groundBracketModelFace": "支架面：主要供pnc路径规划使用",
  "geekplus.gms.client.screen.bracket.example.groundBracketModelLeg": "支架腿：主要供机器人本体对接识别使用",
  "geekplus.gms.client.screen.bracket.example.standardBracketModel": "【标准地面支架】",
  "geekplus.gms.client.screen.bracket.example.standardBracketExplain": "定义：以支架俯视图来看，支架面和支架腿，均为规则矩形，且只有4条腿，称为标准地面支架。",
  "geekplus.gms.client.screen.bracket.example.specialBracketModel": "【异形地面支架】",
  "geekplus.gms.client.screen.bracket.example.specialBracketExplain": "定义：以支架俯视图来看，支架腿是不规则矩形时，或大于4条腿，称为异形地面支架，此时需要测量腿距离中心点的坐标。",
  "geekplus.gms.client.screen.bracket.example.specialBracketDiagram": "示例图：",
  "geekplus.gms.client.screen.bracket.example.special.bracketExplain": "一、参照实际支架找到支架的面和腿，如图：",
  "geekplus.gms.client.screen.bracket.example.special.tips": "注意事项：",
  "geekplus.gms.client.screen.bracket.example.special.tips1": "1、支架面：用于机器人的避障和路径规划，需要进行测量。",
  "geekplus.gms.client.screen.bracket.example.special.tips2": "2、支架腿：需要配置所有激光高度可扫描到的腿，应用于机器人进入前支架识别对接。",
  "geekplus.gms.client.screen.bracket.example.special.tips3": "3、当面和腿是不规则的矩形时，以面和腿的最大矩形面尺寸为准。",
  "geekplus.gms.client.screen.bracket.example.special.configExplain": "二、面和腿的配置说明",
  "geekplus.gms.client.screen.bracket.example.special.configExplainDetail": "需要测量出腿到面的边距1、2距离，单位mm",

  // 提示信息
  "geekplus.gms.client.screen.bracket.tips.palletModel": "托盘被哪种车型搬运，不同车型搬运的托盘，最小立柱宽要求不一样。",
  "geekplus.gms.client.screen.bracket.tips.recognizableDistance": "机器人本体激光识别托盘的距离，通常该参数主要是算法研发使用。",
  "geekplus.gms.client.screen.bracket.tips.specification.face": "托盘面：长宽不限",
  "geekplus.gms.client.screen.bracket.tips.specification.material": "材质：木质/塑料/其他",
  "geekplus.gms.client.screen.bracket.tips.specification.color": "颜色：木色/蓝色/其他",
  "geekplus.gms.client.screen.bracket.tips.specification.pillarSize": "立柱尺寸：",
  "geekplus.gms.client.screen.bracket.tips.specification.palletModel1": "F12ML/F14L/F16L/F35C：边缘立柱宽和中间立柱宽≥60mm",
  "geekplus.gms.client.screen.bracket.tips.specification.palletModel2": "F20T/F20MT：边缘立柱宽和中间立柱宽≥80mm",
  "geekplus.gms.client.screen.bracket.tips.specification.special.usageCases": "适用于场地仅一种托盘场地，且比标准托盘-常规的边缘和中间立柱宽要小。",
  "geekplus.gms.client.screen.bracket.tips.specification.special.material": "材质：木质/塑料/金属/其他",
  "geekplus.gms.client.screen.bracket.tips.specification.special.color": "颜色：木色/蓝色/黑色/灰色/其他",
  "geekplus.gms.client.screen.bracket.tips.specification.special.palletModel1": "F12ML/F14L/F16L/F35C：边缘立柱宽和中间立柱宽≥50mm",
  "geekplus.gms.client.screen.bracket.tips.specification.special.palletModel2": "F20T/F20MT：边缘立柱宽和中间立柱宽≥70mm",
}
