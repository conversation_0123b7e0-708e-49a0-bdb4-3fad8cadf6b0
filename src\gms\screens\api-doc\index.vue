<template>
  <rapi-doc
    :spec-url="specUrl"
    render-style="read"
    theme="light"
    show-header="false"
    show-info="false"
    allow-authentication="false"
    allow-server-selection="false"
    allow-api-list-style-selection="false"
    regular-font="Nunito"
  >
  </rapi-doc>
</template>

<script>
import { defineComponent } from "vue";
// import "rapidoc";

export default defineComponent({
  name: "ApiDoc",
  setup() {
    const specUrl = "/ark/v3/api-docs";

    return {
      specUrl,
    };
  },
});
</script>
