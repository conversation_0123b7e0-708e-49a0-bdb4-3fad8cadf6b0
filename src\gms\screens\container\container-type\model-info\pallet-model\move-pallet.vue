<template>
  <section class="tw-flex tw-flex-col" :class="[$props.mode === 'view' ? '' : 'tw-gap-4 tw-mt-6']">
    <div class="tw-flex tw-items-center tw-gap-2">
      <form-item v-bind="$props.formGroup.surfaceLength" />
      <span>mm</span>
    </div>
    <div class="tw-flex tw-items-center tw-gap-2">
      <form-item v-bind="$props.formGroup.surfaceWidth" />
      <span>mm</span>
    </div>
  </section>
</template>
<script>
import { defineComponent } from "vue";
import { FormItem } from "@srpe-fe/uic-el-form-wrapper-vue2";

export default defineComponent({
  name: "MovePalletModel",
  components: { FormItem },
  props: {
    formValues: { type: Object, default: () => ({}) },
    formGroup: { type: Object, default: () => ({}) },
    mode: { type: String, default: "add" },
  },
  setup() {
    return {};
  },
});
</script>
//
<style lang="scss" scoped>
:deep(.el-input-number.el-input-number--medium) {
  width: 16.5rem;
}
</style>
