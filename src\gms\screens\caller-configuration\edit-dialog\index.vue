<template>
  <gp-dialog
    :visible="visible"
    :width="step === 0 ? '30%' : '60%'"
    :top="step === 0 ? '15vh' : '4vh'"
    border
    @close="cancle"
  >
    <template #title>
      <span :class="co.dialogtitle">{{ title }}</span>
    </template>
    <div class="dialog-content" :style="{ height: step === 0 ? '300px' : '384px' }">
      <!-- <div class="dialog-content"> -->
      <!--新建呼叫器  -->
      <div v-show="step === 0" class="tw-px-1">
        <CallerBaseForm ref="callerBaseForm" :init_value="init_value"></CallerBaseForm>
      </div>
      <!-- 呼叫器配置 -->
      <div v-if="step === 1 || step === 2" :class="co.step1">
        <CallerButtonConfig
          ref="callerButtonConfig"
          :loading-flag="loadingFlag"
          :mode="mode"
          :init_value="init_value"
          :device-id="deviceId"
        >
        </CallerButtonConfig>
      </div>
    </div>
    <template v-if="showFooter">
      <!-- 底部按钮 -->
      <div slot="footer">
        <!-- 保存 -->
        <!-- <gp-button v-if="step === 0" class="w100" @click="closeDialog"> -->
        <gp-button v-if="step === 0" class="w100" @click="saveData">
          <span>{{ $t("geekplus.gms.client.screen.callerConfiguration.save.description") }}</span>
        </gp-button>
        <!-- 保存并下一步 -->
        <gp-button v-if="step === 0" type="primary" :loading="saveLoading" class="w140" @click="saveDialog(false)">
          <span>{{ $t("geekplus.gms.client.screen.callerConfiguration.saveAndNext.description") }}</span>
        </gp-button>
        <!-- 取消 -->
        <gp-button v-if="step === 1" :loading="saveLoading" class="w140" @click="cancle">
          <span>{{ $t("geekplus.gms.client.screen.callerConfiguration.cancel.description") }}</span>
        </gp-button>
        <!-- 保存 -->
        <gp-button v-if="step === 1" type="primary" :loading="saveLoading" class="w140" @click="saveDialog(true)">
          <span>{{ $t("geekplus.gms.client.screen.callerConfiguration.save.description") }}</span>
        </gp-button>
        <gp-button v-if="step === 2" plain class="w140" @click="editBtconfig">
          <span>{{ $t("geekplus.gms.client.screen.callerConfiguration.edit.description") }}</span>
        </gp-button>
      </div>
    </template>
  </gp-dialog>
</template>
<script>
// 导入ramda 库
import { Message } from "geekplus-ui";
import { toRefs } from "vue";
import CallerBaseForm from "./Caller-base-form.vue";
import CallerButtonConfig from "./caller-button-config.vue";
import { defineComponent, ref, computed } from "vue";
import { saveButtons, saveButton } from "gms-apis/caller-configuration";
import { useI18n } from "@/hooks";

export default defineComponent({
  name: "EditDialog",
  components: { CallerBaseForm, CallerButtonConfig },
  props: {
    step: {
      type: Number,
      default: 0,
    },
    visible: {
      type: Boolean,
      default: false,
    },
    mode: {
      type: String,
      default: "add",
    },
    init_value: {
      type: Array,
      default: [],
    },
  },
  emits: ["updateStep", "updateMode", "updateVisible"], // 这里写你要触发的事件名
  setup(props, { emit }) {
    const { mode, step, visible } = toRefs(props);
    const t = useI18n();
    const loadingFlag = ref(true);
    const callerBaseForm = ref(null); // 创建表单的实例
    const callerButtonConfig = ref(null); // 呼叫器按钮配置实例
    const buttonselectvalue = ref("");
    // const step = ref(props.step);
    const saveLoading = ref(false); // 按钮的loading
    const showFooter = ref(true);
    const currentButtonName = ref("1"); // 当前操作的那个按钮
    const deviceId = ref(""); // 呼叫器编号
    const idflag = ref("false"); // 判断是否加id 属性
    const saveData = async () => {
      const saveData = callerBaseForm.value.step0Item.$getData();
      const valid = await callerBaseForm.value.step0Item.$validate();
      if (!valid) return;
      try {
        const data = await saveButtons({ ...saveData, buttonNum: 4 });
        if (data) {
          callerBaseForm.value.step0Item.$setData({
            controllerName: "",
            controllerCode: "",
          });
          step.value = null;
          emit("updatevisible", false);
          Message.success(t("geekplus.gms.client.screen.callerConfiguration.saveSuccess"));
        }
      } catch (error) {
        console.error(error);
      }
    };

    // 定义了一个映射对象
    const triggerTypeMapping = {
      GENERAL_POINT: 0,
      WORKSTATION: 2,
      AREA: 1, // 如果没有对应的值，可以设置为 null 或其他默认值
      PALLET_POINT: 3,
      // 添加其他映射关系，如果有的话
    };
    // 判断这个字段的值 controllerType 1 流程控制 2 节点控制 3 系统控制
    const getTypeValue = (val) => {
      if (val === "10") {
        return "1";
      } else if (val === "40" || val === "43" || val === "44") {
        return "2";
      } else {
        return "3";
      }
    };

    // 判断那个按钮没有完整的保存
    const saveValidation = async () => {
      const buttons = callerButtonConfig.value.buttons;
      for (let index = 0; index < buttons.length; index++) {
        if (buttons[index].state) {
          const form = buttons[index].form;
          // 先进行表单验证
          const isValid = await new Promise((resolve) => {
            form.$validate((valid) => {
              resolve(valid);
            });
          });
          if (Number(buttons[index].buttonselectvalue) === 40) {
            // 检查 positionState0 是否为空（假设为空数组即为未配置）
            if (!buttons[index].positionState0 || buttons[index].positionState0.length === 0) {
              buttons[index].errorflag = true;
            }
          } else if (Number(buttons[index].buttonselectvalue) === 43) {
            // 检查 positionStatein 是否为空
            if (!buttons[index].positionStatein || buttons[index].positionStatein.length === 0) {
              buttons[index].errorflag = true;
            }
          } else if (Number(buttons[index].buttonselectvalue) === 44) {
            // 检查 positionStateleave 是否为空
            if (!buttons[index].positionStateleave || buttons[index].positionStateleave.length === 0) {
              buttons[index].errorflag = true;
            }
          }
          if (!isValid || buttons[index].errorflag) {
            currentButtonName.value = buttons[index].buttonCode;
            // Message.error(`${buttons[index].names}没有配置完成`);
            return false;
          }
          // 根据不同的 operationCommand 进行额外的校验
        }
      }
      return true;
    };

    // 保存和保存下一步函数
    const saveDialog = async (flag) => {
      // 保存并下一步
      if (!flag) {
        const valid = await callerBaseForm.value.step0Item.$validate();
        if (!valid) return;
        try {
          const saveData = callerBaseForm.value.step0Item.$getData();
          const data = await saveButtons({ ...saveData, buttonNum: 4 });

          deviceId.value = data?.code;
          loadingFlag.value = false;
          step.value = 1;
        } catch (error) {
          console.error(error);
        }
        return;
      }
      // 保存配置
      const controllerData = callerBaseForm.value.step0Item.$getData();
      // 先对 state 为 true 的按钮进行校验这里是在保存的时候对所有按钮配置项进行非空校验
      const valid = await saveValidation();
      if (!valid) {
        return;
      }
      const buttons = callerButtonConfig.value.buttons;
      const buttonsData = buttons.map((item, index) => {
        const btnFormData = item.form.$getData();
        let triggerKey = null;
        if (item.positionState0 && item.positionState0.length) {
          triggerKey = item.positionState0[0];
        } else if (item.positionStatein && item.positionStatein.length) {
          triggerKey = item.positionStatein[0];
        } else if (item.positionStateleave && item.positionStateleave.length) {
          triggerKey = item.positionStateleave[0];
        }
        const triggerTypeValue =
          triggerKey !== null && triggerTypeMapping[triggerKey] !== undefined ? triggerTypeMapping[triggerKey] : 0;

        let stopPointIds = "";
        if (item.positionState0 && item.positionState0.length > 1) {
          stopPointIds = item.positionState0[1];
        } else if (item.positionStatein && item.positionStatein.length > 1) {
          stopPointIds = item.positionStatein[1];
        } else if (item.positionStateleave && item.positionStateleave.length > 1) {
          stopPointIds = item.positionStateleave[1];
        } else {
          if (item.triggerflag === true) {
            stopPointIds = btnFormData.triggerposition;
          } else {
            stopPointIds = "";
          }
        }
        const buttonData = {
          // 按钮编码
          buttonCode: item.buttonCode,
          buttonCommand: "0",
          buttonType: "0",
          // 盒子编码
          controllerCode: controllerData.controllerCode,
          // 盒子名称
          controllerName: controllerData.controllerName,
          // 操作指令
          operationCommand: btnFormData.operationCommand,
          // 托盘位  点位  这里有如果
          stopPointId: stopPointIds,
          // 流程配置ID
          workflowConfigId: btnFormData.workflowConfigId,
          workstationId: "",
          workflowName: "",
          // 触发方式
          triggerType: triggerTypeValue,
          bussinessModel: 0,
          nodeType: 0,
          targetPointType: "",
          targetPointCode: "",
          // 容器类型
          shelfCategoryId: btnFormData.shelfCategoryId,
          // 容器角度
          shelfAngle: btnFormData.shelfAngle,
          shelfCode: "",
          controllerType: getTypeValue(btnFormData.operationCommand),
          status: item.state ? 1 : 0,
        };
        if (item.editId) {
          buttonData.id = item.editId;
        }
        return buttonData;
      });
      const data = await saveButton(buttonsData);
      if (data.succ) {
        Message.success(t("geekplus.gms.client.screen.callerConfiguration.saveSuccess"));
        // 清空盒子编码和盒子名称
        callerBaseForm.value.step0Item.$setData({
          controllerName: "",
          controllerCode: "",
        });

        buttons.forEach((button) => {
          button.form.$setData({
            buttonCode: "",
            buttonstart: false,
            operationCommand: "",
            workflowConfigId: "",
            triggerposition: "",
            shelfCategoryId: "",
            shelfAngle: "", // 如果需要重置为特定的默认值
          });
          // 清空其他属性
          button.state = false;
          button.buttonselectvalue = "";
          button.selectworkflowConfigId = "";
          button.triggerflag = false;
          button.defaulttriggerval = "";
          button.editId = "";

          // 清空 positionState 数据
          // button.positionState.nodeList = [];
          button.positionState0 = [];
          button.positionStatein = [];
          button.positionStateleave = [];
        });

        step.value = null;
        idflag.value = false;
        emit("updatevisible", false);
        return;
      }
      Message.error(t("geekplus.gms.client.screen.callerConfiguration.saveErroe"));
    };
    const title = computed(() =>
      step.value === 0
        ? t("geekplus.gms.client.screen.callerConfiguration.createCaller")
        : step.value === 2
        ? t("geekplus.gms.client.screen.callerConfiguration.callerDetails")
        : t("geekplus.gms.client.screen.callerConfiguration.callerButtonConfig")
    );

    // 点击图标的X
    const cancle = () => {
      step.value = 0;
      emit("updatevisible", false);
    };
    // 点击编辑
    const editBtconfig = () => {
      emit("updateStep", 1);
      emit("updateMode", "edit");
      loadingFlag.value = false;
    };
    return {
      saveLoading,
      showFooter,
      currentButtonName,
      saveData,
      saveDialog,
      title,
      buttonselectvalue,
      cancle,
      deviceId,
      editBtconfig,
      getTypeValue,
      idflag,
      saveValidation,
      loadingFlag,
      callerBaseForm,
      callerButtonConfig,
    };
  },
});
</script>
<style lang="scss" module="co" src="./common.scss"></style>
<style lang="scss" scoped>
/* 局部样式 */
.dialog-content {
  height: 400px;
}

.step1 {
  width: 100%;
  height: 350px;
}
.gp-tabs__nav-wrap:after {
  display: none;
}
</style>
