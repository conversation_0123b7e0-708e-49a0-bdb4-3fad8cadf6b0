{"geekplus.gms.client.screen.taskTesting.pageTitle": "Task debugging", "geekplus.gms.client.screen.taskTesting.pageDesc": "The configured workflow and point can be debugged and batch tested.", "geekplus.gms.client.screen.taskTesting.placeholderTaskName": "Task name", "geekplus.gms.client.screen.taskTesting.menu.routeTest": "Route location debugging", "geekplus.gms.client.screen.taskTesting.menu.workflowTest": "Workflow debugging", "geekplus.gms.client.screen.taskTesting.btn.addRouteTest": "New route point debugging", "geekplus.gms.client.screen.taskTesting.btn.addWorkflowTest": "New workflow debugging", "geekplus.gms.client.screen.taskTesting.btn.batchTest": "Batch task testing", "geekplus.gms.client.screen.taskTesting.btn.saveBinSpaceCofnig": "Save the point parameters", "geekplus.gms.client.screen.taskTesting.btn.startSelected": "One-key start", "geekplus.gms.client.screen.taskTesting.btn.pauseSelected": "One-key suspend", "geekplus.gms.client.screen.taskTesting.btn.deleteSelected": "One-key delete", "geekplus.gms.client.screen.taskTesting.link.batchConfig": "Configure in batches > ", "geekplus.gms.client.screen.taskTesting.column.id": "Task ID", "geekplus.gms.client.screen.taskTesting.column.name": "Name", "geekplus.gms.client.screen.taskTesting.column.type": "Type", "geekplus.gms.client.screen.taskTesting.column.status": " State", "geekplus.gms.client.screen.taskTesting.column.progress": "Execution progress", "geekplus.gms.client.screen.taskTesting.column.createTime": "Creation time", "geekplus.gms.client.screen.taskTesting.column.cargoCode": "Shelf bin code", "geekplus.gms.client.screen.taskTesting.column.cargoOrder": "<PERSON><PERSON> bin <PERSON>", "geekplus.gms.client.screen.taskTesting.column.dockHeight": "Docking height", "geekplus.gms.client.screen.taskTesting.column.dropHeight": "Placement height", "geekplus.gms.client.screen.taskTesting.column.pickHeight": "Fetching height", "geekplus.gms.client.screen.taskTesting.column.equipType": "Equipment type", "geekplus.gms.client.screen.taskTesting.listFilterPlaceholder.taskType": "Task type", "geekplus.gms.client.screen.taskTesting.listFilterPlaceholder.taskStatus": "Task status", "geekplus.gms.client.screen.taskTesting.msg.stopConfirm": "The user can only suspend the task that has not been generated. To cancel the task that is being executed, go to the task monitoring interface to cancel. Are you sure to suspend?", "geekplus.gms.client.screen.taskTesting.msg.nodeListEmpty": "Specify the workflow template first", "geekplus.gms.client.screen.taskTesting.msg.taskStartSucceed": "The task is started successfully.", "geekplus.gms.client.screen.taskTesting.msg.taskPauseSucceed": "The task is suspended successfully.", "geekplus.gms.client.screen.taskTesting.msg.taskSuspendSucceed": "The task is suspended successfully.", "geekplus.gms.client.screen.taskTesting.msg.taskRemoveSucceed": "The task is deleted successfully.", "geekplus.gms.client.screen.taskTesting.msg.cargoInfoSaveSucceed": "The point parameters are saved successfully.", "geekplus.gms.client.screen.taskTesting.msg.cargoInfoNotFound": "Failed to query the parameter configuration of the selected point. Configure it in batches first.", "geekplus.gms.client.screen.taskTesting.msg.flowTemplateExpired": "The current template data change. Select it again.", "geekplus.gms.client.screen.taskTesting.msg.flowTemplateExpiredWhenStart": "The current template data change. Edit it again.", "geekplus.gms.client.screen.taskTesting.msg.flowPositionValueMismatch": "The map point data change. Configure it again.", "geekplus.gms.client.screen.taskTesting.header.routeTestNew": "New route point debugging", "geekplus.gms.client.screen.taskTesting.header.routeTestEdit": "Edit route point debugging", "geekplus.gms.client.screen.taskTesting.header.routeTestView": "Route point debugging details", "geekplus.gms.client.screen.taskTesting.header.routeTestCopy": "Copy route point debugging", "geekplus.gms.client.screen.taskTesting.header.flowTestNew": "New workflow debugging", "geekplus.gms.client.screen.taskTesting.header.flowTestCopy": "Copy workflow debugging", "geekplus.gms.client.screen.taskTesting.header.flowTestEdit": "Edit workflow debugging", "geekplus.gms.client.screen.taskTesting.header.flowTestView": "Workflow debugging details", "geekplus.gms.client.screen.taskTesting.sectionTitle.basicInfo": "Basic info", "geekplus.gms.client.screen.taskTesting.sectionTitle.runningPlan": "Execution plan configuration", "geekplus.gms.client.screen.taskTesting.sectionTitle.flowTemplate": "Workflow configuration", "geekplus.gms.client.screen.taskTesting.sectionTitle.nodeConfig": "Point configuration", "geekplus.gms.client.screen.taskTesting.form.labelTaskName": "Task name", "geekplus.gms.client.screen.taskTesting.form.labelRobotType": "Robot type", "geekplus.gms.client.screen.taskTesting.form.labelRobotAssignMode": "Specify the mode", "geekplus.gms.client.screen.taskTesting.form.labelLoadType": "No-load", "geekplus.gms.client.screen.taskTesting.form.labelTestCount": "Number of executions", "geekplus.gms.client.screen.taskTesting.form.labelTestMode": "Debug Mode", "geekplus.gms.client.screen.taskTesting.form.labelFlowTemplate": "Workflow Template", "geekplus.gms.client.screen.taskTesting.form.labelTemplateType": "Template type", "geekplus.gms.client.screen.taskTesting.form.labelPickDrop": "Picking and placement", "geekplus.gms.client.screen.taskTesting.form.labelEmptyOrFull": "Empty/full", "geekplus.gms.client.screen.taskTesting.form.labelRobotOrient": "Robot side orientation", "geekplus.gms.client.screen.taskTesting.form.labelWorkflowEnd": "Whether it is the destination of the workflow", "geekplus.gms.client.screen.taskTesting.form.labelAssignFlow": "Specify the workflow", "geekplus.gms.client.screen.taskTesting.form.labelAssignFlowTemplate": "Specify the workflow template", "geekplus.gms.client.screen.taskTesting.form.labelStartPointCode": "Starting point code", "geekplus.gms.client.screen.taskTesting.form.labelTargetPointCode": "Target location code", "geekplus.gms.client.screen.taskTesting.form.labelMiddlePointCode": "Intermediate point code", "geekplus.gms.client.screen.taskTesting.form.labelFlowAssignMode": "Specify the mode", "geekplus.gms.client.screen.taskTesting.form.labelDeviceCode": "Machine/buffer shelf code", "geekplus.gms.client.screen.taskTesting.form.labelStationStatus": "Machine status", "geekplus.gms.client.screen.taskTesting.form.labelRollLength": "Coil length", "geekplus.gms.client.screen.taskTesting.form.labelRollType": "Coil type", "geekplus.gms.client.screen.taskTesting.form.labelSafetyHeight": "Safety height", "geekplus.gms.client.screen.taskTesting.form.labelExtraParam15": "Parameter 15", "geekplus.gms.client.screen.taskTesting.form.labelExtraParam16": "Parameter 16", "geekplus.gms.client.screen.taskTesting.form.labelExtraParam17": "Parameter 17", "geekplus.gms.client.screen.taskTesting.form.testModeFlow": "Specify the workflow template", "geekplus.gms.client.screen.taskTesting.form.testModeBus": "Combine by scenario", "geekplus.gms.client.screen.taskTesting.form.assignModeById": "Specify the robot ID", "geekplus.gms.client.screen.taskTesting.form.assignModeByModel": "Specify the robot model", "geekplus.gms.client.screen.taskTesting.form.placeholderTaskName": "Please enter the task name", "geekplus.gms.client.screen.taskTesting.form.tipLoadType": "When pick-and-place load test is conducted, the robot will perform queuing by the point selection sequence and automatically return the container to the starting point after arriving at the destination.", "geekplus.gms.client.screen.taskTesting.form.namePick": "Specify the workflow template", "geekplus.gms.client.screen.taskTesting.robotType.cantilever": "Cantilever robot", "geekplus.gms.client.screen.taskTesting.robotType.topModule": "Lifting robot", "geekplus.gms.client.screen.taskTesting.robotType.carrier": "Latent robot", "geekplus.gms.client.screen.taskTesting.robotType.roller": "Roller robot", "geekplus.gms.client.screen.taskTesting.robotType.forklift": "Forklift robot", "geekplus.gms.client.screen.taskTesting.flowOrTempOpts.flow": "Workflow", "geekplus.gms.client.screen.taskTesting.flowOrTempOpts.temp": "Workflow Template", "geekplus.gms.client.screen.taskTesting.dialogCargo.title": "Select the target location"}