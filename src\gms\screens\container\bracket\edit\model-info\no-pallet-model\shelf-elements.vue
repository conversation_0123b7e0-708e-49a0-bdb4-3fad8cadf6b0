<template>
  <div
    class="tw-relative tw-flex tw-flex-col tw-border tw-border-solid tw-border-gray-300 tw-p-2 tw-mb-4 tw-rounded"
    :class="[cn.border, $props.values.key === $attrs.active ? cn.active : '', $props.mode === 'view' ? '' : 'tw-gap-2']"
  >
    <div class="tw-flex tw-items-center tw-justify-between tw-gap-2">
      <div class="tw-flex tw-items-center tw-gap-1">
        <h3 class="tw-text-sm">
          {{ bracketElementType.getLabelByValue($props.values.type)
          }}{{ values.type !== bracketElementType.FACE ? $attrs.index : "" }}
        </h3>
        <gms-tooltip effect="light" placement="left">
          <template #content>
            <img :src="measureImg" alt="measureMethod" class="tw-w-full" />
          </template>
        </gms-tooltip>
      </div>
      <i
        v-if="values.type !== bracketElementType.FACE"
        :disabled="$props.mode === 'view'"
        class="el-icon-delete tw-justify-self-end tw--right-8 tw-text-xl tw-cursor-pointer"
        :class="$props.mode === 'view' ? 'tw-text-gray-500' : 'tw-text-red-500'"
        @click.stop="handleDeleteElement"
      ></i>
    </div>
    <template v-if="$props.values.type === bracketElementType.LEG">
      <div class="tw-flex tw-items-center tw-gap-1">
        <form-item v-bind="formGroup.x" />
        <span>mm</span>
      </div>
      <div class="tw-flex tw-items-center tw-gap-1">
        <form-item v-bind="formGroup.y" />
        <span>mm</span>
      </div>
      <div class="tw-flex tw-items-center tw-gap-1">
        <form-item v-bind="formGroup.length" />
        <span>mm</span>
      </div>
      <div class="tw-flex tw-items-center tw-gap-1">
        <form-item v-bind="formGroup.width" />
        <span>mm</span>
      </div>
      <div v-if="$props.values.type === bracketElementType.LEG" class="tw-flex tw-items-center tw-gap-1">
        <form-item v-bind="formGroup.height" />
        <span>mm</span>
      </div>
    </template>
    <template v-if="$props.values.type === bracketElementType.FACE">
      <div class="tw-flex tw-items-center tw-gap-1">
        <form-item
          v-bind="formGroup.length"
          :label-text="$t('geekplus.gms.client.screen.bracket.form.bracketFaceLength')"
        />
        <span>mm</span>
      </div>
      <div class="tw-flex tw-items-center tw-gap-1">
        <form-item
          v-bind="formGroup.width"
          :label-text="$t('geekplus.gms.client.screen.bracket.form.bracketFaceWidth')"
        />
        <span>mm</span>
      </div>
    </template>
  </div>
</template>
<script>
import { cloneDeep } from "lodash";
import { defineComponent } from "vue";
import { FormItem, createFormItemGroup, validators } from "@srpe-fe/uic-el-form-wrapper-vue2";
import { bracketElementType } from "gms-constants";
import { useI18n } from "@/hooks";
import getContainerImage from "@/utils/containerImages";
const measureFace = getContainerImage("measure-bracket-face.png");
const measureLeg = getContainerImage("measure-leg.jpg");
import GmsTooltip from "gms-components/gms-tooltip.vue";

export default defineComponent({
  name: "ShelfElements",
  components: { FormItem, GmsTooltip },
  props: {
    values: { type: Object, default: () => ({}) },
    formValues: { type: Object, default: () => ({}) },
    mode: { type: String, default: "" },
  },
  setup(props, { emit }) {
    const t = useI18n();

    const measureImg = props.values.type === bracketElementType.FACE ? measureFace : measureLeg;

    const handleGroupChange = ({ name, value }) => {
      emit("update:values", { ...props.values, [name]: value });
    };

    const handleDeleteElement = () => {
      if (props.mode === "view") return;
      const formValues = cloneDeep(props.formValues);
      const i = props.formValues.modelInfo.findIndex((v) => v.key === props.values.key);
      formValues.modelInfo.splice(i, 1);
      emit("update:formValues", formValues);
    };

    const formGroup = createFormItemGroup(
      {
        length: {
          type: "el-input-number",
          labelText: t("geekplus.gms.client.screen.container.form.length"),
          value: props.values.length ?? "",
          validators: [validators.required],
          labelWidth: "110px",
          size: "small",
          min: 0,
          max: 5000,
        },
        width: {
          type: "el-input-number",
          labelText: t("geekplus.gms.client.screen.container.form.width"),
          value: props.values.width ?? "",
          validators: [validators.required],
          labelWidth: "110px",
          size: "small",
          min: 0,
          max: 5000,
        },
        height: {
          type: "el-input-number",
          labelText: t("geekplus.gms.client.screen.container.form.height"),
          value: props.values.height ?? "",
          validators: [validators.required],
          labelWidth: "110px",
          size: "small",
          min: 0,
          max: 5000,
        },
        x: {
          type: "el-input-number",
          labelText: t("geekplus.gms.client.screen.container.form.xAxisFromCenter"),
          value: props.values.x ?? "",
          validators: [validators.required],
          labelWidth: "110px",
          size: "small",
        },
        y: {
          type: "el-input-number",
          labelText: t("geekplus.gms.client.screen.container.form.yAxisFromCenter"),
          value: props.values.y ?? "",
          validators: [validators.required],
          labelWidth: "110px",
          size: "small",
        },
      },
      { handleGroupChange, labelPosition: "right", mode: props.mode === "view" ? "view" : "edit" }
    );
    return { measureImg, bracketElementType, formGroup, handleDeleteElement };
  },
});
</script>

<style lang="scss" module="cn">
:global(.gp-input-number.gp-input-number--small) {
  width: 100%;
}
.border {
  border: 2px solid #efefef;
}
.active {
  border: 2px solid #409eff;
}
</style>
