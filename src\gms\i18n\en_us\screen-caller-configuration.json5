/**
 * 呼叫器页面国际化字符集
 * key 必须以 'geekplus.gms.client.screen.callerConfiguration.' 开头
 */
{
  "geekplus.gms.client.screen.callerConfiguration.title": "Caller",
  "geekplus.gms.client.screen.callerConfiguration.subtitle": "Add and manage callers and configure caller button functions",
  "geekplus.gms.client.screen.callerConfiguration.createCaller": "Create New Caller",
  "geekplus.gms.client.screen.callerConfiguration.callerDetails": "Caller Details",
  "geekplus.gms.client.screen.callerConfiguration.callerButtonConfig": "Caller Button Configuration",
  "geekplus.gms.client.screen.callerConfiguration.id": "ID",
  "geekplus.gms.client.screen.callerConfiguration.name": "Name",
  "geekplus.gms.client.screen.callerConfiguration.ip address": "IP Address",
  "geekplus.gms.client.screen.callerConfiguration.status": "Status",
  "geekplus.gms.client.screen.callerConfiguration.editor": "Editor",
  "geekplus.gms.client.screen.callerConfiguration.edit time": "Edit Time",
  "geekplus.gms.client.screen.callerConfiguration.allocation": "Configuration",
  "geekplus.gms.client.screen.callerConfiguration.deletion": "Delete",
  "geekplus.gms.client.screen.callerConfiguration.deletionSuccess": "Deletion Successful",
  "geekplus.gms.client.screen.callerConfiguration.deletionErroe": "Deletion Failed",
  "geekplus.gms.client.screen.callerConfiguration.saveSuccess": "Save Successful",
  "geekplus.gms.client.screen.callerConfiguration.saveErroe": "Save Failed",
  "geekplus.gms.client.screen.callerConfiguration.FourButtonCallerType": "Caller Type: 4-Button Caller",
  "geekplus.gms.client.screen.callerConfiguration.callerid": "Caller ID",
  "geekplus.gms.client.screen.callerConfiguration.started": "Enabled",
  "geekplus.gms.client.screen.callerConfiguration.not started": "Disabled",
  "geekplus.gms.client.screen.callerConfiguration button number": "Button Code",
  "geekplus.gms.client.screen.callerConfiguration trigger ": "Trigger",
  "geekplus.gms.client.screen.callerConfiguration.timerange": "Time Range",
  "geekplus.gms.client.screen.callerConfiguration.starttime": "Start Time",
  "geekplus.gms.client.screen.callerConfiguration.endtime": "End Time",
  "geekplus.gms.client.screen.callerConfiguration.callercode": "Caller Code",
  "geekplus.gms.client.screen.callerConfiguration.caller id number": "Caller ID Number",
  "geekplus.gms.client.screen.callerConfiguration.button code": "Button Number",
  "geekplus.gms.client.screen.callerConfiguration.execute content": "Execution Content",
  "geekplus.gms.client.screen.callerConfiguration.message": "Message Content",
  "geekplus.gms.client.screen.callerConfiguration.executiTime": "Execution Time",
  "geekplus.gms.client.screen.callerConfiguration.execution result": "Execution Result",
  "geekplus.gms.client.screen.callerConfiguration.confirmDelete": "Are you sure you want to delete this caller configuration?",
  "geekplus.gms.client.screen.callerConfiguration.confirmDelete.description": "Please fill in the required fields",
  "geekplus.gms.client.screen.callerConfiguration.save.description": "Save",
  "geekplus.gms.client.screen.callerConfiguration.saveAndNext.description": "Save and Next",
  "geekplus.gms.client.screen.callerConfiguration.cancel.description": "Cancel",
  "geekplus.gms.client.screen.callerConfiguration.edit.description": "Edit",
  "geekplus.gms.client.screen.callerConfiguration.callerName.description": "Caller Name",
  "geekplus.gms.client.screen.callerConfiguration.inputPrompt.description": "Please Enter",
  "geekplus.gms.client.screen.callerConfiguration.callerBoxUniqueCode": "Unique Number of Caller Box",
  "geekplus.gms.client.screen.callerConfiguration.buttonOne": "Button One",
  "geekplus.gms.client.screen.callerConfiguration.buttonTwo": "Button Two",
  "geekplus.gms.client.screen.callerConfiguration.buttonThree": "Button Three",
  "geekplus.gms.client.screen.callerConfiguration.buttonFour": "Button Four",
  "geekplus.gms.client.screen.callerConfiguration.buttonEnable": "Button Enable",
  "geekplus.gms.client.screen.callerConfiguration.buttonFunction": "Button Function",
  "geekplus.gms.client.screen.callerConfiguration.fillRequiredFields": "Fill in Required Fields",
  "geekplus.gms.client.screen.callerConfiguration.pleaseSelect": "Please Select",
  "geekplus.gms.client.screen.callerConfiguration.processFlow": "Process Flow",
  "geekplus.gms.client.screen.callerConfiguration.triggerLocation": "Trigger Location",
  "geekplus.gms.client.screen.callerConfiguration.containerType": "Container Type",
  "geekplus.gms.client.screen.callerConfiguration.containerAngle": "Container Angle",
  "geekplus.gms.client.screen.callerConfiguration.triggerLocation": "Trigger Location",
  "geekplus.gms.client.screen.callerConfiguration.entryLocation": "Entry Location",
  "geekplus.gms.client.screen.callerConfiguration.exitLocation": "Exit Location",
  "geekplus.gms.client.screen.callerConfiguration.triggerSuccess": "Trigger Successful",
  "geekplus.gms.client.screen.callerConfiguration.configured": "Configured",
  "geekplus.gms.client.screen.callerConfiguration.notConfigured": "Not Configured",
  "geekplus.gms.client.screen.callerConfiguration.trigger": "Trigger"
}