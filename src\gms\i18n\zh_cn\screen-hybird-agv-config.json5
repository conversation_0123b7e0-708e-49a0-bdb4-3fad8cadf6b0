/**
 *   工作站页面国际化字符集
 *   key 必须以 'geekplus.gms.client.screen.hybirdAGVConfig.' 开头
 */
{
  "geekplus.gms.client.screen.hybirdAGVConfig.sectionTitle.equipParams": "设备参数",
  "geekplus.gms.client.screen.hybirdAGVConfig.formLabel.stopPointName": "名称",

  "geekplus.gms.client.screen.hybirdAGVConfig.msg.binCodeOrderEmpty": "[货位点编码] 和 [货位顺序号] 必须同时填写或者同时为空",

  "geekplus.gms.client.screen.hybirdAGVConfig.formLabelHelp.binCodeRow1": "由于上游管理系统，货位点编码可能自定义，比如：YWLN1234，下发任务时传递的为货位点编码，所以需要将上游的货位编码与对接点地图点位在此处进行绑定，这样GMS才能通过货位编码查询到地图点位，正常生成任务.",
  "geekplus.gms.client.screen.hybirdAGVConfig.formLabelHelp.binCodeRow2": "有两种使用场景：",
  "geekplus.gms.client.screen.hybirdAGVConfig.formLabelHelp.binCodeRow3": "1、上游货位编码为自定义时，上游通过货位编码创建任务时，首先需要在此处进行货位编码和地图点位绑定，请求参数需要传递locationTo、toBinOrder(货位顺序号)，由GMS将货位编码转换为地图点位，下发到调度系统",
  "geekplus.gms.client.screen.hybirdAGVConfig.formLabelHelp.binCodeRow4": "2、上游货位编码和GMS地图点位数据一致时，上游通过货位编码创建任务，不需要在此处进行绑定操作，GMS直接将货位编码下发给调度系统",

  "geekplus.gms.client.screen.hybirdAGVConfig.formLabelHelp.binOrder": "货位顺序号用来区分同一个点位，有多层货位的场景，单层默认为1",
  "geekplus.gms.client.screen.hybirdAGVConfig.formLabelHelp.stopPointCode": "机器人到达机台前的地图点位编码",
}
