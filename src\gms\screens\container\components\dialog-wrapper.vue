<template>
  <gp-dialog
    :show-close="false"
    :custom-class="cn.custom_dialog_wrapper"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    v-bind="$attrs"
    v-on="$listeners"
  >
    <slot></slot>
    <template #footer>
      <slot name="footer"></slot>
    </template>
  </gp-dialog>
</template>
<script>
import { defineComponent } from "vue";

export default defineComponent({
  name: "DialogWrapper",
});
</script>
<style lang="scss" module="cn">
.custom_dialog_wrapper {
  border-radius: 5px;
  :global(.gp-dialog__header) {
    padding: 0 0 10px;
  }
  :global(.gp-dialog__body) {
    padding: 10px;
  }
}
</style>
