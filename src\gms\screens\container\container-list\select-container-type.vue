<template>
  <div :class="cn.container_list_root">
    <template v-if="$props.containerTypeList.length">
      <h3 class="tw-text-l tw-font-bold">
        {{ $t("geekplus.gms.client.screen.container.form.pleaseSelectContainerTypeName") }}
      </h3>
      <div class="tw-grid tw-grid-cols-3 tw-w-full tw-p-2 tw-gap-2 tw-gap-x-6">
        <div
          v-for="(item, i) in $props.containerTypeList"
          :key="i"
          class="tw-relative tw-text-center tw-p-2 tw-border tw-border-solid tw-rounded hover:tw-bg-gray-50"
          :class="$props.type === item.id ? 'tw-border-blue-300' : 'tw-border-transparent'"
          @click="handleClick(item.id)"
        >
          <MCheckbox
            :value="$props.type === item.id"
            :name="String(item.id)"
            :true-label="item.id"
            false-label=""
            class="tw-w-full"
            :label="item.id"
          >
            <img
              :src="item.imgUrl"
              :class="`tw-h-24 ${item.shape === containerTypeShape.PALLET_RACKING ? '' : 'tw-w-24'} `"
            />
          </MCheckbox>
          <text-overflow :content="item.name" />
        </div></div
    ></template>
    <!-- <EmptyPage
      v-else
      :button-name="$t('geekplus.gms.client.screen.container.btns.addContainerType')"
      @click="handleAddContainerType"
    /> -->
    <EmptyPage v-else />
  </div>
</template>
<script>
import { defineComponent, onMounted } from "vue";
import eventEmitter from "@/common/eventEmitter";
import { containerTypeShape } from "gms-constants";
import MCheckbox from "@/gms/screens/container/components/m-checkbox.vue";
import TextOverflow from "gms-components/text-overflow.vue";
import EmptyPage from "gms-components/empty-page";

export default defineComponent({
  name: "SelectContainerType",
  components: { MCheckbox, EmptyPage, TextOverflow },
  props: {
    containerTypeList: { type: Array, default: () => [] },
    type: { type: Number, default: null },
    send: { type: Function, default: null },
  },
  emit: ["update:type"],
  setup(props, { emit }) {
    onMounted(() => {
      // 默认选中第一种容器类型
      if (!props.type) {
        emit("update:type", props.containerTypeList[0]?.id);
      }
    });

    const handleAddContainerType = () => {
      props.send("SUCCESS");
      eventEmitter.emit("container:updateActiveName", {
        activeName: "ContainerModel",
        mode: "add",
      });
    };

    const handleClick = (val) => {
      emit("update:type", val);
    };
    return { containerTypeShape, handleClick, handleAddContainerType };
  },
});
</script>
<style lang="scss" module="cn">
.container_list_root {
  :global(.gp-radio__input) {
    position: absolute;
    right: 0;
  }
}
</style>
