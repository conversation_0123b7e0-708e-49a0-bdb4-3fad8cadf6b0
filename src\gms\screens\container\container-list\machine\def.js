import { createMachine } from "xstate";

export const containerMachine = createMachine({
  id: "containerMachine",
  initial: "init",
  states: {
    init: {
      on: {
        CREATE: "add",
        EDIT: "edit",
        ENTRY: "entry",
      },
    },
    add: {
      on: {
        CANCEL: "init",
        ENTRY: "entry",
        SUCCESS: "init",
      },
      initial: "select",
      states: {
        select: {
          on: {
            NEXT: "list",
          },
        },
        list: {
          on: {
            PREV: "select",
          },
          initial: "edit",
          states: {
            edit: {
              on: {
                BATCH: "batching",
                SAVE: "saving",
              },
            },
            batching: {
              on: {
                SUCCESS: "edit",
              },
            },
            saving: {
              on: {
                ERROR: "edit",
              },
            },
          },
        },
      },
    },
    edit: {
      on: {
        CANCEL: "init",
        SUCCESS: "init",
      },
    },
    entry: {
      on: {
        CANCEL: "init",
        SUCCESS: "init",
      },
      initial: "edit",
      states: {
        edit: {
          on: {
            SAVE: "saving",
          },
        },
        saving: {
          on: {
            ERROR: "edit",
          },
        },
      },
    },
  },
});
