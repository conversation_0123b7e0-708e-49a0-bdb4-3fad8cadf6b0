<template>
  <div :class="cn.page_container">
    <div :class="cn.setting_panel">
      <div :class="cn.row">
        <label :class="cn.label">点位类型: </label>
        <gp-checkbox-group v-model="state.groupCheckList">
          <gp-checkbox-button v-for="group in state.groupOptions" :label="group.value" :key="group.value">
            {{ group.label }}
          </gp-checkbox-button>
        </gp-checkbox-group>
      </div>

      <div :class="cn.row">
        <label :class="cn.label">标签显示数量: </label>
        <gp-input-number v-model="state.tagDisplayCount" />
      </div>

      <div :class="cn.row">
        <label :class="cn.label">filterable: </label>
        <gp-switch v-model="state.filterable" />
      </div>

      <div :class="cn.row">
        <label :class="cn.label">item filter: </label>
        only includes <gp-input v-model="state.filterIncludes" clearable style="width: 200px; margin-left: 8px" />
      </div>

      <div :class="cn.row">
        <label :class="cn.label">disable filter: </label>
        only includes <gp-input v-model="state.disableIncludes" clearable style="width: 200px; margin-left: 8px" />
      </div>

      <div :class="cn.row">
        <label :class="cn.label">clearable: </label>
        <gp-switch v-model="state.clearable" />
      </div>
    </div>
    <hr />
    <h1>多选</h1>
    <div :class="cn.row">
      <label :class="cn.label"> edit: </label>
      <PositionSelect
        multiple
        :value="state.valueMultiple"
        :tag-display-count="state.tagDisplayCount"
        :class="cn.select"
        :groups="propGroups"
        :filterable="state.filterable"
        :item-filter="state.itemFilter"
        :disable-filter="state.disableFilter"
        :clearable="state.clearable"
        :overlay-z-index="3000"
        :item-mappers="state.itemMappers"
        placeholder="请选择"
        @change="handleMultipleSelectChange"
      />
      <label :class="cn.label" style="margin-left: 240px"> view: </label>
      <PositionSelect
        v-if="true"
        mode="view"
        multiple
        :value="state.valueMultiple"
        :tag-display-count="state.tagDisplayCount"
        :class="cn.select"
        :groups="propGroups"
      />
    </div>
    <div :class="cn.block"></div>
    <h1 style="margin-top: 420px">单选</h1>

    <div :class="cn.row">
      <label :class="cn.label"> edit: </label>
      <PositionSelect
        :value="state.valueSingle"
        :tag-display-count="state.tagDisplayCount"
        :class="cn.select"
        :groups="propGroups"
        :filterable="state.filterable"
        :item-filter="state.itemFilter"
        :disable-filter="state.disableFilter"
        :clearable="state.clearable"
        @change="handleSingleSelectChange"
        style="width: 200px"
      />

      <label :class="cn.label" style="margin-left: 40px"> view: </label>
      <PositionSelect
        v-if="true"
        mode="view"
        :value="state.valueSingle"
        :tag-display-count="state.tagDisplayCount"
        :class="cn.select"
      />
    </div>
  </div>
</template>

<script>
/* eslint-disable no-console */
import { computed, reactive, watchEffect } from "vue";
import * as R from "ramda";
import { isEmpty } from "gms-utils";
import PositionSelect from "gms-components/business/position-select";
import { locationTypes } from "gms-constants";

export default {
  name: "DemoPositionSelect",
  components: { PositionSelect },
  props: {},
  emits: [],
  setup() {
    const state = reactive({
      groupCheckList: [undefined],
      groupOptions: [
        { label: "全部", value: undefined },
        { label: "地图点位", value: "GENERAL_POINT" },
        { label: "工作站", value: "WORKSTATION" },
        { label: "区域", value: "AREA" },
        { label: "托盘位", value: "PALLET_POINT" },
      ],
      valueMultiple: [
        ["GENERAL_POINT", "00250055"],
        ["GENERAL_POINT", "00350045"],
        ["GENERAL_POINT", "00350055"],
      ],
      valueSingle: ["GENERAL_POINT", "00250055"],
      filterable: true,
      filterIncludes: "",
      disableIncludes: "",
      tagDisplayCount: 6,
      itemFilter: undefined,
      disableHandler: undefined,
      clearable: true,
      itemMappers: {
        [locationTypes.GENERAL_POINT]: (item) => {
          return { ...item, $labelFull: item.$labelFull + "----" };
          // return item;
        },
        [locationTypes.WORKSTATION]: (item) => {
          return { ...item, $labelFull: item.$labelFull + "----" };
          // return item;
        },
        [locationTypes.AREA]: (item) => {
          return { ...item, $labelFull: item.$labelFull + "----" };
          // return item;
        },
        [locationTypes.PALLET_POINT]: (item) => {
          return { ...item, $labelFull: item.$labelFull + "----" };
          // return item;
        },
        [locationTypes.EQUIPMENT_POINT]: (item) => {
          return { ...item, $labelFull: item.$labelFull + "----" };
          // return item;
        },
      },
    });

    const propGroups = computed(() => {
      const { groupCheckList } = state;
      return groupCheckList.includes(undefined)
        ? ["GENERAL_POINT", "WORKSTATION", "AREA", "PALLET_POINT", ["EQUIPMENT_POINT", { deviceModelType: "HOISTER" }]]
        : R.reject(R.equals(undefined), groupCheckList);
      // return [
      //   "GENERAL_POINT",
      //   "WORKSTATION",
      //   "AREA",
      //   "PALLET_POINT",
      //   ["EQUIPMENT_POINT", { deviceModelType: "HOISTER" }],
      // ];
    });

    watchEffect(() => {
      const text = state.filterIncludes;
      state.itemFilter = (item) => {
        if (isEmpty(text)) return true;
        return item.$value.includes(text);
      };
    });

    watchEffect(() => {
      const text = state.disableIncludes;
      state.disableFilter = (item) => {
        if (isEmpty(text)) return false;
        return item.$value.includes(text);
      };
    });

    const handleMultipleSelectChange = (value, selectedItem) => {
      state.valueMultiple = value;
      console.info("select change", value, selectedItem);
    };

    const handleSingleSelectChange = (value, selectedItem) => {
      state.valueSingle = value;
      console.info("select change", value, selectedItem);
    };

    return { state, propGroups, handleSingleSelectChange, handleMultipleSelectChange };
  },
};
</script>

<style lang="scss" module="cn">
.page_container {
  height: 2000px;
}

.setting_panel {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  gap: 8px;
}

.row {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;

  .label {
    margin-right: 8px;
    min-width: 100px;
  }
}

.select {
  width: 400px;
}

.block {
  width: 100px;
  height: 100px;
  position: fixed;
  top: 400px;
  left: 800px;
  background-color: #c8c8c8;
}

hr {
  margin: 24px 0px;
}
</style>
