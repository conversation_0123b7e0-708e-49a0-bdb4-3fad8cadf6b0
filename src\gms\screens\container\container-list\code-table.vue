<template>
  <gp-form ref="form" label-position="left" :class="cn.page_wrapper" :model="state.form">
    <gp-table
      border
      :data="state.form.innerTableData"
      style="width: 100%"
      max-height="300"
      :header-cell-class-name="addHeaderCellClassName"
    >
      <gp-table-column type="index" :label="$t('geekplus.gms.client.commons.cols.index')" width="100" />
      <gp-table-column :label="$t('geekplus.gms.client.screen.container.columns.containerShape')" prop="containerType">
        <template slot-scope="{ row }">
          <MSelect v-model="row.modelInfo.type" disabled :options="containerTypeShape.toLabelValueList()" />
        </template>
      </gp-table-column>
      <gp-table-column :label="$t('geekplus.gms.client.screen.container.columns.containerCode')" prop="code">
        <template slot-scope="scope">
          <gp-form-item
            :prop="`innerTableData.${scope.$index}.code`"
            :rules="[validators.required, validators.checkCode]"
          >
            <gp-input
              v-model="scope.row.code"
              :maxlength="maxLenContainerCode"
              :disabled="!isCodeEditable"
              @change="handleChange(scope)"
            />
          </gp-form-item>
        </template>
      </gp-table-column>
      <gp-table-column
        v-if="machineState.matches('entry')"
        :label="$t('geekplus.gms.client.screen.container.columns.entryEndPoint')"
        prop="placementPointCode"
      >
        <template slot-scope="scope">
          <gp-form-item :prop="`innerTableData.${scope.$index}.placementPointCode`" :rules="[validators.required]">
            <virtual-select
              v-model="scope.row.placementPointCode"
              filterable
              :options="getPointOptions(scope)"
              @change="handleChange(scope)"
            />
          </gp-form-item>
        </template>
      </gp-table-column>
    </gp-table>
  </gp-form>
</template>
<script>
import { cloneDeep } from "lodash";
import { defineComponent, watchEffect, reactive, ref, useCssModule, computed, onMounted } from "vue";
import { containerTypeShape, containerStatus } from "gms-constants";
import { getPointsList, queryStopPointList, getPalletCodeList } from "gms-apis/container";
import { useI18n } from "gms-hooks";
import MSelect from "../components/m-select.vue";
import VirtualSelect from "gms-components/virtual-select";
import { matchesSome } from "./machine/index.js";

const t = useI18n();
/** 容器编码的最大长度 */
const maxLenContainerCode = 8;

const validators = {
  checkCode: {
    validator: (rule, value, callback) => {
      const reg = /^([0-9]|[A-Z0-9]|[A-Za-z]){1,8}$/g;
      if (!reg.test(value)) {
        return callback(rule.message);
      }
      callback();
    },
    message: t("geekplus.gms.client.commons.validator.invalidCode", { num: maxLenContainerCode }),
    trigger: "blur",
  },
  required: { required: true, message: t("geekplus.gms.client.commons.validator.required"), trigger: "change" },
};

const getPointCodes = async () => {
  const data = await getPointsList();
  const list = data.map((v) => ({ value: v.cellCode, label: v.hostCellCode ?? v.cellCode }));
  return list;
};
const getPalletCodes = async () => {
  const data = await getPalletCodeList();
  const list = data.map((v) => ({ value: v.palletLatticeCode, label: v.palletLatticeHostCode ?? v.palletLatticeCode }));
  return list;
};

// 工作站停靠点位
const getStopPointCodes = async (params) => {
  const data = await queryStopPointList(params);
  const list = data.map((v) => ({ value: v.hostCellCode, label: v.name }));
  return list;
};

export default defineComponent({
  name: "CodeTable",
  components: { VirtualSelect, MSelect },
  props: {
    tableData: { type: Array, default: () => [] },
    workstationId: { type: String, default: null },
    stopPointId: { type: String, default: null },
    isWorkstation: { type: Boolean, default: false },
    record: { type: Object, default: null },
    machineState: { type: Object, default: null },
  },
  setup(props, { emit }) {
    const form = ref(null);
    const state = reactive({ form: { innerTableData: [] }, pointCodeList: [], stopPointList: [], palletCodeList: [] });

    // 容器编码是否可以编辑
    const isCodeEditable = computed(() => {
      let ret = false;

      if (matchesSome(props.machineState, ["add.list"])) {
        // 新增状态，下可以编辑
        ret = true;
      } else if (matchesSome(props.machineState, ["edit"]) && props.record.status === containerStatus.INACTIVE) {
        // 编辑状态下，只有离场可以编辑
        ret = true;
      }

      return ret;
    });

    watchEffect(() => {
      state.form.innerTableData = cloneDeep(props.tableData);
    });

    onMounted(() => {
      getPalletCodes().then((list) => (state.palletCodeList = list));
      getPointCodes().then((list) => (state.pointCodeList = list));
      getStopPointCodes({ stationId: props.workstationId, stopPointId: props.stopPointId }).then(
        (data) => (state.stopPointList = data)
      );
    });

    const cn = useCssModule("cn");
    const addHeaderCellClassName = ({ column }) => {
      if (column.property === "code" || column.property === "placementPointCode") {
        return `${cn.required_class} `;
      }
    };

    const handleChange = ({ $index, row, column }) => {
      const tableData = cloneDeep(props.tableData);
      tableData[$index][column.property] = row[column.property];
      emit("update:tableData", tableData);
    };

    // 根据不同的容器形态，计算相应的入场点位下拉选项
    const getPointOptions = ({ row }) => {
      const setItemDisabled = (v) => ({ ...v, disabled: usedPointCodes.includes(v.value) });
      const usedPointCodes = props.tableData.filter((v) => v.placementPointCode).map((v) => v.placementPointCode);
      if (props.isWorkstation) {
        return state.stopPointList.map(setItemDisabled);
      }

      // 搬运托盘（M系列），仅可以入场地图点位
      // RMS 把搬运托盘当作普通货架处理，因此只能入到普通点位
      // 具体改动原因，参考 https://jira.geekplus.cc/browse/SLAM-8550 的备注
      if (row.modelInfo.type === containerTypeShape.PALLET) {
        return state.pointCodeList.map(setItemDisabled);
      }

      // 叉车托盘（F系列），仅可以入场托盘位
      if (row.modelInfo.type === containerTypeShape.FORKLIFT_PALLET) {
        return state.palletCodeList.map(setItemDisabled);
      }

      // 其他容器类型，只能入场普通地图点位
      return state.pointCodeList.map(setItemDisabled);
    };

    return {
      form,
      state,
      containerTypeShape,
      maxLenContainerCode,
      addHeaderCellClassName,
      validators,
      handleChange,
      getPointOptions,
      isCodeEditable,
    };
  },
});
</script>
<style lang="scss" module="cn">
.page_wrapper {
  :global {
    .gp-table {
      .gp-select,
      .gp-form-item {
        margin: 16px 0px;
      }
    }
  }
  .required_class {
    position: relative;
    padding-left: 10px;
    &:before {
      position: absolute;
      top: 12px;
      content: "*";
      color: #f56c6c;
    }
  }
}
</style>
