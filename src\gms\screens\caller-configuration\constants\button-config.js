// 按钮配置常量
import redIcon from "@/assets/images/red.png";
import greenIcon from "@/assets/images/green.png";
import yellowIcon from "@/assets/images/yellow.png";
import blueIcon from "@/assets/images/blue.png";

// 按钮配置模板
export const BUTTON_CONFIGS = [
  { icon: redIcon, name: "geekplus.gms.client.screen.callerConfiguration.buttonOne" },
  { icon: greenIcon, name: "geekplus.gms.client.screen.callerConfiguration.buttonTwo" },
  { icon: yellowIcon, name: "geekplus.gms.client.screen.callerConfiguration.buttonThree" },
  { icon: blueIcon, name: "geekplus.gms.client.screen.callerConfiguration.buttonFour" },
];

// 触发类型映射
export const TRIGGER_TYPE_MAPPING = {
  GENERAL_POINT: 0,
  WORKSTATION: 2,
  AREA: 1,
  PALLET_POINT: 3,
};

// 按钮功能代码常量
export const BUTTON_FUNCTION_CODES = {
  START_WORKFLOW: "10", // 启动流程任务
  CONTINUE_WORKFLOW: "40", // 流程任务继续
  CONTAINER_ENTRY: "43", // 容器入场
  CONTAINER_EXIT: "44", // 容器离场
  SYSTEM_EMERGENCY_STOP: "50", // 系统急停
  SYSTEM_RECOVERY: "70", // 系统恢复
};

// 位置状态映射
export const POSITION_STATE_MAP = {
  [BUTTON_FUNCTION_CODES.CONTINUE_WORKFLOW]: "positionState0",
  [BUTTON_FUNCTION_CODES.CONTAINER_ENTRY]: "positionStatein",
  [BUTTON_FUNCTION_CODES.CONTAINER_EXIT]: "positionStateleave",
};

// 容器类型常量
export const CONTAINER_TYPES = {
  PALLET: "PALLET",
  PALLET_RACKING: "PALLET_RACKING",
};

// 流程类型常量
export const WORKFLOW_TYPES = {
  GENERAL_POINT: "GENERAL_POINT",
  PALLET_POINT: "PALLET_POINT",
  AREA: "AREA",
};

// 按钮状态常量
export const BUTTON_STATUS = {
  DISABLED: 0,
  ENABLED: 1,
};

// 表单字段常量
export const FORM_FIELDS = {
  BUTTON_START: "buttonstart",
  OPERATION_COMMAND: "operationCommand",
  WORKFLOW_CONFIG_ID: "workflowConfigId",
  TRIGGER_POSITION: "triggerposition",
  SHELF_CATEGORY_ID: "shelfCategoryId",
  SHELF_ANGLE: "shelfAngle",
};

// 验证消息常量
export const VALIDATION_MESSAGES = {
  FILL_REQUIRED_FIELDS: "geekplus.gms.client.screen.callerConfiguration.fillRequiredFields",
  CONFIRM_DELETE: "geekplus.gms.client.screen.callerConfiguration.confirmDelete.description",
  TRIGGER_SUCCESS: "geekplus.gms.client.screen.callerConfiguration.triggerSuccess",
};

// 控制器类型常量
export const CONTROLLER_TYPES = {
  CALLER: 4,
  WORKFLOW: 1,
};

// 默认值常量
export const DEFAULT_VALUES = {
  EMPTY_ARRAY: [],
  EMPTY_STRING: "",
  DEFAULT_BUTTON_CODE: "1",
};
