<template>
  <section class="tw-flex tw-gap-6 tw-flex-col tw-mt-6">
    <h3 class="tw-relative tw-font-bold tw-text-l">
      {{ $t("geekplus.gms.client.screen.bracket.form.relatedMapPoints") }}
    </h3>
    <div class="tw-flex tw-flex-col tw-justify-between tw-gap-2 tw--mt-4">
      <div v-for="item in containerList" :key="item.id" class="tw-mb-2">
        <div class="tw-flex tw-items-center">
          <h3 class="tw-w-20 tw-text-right tw-font-normal">
            {{ $t("geekplus.gms.client.screen.bracket.form.mapName") }}：
          </h3>
          {{ item.mapName }}
        </div>
        <div class="tw-flex tw-items-center">
          <h3 class="tw-w-20 tw-text-right tw-font-normal">
            {{ $t("geekplus.gms.client.screen.bracket.form.cellPoints") }}：
          </h3>
          {{ item.placementPointCode }}
        </div>
      </div>
    </div>
  </section>
</template>
<script>
import { defineComponent, onMounted, ref } from "vue";
import { getContainerList } from "gms-apis/container";

const getContainerListById = async (params) => {
  const { data } = await getContainerList({
    limit: 1000,
    offset: 1,
    ...params,
  });
  return data;
};

export default defineComponent({
  name: "RelatedMapPointsWrapper",
  props: { record: { type: Object, default: () => ({}) } },
  setup(props) {
    const containerList = ref([]);
    onMounted(() => {
      getContainerListById({ id: props.record.id }).then((data) => (containerList.value = data));
    });

    return { containerList };
  },
});
</script>
