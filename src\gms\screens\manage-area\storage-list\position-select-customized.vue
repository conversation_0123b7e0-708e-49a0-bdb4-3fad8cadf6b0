<template>
  <PositionSelect v-bind="$attrs" :item-mappers="itemMappers" v-on="$listeners" />
</template>

<script>
import PositionSelect from "gms-components/business/position-select";
import { locationTypes } from "gms-constants";

/**
 * 实施调试专用的点位选择器
 */
export default {
  name: "PositionSelectCustomized",
  components: { PositionSelect },
  setup() {
    const itemMappers = {
      // !!!!!!!!!!!!!!!!!!!!!!!!!!
      // 注意： 这里的 $value 取的 item.name, 不是 item.code
      // 目前后端接口字段比较混乱，有的是 code，有的是 name， 实施调试页面的点位选择器需要的是 name
      // 这里是专门给实施调试页面定制的
      [locationTypes.GENERAL_POINT]: (item) => ({ ...item, $value: item?.name }),
    };
    return { itemMappers };
  },
};
</script>
