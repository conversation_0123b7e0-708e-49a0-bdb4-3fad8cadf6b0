<template>
  <div class="tw-flex tw-flex-col tw-bg-white tw-p-4" :class="cn.page_root">
    <PageHeaderWithAction
      :title="$t('geekplus.gms.client.screen.callerConfiguration.title')"
      :subtitle="$t('geekplus.gms.client.screen.callerConfiguration.subtitle')"
    />
    <list-view ref="listViewRef" @action="handleListAction"></list-view>

    <edit-dialog
      v-if="showEditDialog"
      ref="editPageRef"
      :visible="showEditDialog"
      :mode="editDialogMode"
      :step="stepValue"
      :init_value="rowValue"
      @updateStep="handleStep"
      @updateMode="handleMode"
      @updatevisible="showDialog"
    />
  </div>
</template>
<script>
import { defineComponent, ref } from "vue";
import { useI18n } from "@/hooks";
import { PageHeaderWithAction } from "gms-components/page-header";
import ListView from "./list-view/index.vue";
import EditDialog from "./edit-dialog/index.vue";
import { deleteButtonList, getControllerConfigs } from "gms-apis/caller-configuration";
import { MessageBox, Message } from "geekplus-ui";
// 用来判断是添加还是编辑
export default defineComponent({
  name: "ContainerWrapper",
  components: { PageHeaderWithAction, ListView, EditDialog },
  setup() {
    const t = useI18n();
    // 控制弹窗是添加弹窗还是修改弹窗
    const editDialogMode = ref("");
    const listViewRef = ref(null);
    // 详情页的数据
    const rowValue = ref([]);
    // 控制弹框的显示英寸
    const stepValue = ref(0);
    // const robotType = ref(undefined);
    // 控制弹框的显示
    const showEditDialog = ref(false);
    const refresh = () => listViewRef.value?.refresh();
    const showDialog = (val) => {
      refresh();
      showEditDialog.value = val;
    };
    function handleListAction({ type, row }) {
      switch (type) {
        case "add":
          addItem(row);
          break;
        case "config":
          getControllerConfig(row, "edit", 1);
          break;
        case "detail":
          getControllerConfig(row, "view", 2);
          break;
        case "delete":
          deleteItem(row);
          break;
        default:
          break;
      }
    }
    const addItem = () => {
      showEditDialog.value = true;
      stepValue.value = 0;
      // mode.value = "add";
      editDialogMode.value = "add";
      rowValue.value = [];
    };
    // 删除函数
    const deleteItem = async (row) => {
      try {
        await MessageBox.confirm(
          t("geekplus.gms.client.screen.callerConfiguration.confirmDelete"),
          t("geekplus.gms.client.commons.confirmDelete"),
          {
            confirmButtonText: t("geekplus.gms.client.commons.confirmDelete"),
            confirmButtonClass: "el-button--danger",
          }
        );
        await deleteButtonList({ id: row?.id });
        Message.success(t("geekplus.gms.client.screen.callerConfiguration.deletionSuccess"));
        refresh();
      } catch (e) {
        console.error("not delete the row with id:", row.id);
      }
    };
    // 获取配置器信息的函数
    const getControllerConfig = async (row, mode = "", flag = "") => {
      const data = await getControllerConfigs({ id: row?.id });
      if (data?.succ) {
        editDialogMode.value = mode;
        showEditDialog.value = true;
        stepValue.value = flag;
        rowValue.value = [
          {
            controllerConfigList: data?.data?.controllerConfigList || [],
            code: data?.data?.code,
            name: data?.data?.name,
          },
        ];
      }
    };
    const handleStep = (val) => {
      stepValue.value = val
    };
    const handleMode = (val) => {
      editDialogMode.value = val
    };
    return {
      t,
      handleListAction,
      showEditDialog,
      showDialog,
      listViewRef,
      refresh,
      rowValue,
      stepValue,
      editDialogMode,
      handleStep,
      handleMode,
    };
  },
});
</script>
<style lang="scss" module="cn">
.page_root {
  background-color: #fff;
  border-radius: 6px;
  -webkit-box-shadow: 0 6px 10px 0 rgba(0, 0, 0, 0.08);
  box-shadow: 0 6px 10px 0 rgba(0, 0, 0, 0.08);
  box-sizing: border-box;
  position: relative;
}
</style>
