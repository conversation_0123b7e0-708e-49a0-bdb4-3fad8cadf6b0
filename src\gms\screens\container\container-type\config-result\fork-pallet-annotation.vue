<template>
  <div class="tw-flex tw-justify-center tw-items-center tw-h-full">
    <div v-if="isOneTray" class="tw-relative tw-flex tw-justify-center tw-items-center">
      <img :src="oneTrayImg" :class="cn.img" alt="" />
      <HighLightBox
        :styles="{
          top: '74px',
          left: '-24px',
          width: '300px',
          height: '20px',
          borderDirection: 'borderBottom',
          display: 'flex',
          transform: 'rotate(-33deg)',
          justifyContent: 'center',
        }"
        :is-active="$props.active === 'surfaceLength'"
        :value="surfaceLength"
      />
      <HighLightBox
        :styles="{
          top: '34px',
          left: '268px',
          width: '336px',
          height: '20px',
          borderDirection: 'borderBottom',
          display: 'flex',
          justifyContent: 'center',
          transform: 'rotate(15.5deg)',
        }"
        :is-active="$props.active === 'surfaceWidth'"
        :value="surfaceWidth"
      />
      <HighLightBox
        :styles="{
          bottom: '82px',
          left: '132px',
          width: '42px',
          height: '34px',
          borderDirection: 'borderBottom',
          transform: 'rotate(-90deg)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'flex-end',
        }"
        :is-active="$props.active === 'holeHeight'"
        :value="holeHeight"
      />
      <!-- $props.active === 'holeHeight' -->
      <HighLightBox
        :styles="{
          bottom: '64px',
          left: '56px',
          width: '222px',
          height: '20px',
          borderDirection: 'borderBottom',
          display: 'flex',
          justifyContent: 'center',
          transform: 'rotate(15.5deg)',
          paddingLeft: '40px',
        }"
        :is-active="$props.active === 'holeWidth'"
        :value="holeWidth"
      />
      <!-- $props.active === 'holeWidth' -->
      <HighLightBox
        :styles="{
          bottom: '24px',
          left: '282px',
          width: '44px',
          height: '20px',
          borderDirection: 'borderBottom',
          display: 'flex',
          justifyContent: 'center',
          transform: 'rotate(15.5deg)',
        }"
        :is-active="$props.active === 'edgePillar'"
        :value="edgePillar"
      />
      <!-- $props.active === 'edgePillar' -->
    </div>
    <div v-if="isTwoTray" class="tw-relative tw-flex tw-justify-center tw-items-center">
      <img :src="twoTrayImg" :class="cn.img" alt="" />
      <HighLightBox
        :styles="{
          top: '74px',
          left: '-24px',
          width: '300px',
          height: '20px',
          borderDirection: 'borderBottom',
          display: 'flex',
          transform: 'rotate(-33deg)',
          justifyContent: 'center',
        }"
        :is-active="$props.active === 'surfaceLength'"
        :value="surfaceLength"
      />
      <HighLightBox
        :styles="{
          top: '34px',
          left: '268px',
          width: '336px',
          height: '20px',
          borderDirection: 'borderBottom',
          display: 'flex',
          justifyContent: 'center',
          transform: 'rotate(15.5deg)',
        }"
        :is-active="$props.active === 'surfaceWidth'"
        :value="surfaceWidth"
      />
      <HighLightBox
        :styles="{
          bottom: '60px',
          left: '208px',
          width: '44px',
          height: '34px',
          borderDirection: 'borderBottom',
          transform: 'rotate(-90deg)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'flex-end',
        }"
        :is-active="$props.active === 'holeHeight'"
        :value="holeHeight"
      />
      <!-- $props.active === 'holeHeight' -->
      <HighLightBox
        :styles="{
          bottom: '80px',
          left: '60px',
          width: '74px',
          height: '20px',
          borderDirection: 'borderBottom',
          display: 'flex',
          justifyContent: 'center',
          transform: 'rotate(15.5deg)',
        }"
        :is-active="$props.active === 'holeWidth'"
        :value="holeWidth"
      />
      <!-- $props.active === 'holeWidth' -->
      <HighLightBox
        :styles="{
          bottom: '60px',
          left: '140px',
          width: '52px',
          height: '20px',
          borderDirection: 'borderBottom',
          display: 'flex',
          justifyContent: 'center',
          transform: 'rotate(15.5deg)',
        }"
        :is-active="$props.active === 'centerPillar'"
        :value="centerPillar"
      />
      <!-- $props.active === 'centerPillar' -->
      <HighLightBox
        :styles="{
          bottom: '24px',
          left: '286px',
          width: '44px',
          height: '20px',
          borderDirection: 'borderBottom',
          display: 'flex',
          justifyContent: 'center',
          transform: 'rotate(15.5deg)',
        }"
        :is-active="$props.active === 'edgePillar'"
        :value="edgePillar"
      />
      <!-- $props.active === 'edgePillar' -->
    </div>
  </div>
</template>
<script>
import { defineComponent, computed } from "vue";
import { palletStructureType, palletColorType } from "gms-constants";
import getContainerImage from "@/utils/containerImages";
const doubleBlueImg = getContainerImage("double-blue.svg");
const doubleBlackImg = getContainerImage("double-black.svg");
const doublePalletImg = getContainerImage("double-pallet.svg");
const doubleWoodImg = getContainerImage("double-wood.svg");
const singleBlackImg = getContainerImage("single-black.svg");
const singleBlueImg = getContainerImage("single-blue.svg");
const singlePalletImg = getContainerImage("single-pallet.svg");
const singleWoodImg = getContainerImage("single-wood.svg");
import HighLightBox from "./highligt-box.vue";

export default defineComponent({
  name: "ShelfAnnotation",
  components: { HighLightBox },
  props: {
    formValues: { type: Object, default: () => ({}) },
    active: { type: String, default: "" },
  },
  setup(props) {
    const isOneTray = computed(() => props.formValues.modelInfo.palletStructure === palletStructureType.SINGLE);
    const isTwoTray = computed(() => props.formValues.modelInfo.palletStructure === palletStructureType.DOUBLE);
    const surfaceWidth = computed(() => props.formValues.modelInfo.surfaceWidth);
    const surfaceLength = computed(() => props.formValues.modelInfo.surfaceLength);
    const edgePillar = computed(() => props.formValues.modelInfo.edgePillar);
    const centerPillar = computed(() => props.formValues.modelInfo.centerPillar);
    const holeHeight = computed(() => props.formValues.modelInfo.holeHeight);
    const holeWidth = computed(() => props.formValues.modelInfo.holeWidth);
    const oneTrayImg = computed(() => {
      switch (props.formValues.modelInfo.surfaceColor) {
        case palletColorType.WOOD_COLOR:
          return singleWoodImg;
        case palletColorType.BLUE:
          return singleBlueImg;
        case palletColorType.GRAY:
          return singleBlackImg;
        default:
          return singlePalletImg;
      }
    });

    const twoTrayImg = computed(() => {
      switch (props.formValues.modelInfo.surfaceColor) {
        case palletColorType.WOOD_COLOR:
          return doubleWoodImg;
        case palletColorType.BLUE:
          return doubleBlueImg;
        case palletColorType.GRAY:
          return doubleBlackImg;
        default:
          return doublePalletImg;
      }
    });

    return {
      isOneTray,
      isTwoTray,
      oneTrayImg,
      twoTrayImg,
      surfaceWidth,
      surfaceLength,
      edgePillar,
      centerPillar,
      holeHeight,
      holeWidth,
    };
  },
});
</script>
<style lang="scss" module="cn">
.img {
  width: 600px;
}
</style>
