/**
 * 流程页国际化字符集
 */

{
  // 控制指令
  "lang.venus.web.inner.outterCall": "外呼",
  "lang.venus.web.inner.modelSwitch": "模式切换",
  "lang.venus.web.inner.conveyerMove": "输送线移动",
  "lang.venus.web.inner.openBackDoor": "开后门",
  "lang.venus.web.inner.turnRed": "变红灯",
  "lang.venus.web.inner.turnYellow": "变黄灯",
  "lang.venus.web.inner.leave": "离开",
  "lang.venus.web.inner.removeTask": "移除任务",
  "lang.venus.web.inner.openFrontDoor": "开前门",
  "lang.venus.web.inner.liftMove": "提升机移动",
  "lang.venus.web.inner.arriveStation": "到达工作站",
  "lang.venus.web.inner.robotStopArea": "机器人区域停止",
  "lang.venus.web.inner.closeDoor": "关门",
  "lang.venus.web.inner.leaveStation": "离开工作站",
  "lang.venus.web.inner.robotStop": "机器人全部停止",
  "lang.venus.web.inner.closeFrontDoor": "关前门",
  "lang.venus.web.inner.shelfHighlightResult": "货架突出结果",
  "lang.venus.web.inner.getAndSend": "叉取并送到位置",
  "lang.venus.web.inner.turnGreen": "变绿灯",
  "lang.venus.web.inner.innerCall": "内呼",
  "lang.venus.web.inner.tricolorLightsRing": "三色灯响铃",
  "lang.venus.web.inner.closeBackDoor": "关后门",
  "lang.venus.web.inner.poppickCrossMiddle": "PopPick叉取并送到位置(越过中线)",
  "lang.venus.web.inner.openDoor": "开门",

  // 触发指令
  "lang.venus.web.inner.resetArea": "区域复位",
  "lang.venus.web.inner.speedAreaRecovery": "区域限速恢复",
  "lang.venus.web.inner.safetyDoorStopArea": "安全门触发区域急停",
  "lang.venus.web.inner.greenTrigger": "绿灯上报",
  "lang.venus.web.inner.fireStop": "火警急停",
  "lang.venus.web.inner.speedArea": "区域限速",
  "lang.venus.web.inner.triggerButtonReport": "触发按钮上报",
  "lang.venus.web.inner.stopArea": "区域急停",
  "lang.venus.web.inner.safetyDoorStop": "安全门触发急停",
  "lang.venus.web.inner.yellowTrigger": "黄灯上报",
  "lang.venus.web.inner.redTrigger": "红灯上报",
  "lang.venus.web.inner.fireStopArea": "火警区域急停",
  "lang.venus.web.inner.resetRobotStopName": "安全门恢复机器人停止信号任务",
  "lang.venus.web.inner.stop": "急停",
  "lang.venus.web.inner.reset": "复位",
  "lang.venus.web.inner.reportStatus": "状态上报",
  "lang.venus.web.inner.scannerCodeTrigger": "扫码触发",
  "lang.venus.web.inner.tricolorLightStatusReport": "三色灯状态上报",

  // 设备名称
  "lang.venus.web.inner.safetyStation": "安全方案工作站",
  "lang.venus.web.inner.speedLimit": "限速",
  "lang.venus.web.inner.PopPick": "PopPick",
  "lang.venus.web.inner.elevator": "电梯",
  "lang.venus.web.inner.safetyReset": "安全方案复位",
  "lang.venus.web.inner.door": "门",
  "lang.venus.web.inner.airShowerDoor": "风淋门",
  "lang.venus.web.inner.fireStopName": "火警急停",
  "lang.venus.web.inner.safetyDoor": "安全门",
  "lang.venus.web.inner.trafficLight": "交通灯",
}
