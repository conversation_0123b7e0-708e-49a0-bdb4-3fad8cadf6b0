/**
 *   通知回调配置页面国际化字符集
 *   key 必须以 'geekplus.gms.client.screen.notifyCallbackConfig.' 开头
 */
 {
  "geekplus.gms.client.screen.notifyCallbackConfig.apiProtocol": "接口协议",
  "geekplus.gms.client.screen.notifyCallbackConfig.callback": "回调",
  "geekplus.gms.client.screen.notifyCallbackConfig.notification": "通知",
  "geekplus.gms.client.screen.notifyCallbackConfig.retryInterval": "失败重试时间",
  "geekplus.gms.client.screen.notifyCallbackConfig.interval": "间隔时间",
  "geekplus.gms.client.screen.notifyCallbackConfig.retryCount": "失败重试次数",
  "geekplus.gms.client.screen.notifyCallbackConfig.apiAddress": "接口地址",
  "geekplus.gms.client.screen.notifyCallbackConfig.editTip": "请勿在任务执行中修改，会对任务信息产生影响",
  "geekplus.gms.client.task.notifyCallbackConfig.alert": "客户端代码对应接口文档中的clientCode",
}
