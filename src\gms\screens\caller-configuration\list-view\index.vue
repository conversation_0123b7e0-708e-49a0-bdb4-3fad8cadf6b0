<template>
  <div>
    <div class="tw-text-right tw-mb-4">
      <gp-button type="primary" @click="handleRobotTypeChange">
        {{ $t("geekplus.gms.client.screen.callerConfiguration.createCaller") }}
      </gp-button>
    </div>
    <table-wrapper v-bind="tableState">
      <template #cellName="{ row }">
        <gp-link :underline="false" type="primary" @click="handleClickCode(row)">
          {{ row.code }}
        </gp-link>
      </template>

      <template #statusSlot="{ row }">
        <gp-tag :type="row.controllerStatus === 1 ? 'success' : 'info'">
          {{
            row.controllerStatus === 1
              ? $t("geekplus.gms.client.screen.callerConfiguration.configured")
              : $t("geekplus.gms.client.screen.callerConfiguration.notConfigured")
          }}
        </gp-tag>
      </template>
    </table-wrapper>
  </div>
</template>
<script>
import { defineComponent, ref, onMounted } from "vue";
import { useI18n } from "@/hooks";
import TableWrapper, { useTableState, columnTypes } from "@srpe-fe/uic-el-table-wrapper-vue2";
import { getButtonConfigurations } from "gms-apis/caller-configuration";
import { WK_CONFIG_STATUS } from "gms-constants";

export default defineComponent({
  name: "ListView",
  components: { TableWrapper },
  setup(props, { emit }) {
    const t = useI18n();
    const tableState = useTableState({
      columns: [
        {
          prop: "code",
          label: t("geekplus.gms.client.screen.callerConfiguration.id"),
          type: columnTypes.GEEK_CUSTOMIZED,
          cellSlotName: "cellName",
        },
        // 盒子名称
        {
          prop: "name",
          label: t("geekplus.gms.client.screen.callerConfiguration.name"),

          // width: 90,
        },
        // IP地址
        {
          prop: "ipAddress",
          label: t("geekplus.gms.client.screen.callerConfiguration.ip address"),
        },
        // 状态 1已配置 0 未配置
        {
          prop: "controllerStatus",
          type: columnTypes.GEEK_CUSTOMIZED,
          label: t("geekplus.gms.client.screen.callerConfiguration.status"),
          cellSlotName: "statusSlot",
          // width: 110,
          // formatter: (row, column, value) => ROBOT_TYPE.getLabelByValue(value),
        },
        // 编辑人
        {
          prop: "updatorUsername",
          label: t("geekplus.gms.client.screen.callerConfiguration.editor"),
          // width: 110,
          // formatter: (row, column, value) => ROBOT_TYPE.getLabelByValue(value),
        },
        // 编辑时间
        {
          prop: "updateTime",
          label: t("geekplus.gms.client.screen.callerConfiguration.edit time"),
          // width: 110,
          // formatter: (row, column, value) => ROBOT_TYPE.getLabelByValue(value),
        },
        {
          type: columnTypes.GEEK_ACTION_BUTTONS,
          // displayCount: 4,
          width: 180,
          options: [
            {
              text: t("geekplus.gms.client.screen.callerConfiguration.allocation"),
              handleClick: ({ row }) => emit("action", { type: "config", row }),
            },
            {
              text: t("geekplus.gms.client.screen.callerConfiguration.deletion"),
              handleClick: ({ row }) => emit("action", { type: "delete", row }),
              // isVisible: ({ row }) => !isReleased(row.status),
            },
          ],
        },
      ],
      fetchData: async (params) => {
        const { recordCount, recordList } = await getButtonConfigurations({ ...params });
        return {
          data: recordList,
          total: recordCount,
        };
      },
    });
    function handleRobotTypeChange() {
      emit("action", { type: "add" });
    }

    onMounted(() => {
      refresh();
    });

    // 刷新列表数据
    const refresh = () => {
      tableState.query({ currentPage: 1 });
    };
    const handleClickCode = (row) => {
      emit("action", { type: "detail", row });
    };
    return {
      tableState,
      t, // 需要返回 t，否则模板里用不了
      handleRobotTypeChange,
      refresh,
      WK_CONFIG_STATUS,
      handleClickCode,
    };
  },
});
</script>
<style lang="scss" module="cn">
.page_root {
  background-color: #fff;
  border-radius: 6px;
  -webkit-box-shadow: 0 6px 10px 0 rgba(0, 0, 0, 0.08);
  box-shadow: 0 6px 10px 0 rgba(0, 0, 0, 0.08);
  box-sizing: border-box;
  position: relative;
}
</style>
