import { uniqueId, cloneDeep } from "lodash";
import {
  containerTypeShape,
  containerModel,
  shelfElementType,
  palletSpeciType,
  palletStructureType,
  palletCarType,
  geometricShape,
  containerParts,
} from "gms-constants";
import { validators } from "@srpe-fe/uic-el-form-wrapper-vue2";
import { useI18n } from "@/hooks";
const t = useI18n();

// 托盘类型
export const pallets = [containerTypeShape.PALLET, containerTypeShape.FORKLIFT_PALLET];

export const commonValues = {
  name: "",
  code: "",
  type: containerTypeShape.PALLET_RACKING, // 容器形态
  modelingMethod: containerModel.STANDARD, // 标准模型
};

/**
 * 标准模型 模型信息
 */
export const standardModel = {
  modelInfo: {
    surfaceLength: 1316,
    surfaceWidth: 1316,
    surfaceHeight: 1000,
    legLength: 40,
    legWidth: 40,
    legHeight: 300,
  },
};

/**
 * 非标准模型 模型信息
 */
export const nonStandardModel = {
  shelves: shelfElementType.LEG,
  modelInfo: [
    {
      key: uniqueId(),
      type: shelfElementType.FACE,
      length: 1316,
      width: 1316,
      height: 1000,
      x: 0,
      y: 0,
      faceX: 0,
      faceY: 0,
    },
    {
      key: uniqueId(),
      type: shelfElementType.LEG,
      length: 40,
      width: 40,
      height: 300,
      x: 600,
      y: 600,
    },
    {
      key: uniqueId(),
      type: shelfElementType.LEG,
      length: 40,
      width: 40,
      height: 300,
      x: -600,
      y: 600,
    },
    {
      key: uniqueId(),
      type: shelfElementType.LEG,
      length: 40,
      width: 40,
      height: 300,
      x: -600,
      y: -600,
    },
    {
      key: uniqueId(),
      type: shelfElementType.LEG,
      length: 40,
      width: 40,
      height: 300,
      x: 600,
      y: -600,
    },
  ],
};

/**
 * 搬运托盘
 */
export const movePalletModel = {
  modelInfo: {
    surfaceLength: 1350,
    surfaceWidth: 1350,
  },
};

/**
 * 叉车模型 模型信息
 */
export const forkliftModel = {
  modelInfo: {
    modelingMethod: containerModel.STANDARD,
    palletStructure: palletStructureType.DOUBLE,
    handleableRobotModel: "",
    detectionDistance: 1350,
    materialType: "",
    surfaceColor: "",
    surfaceLength: 1200,
    surfaceWidth: 1000,
    edgePillar: 150,
    centerPillar: 150,
    holeHeight: 120,
    holeWidth: 275,
  },
};

/** 容器类型编码的最大长度 */
const typeCodeMaxLength = 8;

export const baseFormItemCfgs = (values, mode) => ({
  type: {
    type: "el-select",
    labelText: t("geekplus.gms.client.screen.container.columns.containerShape"),
    value: values.type ?? "",
    disabled: true,
    options: containerTypeShape.toLabelValueList(),
    labelPosition: "left",
    validators: [validators.required],
  },
  code: {
    type: "el-input",
    labelText: t("geekplus.gms.client.screen.container.columns.typeCode"),
    value: values.code ?? "",
    maxlength: typeCodeMaxLength,
    disabled: mode !== "add",
    labelPosition: "left",
    validators: [validators.required],
  },
  name: {
    type: "el-input",
    labelText: t("geekplus.gms.client.screen.container.columns.typeName"),
    value: values.name ?? "",
    disabled: mode === "view",
    labelPosition: "left",
    validators: [validators.required],
    maxlength: 32,
  },
});

export const standModelFormItemCgs = (values, mode) => ({
  modelingMethod: {
    type: "el-select",
    labelText: t("geekplus.gms.client.screen.container.columns.containerModel"),
    value: values.modelingMethod ?? "",
    disabled: mode !== "add",
    options: containerModel.toLabelValueList().filter((v) => v.value !== containerModel.FORKLIFT),
    labelPosition: "left",
    validators: [validators.required],
  },
  surfaceLength: {
    type: "el-input-number",
    labelText: t("geekplus.gms.client.screen.container.form.surfaceLength"),
    value: values.modelInfo.surfaceLength ?? "",
    disabled: mode === "view",
    validators: [validators.required],
    min: 0,
    max: 5000,
  },
  surfaceWidth: {
    type: "el-input-number",
    labelText: t("geekplus.gms.client.screen.container.form.surfaceWidth"),
    value: values.modelInfo.surfaceWidth ?? "",
    disabled: mode === "view",
    validators: [validators.required],
    min: 0,
    max: 5000,
  },
  surfaceHeight: {
    type: "el-input-number",
    labelText: t("geekplus.gms.client.screen.container.form.surfaceHeight"),
    value: values.modelInfo.surfaceHeight ?? "",
    disabled: mode === "view",
    validators: [validators.required],
    min: 0,
    max: 10000,
  },
  legLength: {
    type: "el-input-number",
    labelText: t("geekplus.gms.client.screen.container.form.length"),
    value: values.modelInfo.legLength ?? "",
    disabled: mode === "view",
    validators: [validators.required],
    min: 0,
    max: 5000,
  },
  legWidth: {
    type: "el-input-number",
    labelText: t("geekplus.gms.client.screen.container.form.width"),
    value: values.modelInfo.legWidth ?? "",
    disabled: mode === "view",
    validators: [validators.required],
    min: 0,
    max: 5000,
  },
  legHeight: {
    type: "el-input-number",
    labelText: t("geekplus.gms.client.screen.container.form.height"),
    value: values.modelInfo.legHeight ?? "",
    disabled: mode === "view",
    validators: [validators.required],
    min: 0,
    max: 5000,
  },
});

export const isFace = (type) => type === shelfElementType.FACE;

export const noStandModelFormItemCgs = (values, mode) => ({
  modelingMethod: {
    type: "el-select",
    labelText: t("geekplus.gms.client.screen.container.form.containerModel"),
    value: values.modelingMethod ?? "",
    disabled: mode !== "add",
    validators: [validators.required],
    options: containerModel.toLabelValueList().filter((v) => v.value !== containerModel.FORKLIFT),
  },
  shelves: {
    type: "el-select",
    labelText: t("geekplus.gms.client.screen.container.form.shelfElements"),
    value: values.shelves ?? "",
    disabled: true,
    validators: [validators.required],
    options: shelfElementType.toLabelValueList().filter((v) => !isFace(v.value)),
  },
});

export const movePalletFormItemCfgs = (values, mode) => ({
  surfaceLength: {
    type: "el-input-number",
    value: values.modelInfo.surfaceLength ?? "",
    validators: [validators.required],
    disabled: mode === "view",
    min: 1,
    max: 10000,
    labelWidth: "70px",
    labelText: t("geekplus.gms.client.screen.container.form.length"),
  },
  surfaceWidth: {
    type: "el-input-number",
    value: values.modelInfo.surfaceWidth ?? "",
    validators: [validators.required],
    disabled: mode === "view",
    min: 1,
    max: 10000,
    labelWidth: "70px",
    labelText: t("geekplus.gms.client.screen.container.form.width"),
  },
});

const getPillarMinValue = (values) => {
  if (values.modelInfo.modelingMethod === containerModel.STANDARD) {
    return values.modelInfo.handleableRobotModel === "F12ML/F14L/F16L/F35C" ? 60 : 80;
  }
  return values.modelInfo.handleableRobotModel === "F12ML/F14L/F16L/F35C" ? 50 : 70;
};

const labelWidth = "80px";

export const forkFormItemCfgs = (values, mode) => ({
  modelingMethod: {
    type: "el-select",
    labelText: t("geekplus.gms.client.screen.container.form.palletSpecifications"),
    labelHelp: "slot",
    labelWidth,
    value: values.modelInfo.modelingMethod ?? "",
    disabled: mode !== "add",
    validators: [validators.required],
    options: palletSpeciType.toLabelValueList(),
  },
  palletStructure: {
    type: "el-radio-group",
    labelText: t("geekplus.gms.client.screen.container.form.palletStructure"),
    labelWidth,
    value: values.modelInfo.palletStructure ?? "",
    disabled: mode !== "add",
    validators: [validators.required],
    options: palletStructureType.toLabelValueList(),
  },
  handleableRobotModel: {
    type: "el-select",
    labelText: t("geekplus.gms.client.screen.container.form.palletModels"),
    labelHelp: t("geekplus.gms.client.screen.container.tips.palletModel"),
    labelWidth,
    value: values.modelInfo.handleableRobotModel ?? "",
    disabled: mode === "view",
    validators: [validators.required],
    options: palletCarType.toLabelValueList(),
  },
  detectionDistance: {
    type: "el-input-number",
    labelText: t("geekplus.gms.client.screen.container.form.identifyDistances"),
    labelHelp: t("geekplus.gms.client.screen.container.tips.recognizableDistance"),
    labelWidth,
    value: values.modelInfo.detectionDistance ?? "",
    disabled: mode === "view",
    validators: [validators.required],
    min: 1,
    max: 10000,
  },
  materialType: {
    type: "customized",
    labelText: t("geekplus.gms.client.screen.container.form.palletMaterial"),
    labelWidth,
    value: values.modelInfo.materialType ?? "",
    disabled: mode === "view",
    validators: [validators.required],
  },
  surfaceColor: {
    type: "customized",
    labelText: t("geekplus.gms.client.screen.container.form.trayColor"),
    labelWidth,
    value: values.modelInfo.surfaceColor ?? "",
    disabled: mode === "view",
    validators: [validators.required],
  },
  surfaceLength: {
    type: "el-input-number",
    labelText: t("geekplus.gms.client.screen.container.form.palletSideLong"),
    labelWidth,
    value: values.modelInfo.surfaceLength ?? "",
    disabled: mode === "view",
    validators: [validators.required],
    min: 1,
    max: 10000,
  },
  surfaceWidth: {
    type: "el-input-number",
    labelText: t("geekplus.gms.client.screen.container.form.palletSideWide"),
    labelWidth,
    value: values.modelInfo.surfaceWidth ?? "",
    disabled: mode === "view",
    validators: [validators.required],
    min: 1,
    max: 10000,
  },
  edgePillar: {
    type: "el-input-number",
    labelText: t("geekplus.gms.client.screen.container.form.edgeColumnWide"),
    labelWidth,
    value: values.modelInfo.edgePillar ?? "",
    disabled: mode === "view",
    validators: [validators.required],
    min: getPillarMinValue(values),
    max: 1000,
  },
  centerPillar: {
    type: "el-input-number",
    labelText: t("geekplus.gms.client.screen.container.form.middleColumnWide"),
    labelWidth,
    value: values.modelInfo.centerPillar ?? "",
    disabled: mode === "view",
    validators: [validators.required],
    min: getPillarMinValue(values),
    max: 10000,
    isHidden: ({ formData }) => {
      // 单孔托盘没有中间立柱宽
      return formData.palletStructure === palletStructureType.SINGLE;
    },
  },
  holeHeight: {
    type: "el-input-number",
    labelText: t("geekplus.gms.client.screen.container.form.holesHigh"),
    labelWidth,
    value: values.modelInfo.holeHeight ?? "",
    disabled: mode === "view",
    validators: [validators.required],
    min: 1,
    max: 10000,
  },
  holeWidth: {
    type: "el-input-number",
    labelText: t("geekplus.gms.client.screen.container.form.holesWide"),
    labelWidth,
    value: values.modelInfo.holeWidth ?? "",
    disabled: mode === "view",
    validators: [validators.required],
    min: 1,
    max: 10000,
  },
});

export const getFormItemCfgs = (formValues, mode) => {
  if (formValues.type === containerTypeShape.PALLET) {
    return { ...baseFormItemCfgs(formValues, mode), ...movePalletFormItemCfgs(formValues, mode) };
  }
  if (formValues.type === containerTypeShape.FORKLIFT_PALLET) {
    return { ...baseFormItemCfgs(formValues, mode), ...forkFormItemCfgs(formValues, mode) };
  }
  if (formValues.modelingMethod === containerModel.STANDARD) {
    return { ...baseFormItemCfgs(formValues, mode), ...standModelFormItemCgs(formValues, mode) };
  }
  return { ...baseFormItemCfgs(formValues, mode), ...noStandModelFormItemCgs(formValues, mode) };
};

export const getFormValues = (formValues) => {
  if (formValues.type === containerTypeShape.PALLET) {
    return { ...formValues, ...cloneDeep(movePalletModel) };
  }
  if (formValues.type === containerTypeShape.FORKLIFT_PALLET) {
    return { ...formValues, ...cloneDeep(forkliftModel) };
  }
  if (formValues.modelingMethod === containerModel.STANDARD) {
    return { ...formValues, ...cloneDeep(standardModel) };
  }
  return { ...formValues, ...cloneDeep(nonStandardModel) };
};

// 叉车托盘属性mapping
const forkPalletMapping = (formValues) => {
  const data = {};
  data.detectionDistance = formValues.modelInfo.detectionDistance;
  data.handleableRobotModel = formValues.modelInfo.handleableRobotModel;
  data.materialType = formValues.modelInfo.materialType;
  data.surfaceColor = formValues.modelInfo.surfaceColor;
  data.palletStructure = formValues.modelInfo.palletStructure;
  const face = {
    type: containerParts.SURFACE,
    geometricShape: {
      shapeType: geometricShape.RECTANGLE,
      length: formValues.modelInfo.surfaceLength,
      width: formValues.modelInfo.surfaceWidth,
    },
  };
  const hole = {
    type: containerParts.HOLE,
    geometricShape: {
      shapeType: geometricShape.RECTANGLE,
      height: formValues.modelInfo.holeHeight,
      width: formValues.modelInfo.holeWidth,
    },
  };
  const edgeColumn = {
    type: containerParts.PALLET_EDGE_COLUMN,
    geometricShape: {
      shapeType: geometricShape.RECTANGLE,
      width: formValues.modelInfo.edgePillar,
    },
  };
  data.parts = [face, hole, edgeColumn];
  if (formValues.modelInfo.palletStructure === palletStructureType.DOUBLE) {
    data.parts.push({
      type: containerParts.PALLET_CENTER_COLUMN,
      geometricShape: {
        shapeType: geometricShape.RECTANGLE,
        width: formValues.modelInfo.centerPillar,
      },
    });
  }
  return data;
};

// 搬运托盘
const palletMapping = (formValues) => ({
  parts: [
    {
      type: containerParts.SURFACE,
      geometricShape: {
        shapeType: geometricShape.RECTANGLE,
        length: formValues.modelInfo.surfaceLength,
        width: formValues.modelInfo.surfaceWidth,
      },
    },
  ],
});

// 标准货架
const standardMapping = (formValues) => ({
  parts: [
    {
      type: containerParts.SURFACE,
      geometricShape: {
        shapeType: geometricShape.RECTANGLE,
        length: formValues.modelInfo.surfaceLength,
        width: formValues.modelInfo.surfaceWidth,
        height: formValues.modelInfo.surfaceHeight,
      },
    },
    {
      type: containerParts.LEG,
      geometricShape: {
        shapeType: geometricShape.RECTANGLE,
        length: formValues.modelInfo.legLength,
        width: formValues.modelInfo.legWidth,
        height: formValues.modelInfo.legHeight,
      },
    },
  ],
});

const noStandardMapping = (formValues) => {
  return {
    parts: formValues.modelInfo.map((v) => {
      const parts = {
        type: v.type === shelfElementType.FACE ? containerParts.SURFACE : containerParts.LEG,
        geometricShape: {
          shapeType: geometricShape.RECTANGLE,
          length: v.length,
          width: v.width,
          height: v.height,
        },
      };
      if (v.type === shelfElementType.LEG) {
        parts.geometricShape.centerPoint = {
          x: v.x,
          y: v.y,
        };
      }
      return parts;
    }),
  };
};

export const getSubmitMappingData = (formValues) => {
  const data = {
    id: formValues.id,
    name: formValues.name,
    code: formValues.code,
    type: formValues.type,
    modelingMethod: formValues.modelingMethod,
  };
  if (formValues.type === containerTypeShape.FORKLIFT_PALLET) {
    return { ...data, ...forkPalletMapping(formValues) };
  } else if (formValues.type === containerTypeShape.PALLET) {
    return { ...data, ...palletMapping(formValues) };
  }
  if (formValues.modelingMethod === containerModel.STANDARD) {
    return { ...data, ...standardMapping(formValues) };
  }
  return { ...data, ...noStandardMapping(formValues) };
};

const forkExpandMapping = (record) => {
  const formValues = {
    modelInfo: {},
  };
  formValues.modelInfo.detectionDistance = record.detectionDistance;
  formValues.modelInfo.handleableRobotModel = record.handleableRobotModel;
  formValues.modelInfo.materialType = record.materialType;
  formValues.modelInfo.surfaceColor = record.surfaceColor;
  formValues.modelInfo.modelingMethod = record.modelingMethod;
  formValues.modelInfo.palletStructure = record.palletStructure;

  const face = record.parts.find((v) => v.type === containerParts.SURFACE);
  formValues.modelInfo.surfaceLength = face?.geometricShape?.length;
  formValues.modelInfo.surfaceWidth = face?.geometricShape?.width;

  const hole = record.parts.find((v) => v.type === containerParts.HOLE);
  formValues.modelInfo.holeHeight = hole?.geometricShape?.height;
  formValues.modelInfo.holeWidth = hole?.geometricShape?.width;

  const edgeColumn = record.parts.find((v) => v.type === containerParts.PALLET_EDGE_COLUMN);
  formValues.modelInfo.edgePillar = edgeColumn?.geometricShape?.width;

  if (record.palletStructure === palletStructureType.DOUBLE) {
    const centerColumn = record.parts.find((v) => v.type === containerParts.PALLET_CENTER_COLUMN);
    formValues.modelInfo.centerPillar = centerColumn?.geometricShape?.width;
  }

  return formValues;
};

const palletExpandMapping = (record) => ({
  modelInfo: {
    surfaceLength: record.parts[0]?.geometricShape?.length,
    surfaceWidth: record.parts[0]?.geometricShape?.width,
  },
});

const standExpandMapping = (record) => {
  const formValues = { modelInfo: {} };
  const face = record.parts.find((v) => v.type === containerParts.SURFACE);
  formValues.modelInfo.surfaceLength = face?.geometricShape?.length;
  formValues.modelInfo.surfaceWidth = face?.geometricShape?.width;
  formValues.modelInfo.surfaceHeight = face?.geometricShape?.height;

  const leg = record.parts.find((v) => v.type === containerParts.LEG);
  formValues.modelInfo.legLength = leg?.geometricShape?.length;
  formValues.modelInfo.legWidth = leg?.geometricShape?.width;
  formValues.modelInfo.legHeight = leg?.geometricShape?.height;

  return formValues;
};

const noStandardExpandMapping = (record) => {
  return {
    shelves: shelfElementType.LEG,
    modelInfo: record.parts.map((v) => {
      const model = { key: uniqueId() };
      if (v.type === containerParts.SURFACE) {
        model.type = shelfElementType.FACE;
        model.length = v.geometricShape.length;
        model.width = v.geometricShape.width;
        model.height = v.geometricShape.height;
        model.x = 0;
        model.y = 0;
      }
      if (v.type === containerParts.LEG) {
        model.type = shelfElementType.LEG;
        model.length = v.geometricShape.length;
        model.width = v.geometricShape.width;
        model.height = v.geometricShape.height;
        model.x = v.geometricShape.centerPoint?.x;
        model.y = v.geometricShape.centerPoint?.y;
      }
      return model;
    }),
  };
};

export const getExpandModelMapping = (record) => {
  const formValues = {
    id: record.id,
    name: record.name,
    code: record.code,
    type: record.type,
    modelingMethod: record.modelingMethod,
    modelInfo: {},
  };
  if (record.type === containerTypeShape.FORKLIFT_PALLET) {
    return { ...formValues, ...forkExpandMapping(record) };
  } else if (record.type === containerTypeShape.PALLET) {
    return { ...formValues, ...palletExpandMapping(record) };
  }
  if (record.modelingMethod === containerModel.STANDARD) {
    return { ...formValues, ...standExpandMapping(record) };
  }
  return { ...formValues, ...noStandardExpandMapping(record) };
};
