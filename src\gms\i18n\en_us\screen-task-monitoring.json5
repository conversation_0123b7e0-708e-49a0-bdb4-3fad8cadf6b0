{
  "geekplus.gms.client.screen.taskMonitoring.page.title": "Task Monitoring",
  "geekplus.gms.client.screen.taskMonitoring.page.subtitle": "Monitor task execution in real time, and intervene in and handle task error.",
  "geekplus.gms.client.screen.taskMonitoring.filters.robot": "Robot",
  "geekplus.gms.client.screen.taskMonitoring.filters.robotTaskID": "Robot task ID",
  "geekplus.gms.client.screen.taskMonitoring.filters.loadCode": "Container code",
  "geekplus.gms.client.screen.taskMonitoring.filters.point": "Location",
  "geekplus.gms.client.screen.taskMonitoring.filters.workflowEndMethod": "End method",
  "geekplus.gms.client.screen.taskMonitoring.filters.timeRange": "Creation time",
  "geekplus.gms.client.screen.taskMonitoring.filters.timeRange.start.placeholder": "Start time",
  "geekplus.gms.client.screen.taskMonitoring.filters.timeRange.end.placeholder": "Completed time",
  "geekplus.gms.client.screen.taskMonitoring.filters.fuzzySearch.placeholder": "Please enter the task ID/Task name/Upstream task ID",
  "geekplus.gms.client.screen.taskMonitoring.filters.locationEntity.placeholder": "Starting point/destination",
  "geekplus.gms.client.screen.taskMonitoring.columns.task": "Task",
  "geekplus.gms.client.screen.taskMonitoring.columns.taskID": "Task ID",
  "geekplus.gms.client.screen.taskMonitoring.columns.taskName": "Task name",
  "geekplus.gms.client.screen.taskMonitoring.columns.taskName.title": "Click to view task details",
  "geekplus.gms.client.screen.taskMonitoring.columns.upstreamTaskNum": "Upstream task ID",
  "geekplus.gms.client.screen.taskMonitoring.columns.start": "From",
  "geekplus.gms.client.screen.taskMonitoring.columns.end": "To",
  "geekplus.gms.client.screen.taskMonitoring.columns.startPoint": "Starting point",
  "geekplus.gms.client.screen.taskMonitoring.columns.endPoint": "Destination",
  "geekplus.gms.client.screen.taskMonitoring.columns.robotNum": "Robot",
  "geekplus.gms.client.screen.taskMonitoring.columns.robotTaskID": "Robot task ID",
  "geekplus.gms.client.screen.taskMonitoring.columns.container": "Container",
  "geekplus.gms.client.screen.taskMonitoring.columns.container.title": "Click to view container details",
  "geekplus.gms.client.screen.taskMonitoring.columns.taskStatus": "State",
  "geekplus.gms.client.screen.taskMonitoring.columns.workflowEndMethod": "End method",
  "geekplus.gms.client.screen.taskMonitoring.columns.currentStage": "Current stage",
  "geekplus.gms.client.screen.taskMonitoring.columns.exceptionDesc": "Exception description",
  "geekplus.gms.client.screen.taskMonitoring.columns.createTime": "Creation time",
  "geekplus.gms.client.screen.taskMonitoring.columns.create": "Create",
  "geekplus.gms.client.screen.taskMonitoring.columns.completeTime": "Completion time",
  "geekplus.gms.client.screen.taskMonitoring.columns.priority": "Priority",
  "geekplus.gms.client.screen.taskMonitoring.columns.actions": "Operation",
  "geekplus.gms.client.screen.taskMonitoring.tabs.realTimeTask": "Real-time task",
  "geekplus.gms.client.screen.taskMonitoring.tabs.historyTask": "Historical task",
  "geekplus.gms.client.screen.taskMonitoring.actions.recover.popconfirm": "Are you sure to recover?",
  "geekplus.gms.client.screen.taskMonitoring.actions.pause.popconfirm": "Are you sure to pause?",
  "geekplus.gms.client.screen.taskMonitoring.actions.delete.popconfirm": "Are your sure to delete it?",
  "geekplus.gms.client.screen.taskMonitoring.actions.cancel.popconfirm": "Are you sure to cancel?",
  "geekplus.gms.client.screen.taskMonitoring.actions.more": "More",
  "geekplus.gms.client.screen.taskMonitoring.actions.recover": "Recovery",
  "geekplus.gms.client.screen.taskMonitoring.actions.pause": "Pause",
  "geekplus.gms.client.screen.taskMonitoring.actions.priority": "Adjust to the top priority",
  "geekplus.gms.client.screen.taskMonitoring.actions.export": "Export",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTaskStatus.create": "To be issued",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTaskStatus.executing": "Executing",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTaskStatus.exception": "Exception",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTaskStatus.pause": "Paused",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTaskStatus.cancelExecuting": "Canceling",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.currentStage.waitingToSend": "To be issued to RMS",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.currentStage.robotAllocating": "It has been issued to RMS, but is to be assigned to the robot.",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.currentStage.robotAllocated": "It has been assigned to the root, but is to be executed.",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.currentStage.movingToStartPoint": "The robot is heading for the starting point.",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.currentStage.arrivedStartPoint": "The robot has arrived at the starting point.",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.currentStage.fetched": "Removal completed",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.currentStage.prepareToEndPoint": "The robot is to go to the destination.",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.currentStage.movingToEndPoint": "The robot is heading for the destination.",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.currentStage.arrivedEndPoint": "The robot has arrived at the destination.",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.currentStage.readyToLeave": "After placement completed, the robot is to leave.",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.currentStage.movingToCancelPoint": "The robot is heading for the placement point after the task is canceled.",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.currentStage.arrivedCancelPoint": "The robot arrives at the placement point after the task is canceled.",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.currentStage.waitingManualHandle": "Wait for manual handling",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.currentStage.loadedAndMovingToEndPoint": "After removal completed, the robot is heading for the destination.",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.error.category": "Type",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.error.level": "Level",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.error.msg": "Exception description",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.error.deviceTaskError": "Failed to issue the device task for the reason ({msg})",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.error.schedulerTaskError": "Failed to issue the scheduling task for the reason ({msg})",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.error.type.deviceDriver": "Driver",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.error.type.deviceModule": "Module",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.error.type.deviceBehavior": "Behavior",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.error.type.deviceCommunication": "Communication",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.error.type.pathPlan": "Path Planning",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.error.type.deviceTask": "Task",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.error.type.deviceComponent": "Component",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.error.type.deviceLED": "LED strip error",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.error.type.task": "Robot business task",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.error.type.communication": "Robot network communication",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.error.type.deviceDispatch": "Path Schedule",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.error.type.deviceBattery": "Battery error",
  "geekplus.gms.client.screen.taskMonitoring.historyTaskStatus.completed": "Completed",
  "geekplus.gms.client.screen.taskMonitoring.historyTaskStatus.canceled": "Canceled",
  "geekplus.gms.client.screen.taskMonitoring.historyTaskStatus.exception": "Completed manually",
  "geekplus.gms.client.screen.taskMonitoring.historyTaskStatus.closed": "Completed",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.ready": "Prepare",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.create": "to be issued",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.waitQueueRobot": "Issue workstation queuing task",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.suspension": "Pause issuing",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.waitingSendRPC": "Task queuing",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.taskSended": "Task issued",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.moving": "Robot is on the road",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.fetched": "Container fetched",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.executing": "Executing",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.interruptWaiting": "Task interrupted",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.commandExecuting": "Executing command",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.passWaitPoint": "Robot has left",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.deviceExecuting": "Transport within the device",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.nodeWaiting": "Wait for the next task",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.pause": "Task paused",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.undoing": "Undoing",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.backing": "Returning",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.cancelExecuting": "Canceling",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.cancelExecution": "Canceling",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.completed": "Completed",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.exceptionCompleted": "Completed",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.canceled": "Canceled",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.breakCompleted": "Interrupted by interface",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.cancelFailed": "Cancellation failed",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.breakFailed": "Interruption failed by interface",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.taskUnitComplete": "Subtask completed",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.closed": "Completed",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.taskStop": "Task suspended",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.subTaskPause": "Subtask paused",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorCategory.systemDispatch": "System (scheduling module)",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorCategory.systemEquipment": "System (device module)",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorCategory.systemOther": "System (business module)",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorCategory.robot": "Robot",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorCategory.container": "Shelf",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorLevel.warn": "Warning",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorLevel.errorNeedCatch": "Error, intervention is required.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21000": "Positioning lateral deviation of the end-side QR code > 20 mm; angle is greater than 2°.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21001": "The angle deviation of straight moving is greater than 3°.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21002": "The difference between the angle of rotation integral and the angle of encoder exceeds the limit",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21003": "Robot center position offset exceeds the limit",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21004": "The driver wheel skids as it rotates.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21005": "The direction indicated by the control instruction is inconsistent with the actual angle rotation direction. For example, the fault might be caused by wrong instruction or excessive load.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21006": "When fitting the arc according to the point, the accumulated error of the track smoothness exceeds the limit.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21007": "The error of target attitude and stop attitude is over limit",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21008": "Shelf rotation angle is more than 180°",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21009": "The relative error of the shelf QR code and the ground QR code is over limit",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21010": "Crooked while lifting the shelf.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21011": "Gyro integration detection is carried out when the robot is in the stop mode. If the robot in the stop status is rotated by an external force, an error might be reported.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21012": "Image fusion angle change detection is carried out when the robot is in the stop mode. If the robot in the stop status is rotated by an external force, an error might be reported.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21013": "Encoder angle change detection is carried out when the robot is in the stop mode. If the robot in the stop status is rotated by an external force, an error might be reported.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21014": "The robot moves to the charging station with an xy coordinate deviation of 20mm",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21015": "The position of the charging station xy deviates from the integer coordinate by 20 mm",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21016": "The backward direction of charging is wrong.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21017": "The height parameter is wrong when lifting",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21018": "The height parameter is wrong when descending",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21019": "The robot's current point and path starting position exceed the limit",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21020": "It is bad to adjust the shelf while rotating",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21021": "If a shelf QR code position is very poor, do not put the shelf down to prevent hitting the shelf next to it",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21022": "More than 2 QR codes on the ground lost",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21023": "Timeout of getting the DSP data ceil rect",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21024": "Timeout of getting the DSP data ceil decode",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21025": "Wrong angle when planning small path",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21026": "Performing task error when in manual mode received",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21027": "Failed to charge",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21028": "The QR code of the up-facing shelf is not in the field of vision while moving",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21029": "Could not find any QR codes on the ground after switching to QR code navigation mode",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21030": "Wheels skidded while moving",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21031": "Shelf model identification error",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21032": "Emergency stop triggered",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21033": "The manual mode is enabled.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21057": "Front bumper trigger",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21059": "Rear anti-collision bar is triggered.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21072": "Decoding timeout of up-facing shelf QR code",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21060": "Robot Obstacle Avoidance",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21034": "Obstacle detection box or host computer does not receive obstacle detection sensor data.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21035": "Abnormal communication link between the obstacle detection box and MCU",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21036": "Error obstacle-detection sensor data received by the obstacle detection box or host computer.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21037": "Abnormal communication link between the power box and main control",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21038": "The power box received incorrect battery data",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21039": "CAN1 communication fault. For example, no data are received within 400 ms.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21040": "CAN2 communication fault. For example, no data are received within 400 ms.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21041": "Driver disconnected",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21042": "Encoder disconnected",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21043": "DSP communication loss. For example, no DSP data are received within 40 s.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12000": "The robot is disconnected",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12001": "Timeout of sending subtask",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21044": "Gyro temperature have changed too much",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21045": "The base values of two calibrations before and after by the gyro have changed too much",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21046": "The driver wheel skids as it rotates.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21047": "No obstacle avoidance data update within 2 seconds",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21048": "No battery update data is received in 200 seconds.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21049": "The drive wheel is locked up. For example, if the drive wheels do not move for more than 2 seconds in the motion mode of straight line, arc line, rotation, etc., it is considered that they are locked.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21050": "The left wheel skids when moving in a straight line",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21051": "The right wheel skids when moving in a straight line",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21052": "Failed to save image; for example: calibration error of the ground QR code >10°, side error > 4 cm",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21053": "Non-QR code area image feedback; for example: error reporting when a QR code is lost",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21054": "The lifting motor fails to perform the lifting action. For example, an error is reported when the lifting motor does not lift the pallet within 20 s in the lifting status.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21055": "The pulses of the driver wheel encoder have not been updated. For example, the encoder malfunctions when the robot is in linear or arc motion.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21056": "Driver wheel encoder pulse number overflow",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21058": "The QR code of the shelf is decoded incorrectly. For example, the black box has been identified, but the code value is wrong",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21061": "The head tail checksum of the image data uploaded by the camera failed to be parsed.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21062": "Failed to parse the checksum of the image data uploaded by the camera.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21063": "No charging current",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21064": "Charging sensor failure",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21065": "The original data of obstacle detection is not updated within two seconds.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21066": "The driver wheel is overcharged",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21067": "The driver wheel motor over-current",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21068": "Lifter motor over-current",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21069": "DSP lost heartbeat",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21070": "DSP data feedback error",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21071": "Shelf QR code decoding failed. For example, even the black box is not parsed.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21073": "Driver over-temperature",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21074": "Lidar data loss or component malfunction",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21075": "Battery data loss",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21076": "Battery over-temperature protection",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21077": "Motor module can recover from fault",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21078": "Motor module cannot recover from fault (replace part)",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21079": "Absolute encoder battery is low.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21080": "Weighing sensor data loss",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21081": "Communication between main controller and task controller disconnected",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21082": "Task state machine switching error",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21083": "An error command is given at the end of the task.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21084": "Task step stage error",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12002": "Path planning failed",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12003": "The robot is not on the map.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12004": "The starting point or destination of the task is an obstacle.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12005": "Obstacle robots or obstacle shelves on the way",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12006": "The path resource is busy.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12007": "No path can be planned.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21085": "Driver command error",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21086": "Driver motor phase error",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21087": "Driver tracking error",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21088": "Driver feedback error",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21089": "Driver undervoltage",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21090": "Driver overvoltage",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21091": "Driver over-current",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21092": "Driver current short circuit",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21093": "Driver motor temperature is too high.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21094": "Driver motor temperature is too low.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21095": "Driver temperature is too high.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21096": "Driver temperature is too low.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21097": "Drive over-speed alarm",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21098": "Driver address error",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21099": "Component delivery exception",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21100": "Component receipt exception",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21101": "Component communication interrupted",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21102": "Component fault",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12009": "Timeout of Robot fetching shelf",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12010": "Fetch the shelf before reaching the shelf position",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12011": "Waiting point not reached for a long time",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12012": "Robot power does not increase for a long time",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12013": "The robot reaches a low power ratio",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12014": "The charging task cannot be executed without matching to the charging station.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12015": "Without an idle charging station, the charging task cannot be executed.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12016": "There is no available charging station, and the charging task cannot be executed.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12017": "The charging station cell is occupied by other robots.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12018": "Cross-region charging failed",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12019": "Charging across floors failed",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12020": "The current task is blocked and the charging task cannot be executed.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12021": "The current task is not completed and the charging task cannot be performed.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12022": "Charging time too long",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12023": "The robot battery temperature is too high.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12024": "No stops found",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.16000": "Robot exception",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21103": "Depth camera data loss",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21104": "Fisheye camera data loss",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21105": "Network interruption",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21106": "Drive data loss, or device failure",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21107": "Trigger STO",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21108": "Press brake release",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21109": "Dead battery",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21110": "Weighing overload or partial load exceeding limit",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21111": "Location loss",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12025": "The robot is locked.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12026": "Robot battery temperature is too low.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12027": "There is empty pickup & placement exception in the container task.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.22000": "RMS communication is interrupted.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.22001": "CAN communication interruption (charging module)",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.22002": "Screen communication is interrupted.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.22003": "RMS data exception",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.22004": "RMS command exception",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.22005": "Forklift charging station presses emergency stop.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.22006": "Forklift charging station does not detect sensors.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.22007": "Charging module over temperature",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.22008": "Charging current in automatic mode is 0.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.22009": "Charging module warning status",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.22010": "Charging module error status",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.22011": "The charging station reports its unavailability.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.13000": "Charging station disconnection (temporary network interruption)",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.13001": "Charging station disconnection (loss of communication for a long time)",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.13002": "Start sending charging station command timeout",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.13003": "End of charging station command sending timeout",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.13004": "The robot failed to be charged for many times.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.13005": "Charging station restarting",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.13006": "Charging station upgrading",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.14000": "The shelf location is to be confirmed.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.81000": "The batter level of robots across the field is relatively low.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.91000": "Emergency stop button triggered",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.91001": "Emergency stop button disconnected",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.110": "RMS is initializing.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2224": "No available robot is matched by the robot model/ID specified in the workflow (workflow template).",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2014": "Shelf not ready",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2020": "Robot task ID cannot be empty.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2021": "The robot task does not exist.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2030": "Robot ID cannot be empty.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2031": "No available robot is matched by the robot model/ID specified in the workflow (workflow template).",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2040": "Shelf code cannot be empty.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2041": "The internal shelf code does not match the external shelf code.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2042": "Invalid shelf code",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2043": "Shelf code does not exist.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2044": "Shelf still has tasks to do.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2060": "Workstation ID cannot be empty.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2062": "Invalid workstation ID",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2063": "Workstation does not exist.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2061": "The internal workstation does not match the external workstation.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2080": "Destination area ID cannot be empty.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2081": "The destination area does not exist.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2090": "Destination cannot be empty.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2091": "The internal destination coordinate does not match the external destination coordinate.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2093": "Invalid destination QR code",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2094": "Invalid destination cell",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2100": "Destination QR code cannot be empty.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2101": "The destination QR code does not exist.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2110": "The waiting point QR code cannot be empty.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2111": "The waiting point QR code does not exist.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2140": "Invalid cancellation",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2141": "Failed to cancel task",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2150": "Elevator ID cannot be empty.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2151": "The elevator does not exist.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2152": "The floors are not connected by the elevator.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2160": "The robot doesn't arrive at the elevator entry.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2161": "The robot isn't in the elevator.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.3020": "Shelf code cannot be empty.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.3021": "Illegal shelf code",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.3023": "The shelf does not exist.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.3022": "The internal shelf does not match the external shelf.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.3024": "Shelf entering",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.3025": "Shelf already exists.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.3030": "The shelf entry point is beyond the map range.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.3031": "The internal shelf entry point does not match the external shelf entry point.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.3032": "A shelf already exists at this entering point.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.3033": "Entering point cannot be empty.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.3034": "Invalid entering point cell",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.3035": "Requested area does not exist.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.3051": "This area is full.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.3040": "Requested cell code cannot be empty.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.3041": "Invalid requested cell code",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.3043": "Requested cell does not exist.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.3042": "Requested internal cell does not match requested external cell.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.4000": "Query command cannot be empty.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.4001": "Invalid query command",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.4002": "The query command has expired.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.4003": "Unsupported query command",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.4500": "Query data not found",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.1201": "Original task ID cannot be empty.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.1514": "System pause",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.1200": "Device information is empty.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.1216": "Device is disabled.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.1211": "Query equipment information is empty.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.1213": "Query equipment task information is empty.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.1230": "The device task operation is empty.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.1226": "Parameter error",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.1231": "Input parameter value is empty.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.1207": "Missing parameter in body or incorrect format",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.1208": "The input parameter command has not been converted into ASCII, so only numbers can be entered.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.1232": "Mapping command parameter value error",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.1302": "Illegal protocol",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.1301": "Illegal control command",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.10001000": "System emergency Stop",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.10002000": "Workflow configuration exception",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.10002001": "The shelf is not idle.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.10002002": "No available shelves",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.10002003": "The target location (area) is not idle.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.10002004": "No available robots",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.10002005": "Sending task to RMS encountered error.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.10002006": "Failed to recognize the container code",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.10002007": "The recognized container code is inconsistent with the issued container code.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.10002008": "Other exceptions",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.10003001": "Robots queue up.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.10003002": "The execution of interface command ({0}) encountered error.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.10003004": "The target location is occupied by the task ({0}). Check for task exception and handle it.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.10003005": "The target location is occupied by the container ({0}). Please update the container to other location or remove it in the site monitoring page/list of containers.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.10003006": "The destination area ({0}) is fully occupied.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.10003007": "There is no available hoist.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.10003008": "The container ({0}) is locked to stop the task from being executed. Unlock it or handle the task in the site monitoring interface.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.10003009": "The queuing area ({0}) is fully occupied.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2050": "Invalid shelf side",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2051": "Shelf side cannot be empty.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2092": "The destination coordinate is beyond the map's boundary.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2120": "Shelf score is out of range.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2130": "Task priority is out of range.",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.1219": "The original task has unfinished associated tasks.",
  "geekplus.gms.client.screen.taskMonitoring.detail.page.title": "Task details",
  "geekplus.gms.client.screen.taskMonitoring.detail.page.tableTitle": "Subtask information",
  "geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.taskId.label": "Task ID",
  "geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.taskId.copy": "Copy",
  "geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.copySuccess": "Copy succeeded",
  "geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.taskName": "Task name",
  "geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.taskName.value.tooltip": "Click to view the workflow configuration associated with the task",
  "geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.from": "Source",
  "geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.priority": "Priority",
  "geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.creator": "Creator",
  "geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.start": "Starting point",
  "geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.upstreamTaskNum": "Upstream task ID",
  "geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.robotTaskID": "Robot task ID",
  "geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.end": "Destination",
  "geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.createTime": "Creation time",
  "geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.completedTime": "Completion time",
  "geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.currentStage": "Current stage",
  "geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.workflowEndMethod": "End method",
  "geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.exceptionDesc": "Exception description",
  "geekplus.gms.client.screen.taskMonitoring.detail.columns.subTaskId": "Subtask ID",
  "geekplus.gms.client.screen.taskMonitoring.detail.columns.start": "Starting point",
  "geekplus.gms.client.screen.taskMonitoring.detail.columns.target": "Destination location",
  "geekplus.gms.client.screen.taskMonitoring.detail.columns.robotNum": "Robot",
  "geekplus.gms.client.screen.taskMonitoring.detail.columns.container": "Container code",
  "geekplus.gms.client.screen.taskMonitoring.detail.columns.subTaskStatus": "State",
  "geekplus.gms.client.screen.taskMonitoring.detail.columns.createTime": "Creation time",
  "geekplus.gms.client.screen.taskMonitoring.detail.columns.completeTime": "Completion time",
  "geekplus.gms.client.screen.taskMonitoring.detail.columns.actions": "Operation",
  "geekplus.gms.client.screen.taskMonitoring.detail.taskProcess.tabs.process": "Execution process",
  "geekplus.gms.client.screen.taskMonitoring.detail.taskProcess.tabs.log": "Log",
  "geekplus.gms.client.screen.taskMonitoring.statistics.task.all": "All",
  "geekplus.gms.client.screen.taskMonitoring.statistics.task.create": "To be issued",
  "geekplus.gms.client.screen.taskMonitoring.statistics.task.executing": "Executing",
  "geekplus.gms.client.screen.taskMonitoring.statistics.task.pause": "Paused",
  "geekplus.gms.client.screen.taskMonitoring.statistics.task.exception": "Exception",
  "geekplus.gms.client.screen.taskMonitoring.statistics.task.exceptionCompleted": "Completed manually",
  "geekplus.gms.client.screen.taskMonitoring.statistics.task.completed": "Completed",
  "geekplus.gms.client.screen.taskMonitoring.statistics.task.canceled": "Canceled",
  "geekplus.gms.client.screen.taskMonitoring.statistics.task.cancelExecuting": "Canceling",
  "geekplus.gms.client.screen.taskMonitoring.taskProcess.ajax.requestUrl": "Request address: {requestUrl}",
  "geekplus.gms.client.screen.taskMonitoring.taskProcess.ajax.title": "Interface calling details",
  "geekplus.gms.client.screen.taskMonitoring.taskProcess.ajax.req.name": "Sender",
  "geekplus.gms.client.screen.taskMonitoring.taskProcess.ajax.req.time": "Sending time",
  "geekplus.gms.client.screen.taskMonitoring.taskProcess.ajax.req.detail": "Send a message",
  "geekplus.gms.client.screen.taskMonitoring.taskProcess.ajax.res.name": "Receiver",
  "geekplus.gms.client.screen.taskMonitoring.taskProcess.ajax.res.time": "Receiving time",
  "geekplus.gms.client.screen.taskMonitoring.taskProcess.ajax.res.detail": "Response message",
  "geekplus.gms.client.screen.taskMonitoring.taskSource.dio": "Upstream API",
  "geekplus.gms.client.screen.taskMonitoring.taskSource.pda": "PDA",
  "geekplus.gms.client.screen.taskMonitoring.taskSource.station": "Workstation",
  "geekplus.gms.client.screen.taskMonitoring.taskSource.workflowInitiation": "Main workflow triggered",
  "geekplus.gms.client.screen.taskMonitoring.taskSource.device": "Sensor",
  "geekplus.gms.client.screen.taskMonitoring.taskSource.triggerTask": "System trigger",
  "geekplus.gms.client.screen.taskMonitoring.taskSource.wifiButton": "Pager",
  "geekplus.gms.client.screen.taskMonitoring.locationTypes.stopPoint": "Stops",
  "geekplus.gms.client.screen.taskMonitoring.locationTypes.workstation": "Workstation",
  "geekplus.gms.client.screen.taskMonitoring.locationTypes.shelfPoint": "Shelf point",
  "geekplus.gms.client.screen.taskMonitoring.locationTypes.area": "Area",
  "geekplus.gms.client.screen.taskMonitoring.locationTypes.other": "Others",
  "geekplus.gms.client.screen.taskMonitoring.locationTypes.generalPoint": "Map location",
  "geekplus.gms.client.screen.taskMonitoring.locationTypes.palletPoint": "Pallet position",
  "geekplus.gms.client.screen.taskMonitoring.locationTypes.equipmentPoint": "Device location",
  "geekplus.gms.client.screen.taskMonitoring.locationTypes.queueArea": "Queuing Area",
  "geekplus.gms.client.screen.taskMonitoring.locationTypes.denseStorageArea": "High-density storage area",
  "geekplus.gms.client.screen.taskMonitoring.deviceExecutionStatus.success": "Success",
  "geekplus.gms.client.screen.taskMonitoring.deviceExecutionStatus.deviceOffline": "Device is offline",
  "geekplus.gms.client.screen.taskMonitoring.deviceExecutionStatus.signalTrigger": "Trigger by flag bit",
  "geekplus.gms.client.screen.taskMonitoring.deviceExecutionStatus.timeout": "Timeout",
  "geekplus.gms.client.screen.taskMonitoring.deviceExecutionStatus.cancel": "Cancel",
  "geekplus.gms.client.screen.taskMonitoring.deviceExecutionStatus.failed": "Failed",
  "geekplus.gms.client.screen.taskMonitoring.workflowPhase.create": "Create",
  "geekplus.gms.client.screen.taskMonitoring.workflowPhase.executing": "Executing",
  "geekplus.gms.client.screen.taskMonitoring.workflowPhase.completed": "Completed",
  "geekplus.gms.client.screen.taskMonitoring.workflowPhase.closed": "Completed",
  "geekplus.gms.client.screen.taskMonitoring.workflowPhase.canceled": "Canceled",
  "geekplus.gms.client.screen.taskMonitoring.workflowPhase.canceling": "Canceling",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.goFetching": "The robot is fetching the shelf (container).",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.shelfFetched": "The robot has fetched the shelf.",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.goDelivering": "The robot is moving the shelf.",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.queuing": "The robot is queuing up.",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.shelfArrived": "The shelf has arrived at the destination.",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.goReturn": "Returning shelves",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.shelfTurning": "The robot is rotating the shelf.",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.moving": "The robot is moving.",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.arrivedWaitPoint": "The robot has arrived at the waiting point.",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.leavingWaitPoint": "Leave the waiting point",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.receiveFinish": "The roller robot has finished receiving the goods.",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.dropFinish": "The roller robot has finished dropping the goods.",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.arrivedElevatorEntry": "The robot has arrived at the elevator, waiting for entering the elevator.",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.enteredElevator": "The robot has entered the elevator.",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.leavedElevator": "Leave the elevator",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.arrived": "The robot arrives at the destination.",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.fetched": "The task is completed.",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.arrivedAreaEntry": "The robot requests entry into the area.",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.enteredArea": "The robot has entered the area.",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.leavedArea": "The robot has left the area.",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.turnFinished": "Turning has been finished.",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.toWaitbit": "Go to waiting point",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.arrivedWaitbit": "The robot has arrived at the waiting point.",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.arrivedOperate": "The robot arrives at the docking location.",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.operateEntry": "The robot requests to enter the docking location.",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.operateAllowed": "The robot is ready to enter the docking location.",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.adjustOperate": "The robot has adjusted its posture at the docking location.",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.operateFinished": "Docking /action is finished at the docking location (machine).",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.operateUnitCompleted": "The robot leaves the docking location and the unit action is finished.",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.goOffsetFinish": "Front and rear offsets of the composite robots are finished.",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.passWaitPoint": "Pass through the passing point",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.robotTurning": "The robot turns.",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.startNode": "The task is started.",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.waitNode": "Waiting point: {endLocationCode}",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.autoAssignmentNode": "Automatic value assignment point: {endLocationCode}",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.passNode": "Passing point: {endLocationCode}",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.arrivedNode": "Arrive at {endLocationCode}",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.completed": "Task is completed.",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.closed": "The task is completed manually by {operator}.",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.deleted": "The task is deleted by {operator}.",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.canceled": "The task is canceled manually by {operator}.",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.robotRemoved": "The task is canceled manually by removing the robot.",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.robotTerminalCanceled": "The task is canceled manually through the robot end.",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.robotTerminalDeliveryCompleted": "The task is completed manually through the robot end.",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.robotManualDeliveryCompleted": "The task is completed manually through the robot end.",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.cancelWorkflowCompleted": "The task is canceled manually by {operator}.",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.scanCancel": "The task is automatically canceled by {operator}.",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.forceManualFinished": "The task is forcibly completed by {operator}.",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.manualFinished": "The manually completed goods are handled and unlocked by {operator}.",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.manualOneStepFinished": "The task is completed manually by {operator}.",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.forceCancelFinished": "The task is forcibly canceled by {operator}.",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.locationFinished": "The canceled goods are handled and unlocked by {operator}.",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.locationAutoPopFinished": "Cancellation is completed by {operator}.",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.currentFinished": "Goods are handled and unlocked by {operator}.",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.receiveRobotTerminalDeliveryCompleted": {
    richText: [
      {
        font: {
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: "Placement is completed by operating ",
      },
      {
        font: {
          bold: true,
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: "Placement completed",
      },
      {
        font: {
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: " at the robot end.",
      },
    ],
  },
  "geekplus.gms.client.screen.taskMonitoring.execution.name.undoCanceled": "Cancel the task automatically by revoking to the starting point.",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.operator": "(Operator: {operator})",
  "geekplus.gms.client.screen.taskMonitoring.startLocationCode.-1": "Initial position of robot",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.createTask.flowTemplate": "The upstream system issues a task through the workflow template.",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.createTask.flowConfig": "The upstream system issues a task through the workflow.",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.createStationTask": "A workstation task is initiated by the operator ({operator}).",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.nextTask": "The upstream system triggers the task to continue.",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.createExeTask": "Create a subtask ({beginCellCode} → {endCellCode})",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.backExecution": "Roll back a subtask ({beginCellCode} → {endCellCode})",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.undoExecution": "Revoke the subtask ({beginCellCode} → {endCellCode})",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveRobotMoving": "The robot ({robotId}) starts to move.",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveRobotFetched": "The robot ({robotId}) has fetched goods.",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveRobotShelfArrived": "The robot ({robotId}) has placed goods.",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveRobotArrived": "The robot ({robotId}) arrives at {endLocationCode}.",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveRobotArriveWaitPoint": "The robot ({robotId}) arrives at the waiting point {waitCellCode}.",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.leavingWaitPoint": "The robot ({robotId}) leaves the waiting point ({waitCellCode}).",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveRobotActionCompleted": "{command} is completed.",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveRobotActionCompleted.rollerRotate": "The upper structure of roller starts to roll.",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveRobotPassWaitPoint": "The robot ({robotId}) arrives at the passing point ({passByCellCode}).",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.sendDmpTask": "Send the device command ({deviceActionTitle}) to {deviceName}",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.sendDmpTaskFail": "Failed to send the device command ({deviceActionTitle}) to {deviceName} for the reason ({msg})",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.retryDmpTask": "Resend the device command ({deviceActionTitle}) to {deviceName} by the operator ({operator})",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.completeDmpTask": "Manually complete the device command ({deviceActionTitle}) of {deviceName} by the operator ({operator})",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.retryDmpSubTask": "Under the sub-workflow ({taskId}), resend the device command ({deviceActionTitle}) to {deviceName} by the operator ({operator})",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.completeDmpSubTask": "Under the sub-workflow ({taskId}), manually complete the device command ({deviceActionTitle}) of {deviceName} by the operator ({operator}).",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.sendInterfaceTask": "Interface command ({command}) {result}",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.sendInterfaceTask.success": "Execution completed",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.sendInterfaceTask.fail": "Execution failed",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.sendRMSRobotTask": "Issue the robot command ({command}) to {rotate}",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.sendRMSRobotTask.rotate": "(rotate to {neededSide} side)",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.sendRmsComponentCommandTask": "Send the robot command ({action}) to {rotate}",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.sendRmsComponentCommandTask.rotateAngle": "(rotate to {rotate}°)",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.clearWaitPoint": "Clear the waiting point",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.dmpTaskTrigger": "The device triggering command ({deviceActionTitle}) is executed successfully.",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.waitCommand": {
    richText: [
      {
        font: {
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: "Interruption of task execution, wait for ",
      },
      {
        font: {
          bold: true,
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: "Continue the task",
      },
      {
        font: {
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: " command issued by the upstream system or manually triggered to restart it.",
      },
    ],
  },
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.continue": "Continue the task",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.taskOver": "End of task",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.forceCancelSubWorkflow": "In the sub-workflow ({subWorkflowId}), the task is forcibly canceled by the operator ({operator}).",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.forceManualCompletedSubWorkflow": "In the sub-workflow ({subWorkflowId}), the task is completed manually by the operator ({operator}).",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.recoveryWorkflow": "The task is restored by the operator ({operator}).",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.recoveryExecution": "The subtask ({taskId}) is restored by the operator ({operator}).",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.pauseWorkflow": "The task is suspended by the operator ({operator}).",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.pauseExecution": "The subtask ({taskId}) is suspended by the operator ({operator}).",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.cancelWorkflow": "The task is canceled manually by the operator ({operator}).",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.cancelSubWorkflow": "In the sub-workflow ({subWorkflowId}), the task is canceled manually by the operator ({operator}).",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.completeWorkflow": "The task is completed manually by the operator ({operator}).",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.manualCompleteWorkflowManualWaiting": "The manually completed task is waiting for manual handling by the operator ({operator}).",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.adjustPriority": "The task is adjusted to the top priority by the operator ({operator}).",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.pauseSubWorkflow": "In the sub-workflow ({taskId}), the task is suspended by the operator ({operator}).",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.recoverySubWorkflow": "In the sub-workflow ({taskId}), the task is restored by the operator ({operator}).",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.pauseSubExecution": "Under the sub-workflow ({taskId}), the subtask ({subTaskId}) is suspended by the operator ({operator}).",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.recoverySubExecution": "Under the sub-workflow ({taskId}), the subtask ({subTaskId}) is restored by the operator ({operator}).",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.trafficControlApplyEnter": "Request to enter the traffic control area ({trafficControlAreaName})",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.trafficControlApplyEnterFail": "Failed to request to enter the traffic control area ({trafficControlAreaName}) for the reason ({msg})",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveRobotTerminalDeliveryCompleted": {
    richText: [
      {
        font: {
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: "Operate ",
      },
      {
        font: {
          bold: true,
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: "Placement completed",
      },
      {
        font: {
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: " at the robot end",
      },
    ],
  },
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveRobotTerminalPickupCompleted": {
    richText: [
      {
        font: {
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: "Operate ",
      },
      {
        font: {
          bold: true,
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: "Removal completed",
      },
      {
        font: {
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: " at the robot end",
      },
    ],
  },
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.completeSubWorkflow": "In the sub-workflow ({taskId}), the task is manually completed by the operator ({operator}).",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.sendRmsCancelTask": "Issue the cancellation command ({command})",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveRobotTaskCanceled": "The robot task ({taskId}) is canceled",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.internalCreateTask.triggerTask": "The system automatically triggers the task ({name})",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.internalCreateTask.workflowInitiation": "New tasks are automatically created when tasks are executed through the main workflow ({name}).",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.internalCreateTask.nodeRelationTask": "Sensor triggering task (starting point: {startLocationCode}; destination: {endLocationCode})",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.internalCreateTask.automaticReturnEmptyContainerTask": "The system automatically triggers the empty tote return task (empty tote return area: {returnType}).",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveRobotShelfFinished": "Offset completed",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveRobotScanContainerError.cancel": "Failed to recognize the container through QR code; the system will automatically cancel the task for {reason}.",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveRobotScanContainerError.wait": "Failed to recognize the container through QR code; wait for manual handling for {reason}",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveRobotTaskFetched": "The robot has fetched goods.",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveRobotTaskLeaveStart": "The robot ({robotId}) starts to move.",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveRobotTaskMove": "The robot ({robotId}) starts to move.",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveRobotTaskArriveWaitPoint": "The robot arrives at the waiting point ({waitCellCode}).",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.sendRmsCommandTask": "Send the command ({action}) to move to {endLocationCode}",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.sendRmsCommandTaskFail": "Failed to send the command ({action}) to move to {endLocationCode} for the reason ({msg})",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveDmpCallbackSuccess": "The device command ({deviceActionTitle}) is executed successfully.",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveDmpCallbackFail": "Failed to execute the device command ({deviceActionTitle}) for the reason ({msg})",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus": "Notify the upstream system {status}",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatusFail": "Failed to notify the upstream system {status}",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.workflowPhase": "Task status: {workflowPhase}",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.taskPhase": "; subtask status: {taskPhase}",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.robotPhase": "; robot status: {robotPhase}",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.exceptionState": "; exception status: {exceptionState}",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveRobotTaskArrived": "The robot ({robotId}) arrives at {endLocationCode}.",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveRobotTaskAllocation": "The robot (robot ID: {robotId}) is assigned.",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveRobotTaskShelfArrived": "The robot has finished placing the goods.",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.recordDmpTaskSuccess": "The PLC operation ({deviceActionTitle}) is executed successfully.",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.recordDmpTaskFail": "Failed to execute the PLC operation ({deviceActionTitle})",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.recordDmpTaskTitle": "DMP command execution details",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.trafficControlEntered": "The robot has entered the traffic control area ({trafficControlAreaName}).",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.trafficControlEnteredFail": "The robot failed to enter the traffic control area ({trafficControlAreaName}) for the reason ({msg}).",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.trafficControlApplyLeave": "Request to leave the traffic control area ({trafficControlAreaName})",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.trafficControlApplyLeaveFail": "Failed to request to leave the traffic control area ({trafficControlAreaName}) for the reason ({msg})",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.trafficControlLeft": "The robot has left the traffic control area ({trafficControlAreaName}).",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.trafficControlLeftFail": "The robot failed to leave the traffic control area ({trafficControlAreaName}) for the reason ({msg}).",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.robotRemoved": "The robot ({robotId}) is offline.",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveRobotReassign": "The robot (robot ID: {robotId}) is reassigned.",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.cancelWorkflowManualWaiting": "The task is canceled manually by the operator ({operator}), and the goods will be handled at the current position.",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.current.cancelWorkflowManualWaiting": "The task is canceled manually by the operator ({operator}), and the goods will be handled at the current position.",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.location.toDest": "The task is canceled manually by the operator ({operator}), and the goods will be delivered to {endLocationCode} by the robot.",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.location.toDestWaiting": "The robot arrives at {endLocationCode} and waits for manual handling. ",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.forceManualCompleteWorkflow": "The task is forcibly completed by the operator ({operator}).",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.robotTerminalCanceled": {
    richText: [
      {
        font: {
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: "Operate ",
      },
      {
        font: {
          bold: true,
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: "Cancel",
      },
      {
        font: {
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: " at the robot end",
      },
    ],
  },
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.robotIsLockedWaiting": "Wait for manual handling",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.deleteWorkflow": "Delete task",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.deleteExecution": "Delete the subtask ({executionId})",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.recoveryWorkflow": "Restore task",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.recoveryExecution": "Restore the subtask ({executionId})",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.pauseWorkflow": "Suspend task",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.pauseExecution": "Suspend the subtask ({executionId})",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.adjustPriority": "Adjust the task priority to the top priority",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.cancelWorkflow": "Cancel task",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.cancelExecution": "Cancel the subtask ({executionId})",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.pauseSubWorkflow": "Suspend the task in the sub-workflow ({id})",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.recoverySubWorkflow": "Restore the task in the sub-workflow ({id})",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.cancelSubWorkflow": "Cancel the task in the sub-workflow ({id})",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.completeSubWorkflow": "Manually complete the task in the sub-workflow ({id})",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.pauseSubExecution": "Suspend the subtask ({subTask}) under the sub-workflow ({subflow})",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.recoverySubExecution": "Restore the subtask ({subTask}) under the sub-workflow ({subflow})",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.completeWorkflow": "Manually complete task",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.retryDmpSubTask": "Under the sub-workflow ({subflow}), resend the device command ({command}) to {deviceName}.",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.completeDmpSubTask": "Under the sub-workflow ({subflow}), manually complete the command ({command}) for {deviceName}",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.completeDmpTask": "Manually complete the device command ({command}) for {deviceName}",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.retryDmpTask": "Resend the device command ({command}) to {deviceName}",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.current.waiting": "Cancel the task, and the goods will be handled at the current position",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.completeWorkflowManualWaiting": "Complete and unlock",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.startCancel": "Cancel the task, and the goods will be delivered to the starting point",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.presetStartCancel": "Cancel the task, and the goods will be automatically returned to the preset location: starting point",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.locationCancel": "Cancel the task, and the goods will be delivered to the specified location: {endLocationCode}",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.presetLocationCancel": "Cancel the task, and the goods will be automatically returned to the preset location: {endLocationCode}",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.forceCancelWorkflow": "Forcibly cancel the task",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.forceManualCompleteWorkflow": "Forcibly complete the task",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.robotTerminalDeliveryCompleted": "Operate Placement completed at the robot end",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.robotTerminalPickupCompleted": "Operate Removal completed at the robot end",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.forceCancelSubWorkflow": "Forcibly cancel the task in the sub-workflow ({subWorkflowId})",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.forceManualCompleteSubWorkflow": "Manually complete the task in the sub-workflow ({subWorkflowId})",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.adjustPriority.t": "Adjust to the highest priority",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.adjustPriority.b": "Adjust to the lowest priority",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.adjustPriority.u": "Adjust to a higher priority",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.adjustPriority.d": "Adjust to a lower priority",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.backExecution": "Roll back subtask",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.undoExecution": "Revoke the subtask",
  "geekplus.gms.client.screen.taskMonitoring.subflows.id": "Sub-process task ID",
  "geekplus.gms.client.screen.taskMonitoring.subflows.workflowStatus": "Task status in sub-process",
  "geekplus.gms.client.screen.taskMonitoring.subflows.loop": "Cycles",
  "geekplus.gms.client.screen.taskMonitoring.subflows.loop.content": "{total} times are set for the task, and the task is executed for {loop} times.",
  "geekplus.gms.client.screen.taskMonitoring.parentWorkflow.loop": "Cycles",
  "geekplus.gms.client.screen.taskMonitoring.parentWorkflow.loop.content": "{total} times are set for the task, and the task is executed for {loop} times.",
  "geekplus.gms.client.screen.taskMonitoring.parentWorkflow.id": "Affiliated main workflow",
  "geekplus.gms.client.screen.taskMonitoring.componentCommands.14": "Component rotation by angle",
  "geekplus.gms.client.screen.taskMonitoring.componentCommands.15": "Component rotation by plane",
  "geekplus.gms.client.screen.taskMonitoring.componentCommands.1": "lift",
  "geekplus.gms.client.screen.taskMonitoring.componentCommands.2": "Drop",
  "geekplus.gms.client.screen.taskMonitoring.componentCommands.11": "Pickup",
  "geekplus.gms.client.screen.taskMonitoring.componentCommands.12": "Placement",
  "geekplus.gms.client.screen.taskMonitoring.componentCommands.13": "Offset",
  "geekplus.gms.client.screen.taskMonitoring.componentCommands.16": "Flip",
  "geekplus.gms.client.screen.taskMonitoring.componentCommands.17": "Reset",
  "geekplus.gms.client.screen.taskMonitoring.componentCommands.18": "Flip-reset",
  "geekplus.gms.client.screen.taskMonitoring.componentCommands.20": "Body rotation by angle",
  "geekplus.gms.client.screen.taskMonitoring.componentCommands.21": "Start fetching",
  "geekplus.gms.client.screen.taskMonitoring.componentCommands.22": "Finish pitching",
  "geekplus.gms.client.screen.taskMonitoring.componentCommands.rotateAngle": "(Angle of rotation: {param})",
  "geekplus.gms.client.screen.taskMonitoring.componentCommands.rotateDirection": "(Rotate to {param} side)",
  "geekplus.gms.client.screen.taskMonitoring.RMSRobotTasks.mediaPlay": "Voice play",
  "geekplus.gms.client.screen.taskMonitoring.RMSRobotTasks.turnOfSide": "Rotate by plane",
  "geekplus.gms.client.screen.taskMonitoring.RMSRobotTasks.FBShift": "Front and rear offset",
  "geekplus.gms.client.screen.taskMonitoring.RMSRobotTasks.mediaStop": "Stop voice play",
  "geekplus.gms.client.screen.taskMonitoring.filter.locationOpts.group.workstation": "Workstation",
  "geekplus.gms.client.screen.taskMonitoring.filter.locationOpts.group.area": "Area",
  "geekplus.gms.client.screen.taskMonitoring.filter.locationOpts.group.stock": "Pallet position",
  "geekplus.gms.client.screen.taskMonitoring.filter.locationOpts.group.point": "Map location",
  "geekplus.gms.client.screen.taskMonitoring.systemError.systemErrorHasCanceled": "The system task is canceled",
  "geekplus.gms.client.screen.taskMonitoring.systemError.taskCanceled": "The task is canceled",
  "geekplus.gms.client.screen.taskMonitoring.systemError.checkRobotAndRestart": "Check for goods on the robot, handle them, and restart the robot.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.taskDoneByManual": "The task is completed manually.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.robotReleased": "The robot is released.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.taskCanceling": "Canceling task, please wait...",
  "geekplus.gms.client.screen.taskMonitoring.systemError.taskOperating": "Handling, please wait...",
  "geekplus.gms.client.screen.taskMonitoring.systemError.confirmToCancelTask": "Are you sure to cancel the current task?",
  "geekplus.gms.client.screen.taskMonitoring.systemError.cancelTaskTip": "Tasks cannot be recovered once canceled. Please operate with caution.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forceCancel.cancelFailed": "Cancellation failed",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forceCancel.operateFailed": "Operation failed",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forceCancel.abortCancelTask": "Cancel cancellation",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.loaded.selectSendLocation": "Select the position for goods delivery after task cancellation",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.loaded.current.canceledTip": {
    richText: [
      {
        font: {
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: "Click ",
      },
      {
        font: {
          bold: true,
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: "Finish",
      },
      {
        font: {
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: ", and the system will release the robot and point. If necessary, manually update the inventory status.",
      },
    ],
  },
  "geekplus.gms.client.screen.taskMonitoring.systemError.operationPrompt": "Operation tips",
  "geekplus.gms.client.screen.taskMonitoring.systemError.robotLoaded.selectSendLocation": "Select the position for goods delivery after task cancellation",
  "geekplus.gms.client.screen.taskMonitoring.systemError.resendTask.resendUseTipTitle": "Manually restore the on-site device, and resend the instruction after confirming that the device can operate normally.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.resendTask.resendUseTip": "The system will resend the device command that failed to be executed, and all PLC operations in such command will be re-executed.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.manualCompleteTask.useTipTitle": "Manually turn on the device, and skip the device command after confirming that the robot can pass.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.manualCompleteTask.useTip": "Skip the device command, and the system will clear the waiting point for the robot to continue to move.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.manualCompleteTask.confirmSkip": "Confirm skipping",
  "geekplus.gms.client.screen.taskMonitoring.systemError.fork.takeTipTitle": {
    richText: [
      {
        font: {
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: "Manually remove the goods, and click ",
      },
      {
        font: {
          bold: true,
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: "Finish",
      },
      {
        font: {
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: ".",
      },
    ],
  },
  "geekplus.gms.client.screen.taskMonitoring.systemError.fork.takeTip": {
    richText: [
      {
        font: {
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: "Remove goods and click ",
      },
      {
        font: {
          bold: true,
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: "Finish",
      },
      {
        font: {
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: ", and then the task is truly canceled, and the robot is released.",
      },
    ],
  },
  "geekplus.gms.client.screen.taskMonitoring.systemError.composite.takeTipTitle": "Confirm manual removal of goods",
  "geekplus.gms.client.screen.taskMonitoring.systemError.manualComplete.useTip": "In the case that the task cannot be continuously completed by the robot due to robot or device failure, this function can be used.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.manualComplete.releaseTipTitle": "Follow the steps below after manually delivering goods to the destination:",
  "geekplus.gms.client.screen.taskMonitoring.systemError.manualComplete.releaseTip1": "1. Manually update the container (if any) to the destination.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.manualComplete.releaseTip2": {
    richText: [
      {
        font: {
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: "2. Click ",
      },
      {
        font: {
          bold: true,
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: "Finish and unclock",
      },
      {
        font: {
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: ", and the system will release the robot, container, and point.",
      },
    ],
  },
  "geekplus.gms.client.screen.taskMonitoring.systemError.manualComplete.releaseTip3": "3. If necessary, manually update the inventory.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.manualComplete.useTip3": "After the above steps are completed, the system automatically releases the robot.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.manualComplete.taskHasCompleted": "Manual completion cannot be operated for the completed task.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.manualComplete.taskHasCanceled": "Manual completion cannot be operated for the canceled task.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.manualComplete.cancelExecuting": "Manual completion cannot be operated for the task being canceled.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.manualComplete.completeSuccessfully": "Task is completed.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.manualComplete.forceCompleteSuccessfully": "The system task is completed.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.autpPop.sendSuccessTitle": "Goods are delivered.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.autpPop.sendSuccessTip": "After {name} task is canceled, the goods are delivered to the specified location. View the task details in the historical task.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.roller.takedGoods": "Confirm manual removal of goods",
  "geekplus.gms.client.screen.taskMonitoring.systemError.roller.releaseRobotTips": {
    richText: [
      {
        font: {
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: "Remove goods and click ",
      },
      {
        font: {
          bold: true,
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: "Finish",
      },
      {
        font: {
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: ", and then the robot is released.",
      },
    ],
  },
  "geekplus.gms.client.screen.taskMonitoring.systemError.roller.current.releaseRobotTips": {
    richText: [
      {
        font: {
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: "Click ",
      },
      {
        font: {
          bold: true,
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: "Finish",
      },
      {
        font: {
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: ", and the system will release the robot and point.",
      },
    ],
  },
  "geekplus.gms.client.screen.taskMonitoring.systemError.roller.current.releaseRobotTipTitle": {
    richText: [
      {
        font: {
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: "After the task is canceled, remove the goods and click ",
      },
      {
        font: {
          bold: true,
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: "Finish",
      },
      {
        font: {
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: ".",
      },
    ],
  },
  "geekplus.gms.client.screen.taskMonitoring.systemError.roller.location.cancelSuccessToLocation": "Goods are delivered to {0}.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.roller.location.cancelSuccessToLocationTip1": {
    richText: [
      {
        font: {
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: "Perform manual placement and click ",
      },
      {
        font: {
          bold: true,
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: "Finish",
      },
      {
        font: {
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: ", and then the system will release the robot and point.",
      },
    ],
  },
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.fetching.cancelUseageTitle": "Follow the steps below to cancel the task when the robot is docking for picking and placement:",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.fetching.cancelUseageTip1": "1. Press the emergency stop button to stop the robot and its upper structure.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.fetching.cancelUseageTip2": "2. Switch the robot and its upper structure to the manual mode.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.fetching.cancelUseageTip3": "3. Handle goods to prevent dropping and collision.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.fetching.cancelUseageTip4": "4. Remotely control the robot to separate from the machine or buffer shelf.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.fetching.cancelUseageTip5": {
    richText: [
      {
        font: {
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: "After the above steps are completed, Click ",
      },
      {
        font: {
          bold: true,
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: "Confirm",
      },
      {
        font: {
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: ".",
      },
    ],
  },
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.fetching.cancelSuccessTitle": "The task is canceled",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.fetching.cancelSuccessTip": "Confirm that goods are removed from the robot, and click the button to unlock the robot. If necessary, manually update the inventory.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.releaseTips": "Confirm that goods are removed from the robot, and click the button to release the robot.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.location.cancelSuccessToLocation": "Goods are delivered to {0}.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.location.cancelSuccessToLocationTip": {
    richText: [
      {
        font: {
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: "Perform manual placement and click ",
      },
      {
        font: {
          bold: true,
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: "Finish",
      },
      {
        font: {
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: ", and then the system will release the robot and point. If necessary, manually update the inventory status.",
      },
    ],
  },
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.current.cancelSuccessTitle": "After the task is canceled, remove the goods and click Finish.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.current.cancelSuccessTip": "Click Finish, and the system will release the robot and point. If necessary, manually update the inventory status.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.doubleLoads.cancelTipTitle": "Manually remove the empty barrel, ",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.doubleLoads.cancelTip": "and select the location for the delivery of full coil.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.loads.cancelTip": "Select the position for goods delivery after task cancellation",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.forceCancel.unloads.cancelTitle": "In case of failure or timeout of robot task cancellation, forcibly cancel the system task.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.forceCancel.unloads.cancelTip": "Follow the steps below after the task is forcibly canceled:",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.forceCancel.unloads.cancelTip1": "1. Check for goods carried by the robot, and handle them to prevent dropping and collision.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.forceCancel.unloads.cancelTip2": "2. Restart the robot.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.forceCancel.loads.failReason": "In case of failure or timeout of robot task cancellation, forcibly cancel the system task.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.forceCancel.loads.forceCancelTaskTip": "Follow the steps below after the task is forcibly canceled:",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.forceCancel.loads.step1": "1. Check for goods carried by the robot, and handle them to prevent dropping and collision.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.forceCancel.loads.step2": "2. Restart the robot.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.forceCancel.loads.step3": "3. Unlock the robot in the site monitoring interface.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.forceCancel.loads.step4": "4. If necessary, manually update the inventory.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.roller.forceCancel.failReason": "In case of failure or timeout of robot task cancellation, forcibly cancel the system task.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.roller.forceCancel.forceCancelTaskTip": "Follow the steps below after the task is forcibly canceled:",
  "geekplus.gms.client.screen.taskMonitoring.systemError.roller.forceCancel.checkAndHandleGoods": "1. Check for goods carried by the robot, and handle them to prevent dropping and collision.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.roller.forceCancel.restartRobot": "2. Restart the robot.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.roller.forceCancel.loads.failReason": "In case of failure or timeout of robot task cancellation, forcibly cancel the system task.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.roller.forceCancel.loads.forceCancelTaskTip": "Follow the steps below after the task is forcibly canceled:",
  "geekplus.gms.client.screen.taskMonitoring.systemError.roller.forceCancel.loads.step1": "1. Check for goods carried by the robot, and handle them to prevent dropping and collision.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.roller.forceCancel.loads.step2": "2. Restart the robot.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.roller.forceCancel.loads.step3": "3. Unlock the robot in the site monitoring interface.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.roller.fetching.cancelTip": {
    richText: [
      {
        font: {
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: "To cancel the task, click ",
      },
      {
        font: {
          bold: true,
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: "Confirm",
      },
      {
        font: {
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: " after confirming that there are no risks of dropping and collision when the robot is docking for picking and placement.",
      },
    ],
  },
  "geekplus.gms.client.screen.taskMonitoring.systemError.roller.fetching.releaseTip": "Confirm that goods are removed from the robot, and click the button to unlock the robot.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.roller.fetching.releaseTitle": "The task is canceled",
  "geekplus.gms.client.screen.taskMonitoring.systemError.roller.autoPop.cancelSuccessTitle": "The task is canceled successfully.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.roller.autoPop.cancelSuccessTip": "Goods will be delivered by the robot to the selected location.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.autoRecycle.cancelSuccessTip": "Goods will be delivered by the robot to the preset location: {location}.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.roller.autoPop.sendSuccessTitle": "Goods are delivered.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.roller.autoPop.sendSuccessTip": "After {name} task is canceled, the goods are delivered to the specified location. View the task details in the historical task.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.deviceTask.resendSuccess": "The task is resent successfully.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.deviceTask.manualCompleteSuccess": "The device task is completed manually.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.currentStateNotSupportCancel": "Cancellation is not supported at the current stage.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.taskHasCompleted": "The completed task cannot be canceled.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.taskHasFinished": "The task is completed.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.goodsReturnedToScheduledPlace": "After the task is canceled, goods will be returned by the robot to the preset location.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.taskHasDeleted": "The system task is deleted.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.deleteConfirm": "Are you sure to delete the current task?",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.deleteStepTitle": "Follow the steps below after deleting the system task:",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.deleteStep1": "1. Check for goods carried by the robot, and handle them to prevent dropping and collision.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.deleteStep2": "2. Restart the robot.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.deleteStep3": "3. Manually update the container location.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.unloads.cancelTaskTip": "Tasks cannot be recovered once canceled. Please operate with caution.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.loadChangeTip": 'The robot has turned from the "load state" to the "no-load state". Re-operate it.',
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.selectSendLocation": "Select the return point.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.taskCompletedCannotDeleted": "The completed task cannot be deleted.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.load.current.cancelSuccessTip": {
    richText: [
      {
        font: {
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: "Click ",
      },
      {
        font: {
          bold: true,
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: "Finish",
      },
      {
        font: {
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: ", and the system will release the robot, container, and point.",
      },
    ],
  },
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.load.wait.cancelSuccessTitle": {
    richText: [
      {
        font: {
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: "After the task is canceled, remove the goods, update the container location, and then click ",
      },
      {
        font: {
          bold: true,
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: "Finish",
      },
      {
        font: {
          size: 11,
          color: {
            theme: 1,
          },
          name: "等线",
          charset: 134,
          scheme: "minor",
        },
        text: ".",
      },
    ],
  },
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.forceCancel.unload.useTip": "In case of failure or timeout of robot task cancellation, forcibly cancel the system task.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.forceCancel.unload.useStep": "Follow the steps below after the task is forcibly canceled:",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.forceCancel.unload.step1": "1. Check for goods carried by the robot, and handle them to prevent dropping and collision.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.forceCancel.unload.step2": "2. Restart the robot.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.forceCancel.load.useTip": "In case of failure or timeout of robot task cancellation, forcibly cancel the system task.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.forceCancel.load.useStep": "Follow the steps below after the task is forcibly canceled:",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.forceCancel.load.step1": "1. Check for goods carried by the robot, and handle them to prevent dropping and collision.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.forceCancel.load.step2": "2. Manually update the container location.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.forceCancel.load.step3": "3. Restart the robot.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.forceCancel.load.step4": "4. Unlock the robot in the site monitoring interface.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.manualComplete.forceCancel.useTip": "In case of failure or timeout of robot task processing, forcibly complete the system task.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.manualComplete.forceCancel.useStep": "Follow the steps below after the task is forcibly completed:",
  "geekplus.gms.client.screen.taskMonitoring.systemError.manualComplete.forceCancel.step1": "1. Check for goods carried by the robot, and handle them to prevent dropping and collision.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.manualComplete.forceCancel.step2": "2. Manually update the location of the container (if any).",
  "geekplus.gms.client.screen.taskMonitoring.systemError.manualComplete.forceCancel.step3": "3. Restart the robot.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.manualComplete.forceCancel.step4": "4. Unlock the robot in the site monitoring interface.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.arrivedCancelPoint.location": "The robot arrives at the placement point after the task is canceled and waits for manual handling.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.arrivedCancelPoint.autoPop": "The robot arrives at the placement point after the task is canceled.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.arrivedCancelPoint.current": "Wait for manual handling",
  "geekplus.gms.client.screen.taskMonitoring.systemError.button.toHandle": "Handling",
  "geekplus.gms.client.screen.taskMonitoring.systemError.tips.operateSuccess": "Operation succeeded",
  "geekplus.gms.client.screen.taskMonitoring.systemError.location.cancelSuccessToLocation": "The task is canceled successfully, and the robot is heading for {location}.",
  "geekplus.gms.client.screen.taskMonitoring.systemError.location.cancelSuccessToLocationTip": "After the robot delivers the goods, perform manual operation to complete the task.",
  "geekplus.gms.client.screen.taskMonitoring.componentTaskPhase.receiveFinish": "Pickup",
  "geekplus.gms.client.screen.taskMonitoring.componentTaskPhase.dropFinish": "Placement",
  "geekplus.gms.client.screen.taskMonitoring.componentTaskPhase.goOffsetFinish": "Offset",
  "geekplus.gms.client.screen.taskMonitoring.componentTaskPhase.turnFinished": "Rotate",
  "geekplus.gms.client.screen.taskMonitoring.sendRmsCommandTask.action.goFetch": "Pickup",
  "geekplus.gms.client.screen.taskMonitoring.sendRmsCommandTask.action.goReturn": "Placement",
  "geekplus.gms.client.screen.taskMonitoring.systemName.upstream": "Upstream system",
  "geekplus.gms.client.screen.taskMonitoring.systemName.upstream.clientCode": "Upstream system ({clientCode})",
  "geekplus.gms.client.screen.taskMonitoring.containerDetail.loadingStatus.label": "Full (loaded, materials type: {materialType})",
  "geekplus.gms.client.screen.taskMonitoring.taskProcess.recordDmpTaskError.actionType": "Operation type: {value}",
  "geekplus.gms.client.screen.taskMonitoring.taskProcess.recordDmpTaskError.plcAddress": "Register address: {value}",
  "geekplus.gms.client.screen.taskMonitoring.taskProcess.recordDmpTaskError.configWriteValue": "Configured write value: {value}",
  "geekplus.gms.client.screen.taskMonitoring.taskProcess.recordDmpTaskError.expectReadValue": "Expected read value: {value}",
  "geekplus.gms.client.screen.taskMonitoring.taskProcess.recordDmpTaskError.actualWriteValue": "Actual write value: {value}",
  "geekplus.gms.client.screen.taskMonitoring.taskProcess.recordDmpTaskError.actualReadValue": "Actual read value: {value}",
  "geekplus.gms.client.screen.taskMonitoring.taskProcess.recordDmpTaskError.startTime": "Time of starting execution: {value}",
  "geekplus.gms.client.screen.taskMonitoring.taskProcess.recordDmpTaskError.endTime": "Time of execution completion: {value}",
  "geekplus.gms.client.screen.taskMonitoring.taskProcess.recordDmpTaskError.taskStatus": "State",
  "geekplus.gms.client.screen.taskMonitoring.taskProcess.recordDmpTaskError.errorMsg": "Abnormal information",
  "geekplus.gms.client.screen.taskMonitoring.taskProcess.recordDmpTaskError.table.plcAddress": "Register address",
  "geekplus.gms.client.screen.taskMonitoring.taskProcess.recordDmpTaskError.table.plcValue": "Register value",
  "geekplus.gms.client.screen.taskMonitoring.taskProcess.recordDmpTaskError.table.plcAddressGroup": "Register address: register set",
  "lang.venus.common.plc.err.ioException": "IO exception",
  "lang.venus.common.plc.err.slaveException": "Slave exception",
  "lang.venus.common.plc.err.noConnect": "Offline exception",
  "lang.venus.common.plc.err.otherException": "Contact the developer for other exceptions.",
  "lang.venus.common.plc.err.notSupportWrite": "Write is not supported",
  "geekplus.gms.client.screen.taskMonitoring.scanAbnormal.30001": "Failed to recognize the container code",
  "geekplus.gms.client.screen.taskMonitoring.scanAbnormal.30002": "The recognized container code is inconsistent with the issued container code.",
  "geekplus.gms.client.screen.taskMonitoring.emptyContainerReturnType.workflowStartNode": "Seven points in the workflow",
  "geekplus.gms.client.screen.taskMonitoring.emptyContainerReturnType.containerEnterMapDest": "Destination for container entry",
  "geekplus.gms.client.screen.taskMonitoring.locationTypes.start": "Starting point",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.createTask.transportationTask": "Transport task issued",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.robotAssigned": "Assigned to robot",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.robotArrived": "Robot has arrived",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.goToPickup": "Go to the target point to pick up goods",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.startPickingUp": "Start picking up goods",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.pickupCompleted": "Picking up goods completed",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.goToUnloading": "Go to the target point to deliver goods",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.startUnloading": "Start delivering goods",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.unloadingCompleted": "Delivering goods completed",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.robotLeaving": "Robot departs",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.jobWaitingContinue": "Waiting interrupted",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.jobWaitingUpdate": "Waiting for work update",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.arrivedWaitPoint": "Arrived at the waiting point",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.leavingWaitPoint": "Left the waiting point",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.passWaitPoint": "Passed by waypoints",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.arrivedQueuePoint": "Arrived at the queuing point",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.arrivedElevatorEntry": "Arrived at the waiting point in front of the elevator door, waiting to enter the elevator",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.enteredElevator": "Has entered the elevator",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.leavedElevator": "Has left the elevator",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.scanError": "Scanning error",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.jobCompleted": "Operation completed",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.taskCompleted":"The task is completed",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.jobCancelExecuting": "Task cancellation in progress",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.jobCanceled": "Task canceled",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.jobCancelFailed": "Operation cancel failed, manual intervention required",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.jobWaitingManual": "Waiting for manual intervention to handle the task, processing container",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.jobManualCompleteExecuting": "Manual completion in progress",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.jobManualCompleted": "Task manually completed",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.jobManualCompleteFailed": "Manual completion failed, manual intervention required",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.externalRequestFailed": "Command ({commandCode}) request failed",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.externalRequestSuccess": "Command ({commandCode}) request successful",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.jobError": "Operation anomaly",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.executionCompleted": "Primary task completed",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.subTaskCompleted": "The subtask is completed",
}
