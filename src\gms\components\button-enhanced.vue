<template>
  <gp-button type="primary" :class="cn.button_enhanced" :disabled="isDisabled" @click="handleClick">
    <slot></slot>
  </gp-button>
</template>

<script>
import { computed, reactive } from "vue";

export default {
  name: "ButtonEnhanced",
  components: {},
  props: {
    type: {
      type: String,
      default: "primary",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    actionInterval: {
      type: Number,
      default: 300,
    },
  },
  emits: ["click"],
  setup(props, ctx) {
    const state = reactive({
      inProgress: false,
    });

    const isDisabled = computed(() => {
      return props.disabled || state.inProgress;
    });

    const handleClick = () => {
      if (isDisabled.value) return;

      // disable button during action interval
      state.inProgress = true;
      setTimeout(() => {
        state.inProgress = false;
      }, props.actionInterval);

      ctx.emit("click");
    };

    return { state, isDisabled, handleClick };
  },
};
</script>

<style lang="scss" module="cn">
.button_enhanced {
  //
}
</style>
