<template>
  <div class="tw-w-full tw-h-full tw-bg-white">
    <div class="tw-flex tw-flex-col tw-justify-center tw-items-center tw-h-full">
      <img :src="emptyImg" class="tw-w-[164px] tw-h-[100px]" />

      <!--  -->
      <!-- <div class="tw-mt-4">
        {{ $t("geekplus.gms.client.commons.emptyMsg") }}，{{ $t("geekplus.gms.client.commons.please") }}
        <gp-button type="text" size="normal" @click="$emit('click')">{{ $props.buttonName }}</gp-button>
      </div> -->
      <div class="tw-mt-4">
        {{ $t("geekplus.gms.client.commons.emptyMsg") }}
      </div>
    </div>
  </div>
</template>
<script>
import { defineComponent } from "vue";
import emptyImg from "@/gms/assets/svg/empty.svg";

export default defineComponent({
  name: "EmptyPage",
  props: {
    buttonName: {
      type: String,
      default: "Add",
    },
  },
  setup() {
    return {
      emptyImg,
    };
  },
});
</script>
