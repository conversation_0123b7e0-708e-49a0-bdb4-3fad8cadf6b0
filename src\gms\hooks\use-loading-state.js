import { reactive } from "vue";

/**
 * @description: use loading state for ajax request
 * @param {Function} ajaxFn
 * @param {Object} options
 * @param {Number} options.delay time to wait before show loading
 * @return {Function} return a function with loading state
 * @author: <EMAIL>
 *
 * @example:
 * ------------------------------
 * const requestFn = () => ajax.get('/api/data');
 * const requestHandler = useLoadingState(requestFn);
 * requestHandler();
 * console.info(requestHandler.loading); // true
 */
// signature: (ajaxFn) => function
export function useLoadingState(ajaxFn, { delay = 500 } = {}) {
  const state = reactive({
    pending: false,
    loading: false,
  });

  let timer = null;

  const requestHandler = (...args) => {
    clearTimeout(timer);
    const dataPromise = ajaxFn(...args);
    timer = setTimeout(() => (state.loading = true), delay);
    state.pending = true;

    dataPromise.finally(() => {
      clearTimeout(timer);
      state.loading = false;
      state.pending = false;
    });

    return dataPromise;
  };

  const requestProxy = new Proxy(requestHandler, {
    get: (target, prop, receiver) => {
      if (prop === "loading") {
        return state.loading;
      }

      if (prop === "pending") {
        return state.pending;
      }

      return Reflect.get(target, prop, receiver);
    },
  });

  return requestProxy;
}
