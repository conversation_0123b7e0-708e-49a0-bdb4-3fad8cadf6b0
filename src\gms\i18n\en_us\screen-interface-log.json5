/**
 *   工作站页面国际化字符集
 *   key 必须以 'geekplus.gms.client.screen.interfaceLog.' 开头
 */
 {
  "geekplus.gms.client.screen.interfaceLog.table.action.view": "View",
  "geekplus.gms.client.screen.interfaceLog.title": "Interface Log",
  "geekplus.gms.client.screen.interfaceLog.subtitle": "",
  "geekplus.gms.client.screen.interfaceLog.messageid": "Message ID",
  "geekplus.gms.client.screen.interfaceLog.requestbody": "Request Body",
  "geekplus.gms.client.screen.interfaceLog.interfacename": "Interface Name",
  "geekplus.gms.client.screen.interfaceLog.executecommand": "Execute Command",
  "geekplus.gms.client.screen.interfaceLog.communication status": "Communication Status",
  "geekplus.gms.client.screen.interfaceLog.task status": "Task Status",
  "geekplus.gms.client.screen.interfaceLog.response code": "Response Code",
  "geekplus.gms.client.screen.interfaceLog.external task id": "External Task ID",
  "geekplus.gms.client.screen.interfaceLog.container number": "Container Number",
  "geekplus.gms.client.screen.interfaceLog.starting point code": "Starting Point Code",
  "geekplus.gms.client.screen.interfaceLog.destination code": "Destination Code",
  "geekplus.gms.client.screen.interfaceLog.robot code": "Robot Code",
  "geekplus.gms.client.screen.interfaceLog.time range": "Time Range",
  "geekplus.gms.client.screen.interfaceLog.geekplus": "Geekplus",
  "geekplus.gms.client.screen.interfaceLog.success": "Success",
  "geekplus.gms.client.screen.interfaceLog.failure": "Failure",
  "geekplus.gms.client.screen.interfaceLog.normal": "Normal",
  "geekplus.gms.client.screen.interfaceLog.abnormal": "Abnormal",
  "geekplus.gms.client.screen.interfaceLog.point to point transport movingrequestmsg": "Point-to-Point Transport - MovingRequestMsg"
}
