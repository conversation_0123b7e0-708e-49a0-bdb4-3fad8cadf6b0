/**
 *   工作站页面国际化字符集
 *   key 必须以 'geekplus.gms.client.screen.interfaceLog.' 开头
 */
{
  "geekplus.gms.client.screen.interfaceLog.table.action.view": "查看",
  "geekplus.gms.client.screen.interfaceLog.title": "接口日志",
  "geekplus.gms.client.screen.interfaceLog.subtitle": "",
  "geekplus.gms.client.screen.interfaceLog.messageid": "消息编号",
  "geekplus.gms.client.screen.interfaceLog.requestbody": "请求主体",
  "geekplus.gms.client.screen.interfaceLog.interfacename": "接口名称",
  "geekplus.gms.client.screen.interfaceLog.executecommand": "执行命令",
  "geekplus.gms.client.screen.interfaceLog.communication status": "通讯状态",
  "geekplus.gms.client.screen.interfaceLog.task status": "任务状态",
  "geekplus.gms.client.screen.interfaceLog.response code": "应答代码",
  "geekplus.gms.client.screen.interfaceLog.external task id": "外部任务号",
  "geekplus.gms.client.screen.interfaceLog.container number": "容器号",
  "geekplus.gms.client.screen.interfaceLog.starting point code": "起点编码",
  "geekplus.gms.client.screen.interfaceLog.destination code": "终点编码",
  "geekplus.gms.client.screen.interfaceLog.robot code": "机器人编码",
  "geekplus.gms.client.screen.interfaceLog.time range": "时间范围",
  "geekplus.gms.client.screen.interfaceLog.geekplus": "geekplus",
  "geekplus.gms.client.screen.interfaceLog.success": "成功",
  "geekplus.gms.client.screen.interfaceLog.failure": "失败",
  "geekplus.gms.client.screen.interfaceLog.normal": "正常",
  "geekplus.gms.client.screen.interfaceLog.abnormal": "异常",
  "geekplus.gms.client.screen.interfaceLog.point to point transport movingrequestmsg": "点到点搬运-MovingRequestMsg ",
}
