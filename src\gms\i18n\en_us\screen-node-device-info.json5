/**
 * 点位设备绑定页面国际化字符集
 * key 必须以 'geekplus.gms.client.screen.nodeDeviceInfo.' 开头
 */
{ 
    "geekplus.gms.client.screen.nodeDeviceInfo.title": "Point Device Binding",
    "geekplus.gms.client.screen.nodeDeviceInfo.subtitle": "Bind the relationship between points and external docking devices, allowing devices to be matched based on points for interaction",
    "geekplus.gms.client.screen.nodeDeviceInfo.pageTitle": "Title",
    "geekplus.gms.client.screen.nodeDeviceInfo.pageSubTitle": "Subtitle",
    "geekplus.gms.client.screen.nodeDeviceInfo.title.batchImportNodeDeviceInfo": "No interactive instruction is bound.",
    "lang.ark.fed.screen.nodeDeviceInfo.cellBindStatus.none": "Batch import the device information at the point.",
    "lang.ark.fed.screen.nodeDeviceInfo.cellBindStatus.device": "Only bind the interactive instruction of the device.",
    "lang.ark.fed.screen.nodeDeviceInfo.cellBindStatus.interface": "Only bind the interactive instruction of the interface.",
    "lang.ark.fed.screen.nodeDeviceInfo.cellBindStatus.all": "The interactive instructions of the device and interface have been bound."
}