/**
 * 点位设备绑定页面国际化字符集
 * key 必须以 'geekplus.gms.client.screen.nodeDeviceInfo.' 开头
 */
{ 
  "geekplus.gms.client.screen.nodeDeviceInfo.title": "点位设备绑定",
  "geekplus.gms.client.screen.nodeDeviceInfo.subtitle": "绑定点位和外部对接设备的关系，可以基于点位匹配交互的设备",
  "geekplus.gms.client.screen.nodeDeviceInfo.pageTitle": "标题",
  "geekplus.gms.client.screen.nodeDeviceInfo.pageSubTitle": "子标题",
  "geekplus.gms.client.screen.nodeDeviceInfo.title.batchImportNodeDeviceInfo": "未绑定任何交互指令",
  "lang.ark.fed.screen.nodeDeviceInfo.cellBindStatus.none": "批量导入点位设备信息",
  "lang.ark.fed.screen.nodeDeviceInfo.cellBindStatus.device": "仅绑定设备交互指令",
  "lang.ark.fed.screen.nodeDeviceInfo.cellBindStatus.interface": "仅绑定接口交互指令",
  "lang.ark.fed.screen.nodeDeviceInfo.cellBindStatus.all": "已绑定设备和接口交互指令",
}