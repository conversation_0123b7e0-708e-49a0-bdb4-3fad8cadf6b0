import { isDev } from "gms-constants";
import Vue from "vue";
import Router from "vue-router";

Vue.use(Router);

/** 维护router信息 */
const routes = [
  // 地图操作 地图管理、机器人信息
  {
    path: "/",
    redirect: "home",
    component: () => import("@/contents/app-layout"),
    meta: { requiresAuth: false },
    children: [
      {
        path: "/apiDoc",
        name: "apiDoc",
        meta: {
          requiresAuth: false,
        },
        component: () => import("@/gms/screens/api-doc"),
      },
      {
        path: "/home",
        name: "home",
        component: () => import("@/contents/home"),
      },
      {
        path: "config",
        redirect: "mapManage",
      },
      {
        path: "operator",
        redirect: "taskManage",
      },
      {
        path: "systemSetting",
        redirect: "systemConfig",
      },
      {
        path: "404",
        name: "404",
        component: () => import("@/contents/404"),
      },
      // 容器日志
      {
        path: "containerLog",
        name: "containerLog",
        component: () => import("@/contents/containerLog/containerLog"),
      },
      {
        path: "taskManage",
        name: "taskManage",
        component: () => import("@/gms/screens/task-monitoring"),
      },
      {
        path: "taskManage/:id",
        name: "taskManageDetail",
        component: () => import("@/gms/screens/task-monitoring/task-detail"),
      },
      {
        path: "taskTesting",
        redirect: "taskTesting/list",
      },
      {
        path: "taskTesting/:type/:mode?/:id?",
        name: "taskTesting",
        props: true,
        component: () => import("@/gms/screens/task-testing"),
      },
      {
        path: "operationLog",
        name: "operationLog",
        component: () => import("@/contents/operationLog/operationLog"),
      },
      {
        path: "buttonConfiguration",
        name: "buttonConfiguration",
        component: () => import("@/contents/buttonConfiguration/buttonConfiguration"),
      },
      {
        path: "moduleInfoConfiguration",
        name: "moduleInfoConfiguration",
        component: () => import("@/contents/moduleInfoConfiguration/moduleInfoConfiguration"),
      },
      // 流程
      {
        path: "flowConfig",
        name: "flowConfig",
        component: () => import("@/gms/screens/flow-config/index.vue"),
      },
      // 流程模板
      {
        path: "flowTemplate",
        name: "flowTemplate",
        component: () => import("@/gms/screens/flow-template/index.vue"),
      },
      // 交互配置
      {
        path: "flowNodeConfig",
        name: "flowNodeConfig",
        component: () => import("@/gms/screens/flow-node-config"),
      },
      // 节点规则配置
      {
        path: "flowRuleConfig",
        name: "flowRuleConfig",
        component: () => import("@/contents/flowRuleConfig/flowRuleConfig"),
      },
      /* GMS 3.3 移动到右上角
      {
        path: "systemConfig",
        name: "systemConfig",
        component: () => import("@/contents/systemConfig"),
      },
      */
      // 等待点交互
      {
        path: "nodeConfig",
        name: "nodeConfig",
        component: () => import("@/contents/nodeConfig/nodeConfig"),
      },
      // 回调接口
      {
        path: "callbackAddressConfig",
        name: "callbackAddressConfig",
        component: () => import("@/contents/callbackInterface/callbackInterfacenew"),
      },
      // 任务触发器
      {
        path: "taskSchedule",
        name: "taskSchedule",
        component: () => import("@/contents/taskSchedule/taskSchedule"),
      },
      // 任务触发器监控
      {
        path: "taskScheduleMonitor",
        name: "taskScheduleMonitor",
        component: () => import("@/contents/taskScheduleMonitor/taskScheduleMonitor"),
      },
      // 工作站暂存排队控制
      {
        path: "wscontrol",
        name: "wscontrol",
        component: () => import("@/contents/workStationControl/workStationControl"),
      },
      // 参数配置
      {
        path: "parameterConfigOuter",
        name: "parameterConfigOuter",
        component: () => import("@/contents/parameterConfigOuter"),
      },
      // 调度参数配置
      {
        path: "schedulingParameterConfiguration",
        name: "schedulingParameterConfiguration",
        component: () => import("@/contents/schedulingParameterConfiguration"),
      },
      // 参数配置模板
      {
        path: "parameterConfigurationTemplate",
        name: "parameterConfigurationTemplate",
        component: () => import("@/contents/parameterConfigurationTemplate"),
      },

      // 工作站管理
      {
        path: "workstationEditIndex",
        name: "workstationEditIndex",
        component: () => import("@/gms/screens/workstations"),
      },
      // 区域管理
      {
        path: "/areaEditIndex",
        name: "areaEditIndex",
        // component: () => import("@/contents/manageArea/index"),
        component: () => import("@/gms/screens/manage-area/index.vue"),
      },
      // 区域管理
      {
        path: "/workstationConfigIndex",
        name: "workstationConfigIndex",
        component: () => import("@/contents/workstationConfigIndex"),
      },
      // 新增物理按钮日志 2020-12-4
      {
        path: "physicalButtonLog",
        name: "physicalButtonLog",
        component: () => import("@/contents/physicalButtonLog/physicalButtonLog"),
      },
      // 接口日志 InterfaceLog
      {
        path: "/interfaceLog",
        name: "interfaceLog",
        component: () => import("@/contents/interfaceLog/interfaceLog"),
      },
      // 物料管理
      {
        path: "/goods",
        name: "goods",
        component: () => import("@/contents/goodsManage"),
      },
      // 点位设备信息
      {
        path: "/nodeDeviceInfo",
        name: "nodeDeviceInfo",
        component: () => import("@/contents/node-device-info"),
      },
      // 登录日志
      {
        path: "/loginLog",
        name: "loginLog",
        component: () => import("@/contents/loginLog"),
      },
      // 设备关联信息
      {
        path: "/equipmentAssociatedInfo",
        name: "equipmentAssociatedInfo",
        component: () => import("@/contents/equipment-associated-info"),
      },
      // gms 容器
      {
        path: "/gmsContainer",
        name: "gmsContainer",
        component: () => import("@/gms/screens/container/index.vue"),
      },

      {
        path: "/gmsContainer/modelDetail",
        name: "modelDetail",
        component: () => import("@/gms/screens/container/container-type/detail.vue"),
      },

      // gms 容器管理
      {
        path: "/gmsContainerList",
        name: "gmsContainerList",
        component: () => import("@/gms/screens/container/container-list"),
      },
      // PDA设置
      {
        path: "/pdaConfig",
        name: "pdaConfig",
        component: () => import("@/contents/pdaConfig"),
      },
      // licenseEditIndex
      // ==================== iframe 迁移
      // 地图编辑
      {
        path: "mapManage",
        name: "mapManage",
        component: () => import("@/contents/rmsMapManage"),
      },
      // 地图监控
      {
        path: "admin",
        name: "admin",
        component: () => import("@/contents/rmsMonitor"),
      },
      // 机器人列表
      {
        path: "/robotList",
        name: "robotList",
        component: () => import("@/contents/rmsRobotList"),
      },
      //机器人参数配置
      {
        path: "/robotParamConfig",
        name: "robotParamConfig",
        component: () => import("@/contents/rmsRobotParamConfig"),
      },
      //充电站信息
      {
        path: "/chargeInfo",
        name: "chargeInfo",
        component: () => import("@/contents/rmsChargeInfo"),
      },
      //托盘位管理
      {
        path: "/palletPositionManage",
        name: "palletPositionManage",
        component: () => import("@/contents/rmsPalletPositionManage"),
      },
      // 机器人软件管理
      {
        path: "/robotVersionManage",
        name: "robotVersionManage",
        component: () => import("@/contents/rmsRobotVersionManage"),
      },
      // 机器人升级日志
      {
        path: "/robotUpgradeLog",
        name: "robotUpgradeLog",
        component: () => import("@/contents/rmsRobotUpgradeLog"),
      },
      // 对接模型
      {
        path: "/dockModel",
        name: "dockModel",
        component: () => import("@/contents/rmsDockModel"),
      },
      // 仓库管理----机器人型号管理
      {
        path: "/rmsRobotListMange",
        name: "rmsRobotListMange",
        component: () => import("@/contents/rmsRobotListMange"),
      },
      // 用户列表(janus)
      {
        path: "/userArk",
        name: "userArk",
        component: () => import("@/contents/janus-user"),
      },
      // 角色列表(janus)
      {
        path: "/roleArk",
        name: "roleArk",
        component: () => import("@/contents/janus-role"),
      },
      // 密码策略(janus)
      {
        path: "/passwordArk",
        name: "passwordArk",
        component: () => import("@/contents/janus-password-strategy"),
      },
      //dmp接入
      //设备模型
      // 机器人列表
      {
        path: "/dmpDeviceModel",
        name: "dmpDeviceModel",
        component: () => import("@/contents/dmp-device-model"),
      },
      //设备实例
      {
        path: "/dmpInstance",
        name: "dmpInstance",
        component: () => import("@/contents/dmp-instance"),
      },
      //模板实例
      {
        path: "/dmpTemplateInstance",
        name: "dmpTemplateInstance",
        component: () => import("@/contents/dmp-template-instance"),
      },
      //任务管理
      {
        path: "/dmpTaskManage",
        name: "dmpTaskManage",
        component: () => import("@/contents/dmp-task-manage"),
      },
      //应用维护
      {
        path: "/dmpApplyMaintenance",
        name: "dmpApplyMaintenance",
        component: () => import("@/contents/dmp-apply-maintenance"),
      },
      //心跳管理
      {
        path: "/dmpHeartbeat",
        name: "dmpHeartbeat",
        component: () => import("@/contents/dmp-heartbeat"),
      },
      // ====== iframe ==================
      // 交通管制监控页
      {
        path: "trafficControlNew",
        name: "trafficControlNew",
        component: () => import("@/contents/trafficControlNew"),
      },
      {
        path: "/transportDest",
        name: "transportDest",
        component: () => import("@/gms/screens/pda-config"),
      },
      // 呼叫器配置
      {
        path: "/callerConfiguration",
        name: "callerConfiguration",
        component: () => import("@/gms/screens/caller-configuration"),
      },
      // 传感器触发任务
      {
        path: "pointControl",
        name: "pointControl",
        component: () => import("@/contents/pointControl"),
      },
      // 复合机器人
      {
        path: "/hybridRobot/:type?",
        name: "hybridRobot",
        props: true,
        component: () => import("@/contents/hybridRobot"),
      },
      // 接口配置
      {
        path: "interfaceConfiguration",
        name: "interfaceConfiguration",
        component: () => import("@/contents/interfaceConfiguration"),
      },
      {
        path: "/systemConfig",
        name: "systemConfig",
        component: () => import("@/contents/systemConfig"),
      },
      {
        path: "/licenseEditIndex",
        name: "licenseEditIndex",
        meta: { requiresAuth: false },
        component: () => import("@/contents/auth-license"),
      },
      {
        path: "/changeViewBg",
        name: "changeViewBg",
        component: () => import("@/contents/changeViewBg"),
      },
    ],
  },
  {
    path: "/403",
    name: "403",
    meta: { requiresAuth: false },
    component: () => import("@/contents/403"),
  },
  {
    path: "/resetTask",
    name: "resetTask",
    meta: { requiresAuth: false },
    component: () => import("@/contents/resetTask"),
  },
  // 清除锁
  {
    path: "/clearLock",
    name: "clearLock",
    meta: { requiresAuth: false },
    component: () => import("@/contents/clearLock"),
  },
  {
    path: "/station/:id",
    name: "station",
    component: () => import("@/contents/workStation"),
  },
  {
    path: "/m",
    component: () => import("@/contents/mWorkStation/mWorkStation"),
    name: "m",
    children: [
      {
        path: "station/:id",
        name: "mStation",
        component: () => import("@/contents/mWorkStation/station/station"),
      },
      {
        path: "station/:id/:stopPoint",
        name: "mStationStopPoint",
        component: () => import("@/contents/mWorkStation/station/stopPointTask"),
      },
    ],
  },
  {
    path: "/triggerBtnInfo",
    name: "triggerBtnInfo",
    component: () => import("@/contents/triggerBtnInfo"),
  },

  // Auth Manage FE Start
  {
    path: "/changepw",
    name: "changepw",
    component: () => import("@/contents/auth-changepw"),
  },
  // Auth Manage FE End
];

/** 获取Router实例 */
const routerEntity = new Router({ routes });

if (isDev) {
  routerEntity.addRoute({
    path: "/demo",
    name: "demo",
    redirect: "/demo/position-select",
    meta: {
      requiresAuth: false,
    },
    component: () => import("@/gms/screens/demo"),
    children: [
      {
        path: "/demo/map-select",
        name: "positionSelect",
        meta: {
          requiresAuth: false,
        },
        component: () => import("@/gms/screens/demo/demo-map-select"),
      },
      {
        path: "/demo/position-select",
        name: "positionSelect",
        meta: {
          requiresAuth: false,
        },
        component: () => import("@/gms/screens/demo/demo-position-select"),
      },
      {
        path: "/demo/virtual-select",
        name: "virtualSelect",
        meta: {
          requiresAuth: false,
        },
        component: () => import("@/gms/screens/demo/demo-virtual-select"),
      },
      {
        path: "/demo/editable-list",
        name: "editableList",
        meta: {
          requiresAuth: false,
        },
        component: () => import("@/gms/screens/demo/demo-editable-list"),
      },
      {
        path: "/demo/edit-table",
        name: "editTable",
        meta: {
          requiresAuth: false,
        },
        component: () => import("@/gms/screens/demo/demo-edit-table"),
      },
    ],
  });
}

export default routerEntity;
