/**
 *   通用国际化字符集
 *   key 必须以 'geekplus.gms.client.commons.' 开头
 */
{
  "geekplus.gms.client.commons.btn.reset": "重置",
  "geekplus.gms.client.commons.btn.submit": "提交",
  "geekplus.gms.client.commons.btn.query": "查询",
  "geekplus.gms.client.commons.btn.save": "保存",
  "geekplus.gms.client.commons.btn.cancel": "取消",
  "geekplus.gms.client.commons.btn.cancelView": "取消查看",
  "geekplus.gms.client.commons.btn.saveAndNext": "保存并下一步",
  "geekplus.gms.client.commons.btn.edit": "编辑",
  "geekplus.gms.client.commons.btn.add": "新增",
  "geekplus.gms.client.commons.btn.refresh": "刷新",
  "geekplus.gms.client.commons.btn.cancelEditing": "取消编辑",
  "geekplus.gms.client.commons.btn.details": "详情",
  "geekplus.gms.client.commons.btn.delete": "删除",
  "geekplus.gms.client.commons.btn.batchDelete": "批量删除",
  "geekplus.gms.client.commons.btn.nextStep": "下一步",
  "geekplus.gms.client.commons.btn.prevStep": "上一步",
  "geekplus.gms.client.commons.btn.confirm": "确定",
  "geekplus.gms.client.commons.btn.cancelEdit": "取消编辑",
  "geekplus.gms.client.commons.btn.start": "开始",
  "geekplus.gms.client.commons.btn.pause": "暂停",
  "geekplus.gms.client.commons.btn.recover": "恢复",
  "geekplus.gms.client.commons.btn.moreAction": "筛选",
  "geekplus.gms.client.commons.btn.closeDialog": "关闭窗口",
  "geekplus.gms.client.commons.btn.close": "关闭",
  "geekplus.gms.client.commons.btn.publish": "发布",
  "geekplus.gms.client.commons.btn.update": "更新",
  "geekplus.gms.client.commons.btn.expand": "展开",
  "geekplus.gms.client.commons.btn.collapse": "收起",
  "geekplus.gms.client.commons.btn.forceCancel": "强制取消",
  "geekplus.gms.client.commons.btn.forceComplete": "强制完成",
  "geekplus.gms.client.commons.btn.giveUp": "放弃",
  "geekplus.gms.client.commons.btn.done": "完成",
  "geekplus.gms.client.commons.btn.confirmDone": "确认完成",
  "geekplus.gms.client.commons.btn.confirmResend": "确认重发",
  "geekplus.gms.client.commons.btn.completeAndRelease": "完成并解除锁定",
  "geekplus.gms.client.commons.btn.resendDeviceTask": "重发该设备指令",
  "geekplus.gms.client.commons.btn.manualCompleteDeviceTask": "跳过该设备指令",
  "geekplus.gms.client.commons.btn.manualComplete": "完成",
  "geekplus.gms.client.commons.btn.gotIt": "我知道了",
  "geekplus.gms.client.commons.btn.config": "配置",
  "geekplus.gms.client.commons.btn.copy": "复制",
  "geekplus.gms.client.commons.btn.copyLink": "复制地址",
  "geekplus.gms.client.commons.btn.visit": "访问",
  "geekplus.gms.client.commons.btn.tryLater": "稍后再试",
  "geekplus.gms.client.commons.btn.downloading": "正在下载",
  "geekplus.gms.client.commons.btn.completeTask": "确定完成任务",
  "geekplus.gms.client.commons.btn.suspend": "中止",
  "geekplus.gms.client.commons.btn.import": "导入",
  "geekplus.gms.client.commons.btn.export": "导出",
  "geekplus.gms.client.commons.btn.view": "查看",
  "geekplus.gms.client.commons.btn.logDownload": "日志下载",

  "geekplus.gms.client.commons.text.fileImport": "文件导入",
  "geekplus.gms.client.commons.text.downloadDataTpl": "请下载数据模板，按格式导入",
  "geekplus.gms.client.commons.text.importSuccessfully": "导入成功",

  "geekplus.gms.client.commons.label.templateType": "模板类型",

  "geekplus.gms.client.commons.confirmDelete": "确认删除",
  "geekplus.gms.client.commons.assignStrategy": "赋值策略",

  "geekplus.gms.client.commons.dialogTitle.confirm": "确认",

  "geekplus.gms.client.commons.cols.id": "ID",
  "geekplus.gms.client.commons.cols.actions": "操作",
  "geekplus.gms.client.commons.cols.updateTime": "编辑时间",
  "geekplus.gms.client.commons.cols.updateUser": "编辑人",
  "geekplus.gms.client.commons.cols.createBy": "创建人",
  "geekplus.gms.client.commons.cols.createTime": "创建时间",
  "geekplus.gms.client.commons.cols.lastModifiedTime": "最后编辑时间",
  "geekplus.gms.client.commons.cols.lastModifiedBy": "最后编辑人",
  "geekplus.gms.client.commons.cols.index": "序号",
  "geekplus.gms.client.commons.cols.size": "尺寸",

  "geekplus.gms.client.commons.tips.pleaseSelect": "请选择",
  "geekplus.gms.client.commons.tips.confirmDelete": "确定删除",
  "geekplus.gms.client.commons.tips.copied": "已复制",
  "geekplus.gms.client.commons.tips.copySucceed": "复制成功",
  "geekplus.gms.client.commons.tips.confirmDeleteMsg": "删除后，数据将无法恢复，确定删除吗？",
  "geekplus.gms.client.commons.tips.dataDeleteSuccess": "删除成功",
  "geekplus.gms.client.commons.tips.prompt": "提示",
  "geekplus.gms.client.commons.tips.operationSuccess": "操作成功",
  "geekplus.gms.client.commons.tips.cancelSuccess": "取消成功",
  "geekplus.gms.client.commons.tips.uploadA": "只能上传 excel 文件({fileTypes})，且不超过{sizeLimit}",
  "geekplus.gms.client.commons.tips.uploadSucceedA": "数据导入完成，共导入数据 {count} 条",
  "geekplus.gms.client.commons.tips.tempalteDownload": "模板下载",
  "geekplus.gms.client.commons.tips.dataSaveInProgress": "数据保存中...",

  "geekplus.gms.client.commons.unit.times": "次",

  // =========== AGV 常用动作 =========== //
  "geekplus.gms.client.commons.agvAction.load": "上料",
  "geekplus.gms.client.commons.agvAction.unload": "下料",
  "geekplus.gms.client.commons.agvAction.equipLoad": "设备上料",
  "geekplus.gms.client.commons.agvAction.equipLoadEmpty": "设备上空",
  "geekplus.gms.client.commons.agvAction.equipUnload": "设备下料",
  "geekplus.gms.client.commons.agvAction.equipUnloadEmpty": "设备下空",
  "geekplus.gms.client.commons.agvAction.equipUnloadFull": "设备下满",

  "geekplus.gms.client.commons.agvAction.equipLoadFull": "设备上满",
  "geekplus.gms.client.commons.agvAction.loadFullUnloadEmpty": "上满下空",
  "geekplus.gms.client.commons.agvAction.loadEmptyUnloadFull": "上空下满",
  "geekplus.gms.client.commons.agvAction.pick": "取",
  "geekplus.gms.client.commons.agvAction.pickEmpty": "取空",
  "geekplus.gms.client.commons.agvAction.pickMaterial": "取料",
  "geekplus.gms.client.commons.agvAction.pickMaterialFull": "取满料",
  "geekplus.gms.client.commons.agvAction.drop": "放",
  "geekplus.gms.client.commons.agvAction.dropEmpty": "放空",
  "geekplus.gms.client.commons.agvAction.dropMaterial": "放料",
  "geekplus.gms.client.commons.agvAction.dropMaterialFull": "放满料",

  // =========== AGV 常用状态 =========== //
  "geekplus.gms.client.commons.status.empty": "空",
  "geekplus.gms.client.commons.status.full": "满",
  "geekplus.gms.client.commons.status.loaded": "负载",
  "geekplus.gms.client.commons.status.noLoad": "空载",

  // =========== AGV 常用设备 =========== //
  "geekplus.gms.client.commons.equip.cacheRack": "缓存架",
  "geekplus.gms.client.commons.equip.machine": "机台",

  // =========== AGV 常用技术名词 =========== //
  "geekplus.gms.client.commons.name.robotId": "机器人ID",
  "geekplus.gms.client.commons.name.robotModel": "机器人型号",
  "geekplus.gms.client.commons.name.mapNodeCode": "点位编码",
  "geekplus.gms.client.commons.name.cargoCode": "货位编码",
  "geekplus.gms.client.commons.name.cargoOrderNo": "货位顺序号",
  "geekplus.gms.client.commons.name.shift": "偏移量",
  "geekplus.gms.client.commons.name.dockHeight": "对接高度",
  "geekplus.gms.client.commons.name.dropHeight": "放料高度",
  "geekplus.gms.client.commons.name.pickHeight": "取料高度",
  "geekplus.gms.client.commons.name.offsetX": "上装X轴偏移量",
  "geekplus.gms.client.commons.name.businessScene": "业务场景",
  "geekplus.gms.client.commons.name.axisA": "A轴",
  "geekplus.gms.client.commons.name.axisB": "B轴",
  "geekplus.gms.client.commons.name.axisAB": "AB轴",
  "geekplus.gms.client.commons.name.equipType": "设备类型",
  "geekplus.gms.client.commons.name.nodeCode": "节点编码",
  "geekplus.gms.client.commons.name.workflowTemplate": "流程模板",
  "geekplus.gms.client.commons.name.none": "无",

  "geekplus.gms.client.commons.title.language": "语言",
  "geekplus.gms.client.commons.title.edit": "编辑",
  "geekplus.gms.client.commons.title.detail": "详情",

  // ===========  常用校验信息 =========== //
  "geekplus.gms.client.commons.validator.invalidCode": "最多输入{num}个字符,支持字母、数字",
  "geekplus.gms.client.commons.validator.invalidName": "最多输入{num}个字符,支持汉字、字母和数字",
  "geekplus.gms.client.commons.validator.required": "请填写必填项",
  "geekplus.gms.client.commons.validator.pleaseInputNumbersFrom1To50": "请输入1-50之间的数字",
  "geekplus.gms.client.commons.validator.maximumChars": "请输入最多{num}个字符",
  "geekplus.gms.client.commons.validator.positiveInteger": "请输入正整数",
  "geekplus.gms.client.commons.validator.upTo4Bits": "最多只能输入4个字母",

  "geekplus.gms.client.commons.form.viewExample": "查看示例",
  "geekplus.gms.client.commons.emptyMsg": "暂无数据",
  "geekplus.gms.client.commons.please": "去",

  //
  // =========== 常量枚举 =========== //
  //
  // 工作站访问权限
  "geekplus.gms.client.commons.constants.workstationVisitAuth.all": "全部用户可访问",
  "geekplus.gms.client.commons.constants.workstationVisitAuth.some": "指定用户可访问",

  // 工作站功能配置生效方式
  "geekplus.gms.client.commons.constants.workstationSettingApplyMethod.all": "全部工作站生效",
  "geekplus.gms.client.commons.constants.workstationSettingApplyMethod.some": "指定工作站生效",

  // 工作站停靠点状态
  "geekplus.gms.client.commons.constants.workstationDockingPointStatus.free": "空闲",
  "geekplus.gms.client.commons.constants.workstationDockingPointStatus.arrived": "已到达",

  // 容器状态
  "geekplus.gms.client.commons.constants.containerStatus.idle": "空闲",
  "geekplus.gms.client.commons.constants.containerStatus.working": "工作中",
  "geekplus.gms.client.commons.constants.containerStatus.leave": "离场",
  "geekplus.gms.client.commons.constants.containerStatus.draft": "草稿",
  "geekplus.gms.client.commons.constants.containerStatus.locked": "锁定",

  // 容器类型
  "geekplus.gms.client.commons.constants.containerTypeShape.shelf": "货架",
  "geekplus.gms.client.commons.constants.containerTypeShape.materialVehicle": "料车、料柜",
  "geekplus.gms.client.commons.constants.containerTypeShape.materialBox": "料箱",
  "geekplus.gms.client.commons.constants.containerTypeShape.rollCintainer": "笼车",
  "geekplus.gms.client.commons.constants.containerTypeShape.movePallet": "搬运托盘(M系列)",
  "geekplus.gms.client.commons.constants.containerTypeShape.forkliftPallet": "叉车托盘(F系列)",
  "geekplus.gms.client.commons.constants.containerTypeShape.materialVehicleBox": "料车、料柜",

  // 容器编码生成规则
  "geekplus.gms.client.commons.constants.codeRule.default": "默认规则",
  "geekplus.gms.client.commons.constants.codeRule.customized": "自定义规则",

  // 容器空满状态
  "geekplus.gms.client.commons.constants.loadingStatus.loaded": "满（有货）",
  "geekplus.gms.client.commons.constants.loadingStatus.unloaded": "空",

  // 容器模型
  "geekplus.gms.client.commons.constants.containerModel.standard": "标准容器",
  "geekplus.gms.client.commons.constants.containerModel.not_standard": "异形容器",
  "geekplus.gms.client.commons.constants.containerModel.forklift": "叉车托盘模型",

  // 托盘规格
  "geekplus.gms.client.commons.constants.palletSpeciType.standard": "标准托盘-常规(推荐)",
  "geekplus.gms.client.commons.constants.palletSpeciType.special": "标准托盘-特殊",

  // 托盘结构
  "geekplus.gms.client.commons.constants.palletStructureType.single": "单孔托盘",
  "geekplus.gms.client.commons.constants.palletStructureType.double": "双孔托盘",

  // 货架元素
  "geekplus.gms.client.commons.constants.shelfElementType.shelfFace": "容器尺寸",
  "geekplus.gms.client.commons.constants.shelfElementType.shelfLeg": "容器腿",

  // 托盘材质
  "geekplus.gms.client.commons.constants.palletMaterialType.wood": "木质",
  "geekplus.gms.client.commons.constants.palletMaterialType.plastics": "塑料",
  "geekplus.gms.client.commons.constants.palletMaterialType.matal": "金属",
  "geekplus.gms.client.commons.constants.palletMaterialType.other": "其他",

  // 托盘颜色
  "geekplus.gms.client.commons.constants.palletColorType.woody": "木色",
  "geekplus.gms.client.commons.constants.palletColorType.blue": "蓝色",
  "geekplus.gms.client.commons.constants.palletColorType.black": "黑色/灰色",
  "geekplus.gms.client.commons.constants.palletColorType.other": "其他",

  // 支架形态
  "geekplus.gms.client.commons.constants.bracketShapeType.ground": "地面支架",

  // 支架模型
  "geekplus.gms.client.commons.constants.bracketModelType.standard": "标准支架",
  "geekplus.gms.client.commons.constants.bracketModelType.special": "异形支架",

  // 支架元素
  "geekplus.gms.client.commons.constants.bracketElementType.face": "支架尺寸",
  "geekplus.gms.client.commons.constants.bracketElementType.leg": "支架腿",

  // 流程状态
  "geekplus.gms.client.commons.constants.processStatus.inDesign": "设计中",
  "geekplus.gms.client.commons.constants.processStatus.published": "已发布",

  // 停靠点类型
  "geekplus.gms.client.commons.constants.stopPointTypes.common": "地图点位",
  "geekplus.gms.client.commons.constants.stopPointTypes.pallet": "托盘位",

  // GAV Class
  // - FORKLIFT 叉车 - ROLLER 辊筒 - CARRIER 潜伏 - TOP_MODULE 举升（复合）
  "geekplus.gms.client.commons.constants.agvClass.forklift": "叉车",
  "geekplus.gms.client.commons.constants.agvClass.roller": "辊筒",
  "geekplus.gms.client.commons.constants.agvClass.carrier": "潜伏",
  "geekplus.gms.client.commons.constants.agvClass.topModule": "复合",

  // 任务调试状态
  "geekplus.gms.client.commons.constants.testStatus.inProgress": "执行中",
  "geekplus.gms.client.commons.constants.testStatus.paused": "暂停中",
  "geekplus.gms.client.commons.constants.testStatus.done": "已完成",
  "geekplus.gms.client.commons.constants.testStatus.suspend": "已中止",
  "geekplus.gms.client.commons.constants.testStatus.waiting": "待执行",

  // 朝向
  "geekplus.gms.client.commons.constants.FBLROrientations.front": "F (前面)",
  "geekplus.gms.client.commons.constants.FBLROrientations.back": "B (后面)",
  "geekplus.gms.client.commons.constants.FBLROrientations.left": "L (左面)",
  "geekplus.gms.client.commons.constants.FBLROrientations.right": "R (右面)",

  // 任务发起策略
  "geekplus.gms.client.commons.constants.taskLaunchRule": "任务发起策略",
  "geekplus.gms.client.commons.constants.taskLaunchRule.none": "不判断是否空闲机器人",
  "geekplus.gms.client.commons.constants.taskLaunchRule.queue": "无可用空闲机器人任务排队",
  "geekplus.gms.client.commons.constants.taskLaunchRule.fail": "无可用空闲机器人任务失败",

  // 送货位置类型
  "geekplus.gms.client.screen.taskMonitoring.locationTypeEnum.start": "起点",
  "geekplus.gms.client.screen.taskMonitoring.locationTypeEnum.current": "当前位置",
  "geekplus.gms.client.screen.taskMonitoring.locationTypeEnum.location": "指定位置",
  "geekplus.gms.client.screen.taskMonitoring.rollerLocationTypeEnum.current": "当前位置（手动处理）",
  "geekplus.gms.client.screen.taskMonitoring.rollerLocationTypeEnum.location": "指定位置（手动处理）",
  "geekplus.gms.client.screen.taskMonitoring.rollerLocationTypeEnum.transportLine": "指定输送线（自动放货）",

  // 点位类型
  "geekplus.gms.client.screen.taskMonitoring.cellTypeEnum.station": "工作站",
  "geekplus.gms.client.screen.taskMonitoring.cellTypeEnum.map": "地图点位",
  "geekplus.gms.client.screen.taskMonitoring.cellTypeEnum.rollerStation": "辊筒工位",
  "geekplus.gms.client.screen.taskMonitoring.cellTypeEnum.pallet": "托盘位",
  "geekplus.gms.client.screen.taskMonitoring.cellTypeEnum.area": "区域",

  "geekplus.gms.client.commons.placeholder.search": "查询",
  "geekplus.gms.client.commons.placeholder.startTime": "开始时间",
  "geekplus.gms.client.commons.placeholder.endTime": "结束时间",

  "geekplus.gms.client.commons.toast.developing": "开发中，敬请期待！",
  "geekplus.gms.client.commons.clickDownload": "点击下载",

  // 冒号
  "geekplus.gms.client.commons.colon": "：",
  // 分号
  "geekplus.gms.client.commons.semicolon": "；",
  // dmp 动作类型  读/写
  "geekplus.gms.client.commons.constants.dmpActionTypeEnum.read": "读",
  "geekplus.gms.client.commons.constants.dmpActionTypeEnum.write": "写",

  // dmp 任务状态
  "geekplus.gms.client.commons.constants.dmpTaskStatusEnum.create": "创建",
  "geekplus.gms.client.commons.constants.dmpTaskStatusEnum.executing": "执行中",
  "geekplus.gms.client.commons.constants.dmpTaskStatusEnum.complete": "完成",
  "geekplus.gms.client.commons.constants.dmpTaskStatusEnum.cancel": "取消",
  "geekplus.gms.client.commons.constants.dmpTaskStatusEnum.error": "异常",

  // dmp 任务状态
  "geekplus.gms.client.commons.constants.dmpExceptionStatusEnum.normal": "正常",
  "geekplus.gms.client.commons.constants.dmpExceptionStatusEnum.offline": "设备离线",
  "geekplus.gms.client.commons.constants.dmpExceptionStatusEnum.timeout": "超时",
  "geekplus.gms.client.commons.constants.dmpExceptionStatusEnum.triggerBitFlag": "标志位触发",
  "geekplus.gms.client.commons.constants.dmpExceptionStatusEnum.systemError": "系统内部异常",

  // 流程模板类型
  "geekplus.gms.client.commons.constants.flowTemplateType.pToP": "点到点",
  "geekplus.gms.client.commons.constants.flowTemplateType.pToMP": "点到多点",
  "geekplus.gms.client.commons.constants.flowTemplateType.DP": "动态点",
  "geekplus.gms.client.commons.constants.flowTemplateType.DU": "动态单元",

  // 任务测试类型
  "geekplus.gms.client.commons.constants.taskTestType.route": "路线点位调试",
  "geekplus.gms.client.commons.constants.taskTestType.workflow": "流程调试",

  // 任务结束方式
  "geekplus.gms.client.commons.taskCloseModeEnum.normal": "正常完成",
  "geekplus.gms.client.commons.taskCloseModeEnum.gmsManual": "任务监控手动完成",
  "geekplus.gms.client.commons.taskCloseModeEnum.robotManual": "机器人端手动完成",
  "geekplus.gms.client.commons.taskCloseModeEnum.gmsCancel": "任务监控取消",
  "geekplus.gms.client.commons.taskCloseModeEnum.apiCancel": "上游接口取消",
  "geekplus.gms.client.commons.taskCloseModeEnum.removeRobot": "移除机器人取消",
  "geekplus.gms.client.commons.taskCloseModeEnum.robotCancel": "机器人端取消",
  "geekplus.gms.client.commons.taskCloseModeEnum.apiManual": "上游接口完成",
  "geekplus.gms.client.commons.taskCloseModeEnum.pdaManual": "PDA端手动完成",
  "geekplus.gms.client.commons.taskCloseModeEnum.pdaCancel": "PDA端取消",
  "geekplus.gms.client.commons.taskCloseModeEnum.stationCancel": "工作站取消",
  "geekplus.gms.client.commons.taskCloseModeEnum.stationManual": "工作站手动完成",
  "geekplus.gms.client.commons.taskCloseModeEnum.scanError": "扫码异常取消",

  // 执行过程点位阶段
  "geekplus.gms.client.commons.locationPhaseEnum.begin": "开始",
  "geekplus.gms.client.commons.locationPhaseEnum.startLocation": "起点",
  "geekplus.gms.client.commons.locationPhaseEnum.midLocation": "中间点",
  "geekplus.gms.client.commons.locationPhaseEnum.endLocation": "终点",
  "geekplus.gms.client.commons.locationPhaseEnum.finish": " 结束",

  // 机器人指令
  "geekplus.gms.client.commons.interactionCommands.goReceive": "取货",
  "geekplus.gms.client.commons.interactionCommands.goDrop": "放货",
  "geekplus.gms.client.commons.interactionCommands.goShelf": "偏移",
  "geekplus.gms.client.commons.interactionCommands.goTurn": "旋转",

  // 任务异常状态
  "geekplus.gms.client.commons.exceptionState.cancelExecuting": "取消中",
  "geekplus.gms.client.commons.exceptionState.cancelFinished": "已取消",
  "geekplus.gms.client.commons.exceptionState.cancelFailed": "取消失败",
  "geekplus.gms.client.commons.exceptionState.cancelNewDestExecuting": "执行取消后新目的地任务",
  "geekplus.gms.client.commons.exceptionState.cancelManualWaiting": "等待人工处理",
  "geekplus.gms.client.commons.exceptionState.cancelCompleted": "取消完成",
  "geekplus.gms.client.commons.exceptionState.cancelForceCompleted": "强制取消",
  "geekplus.gms.client.commons.exceptionState.manualCompeteExecuting": "手动完成执行中",
  "geekplus.gms.client.commons.exceptionState.manualCompeteFailed": "手动完成失败",
  "geekplus.gms.client.commons.exceptionState.manualManualWaiting": "手动完成等待人工处理",
  "geekplus.gms.client.commons.exceptionState.manualCompleted": "手动完成",
  "geekplus.gms.client.commons.exceptionState.manualForcedCompleted": "强制完成",

  // 内部任务类型
  "geekplus.gms.client.commons.internalTask.triggerTask": "系统触发器",
  "geekplus.gms.client.commons.internalTask.workflowInitiation": "主子流程",
  "geekplus.gms.client.commons.internalTask.nodeRelationTask": "点位对照任务",
  "geekplus.gms.client.commons.internalTask.autoMaticReturnEmptyContainerTask": "自动返回空托盘任务",
  "geekplus.gms.client.commons.internalTask.waveTask": "波次任务",

  // 上装滚动标志
  "geekplus.gms.client.commons.constants.rollerFlag.yes": "滚动",
  "geekplus.gms.client.commons.constants.rollerFlag.no": "不滚动",

  // 复合机器人子类型
  "geekplus.gms.client.commons.constants.topModuleType.singleLift": "单举升",
  "geekplus.gms.client.commons.constants.topModuleType.doubleLift": "双举升",
  "geekplus.gms.client.commons.constants.topModuleType.singleSpiralArm": "单悬臂",
}
