<template>
  <div :key="uniqueId()" :style="boxStyles" :class="hoverClass">
    {{ value }}
  </div>
</template>
<script>
import { uniqueId } from "lodash";
import { defineComponent, computed, useCssModule } from "vue";
export default defineComponent({
  name: "HighLightBox",
  props: {
    value: { type: Number, default: null },
    isActive: { type: Boolean, default: false },
    styles: { type: Object, default: () => ({}) },
  },
  setup(props) {
    const boxStyles = computed(() => ({
      position: "absolute",
      fontSize: "12px",
      color: "red",
      cursor: "pointer",
      ...props.styles,
      [props.styles.borderDirection]: props.isActive ? "4px solid red" : "",
    }));

    const cn = useCssModule("cn");
    const hoverClass = computed(() => (props.styles.borderDirection === "borderTop" ? cn.top_hover : cn.bottom_hover));

    return { props, boxStyles, uniqueId, hoverClass };
  },
});
</script>
<style lang="scss" module="cn">
.top_hover {
  border-top: 4px solid transparent;
  &:hover {
    border-top: 4px solid red;
  }
}

.bottom_hover {
  border-bottom: 4px solid transparent;
  &:hover {
    border-bottom: 4px solid red;
  }
}
</style>
