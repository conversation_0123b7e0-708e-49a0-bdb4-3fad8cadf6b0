<template>
  <div>
    <!-- 按钮编号和触发按钮 -->
    <div v-if="mode === 'view'" class="button-wrapper tw-mt-2.5">
      <div class="wrapper-content">
        <div class="btn-label">{{ $t("geekplus.gms.client.screen.callerConfiguration button number") }}</div>
        <div class="btn-number">{{ button.buttonCode }}</div>
      </div>
      <gp-button
        class="tw-w-20 tw-h-8"
        :loading="triggerLoading"
        type="primary"
        :disabled="!button.state"
        @click="handleTriggerClick"
      >
        {{ $t("geekplus.gms.client.screen.callerConfiguration.trigger") }}
      </gp-button>
    </div>
    <!-- 按钮启用 -->
    <form-item class="tw-mb-3.5" v-bind="button.form.buttonstart" label-position="top" />
    <!-- 按钮功能 -->
    <form-item
      :class="button.state ? '' : 'classrequire'"
      class="feature tw-mb-3.5"
      v-bind="button.form.operationCommand"
    />

    <!-- 流程配置 -->
    <form-item class="tw-mb-3.5" v-bind="button.form.workflowConfigId" />

    <!-- 触发位置2 -->
    <template v-if="button.triggerflag && button.buttonselectvalue == '10'">
      <form-item class="tw-mb-3.5" v-bind="button.form.triggerposition" />
    </template>

    <!-- 触发位置 -->
    <div v-show="showPositionSelect(button)" class="triggerLocation">
      <label class="code_label">{{ positionLabel(button) }}:</label>
      <PositionSelect
        v-show="mode === 'edit' || mode === 'add'"
        :class="button.errorflag ? 'editclass' : ''"
        :value="getButtonPositionStateValue(button)"
        mode="edit"
        :multiple="false"
        :groups="grpupsValue(button)"
        :handle-change="(codeValue, codeItem) => handleChangeNodeCode(codeValue, codeItem)"
        :overlay-z-index="3000"
      />
      <div v-if="button.errorflag" class="error_msg">{{ button.errormsg }}</div>

      <PositionSelect
        v-show="mode === 'view'"
        class="viewclass"
        :value="getButtonPositionStateValue(button)"
        mode="view"
        :multiple="false"
        :groups="grpupsValue(button)"
        :handle-change="(codeValue, codeItem) => handleChangeNodeCode(codeValue, codeItem)"
        :overlay-z-index="3000"
      />
    </div>

    <!-- 容器类型容器角度 -->
    <div style="display: flex; gap: 20px">
      <form-item class="tw-mb-3.5" v-bind="button.form.shelfCategoryId" label-position="top" />
      <form-item class="tw-mb-3.5" v-bind="button.form.shelfAngle" label-position="top" />
    </div>
  </div>
</template>

<script>
import { defineComponent } from "vue";
import { FormItem } from "@srpe-fe/uic-el-form-wrapper-vue2";
import PositionSelect from "../position-select-customized.vue";
import { BUTTON_FUNCTION_CODES, WORKFLOW_TYPES } from "../common.js";
import { useI18n } from "@/hooks";

export default defineComponent({
  name: "ButtonContent",
  components: { FormItem, PositionSelect },
  props: {
    button: {
      type: Object,
      required: true,
    },
    mode: {
      type: String,
      default: "add",
    },
    triggerLoading: {
      type: Boolean,
      default: false,
    },
    btnIndex: {
      type: Number,
      default: 0,
    },
    deviceId: {
      type: String,
      default: "",
    },
  },
  emits: ["trigger-button", "change-node-code"],
  setup(props, { emit }) {
    const t = useI18n();

    // 是否显示触发位置
    const showPositionSelect = (btn) => {
      const f = btn.buttonselectvalue;
      return btn.state && f && ["40", "44", "43"].includes(f);
    };

    // 触发位置的标题
    const positionLabel = (btn) => {
      const f = btn.buttonselectvalue;
      if (f === BUTTON_FUNCTION_CODES.CONTINUE_WORKFLOW)
        //流程继续用的是这个标题
        return t("geekplus.gms.client.screen.callerConfiguration.triggerLocation");
      if (f === BUTTON_FUNCTION_CODES.CONTAINER_ENTRY)
        //容器入场用的是这个标题
        return t("geekplus.gms.client.screen.callerConfiguration.entryLocation");
      // 容器离场用的是这个标题
      if (f === BUTTON_FUNCTION_CODES.CONTAINER_EXIT)
        return t("geekplus.gms.client.screen.callerConfiguration.exitLocation");
      return "";
    };

    // 获取渲染的值
    const getButtonPositionStateValue = (button) => {

      if (button.buttonselectvalue === BUTTON_FUNCTION_CODES.CONTINUE_WORKFLOW) {
        return button.positionState0;
      } else if (button.buttonselectvalue === BUTTON_FUNCTION_CODES.CONTAINER_ENTRY) {
        return button.positionStatein;
      } else if (button.buttonselectvalue === BUTTON_FUNCTION_CODES.CONTAINER_EXIT) {
        return button.positionStateleave;
      } else {
        return [];
      }
    };

    // 获取groups 的值
    const grpupsValue = (item) => {
      if (item.buttonselectvalue === BUTTON_FUNCTION_CODES.CONTINUE_WORKFLOW) {
        return [WORKFLOW_TYPES.GENERAL_POINT, WORKFLOW_TYPES.PALLET_POINT];
      } else if (item.buttonselectvalue === BUTTON_FUNCTION_CODES.CONTAINER_ENTRY) {
        return [WORKFLOW_TYPES.GENERAL_POINT, WORKFLOW_TYPES.PALLET_POINT];
      } else if (item.buttonselectvalue === BUTTON_FUNCTION_CODES.CONTAINER_EXIT) {
        return [WORKFLOW_TYPES.GENERAL_POINT, WORKFLOW_TYPES.PALLET_POINT, WORKFLOW_TYPES.AREA];
      } else {
        return [];
      }
    };

    const handleTriggerClick = () => {
      emit("trigger-button", { ...props.button, deviceId: props.deviceId });
    };

    const handleChangeNodeCode = (codeValue, codeItem) => {
      emit("change-node-code", { btnIndex: props.btnIndex, codeValue, codeItem, button: props.button });
    };

    return {
      t,
      showPositionSelect,
      positionLabel,
      getButtonPositionStateValue,
      grpupsValue,
      handleTriggerClick,
      handleChangeNodeCode,
    };
  },
});
</script>

<style lang="scss" scoped>
.error_msg {
  font-size: 10px;
  color: gms.$colorDanger;
  margin-left: 6px;
}

.button-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.code_label {
  width: 120px;
  &:before {
    color: gms.$colorDanger;
    content: "*";
    margin-right: 4px;
  }
}
</style>
