<template>
  <div class="tw-flex tw-flex-col tw-bg-white tw-p-4" :class="cn.page_root">
    <PageHeaderWithAction
      :title="$t('geekplus.gms.client.screen.container.form.containerTitle')"
      :subtitle="$t('geekplus.gms.client.screen.container.form.containerSubTitle')"
    />

    <gp-tabs v-model="state.currentActiveName" @tab-click="handleTabChange">
      <gp-tab-pane :label="$t('geekplus.gms.client.screen.container.form.containerList')" name="ContainerList" />
      <!-- <gp-tab-pane :label="$t('geekplus.gms.client.screen.container.columns.containerType')" name="ContainerModel" />
      <gp-tab-pane :label="$t('geekplus.gms.client.screen.container.tab.bracketModel')" name="BracketModel" /> -->
    </gp-tabs>
    <component :is="state.currentActiveName" ref="pageRef" />
  </div>
</template>
<script>
import { defineComponent, reactive, onMounted, ref, nextTick, onUnmounted } from "vue";
import { PageHeaderWithAction } from "gms-components/page-header";
import eventEmitter from "@/common/eventEmitter";
import ContainerList from "./container-list";
import ContainerModel from "./container-type/list.vue";
import BracketModel from "./bracket";
import EmptyPage from "gms-components/empty-msg";

export default defineComponent({
  name: "ContainerWrapper",
  components: { ContainerList, ContainerModel, EmptyPage, PageHeaderWithAction, BracketModel },
  props: {
    activeName: { type: String, default: null },
  },
  setup(props, { emit }) {
    const pageRef = ref(null);

    const state = reactive({
      currentActiveName: "ContainerList",
    });

    const handleTabChange = () => {
      emit("update:activeName", state.currentActiveName);
    };

    const updateActiveName = ({ activeName, mode }) => {
      state.currentActiveName = activeName;
      if (activeName === "ContainerModel") {
        if (!pageRef.value) return;
        nextTick(() => {
          mode === "add" && pageRef?.value?.handleAdd();
        });
      }
    };

    onMounted(() => {
      eventEmitter.on("container:updateActiveName", updateActiveName);

      if (props.activeName) {
        state.currentActiveName = props.activeName;
      }
    });

    onUnmounted(() => {
      eventEmitter.removeAllListeners("container:updateActiveName");
    });

    return { pageRef, state, handleTabChange };
  },
});
</script>

<style lang="scss" module="cn">
.page_root {
  background-color: #fff;
  border-radius: 6px;
  -webkit-box-shadow: 0 6px 10px 0 rgba(0, 0, 0, 0.08);
  box-shadow: 0 6px 10px 0 rgba(0, 0, 0, 0.08);
  box-sizing: border-box;
  position: relative;
}
</style>
