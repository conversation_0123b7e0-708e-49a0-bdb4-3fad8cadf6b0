<template>
  <div class="tw-relative tw-h-full tw-w-full tw-flex tw-flex-col tw-justify-around tw-items-center tw-gap-4 tw-pl-20">
    <v-stage ref="stage" :config="stageConfig">
      <v-layer ref="layer">
        <v-line :config="leftLineConfig" />
        <v-line :config="rightLineConfig" />
        <template v-for="model in props.formValues.modelInfo.filter((v) => v.type === bracketElementType.LEG)">
          <v-rect
            :key="model.key"
            :config="{
              ...transformParam(model, $props.active, ratio),
              draggable: false,
              strokeScaleEnabled: false,
            }"
            @tap="updateSelectedShape(model.key)"
            @click="updateSelectedShape(model.key)"
          />
          <v-rect :key="`${model.key}Point`" :config="getLegPointConfig(model, ratio)"></v-rect>
          <!-- <v-text :key="`${model.key}NameText`" :config="getLegNameConfig(model, index, ratio)" /> -->
          <v-text :key="`${model.key}LengthText`" :config="getLengthTextConfig(model, ratio)" />
          <v-text :key="`${model.key}WidthText`" :config="getWidthTextConfig(model, ratio)" />
          <v-line :key="`${model.key}LengthLine`" :config="getLegLengthLine(model, ratio)" />
          <v-line :key="`${model.key}widthLine`" :config="getLegWidthLine(model, ratio)" />
          <v-line :key="`${model.key}xToOriginLine`" :config="getXToOriginLine(model, ratio)" />
          <v-line :key="`${model.key}yToOriginLine`" :config="getYToOriginLine(model, ratio)" />
          <v-text :key="`${model.key}xToOriginText`" :config="getXToOriginText(model, ratio)" />
          <v-text :key="`${model.key}YToOriginText`" :config="getYToOriginText(model, ratio)" />
        </template>

        <v-arrow :config="xArrowConfig" />
        <v-arrow :config="yArrowConfig" />
        <v-rect :config="originPointConfig"></v-rect>
        <v-text :config="textConfig" />
        <v-text :config="xTextConfig" />
        <v-text :config="yTextConfig" />
      </v-layer>
    </v-stage>
    <div class="tw-text-center">
      <img :src="overlookRobotImg" class="tw-w-14" alt="" />
      <p class="tw-text-sm">
        {{ $t("geekplus.gms.client.screen.container.form.bracket.shelfOverLookDiagram") }}
      </p>
      <p class="tw-sub-title">{{ $t("geekplus.gms.client.screen.container.form.measureYAxisMethod") }}</p>
    </div>
    <div class="tw-absolute tw-top-4 tw-left-4 tw-text-center">
      <h3 class="tw-mb-2 tw-title">
        {{ $t("geekplus.gms.client.screen.container.form.bracket.specialShelfModelConfig") }}
      </h3>
      <img :src="specialShelfImg" class="2xl:tw-w-72 tw-w-40" alt="" />
      <p class="tw-text-sm tw-mt-4">
        {{ $t("geekplus.gms.client.screen.container.form.bracket.shelf3DDiagram") }}
      </p>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, watchEffect } from "vue";
import { bracketElementType } from "gms-constants";
import getContainerImage from "@/utils/containerImages";
const specialShelfImg = getContainerImage("special-bracket.svg");
import overlookRobotImg from "@/gms/assets/images/container/overlook-robot.svg";
import useKonva, { arrowCommonConfig, originPoint, containerWidth, scale } from "@/gms/screens/container/useKonva";

/* const clientH = document.body.clientHeight;
const lineOffset1 = clientH < 650 ? 20 : clientH < 800 ? 70 : clientH < 1000 ? 100 : 200;
const lineOffset2 = clientH < 650 ? 50 : clientH < 800 ? 110 : clientH < 1000 ? 150 : 250;
const lineOffset3 = clientH < 650 ? 50 : clientH < 800 ? 70 : clientH < 1000 ? 150 : 400;

const lineP1 = ((containerWidth - lineOffset1) * scale) / 2;
const lineP2 = ((containerWidth - lineOffset2) * scale) / 2;
const lineP3 = (containerWidth * scale - lineOffset3) / 2;
 */
const lineOffset1 = 50;
const lineOffset2 = 100;

const lineP1 = ((containerWidth - lineOffset1) * scale) / 2;
const lineP2 = ((containerWidth - lineOffset2) * scale) / 2;
const lineP3 = (containerWidth * scale) / 2;

export default defineComponent({
  name: "NoStandardAnnotation",
  components: {},
  props: {
    formValues: { type: Object, default: () => ({}) },
    active: { type: String, default: "" },
  },
  emits: ["update:active"],
  setup(props, { emit }) {
    const layer = ref(null);
    const leftLineConfig = ref({
      ...originPoint,
      points: [-lineP1, lineP3, -lineP2, lineP1, -lineP2, -lineP1, -lineP1, -lineP3],
      ...arrowCommonConfig,
      stroke: "gray",
      strokeWidth: 6,
    });
    const rightLineConfig = ref({
      ...originPoint,
      points: [lineP1, lineP3, lineP2, lineP1, lineP2, -lineP1, lineP1, -lineP3],
      ...arrowCommonConfig,
      stroke: "gray",
      strokeWidth: 6,
    });

    const updateSelectedShape = (key) => {
      emit("update:active", key);
    };

    const ratio = ref(1);
    watchEffect(() => {
      const faceModel = props.formValues.modelInfo.find((v) => v.type === bracketElementType.FACE) ?? {};
      ratio.value = faceModel.length / (lineP2 * 2);
    });

    return {
      props,
      layer,
      specialShelfImg,
      overlookRobotImg,
      leftLineConfig,
      rightLineConfig,
      bracketElementType,
      updateSelectedShape,
      ratio,
      ...useKonva(),
    };
  },
});
</script>
