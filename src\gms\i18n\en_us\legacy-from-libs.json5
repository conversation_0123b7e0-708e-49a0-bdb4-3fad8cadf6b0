{"libsKey.key1": "Role name", "libsKey.key2": "Please enter", "libsKey.key3": "Description", "libsKey.key4": "Permission Type", "libsKey.key5": "Please choose", "libsKey.key6": "Page Permission", "libsKey.key7": "Data Permission", "libsKey.key8": "Grant sub-system to", "libsKey.key9": "<PERSON>", "libsKey.key10": "Associated user", "libsKey.key11": "Associated user", "libsKey.key12": "Keyword to filter", "libsKey.key13": "Cancel", "libsKey.key14": "Determine", "libsKey.key15": "Tips", "libsKey.key16": "Quantity:", "libsKey.key17": "No SKU information", "libsKey.key18": "Large package", "libsKey.key19": "Medium package", "libsKey.key20": "Small package", "libsKey.key21": "Packing", "libsKey.key23": "Barcode", "libsKey.key24": "Please scan barcode", "libsKey.key25": "Please enter the bar code", "libsKey.key26": "Submit", "libsKey.key27": "Input at least one character", "libsKey.key28": "Query criteria", "libsKey.key29": "Reset", "libsKey.key30": "Unfold", "libsKey.key31": "Fold", "libsKey.key32": "Query", "libsKey.key33": "Network connection timeout", "libsKey.key34": "System exception. Please contact administrator", "libsKey.key35": "User name", "libsKey.key36": "Enter user name", "libsKey.key37": "Password", "libsKey.key38": "Please input a password", "libsKey.key39": "Sign in", "libsKey.key40": "Password", "libsKey.key41": "Workstation type", "libsKey.key42": "Please select workstation type", "libsKey.key43": "Exit system", "libsKey.key44": "Switch user", "libsKey.key45": "Please complete the unfinished task on the workstation and exit", "libsKey.key46": "Query result", "libsKey.key47": "More", "libsKey.key48": "Select All", "libsKey.key49": "Select {num} items", "libsKey.key50": "Disable", "libsKey.key51": "Enable", "libsKey.key52": "No matched item", "libsKey.key53": "No matched value", "libsKey.key54": "Please check out", "libsKey.key55": "Invalid code", "libsKey.key56": "Should be less than {num} in length", "libsKey.key57": "Special characters entered", "libsKey.key58": "Please enter a positive integer", "libsKey.key59": "Please limit your input to {num} decimal places", "libsKey.key60": "Only English letters", "libsKey.key61": "Invalid email address", "libsKey.key62": "Invalid fax number", "libsKey.key63": "Invalid telephone number", "libsKey.key64": "Execution order is required", "libsKey.key65": "Execution order must be positive integers", "libsKey.key66": "Only English letters or numbers", "libsKey.key67": "Invalid url. Please start with \"http://\"", "libsKey.loading": "Loading", "libsKey.selectNone": "Clear", "libsKey.selectAll": "All", "libsKey.rownum": "No", "libsKey.emptyText": "No Data", "libsKey.key258": "Switching type", "libsKey.key22": "Click to manually enter the barcode."}