{"geekplus.gms.client.screen.robotHybird.columns.dockingHeight": "Docking height", "geekplus.gms.client.screen.robotHybird.columns.dropHeight": "Placement height", "geekplus.gms.client.screen.robotHybird.columns.pickHeight": "Fetching height", "geekplus.gms.client.screen.robotHybird.columns.deviceType": "Equipment type", "geekplus.gms.client.screen.robotHybird.columns.name": "Name", "geekplus.gms.client.screen.robotHybird.dialogAdd.heightTip.th.1_1": "Parameter name", "geekplus.gms.client.screen.robotHybird.dialogAdd.heightTip.th.1_2": "Low-precision lifting robot", "geekplus.gms.client.screen.robotHybird.dialogAdd.heightTip.th.1_3": "Cantilever robot", "geekplus.gms.client.screen.robotHybird.dialogAdd.heightTip.th.2_1": "Machine", "geekplus.gms.client.screen.robotHybird.dialogAdd.heightTip.th.2_2": "Buffer shelf", "geekplus.gms.client.screen.robotHybird.dialogAdd.heightTip.th.2_3": "Machine", "geekplus.gms.client.screen.robotHybird.dialogAdd.heightTip.th.2_4": "Buffer shelf", "geekplus.gms.client.screen.robotHybird.dialogAdd.heightTip.td.1_1": "Parameter 11", "geekplus.gms.client.screen.robotHybird.dialogAdd.heightTip.td.1_2": "Docking height", "geekplus.gms.client.screen.robotHybird.dialogAdd.heightTip.td.1_3": "Docking height", "geekplus.gms.client.screen.robotHybird.dialogAdd.heightTip.td.1_4": "Height of the machine shaft", "geekplus.gms.client.screen.robotHybird.dialogAdd.heightTip.td.1_5": "Height of cross mark", "geekplus.gms.client.screen.robotHybird.dialogAdd.heightTip.td.2_1": "Parameter 12", "geekplus.gms.client.screen.robotHybird.dialogAdd.heightTip.td.2_2": "Placement height", "geekplus.gms.client.screen.robotHybird.dialogAdd.heightTip.td.2_3": "Placement height", "geekplus.gms.client.screen.robotHybird.dialogAdd.heightTip.td.2_4": "Distance from shaft end to outer frame of machine", "geekplus.gms.client.screen.robotHybird.dialogAdd.heightTip.td.2_5": "Height of material center", "geekplus.gms.client.screen.robotHybird.dialogAdd.heightTip.td.3_1": "Parameter 13", "geekplus.gms.client.screen.robotHybird.dialogAdd.heightTip.td.3_2": "Fetching height", "geekplus.gms.client.screen.robotHybird.dialogAdd.heightTip.td.3_3": "Fetching height", "geekplus.gms.client.screen.robotHybird.dialogAdd.heightTip.td.3_4": "Pushing distance to machine", "geekplus.gms.client.screen.robotHybird.dialogAdd.heightTip.comments": "Note: The above three heights correspond to extra parameters 11, 12, and 13 in the interface file, which shall be set according to the machine or buffer shelf."}