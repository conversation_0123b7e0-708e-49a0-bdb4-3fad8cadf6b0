<template>
  <div class="tw-flex tw-flex-col tw-gap-5" :class="cn.root">
    <h3 class="tw-title">
      {{ t("geekplus.gms.client.screen.container.form.stepOne") }}:
      {{ t("geekplus.gms.client.screen.container.form.pleaseSelectContainerShape") }}
    </h3>
    <section class="tw-pb-4 tw-pt-2">
      <div class="tw-grid tw-grid-cols-5 tw-w-full tw-gap-x-2 tw-gap-y-6">
        <gp-card
          v-for="(item, i) in state.imgList"
          :key="i"
          shadow="hover"
          :body-style="{ padding: '0px' }"
          class="tw-relative tw-p-2 tw-h-56 tw-border tw-border-solid tw-rounded hover:tw-bg-gray-50"
          :class="[$props.type === item.type ? 'tw-border-blue-300' : 'tw-border-grey-n3']"
          @click.native="handleSelectShape(item.type)"
        >
          <MCheckbox
            :value="$props.type === item.type"
            :name="String(item.type)"
            :true-label="item.type"
            false-label=""
            class="tw-w-full tw-text-center"
            :label="item.type"
            :disabled="$props.mode !== 'add'"
          >
            <img :src="item.url" alt="container thumb" class="tw-h-44 tw-w-44 tw-pt-4 tw-pb-2 tw-px-4" />
            <h3 class="tw-text-sm">{{ item.name }}</h3>
          </MCheckbox>
        </gp-card>
      </div>
    </section>
  </div>
</template>

<script>
import { defineComponent, onMounted, reactive } from "vue";
import shelfImg from "@/gms/assets/images/container/solid-shelf.svg";
import materialBoxImg from "@/gms/assets/images/container/solid-material-box.svg";
import rollCarImg from "@/gms/assets/images/container/solid-roll-car.svg";
import movePalletImg from "@/gms/assets/images/container/solid-move-pallet.svg";
import forkPalletImg from "@/gms/assets/images/container/solid-double-pallet.svg";
import { useI18n } from "gms-hooks";
import { containerTypeShape } from "gms-constants";
import MCheckbox from "@/gms/screens/container/components/m-checkbox.vue";

export default defineComponent({
  name: "SelectShape",
  components: { MCheckbox },
  props: { type: { type: String, default: "" }, mode: { type: String, default: "add" } },
  emit: ["update:step", "update:type"],
  setup(props, { emit }) {
    const t = useI18n();
    const state = reactive({
      activeStep: 1,
      imgList: [],
    });

    onMounted(() => {
      state.imgList = [
        {
          name: t("geekplus.gms.client.commons.constants.containerTypeShape.shelf"),
          url: shelfImg,
          type: containerTypeShape.PALLET_RACKING,
        },
        {
          name: t("geekplus.gms.client.commons.constants.containerTypeShape.materialVehicleBox"),
          url: materialBoxImg,
          type: containerTypeShape.CONTAINER_BIN,
        },
        {
          name: t("geekplus.gms.client.commons.constants.containerTypeShape.rollCintainer"),
          url: rollCarImg,
          type: containerTypeShape.TROLLEY,
        },
        {
          name: t("geekplus.gms.client.commons.constants.containerTypeShape.movePallet"),
          url: movePalletImg,
          type: containerTypeShape.PALLET,
        },
        {
          name: t("geekplus.gms.client.commons.constants.containerTypeShape.forkliftPallet"),
          url: forkPalletImg,
          type: containerTypeShape.FORKLIFT_PALLET,
        },
      ];
    });

    const handleSelectShape = (type) => {
      if (props.mode !== "add") return;
      emit("update:type", type);
    };

    return { state, handleSelectShape, containerTypeShape, t };
  },
});
</script>

<style lang="scss" module="cn">
.root {
  :global(.gp-checkbox__label) {
    padding-left: 0;
  }
}
</style>
