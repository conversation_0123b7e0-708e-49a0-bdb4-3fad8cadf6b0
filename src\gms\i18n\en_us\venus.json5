{"lang.venus.web.inner.outterCall": "Outbound call", "lang.venus.web.inner.modelSwitch": "Mode switch", "lang.venus.web.inner.conveyerMove": "Conveyor line movement", "lang.venus.web.inner.openBackDoor": "Open the back door", "lang.venus.web.inner.turnRed": "Turn to red light", "lang.venus.web.inner.turnYellow": "Turn to yellow light", "lang.venus.web.inner.leave": "Leave", "lang.venus.web.inner.removeTask": "Remove the task", "lang.venus.web.inner.openFrontDoor": "Open the front door", "lang.venus.web.inner.liftMove": "Hoist movement", "lang.venus.web.inner.arriveStation": "Arrive at the workstation", "lang.venus.web.inner.robotStopArea": "Stop in the robot area", "lang.venus.web.inner.closeDoor": "Close the door.", "lang.venus.web.inner.leaveStation": "Leave the workstation", "lang.venus.web.inner.robotStop": "All robots stop", "lang.venus.web.inner.closeFrontDoor": "Close the front door", "lang.venus.web.inner.shelfHighlightResult": "Results of protrusion from the shelf", "lang.venus.web.inner.getAndSend": "Fork and deliver goods to the position", "lang.venus.web.inner.turnGreen": "Turn to green light", "lang.venus.web.inner.innerCall": "Inbound call", "lang.venus.web.inner.tricolorLightsRing": "Tricolor light ring", "lang.venus.web.inner.closeBackDoor": "Close the back door", "lang.venus.web.inner.poppickCrossMiddle": "PopPick forks and delivers goods to the position (over the center line)", "lang.venus.web.inner.openDoor": "Open the door", "lang.venus.web.inner.resetArea": "Area reset", "lang.venus.web.inner.speedAreaRecovery": "Restore region speed limit", "lang.venus.web.inner.safetyDoorStopArea": "The safety door triggers emergency stop in the area.", "lang.venus.web.inner.greenTrigger": "Green light reporting", "lang.venus.web.inner.fireStop": "Emergency stop due to fire alarm", "lang.venus.web.inner.speedArea": "Region speed limit", "lang.venus.web.inner.triggerButtonReport": "Button triggering reporting", "lang.venus.web.inner.stopArea": "Emergency stop in the area", "lang.venus.web.inner.safetyDoorStop": "The safety door triggers emergency stop.", "lang.venus.web.inner.yellowTrigger": "Yellow light reporting", "lang.venus.web.inner.redTrigger": "Red light reporting", "lang.venus.web.inner.fireStopArea": "Emergency stop in the fire alarm area", "lang.venus.web.inner.resetRobotStopName": "After the safety door is restored, the robot stops executing the signal task.", "lang.venus.web.inner.stop": "Emergency stop", "lang.venus.web.inner.reset": "Reset", "lang.venus.web.inner.reportStatus": "Status reporting", "lang.venus.web.inner.scannerCodeTrigger": "Trigger by code scanning", "lang.venus.web.inner.tricolorLightStatusReport": "Tricolor light status reporting", "lang.venus.web.inner.safetyStation": "Workstation under safety solution", "lang.venus.web.inner.speedLimit": "Speed Limit", "lang.venus.web.inner.PopPick": "PopPick", "lang.venus.web.inner.elevator": "Elevator", "lang.venus.web.inner.safetyReset": "Safety solution reset", "lang.venus.web.inner.door": "Door", "lang.venus.web.inner.airShowerDoor": "Air shower door", "lang.venus.web.inner.fireStopName": "Emergency stop due to fire alarm", "lang.venus.web.inner.safetyDoor": "Safety door", "lang.venus.web.inner.trafficLight": "Traffic light"}