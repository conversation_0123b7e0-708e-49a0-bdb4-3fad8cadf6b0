/**
 *   组件国际化字符集
 *   key 必须以 'geekplus.gms.client.components.' 开头
 */
{
  "geekplus.gms.client.components.dialogForm.closeConfirm.title": "关闭确认",
  "geekplus.gms.client.components.dialogForm.closeConfirm.msg": "退出将会丢失所有表单修改, 确认要退出吗?",
  "geekplus.gms.client.components.dialogForm.closeConfirm.btnCancel": "取消",
  "geekplus.gms.client.components.dialogForm.closeConfirm.btnConfirm": "确认关闭",
  "geekplus.gms.client.components.responseErrorWrapper.responseError": "API 请求异常",

  // 帮助文档弹框
  "geekplus.gms.client.components.dialogHelp.usageManual": "使用手册",
  "geekplus.gms.client.components.dialogHelp.usageManual.gms": "极智嘉搬运系统GMS功能手册",
  "geekplus.gms.client.components.dialogHelp.usageManual.pda": "极智嘉搬运系统PDA操作手册",
  "geekplus.gms.client.components.dialogHelp.api": "接口文档",
  "geekplus.gms.client.components.dialogHelp.api.gms": "GMS接口文档",
  "geekplus.gms.client.components.dialogHelp.api.usage": "接口对接帮助",
  "geekplus.gms.client.components.dialogHelp.configTable": "设备接入配置规范表",
  "geekplus.gms.client.components.dialogHelp.configTable.single": "单悬臂对接-辊分、模切、卷绕机台IO盒配置表",
  "geekplus.gms.client.components.dialogHelp.configTable.double": "低精度双举升对接-涂布、辊分机台IO盒配置表",
  "geekplus.gms.client.components.dialogHelp.configTable.plc": "双举升、单悬臂组件PLC接入配置表",
  "geekplus.gms.client.components.dialogHelp.configTable.door": "门的IO盒接入配置表",

  // 关于GMS弹框
  "geekplus.gms.client.components.dialogAbout.gms": "关于GMS",
  "geekplus.gms.client.components.dialogAbout.geek+": "北京极智嘉科技有限公司",
  "geekplus.gms.client.components.dialogAbout.3rdLibs": "本产品还包含 {0} 许可证所涵盖的以下库：",
  "geekplus.gms.client.components.dialogAbout.configLib": "配置库",

  // 机器人类型选择组件
  "geekplus.gms.client.components.robotTypes.lift": "潜伏",
  "geekplus.gms.client.components.robotTypes.roller": "辊筒",
  "geekplus.gms.client.components.robotTypes.forklift": "叉车",
  "geekplus.gms.client.components.robotTypes.hybrid": "复合",

  "geekplus.gms.client.components.uploader.error.exceedLimit": "最多上传{0}个文件",

  // 自动赋值点属性面板
  "geekplus.gms.client.components.autoSetNode.placeholder.enterControlNodeEtc": "输入控制节点/当前节点/交互动作",
  "geekplus.gms.client.components.autoSetNode.currentNodeEquals": "当前节点等于",
  "geekplus.gms.client.components.autoSetNode.nodeAssignStrategy": "节点赋值策略",
  "geekplus.gms.client.components.autoSetNode.targetableMaterialPort": "可命中的料口",
  "geekplus.gms.client.components.autoSetNode.tooltip.assignStrategy": "策略{index}: {controlLogic}等于[{relatedNode}]时，当前节点等于[{currentNode}]",
  "geekplus.gms.client.components.autoSetNode.filename.assignStrategyImportTemplate": "赋值策略导入模板",
  "geekplus.gms.client.components.autoSetNode.dialog.batchImportNodeAssignStrategy": "批量导入节点赋值策略",
  "geekplus.gms.client.components.autoSetNode.error.duplicateRelatedNode": "控制节点重复",
  "geekplus.gms.client.components.autoSetNode.error.pleaseSelectRelatedNode": "请选择控制节点",
  "geekplus.gms.client.components.autoSetNode.error.pleaseSelectCurrentNode": "请选择当前节点",
  "geekplus.gms.client.components.autoSetNode.error.nthRowRelatedNodeExisted": "第{row}行控制点已经存在",
  "geekplus.gms.client.components.autoSetNode.error.twoRelatedNodesRowDuplicate": "第{0}行和第{1}行控制点重复",

  // 点位选择器
  "geekplus.gms.client.components.positionSelect.group.mapPoint": "地图点位",
  "geekplus.gms.client.components.positionSelect.group.workstation": "工作站",
  "geekplus.gms.client.components.positionSelect.group.area": "区域",
  "geekplus.gms.client.components.positionSelect.group.pallet": "托盘位",
  "geekplus.gms.client.components.positionSelect.group.equipment": "设备点位",
  "geekplus.gms.client.components.positionSelect.reloadIconTip": "点击刷新数据",
  "geekplus.gms.client.components.positionSelect.searchResultTip": "{count}个结果",
  "geekplus.gms.client.components.positionSelect.selectedListTitle": "已选 ({count})",
  "geekplus.gms.client.components.positionSelect.sortIconTip": "已选数量小于{count}时可进行拖拽排序",

  // 地图点位选择器
  "geekplus.gms.client.components.mapSelect.pleaseSelectCell": "请选择单元格",
}
