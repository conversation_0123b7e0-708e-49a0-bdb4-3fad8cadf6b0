/**
 *   工作站页面国际化字符集
 */
{
  "geekplus.gms.client.screen.workstations.title": "Workbench Page",
  "geekplus.gms.client.screen.workstations.pageTitle": "Workstation",
  "geekplus.gms.client.screen.workstations.pageSubTitle": "The workstation is an area for actual business operation, which usually consists of one or more work points.",
  "geekplus.gms.client.screen.workstations.btns.addStation": "New workstation",
  "geekplus.gms.client.screen.workstations.btns.configStation": "Workstation function configuration",
  "geekplus.gms.client.screen.workstations.columns.stationCode": "Code",
  "geekplus.gms.client.screen.workstations.columns.stationName": "Name",
  "geekplus.gms.client.screen.workstations.columns.stationType": "Type",
  "geekplus.gms.client.screen.workstations.columns.points": "Contained points",
  "geekplus.gms.client.screen.workstations.columns.deliveryType": "Distribution mode",
  "geekplus.gms.client.screen.workstations.columns.editor": "Editor",
  "geekplus.gms.client.screen.workstations.columns.editTime": "Edit time",
  "geekplus.gms.client.screen.workstations.columns.actions": "Operation",
  "geekplus.gms.client.screen.workstations.tooltip.listView": "List view",
  "geekplus.gms.client.screen.workstations.tooltip.cardView": "Card view",
  "geekplus.gms.client.screen.workstations.tooltip.copyLink": "Copy the workstation address",
  "geekplus.gms.client.screen.workstations.tooltip.visitPage": "Visit the workstations page",
  "geekplus.gms.client.screen.workstations.tooltip.removeWorkstation": "Delete the workstation",
  "geekplus.gms.client.screen.workstations.tooltip.configWorkstation": "Configure the workstation",
  "geekplus.gms.client.screen.workstations.placeholder.search": "Please enter the workstation name/point/code",
  "geekplus.gms.client.screen.workstations.removeConfirm.title": "Deletion confirmation",
  "geekplus.gms.client.screen.workstations.removeConfirm.desc": "Are you sure to delete this workstation?",
  "geekplus.gms.client.screen.workstations.removeConfirm.btnConfirm": "Confirm deletion",
  "geekplus.gms.client.screen.workstations.removeConfirm.btnCancel": "Cancel",
  "geekplus.gms.client.screen.workstations.dialogDetail.titleAdd": "New workstation",
  "geekplus.gms.client.screen.workstations.dialogDetail.titleEdit": "Edit workstation",
  "geekplus.gms.client.screen.workstations.dialogDetail.titleDelivery": "Select the workstation distribution mode",
  "geekplus.gms.client.screen.workstations.dialogDetail.titlePosition": "Select the contained points of the workstation",
  "geekplus.gms.client.screen.workstations.dialogDetail.titlePositionDesc": "The sequence of selecting the points is the sequence of displaying them in the workstation.",
  "geekplus.gms.client.screen.workstations.dialogDetail.titleQueue": "Enable the queuing function in the workstation",
  "geekplus.gms.client.screen.workstations.dialogDetail.titleQueueDesc": "When it is necessary to make the robots be on standby in the workstation where there is cycle time requirement, the queuing function can be enabled.",
  "geekplus.gms.client.screen.workstations.dialogDetail.labelDockPosition": "Queuing point",
  "geekplus.gms.client.screen.workstations.dialogDetail.labelRobotCount": "Number of queued robots",
  "geekplus.gms.client.screen.workstations.dialogDetail.labelPriority": "Priority",
  "geekplus.gms.client.screen.workstations.dialogDetail.btnAddQueue": "+ Add a queuing point",
  "geekplus.gms.client.screen.workstations.dialogDetail.cardDescA": "The workstation can initiate all passing workflows.",
  "geekplus.gms.client.screen.workstations.dialogDetail.cardDescB": "The workstation can only initiate workflows that start from it.",
  "geekplus.gms.client.screen.workstations.dialogDetail.labelStationCode": "Workstation coding",
  "geekplus.gms.client.screen.workstations.dialogDetail.labelPointType": "Location type",
  "geekplus.gms.client.screen.workstations.dialogDetail.labelStationName": "Workstation name",
  "geekplus.gms.client.screen.workstations.dialogDetail.labelPointList": "List of locations",
  "geekplus.gms.client.screen.workstations.dialogDetail.labelVisitAuth": "Configure workstation access permissions",
  "geekplus.gms.client.screen.workstations.dialogDetail.labelAuthUsers": "Access permissions",
  "geekplus.gms.client.screen.workstations.dialogDetail.labelAuthUserList": "Accessible users",
  "geekplus.gms.client.screen.workstations.dialogDetail.authUsersOption.user": "User",
  "geekplus.gms.client.screen.workstations.dialogDetail.authUsersOption.role": "User role",
  "geekplus.gms.client.screen.workstations.dialogSettings.title": "Workstation function configuration",
  "geekplus.gms.client.screen.workstations.dialogSettings.titleDesc": "According to different scenarios, users can flexibly select the functions to be enabled or disabled.",
  "geekplus.gms.client.screen.workstations.dialogSettings.subTitleSelectWorksite": "Select the effective workstations",
  "geekplus.gms.client.screen.workstations.dialogSettings.subTitleTask": "Task monitoring module",
  "geekplus.gms.client.screen.workstations.dialogSettings.subTitleProcess": "Workflow operation",
  "geekplus.gms.client.screen.workstations.dialogSettings.cfgReturn": "Return button",
  "geekplus.gms.client.screen.workstations.dialogSettings.cfgReturnDesc": "If the task return button is displayed, the task return workflow can be initiated.",
  "geekplus.gms.client.screen.workstations.dialogSettings.cfgRollback": "Revoke button",
  "geekplus.gms.client.screen.workstations.dialogSettings.cfgRollbackDesc": "If the task revoke button is displayed, the initiated task can be canceled.",
  "geekplus.gms.client.screen.workstations.dialogSettings.cfgCancel": "Cancel button",
  "geekplus.gms.client.screen.workstations.dialogSettings.cfgCancelDesc": "If the task cancel button is displayed, the task that changes on site can be canceled.",
  "geekplus.gms.client.screen.workstations.dialogSettings.cfgShowProcessType": "Display by workflow category",
  "geekplus.gms.client.screen.workstations.dialogSettings.cfgShowProcessTypeDesc": "If the list of workflows is displayed by the workflow category, the workflow can be quickly initiated by its category.",
  "geekplus.gms.client.screen.workstations.dialogSettings.cfgProcessRestart": "Restart workflow",
  "geekplus.gms.client.screen.workstations.dialogSettings.cfgProcessRestartDesc": "Restart the executed workflow",
  "geekplus.gms.client.screen.workstations.dialogSettings.cfgProcessRestartPDA": "Restart workflow - PDA",
  "geekplus.gms.client.screen.workstations.dialogSettings.cfgProcessRestartPDADesc": "Restart workflow on PDA",
  "geekplus.gms.client.screen.workstations.dialogSettings.cfgShowSubProcess": "Display sub-workflow",
  "geekplus.gms.client.screen.workstations.dialogSettings.cfgShowSubProcessDesc": "Whether to display subtasks associated with the workflow in the list of workflows",
  "geekplus.gms.client.screen.workstations.dialogSettings.submitConfirmMsg": "Save and apply the settings, and then the settings will take effect immediately and may affect normal operation on site. Save and apply the settings in non-working hours.",
  "geekplus.gms.client.screen.workstations.dialogSettings.submitConfirmBtn": "Confirm and apply",
  "geekplus.gms.client.screen.workstations.dialogSettings.submitBtn": "Save and apply",
  "geekplus.gms.client.screen.workstations.dialogSettings.labelApplyMethod": "Effective range",
  "geekplus.gms.client.screen.workstations.dialogSettings.labelApplyWorksites": "Effective workstation",
}
