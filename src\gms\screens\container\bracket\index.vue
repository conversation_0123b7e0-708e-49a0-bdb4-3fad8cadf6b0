<template>
  <div class="tw-flex-grow tw-flex tw-flex-col" :class="cn.page_wrapper">
    <div v-if="state.mode === 'list'" class="tw-flex tw-flex-col tw-flex-grow">
      <div v-loading="state.loading">
        <div class="tw-text-right">
          <gp-button type="primary" @click="state.mode = 'add'">
            {{ $t("geekplus.gms.client.screen.bracket.btns.addBracket") }}
          </gp-button>
        </div>
        <div class="tw-grid tw-grid-cols-4 tw-gap-4 tw-mt-4">
          <gp-card
            v-for="item in state.bracketList"
            :key="item.id"
            class="tw-group tw-cursor-pointer tw-rounded-md"
            :body-style="{ padding: 0 }"
            shadow="hover"
            @click.native="handleEdit(item, 'view')"
          >
            <div>
              <div class="tw-relative tw-flex tw-justify-center tw-pt-[47px] tw-pb-[37px] tw-bg-[#F9FBFC]">
                <img :src="bracketImg" class="tw-w-[178px] tw-h-[96px]" />
                <gp-tag
                  :type="item.modelingMethod !== 'PRECISION_MODELING' ? 'warning' : 'default'"
                  class="tw-absolute tw-top-0 tw-right-0"
                  >{{ bracketModelType.getLabelByValue(item.modelingMethod) }}</gp-tag
                >
              </div>
              <text-overflow
                class="tw-text-base tw-font-medium tw-px-4 tw-py-1"
                :content="item.name"
                placement="top-start"
              />
              <div class="tw-flex tw-justify-end tw-gap-2 tw-px-4 tw-py-2 tw-opacity-0 group-hover:tw-opacity-100">
                <svg-edit class="tw-w-4 hover:tw-cursor-point" @click.stop="handleEdit(item, 'edit')" />
                <svg-delete class="tw-w-4 hover:tw-cursor-point" @click.stop="handleDelete(item)" />
              </div>
            </div>
          </gp-card>
        </div>
        <gp-pagination
          v-if="state.bracketList.length"
          class="tw-mt-4 tw-text-right"
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="state.total"
          :current-page.sync="state.offset"
          :page-size.sync="state.limit"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </gp-pagination>
      </div>
      <empty-page
        v-if="!state.bracketList.length && !state.loading"
        :button-name="$t('geekplus.gms.client.screen.bracket.btns.addBracket')"
        @click="state.mode = 'add'"
      />
    </div>
    <AddBracket v-if="state.mode !== 'list'" :mode.sync="state.mode" :record="state.record" @save="init()" />
  </div>
</template>

<script>
import { MessageBox, Message } from "geekplus-ui";
import { defineComponent, onMounted, reactive } from "vue";
import { bracketModelType, bracketShapeType } from "gms-constants";
import { deleteContainerType, getContainerTypeList } from "gms-apis/container";
import { useI18n } from "gms-hooks";
import icons from "gms-assets/svg-icons";
import bracketImg from "@/gms/assets/images/container/solid-bracket.svg";
import TextOverflow from "@/gms/components/text-overflow";
import EmptyPage from "gms-components/empty-page";
import AddBracket from "./edit";

const getBrackets = async (params) => {
  return getContainerTypeList({
    type: [bracketShapeType.GROUND_SUPPORT],
    ...params,
  });
};

export default defineComponent({
  name: "GMSBracket",
  components: { AddBracket, EmptyPage, TextOverflow, ...icons },
  setup() {
    const t = useI18n();
    const state = reactive({
      bracketList: [],
      mode: "list",
      loading: true,
      limit: 10,
      offset: 1,
      total: 0,
      checkedValue: "",
    });

    const queryList = () => {
      state.loading = true;

      getBrackets({ limit: state.limit, offset: state.offset }).then(({ total, currentPage, data }) => {
        state.bracketList = data;
        state.total = total;
        state.offset = currentPage;
        state.loading = false;
      });
    };

    const init = () => {
      queryList();
    };

    const handleEdit = (row, mode) => {
      state.record = row;
      state.mode = mode;
    };

    const handleDelete = (row) => {
      let msg = t("geekplus.gms.client.commons.tips.confirmDeleteMsg");
      if (row.placementPointCode) {
        msg = t("geekplus.gms.client.screen.bracket.tips.notAllowDeleteMsg");
      }

      MessageBox.confirm("", msg, {
        confirmButtonText: t("geekplus.gms.client.commons.btn.confirm"),
        cancelButtonText: t("geekplus.gms.client.commons.btn.cancel"),
        type: "warning",
        showClose: false,
      }).then(() => {
        // 关联点位后不允许删除
        if (row.placementPointCode) return;

        deleteContainerType({ id: row.id }).then(() => {
          Message.success(t("geekplus.gms.client.commons.tips.operationSuccess"));

          const maxOffset = Math.ceil((state.total - 1) / state.limit);
          if (state.offset > maxOffset) {
            state.offset = maxOffset;
          }

          queryList();
        });
      });
    };

    const handleSizeChange = (val) => {
      state.limit = val;
      queryList();
    };

    const handleCurrentChange = (val) => {
      state.offset = val;
      queryList();
    };

    onMounted(() => {
      init();
    });

    return {
      state,
      bracketImg,
      bracketModelType,
      init,
      handleEdit,
      handleDelete,
      handleSizeChange,
      handleCurrentChange,
    };
  },
});
</script>
<style lang="scss" module="cn">
.page_wrapper {
  :global(.gp-message-box__title) {
    display: flex;
    align-items: center;
  }
}
</style>
