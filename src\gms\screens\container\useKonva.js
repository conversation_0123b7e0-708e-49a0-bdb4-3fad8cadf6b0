import { ref } from "vue";
import { useI18n } from "gms-hooks";
import { bracketElementType, shelfElementType } from "gms-constants";

const clientH = document.body.clientHeight;
export const containerWidth = clientH < 800 ? 360 : clientH < 1000 ? 460 : 560;
const scaleBy = 1.01;
export const scale = 3;

export const originX = containerWidth / 2;

export const originY = containerWidth / 2;

export const originPoint = {
  x: (originX * scale) / 2,
  y: (originY * scale) / 2,
};

/** 坐标轴颜色 */
const axisColor = "#00ddd9";

export const arrowCommonConfig = {
  pointerLength: 16 * scale,
  pointerWidth: 6 * scale,
  strokeScaleEnabled: false,
  stroke: axisColor,
  strokeWidth: 2,
  fill: axisColor,
};

export default function useKonva(type = "bracket") {
  const t = useI18n();
  const stage = ref(null);
  const stageConfig = ref({
    width: containerWidth,
    height: containerWidth,
    offsetX: (-originX / 2) * scale,
    offsetY: (-originY / 2) * scale,
    scaleX: 1 / scale,
    scaleY: 1 / scale,
    strokeScaleEnabled: false,
  });

  const xArrowConfig = ref({
    ...originPoint,
    points: [-(containerWidth * scale) / 2, 0, (containerWidth * scale) / 2, 0],
    ...arrowCommonConfig,
  });

  const yArrowConfig = ref({
    ...originPoint,
    points: [0, (containerWidth * scale) / 2, 0, (-containerWidth * scale) / 2],
    ...arrowCommonConfig,
  });

  const textConfig = ref({
    text: `${
      type === "shelf"
        ? t("geekplus.gms.client.screen.container.form.shelfCenter")
        : t("geekplus.gms.client.screen.bracket.form.bracketCenter")
    }`,
    x: originPoint.x + 10,
    y: originPoint.y + 20,
    strokeScaleEnabled: false,
    fontSize: 10 * scale,
  });

  const xTextConfig = ref({
    text: t("x"),
    x: originPoint.x + (originX * 3 - 40),
    y: originPoint.y + 20,
    strokeScaleEnabled: false,
    fontSize: 20 * scale,
  });

  const yTextConfig = ref({
    text: t("y"),
    x: originPoint.x + 20,
    y: originPoint.y - originY * scale,
    strokeScaleEnabled: false,
    fontSize: 20 * scale,
  });

  const originPointConfig = ref({
    ...originPoint,
    x: originPoint.x - 15,
    y: originPoint.y - 15,
    width: 30,
    height: 30,
    fill: axisColor,
    strokeWidth: 30,
    cornerRadius: 20,
  });

  const getLegPointConfig = (model, ratio) => {
    return {
      ...originPoint,
      offsetX: (-model.x + 10) / ratio,
      offsetY: (model.y + 10) / ratio,
      width: 14,
      height: 14,
      fill: "black",
      strokeWidth: 10,
      cornerRadius: 10,
    };
  };

  const getLegNameConfig = (model, index, ratio) => {
    const offsetY = Math.abs(model.y + model.width / 2) / ratio;
    return {
      ...originPoint,
      text: `${type === "shelf" ? shelfElementType.getLabelByValue("LEG") : bracketElementType.getLabelByValue("LEG")}${
        index + 1
      }`,
      offsetX: -model.x / ratio + 30,
      offsetY: model.y >= 0 ? offsetY + 100 : -offsetY - model.width,
      fill: "black",
      strokeWidth: 20,
      fontSize: 10 * scale,
    };
  };

  const getLegLengthLine = (model, ratio) => {
    const x1 = model.x > 0 ? model.x / ratio - model.length / 2 : model.x / ratio + model.length / 2;
    const x2 = model.x > 0 ? model.x / ratio + model.length / 2 : model.x / ratio - model.length / 2;
    const y =
      model.y > 0 ? -(model.y / ratio + model.width / 2 + 20) : Math.abs(model.y) / ratio - model.width / 2 - 20;
    let points = [x1, y, x2, y];
    return {
      ...originPoint,
      points,
      stroke: "black",
      strokeWidth: 2,
      lineJoin: "round",
      dash: [10, 10],
    };
  };
  const getLegWidthLine = (model, ratio) => {
    const x = model.x / ratio + model.length / 2 + 10;
    const y = model.y > 0 ? -model.y / ratio + model.width / 2 : -model.y / ratio + model.width / 2;
    const y1 = model.y >= 0 ? -model.y / ratio - model.width / 2 : -model.y / ratio - model.width / 2;
    let points = [x, y1, x, y];

    return {
      ...originPoint,
      points,
      stroke: "black",
      strokeWidth: 2,
      lineJoin: "round",
      dash: [10, 10],
    };
  };

  const getLengthTextConfig = (model, ratio) => {
    const offsetX = -model.x + 60;
    const offsetY = (model.y + model.width + 60) / ratio;
    return {
      ...originPoint,
      text: `${model.length}mm`,
      offsetX: offsetX / ratio,
      offsetY: offsetY,
      fill: "black",
      strokeWidth: 20,
      fontSize: 10 * scale,
    };
  };

  const getWidthTextConfig = (model, ratio) => {
    const offsetX = -model.x - model.length / 2;
    const offsetY = model.y + 30;
    return {
      ...originPoint,
      text: `${model.width}mm`,
      offsetX: offsetX / ratio - 40,
      offsetY: offsetY / ratio,
      fill: "black",
      strokeWidth: 20,
      fontSize: 10 * scale,
    };
  };

  const getXToOriginLine = (model, ratio) => {
    let y1 = Math.abs(model.y / ratio);
    let x2 = Math.abs(model.x / ratio);
    let y2 = Math.abs(model.y / ratio);
    let points = [0, model.y > 0 ? -y1 : y1, model.x > 0 ? x2 : -x2, model.y > 0 ? -y2 : y2];
    return {
      ...originPoint,
      points,
      stroke: "red",
      strokeWidth: 5,
      lineJoin: "round",
      dash: [10, 10],
    };
  };

  const getYToOriginLine = (model, ratio) => {
    let x1 = Math.abs(model.x / ratio);
    let y1 = Math.abs(model.y / ratio);
    let x2 = Math.abs(model.x / ratio);
    let points = [model.x > 0 ? x1 : -x1, model.y > 0 ? -y1 : y1, model.x > 0 ? x2 : -x2, 0];
    return {
      ...originPoint,
      points,
      stroke: "red",
      strokeWidth: 5,
      lineJoin: "round",
      dash: [10, 10],
    };
  };

  const getXToOriginText = (model, ratio) => {
    const offsetY = model.y > 0 ? model.y + 80 : model.y - 40;
    return {
      ...originPoint,
      text: `${model.x}mm`,
      offsetX: -model.x / 2 / ratio,
      offsetY: offsetY / ratio,
      fill: "red",
      strokeWidth: 20,
      fontSize: 10 * scale,
    };
  };
  const getYToOriginText = (model, ratio) => {
    const offsetX = model.x > 0 ? -model.x - 20 : -model.x - 20;
    return {
      ...originPoint,
      text: `${model.y}mm`,
      offsetX: offsetX / ratio,
      offsetY: model.y / 2 / ratio,
      fill: "red",
      strokeWidth: 20,
      fontSize: 10 * scale,
    };
  };

  const transformParam = (model, active, ratio) => {
    const { length, width, type, faceX, faceY, x: offsetX, y: offsetY } = model;

    if (type === bracketElementType.FACE) {
      return {
        width: length,
        height: width,
        x: faceX,
        y: faceY,
      };
    }
    return {
      x: originPoint.x - model.length / 2,
      y: originPoint.y - model.width / 2,
      width: length,
      height: width,
      offsetX: -offsetX / ratio,
      offsetY: offsetY / ratio,
      id: model.key,
      name: model.key,
      strokeWidth: 2,
      stroke: active === model.key ? "#409EFF" : "gray",
    };
  };

  // 滚轮事件，放大缩小shape
  const handleWheel = (e) => {
    e.evt.preventDefault();
    const node = stage.value.getNode();
    const { x, y } = node.position();
    const oldScale = node.scale().x;
    const pointer = node.pointerPos;

    const mousePointTo = {
      x: (pointer.x - x) / oldScale,
      y: (pointer.y - y) / oldScale,
    };

    let direction = e.evt.deltaY > 0 ? 1 : -1;

    if (e.evt.ctrlKey) {
      direction = -direction;
    }

    const newScale = direction > 0 ? oldScale * scaleBy : oldScale / scaleBy;

    node.scale({ x: newScale, y: newScale });

    const newPos = {
      x: pointer.x - mousePointTo.x * newScale,
      y: pointer.y - mousePointTo.y * newScale,
    };
    node.position(newPos);
  };

  return {
    stage,
    stageConfig,
    xArrowConfig,
    yArrowConfig,
    textConfig,
    originPointConfig,
    xTextConfig,
    yTextConfig,
    handleWheel,
    transformParam,
    getLegPointConfig,
    getLegNameConfig,
    getLegLengthLine,
    getLegWidthLine,
    getLengthTextConfig,
    getWidthTextConfig,
    getXToOriginLine,
    getYToOriginLine,
    getXToOriginText,
    getYToOriginText,
  };
}
