<template>
  <div class="tw-flex tw-flex-col tw-relative tw--mt-4">
    <!-- 搜索框 -->
    <div class="tw-flex tw-justify-end tw-gap-2 tw-my-4">
      <QueueFilter @change="handleFilterChange">
        <template #suffix>
          <ButtonEnhanced type="primary" @click="handleAdd">
            {{ $t("lang.ark.fed.addNew") }}
          </ButtonEnhanced>
        </template>
      </QueueFilter>
    </div>
    <!-- 表格数据 -->
    <!-- <table-wrapper v-bind="tableState"> </table-wrapper> -->
  </div>
</template>
<script>
import QueueFilter from "./queue-filter.vue";
import { defineComponent } from "vue";
import { useI18n } from "gms-hooks";
export default defineComponent({
  name: "QueueList",
  components: {
    QueueFilter,
  },
  setup() {
    const t = useI18n();
    const handleFilterChange = (filterParams) => {
      console.log("filterParams", filterParams);
      //   state.filterParams = filterParams;
      //   tableState.query({ currentPage: 1 });
    };
    const handleAdd = () => {
      console.log("handleAdd");
    };
    // 分组策略
    return {
      t,
      handleFilterChange,
      handleAdd
    };
  },
});
</script>
