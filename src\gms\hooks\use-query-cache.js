import { useRouteCache } from "./use-route-cache";
import { queryParamToString } from "./utils";

/**
 * DESCRIPTION:
 * ----------------------------
 *  useQueryCache is a function that can be used to cache ajax get requests by query params.
 *
 *
 *  SIGNATURE:
 *  ----------------------------
 *  useQueryCache: ajaxFn => requestHandler => Promise
 *  ajaxFn: queryParams => Promise
 *  requestHandler: queryParams => Promise
 *
 *  USAGE:
 *  ----------------------------
 *  const ajaxFn = (ajaxParams, others) => ajax.get('url', ajaxParams);
 *
 *  // wrap by useQueryCache()
 *  const requestHandler = useQueryCache(ajaxFn);
 *
 *  // first time, request will be sent
 *  const dataPromise = requestHandler({userName: 'Jane'});
 *
 *  // second time, request will not be sent, data will be returned from cache
 *  requestHandler({userName: 'Jane'});
 *
 *  // if params are different, request will be resent
 *  requestHandler({userName: 'Mark'});
 *
 */
export function useQueryCache(ajaxFn) {
  // cache key is query string
  // chache value is request handler function
  const cache = new Map();

  // first parameter is an object here, will be stringified and used as cache key
  // other parameters are passed to ajaxFn
  const requestHandler = (queryParams, ...rest) => {
    const queryString = queryParamToString(queryParams);

    if (!cache.has(queryString)) {
      cache.set(
        queryString,
        useRouteCache(() => ajaxFn(queryParams, ...rest))
      );
    }

    const dataPromise = cache.get(queryString)();

    // if request fails, remove cache, so that next time it will be sent again
    dataPromise.catch((e) => {
      cache.delete(queryString);
      return e;
    });

    return dataPromise;
  };

  /**
   * clear all cache
   */
  requestHandler.clearCache = () => {
    cache.forEach((requestHandler) => requestHandler.clearCache());
    cache.clear();
  };

  return requestHandler;
}
