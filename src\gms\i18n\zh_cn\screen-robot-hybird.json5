/**
 *   机器人-复合机器人   页面国际化字符集
 *   key 必须以 'geekplus.gms.client.screen.robotHybird.' 开头
 */
{
  "geekplus.gms.client.screen.robotHybird.columns.dockingHeight": "对接高度",
  "geekplus.gms.client.screen.robotHybird.columns.dropHeight": "放料高度",
  "geekplus.gms.client.screen.robotHybird.columns.pickHeight": "取料高度",
  "geekplus.gms.client.screen.robotHybird.columns.deviceType": "设备类型",
  "geekplus.gms.client.screen.robotHybird.columns.name": "名称",

  "geekplus.gms.client.screen.robotHybird.dialogAdd.heightTip.th.1_1": "参数名称",
  "geekplus.gms.client.screen.robotHybird.dialogAdd.heightTip.th.1_2": "举升低精度机器人",
  "geekplus.gms.client.screen.robotHybird.dialogAdd.heightTip.th.1_3": "悬臂机器人",

  "geekplus.gms.client.screen.robotHybird.dialogAdd.heightTip.th.2_1": "机台",
  "geekplus.gms.client.screen.robotHybird.dialogAdd.heightTip.th.2_2": "缓存架",
  "geekplus.gms.client.screen.robotHybird.dialogAdd.heightTip.th.2_3": "机台",
  "geekplus.gms.client.screen.robotHybird.dialogAdd.heightTip.th.2_4": "缓存架",

  "geekplus.gms.client.screen.robotHybird.dialogAdd.heightTip.td.1_1": "参数11",
  "geekplus.gms.client.screen.robotHybird.dialogAdd.heightTip.td.1_2": "对接高度",
  "geekplus.gms.client.screen.robotHybird.dialogAdd.heightTip.td.1_3": "对接高度",
  "geekplus.gms.client.screen.robotHybird.dialogAdd.heightTip.td.1_4": "机台轴高度",
  "geekplus.gms.client.screen.robotHybird.dialogAdd.heightTip.td.1_5": "十字标识高度",

  "geekplus.gms.client.screen.robotHybird.dialogAdd.heightTip.td.2_1": "参数12",
  "geekplus.gms.client.screen.robotHybird.dialogAdd.heightTip.td.2_2": "放料高度",
  "geekplus.gms.client.screen.robotHybird.dialogAdd.heightTip.td.2_3": "放料高度",
  "geekplus.gms.client.screen.robotHybird.dialogAdd.heightTip.td.2_4": "机台轴端到机台外边框的距离",
  "geekplus.gms.client.screen.robotHybird.dialogAdd.heightTip.td.2_5": "物料中心高度",

  "geekplus.gms.client.screen.robotHybird.dialogAdd.heightTip.td.3_1": "参数13",
  "geekplus.gms.client.screen.robotHybird.dialogAdd.heightTip.td.3_2": "取料高度",
  "geekplus.gms.client.screen.robotHybird.dialogAdd.heightTip.td.3_3": "取料高度",
  "geekplus.gms.client.screen.robotHybird.dialogAdd.heightTip.td.3_4": "推入机台距离",

  "geekplus.gms.client.screen.robotHybird.dialogAdd.heightTip.comments": "备注：以上3个高度分别对应接口文件中的扩展参数11、扩展参数12、扩展参数13，请根据机台或者缓存架自行配置",
}
