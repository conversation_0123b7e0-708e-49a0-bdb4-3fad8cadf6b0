<template>
  <ContainerWrapper
    ref="containerRef"
    active-name="ContainerModel"
    :mode.sync="state.mode"
    :record="state.currentRecord"
  />
</template>
<script>
import { defineComponent, onMounted, reactive, ref } from "vue";
import { getContainerList } from "gms-apis/container";
import ContainerWrapper from "@/gms/screens/container/index.vue";

const getContainerByCode = async (params) => {
  let { data } = await getContainerList({
    limit: 100,
    offset: 1,
    ...params,
  });
  return data[0]?.modelInfo;
};

export default defineComponent({
  name: "ModelDetail",
  components: { ContainerWrapper },
  setup() {
    const containerRef = ref(null);
    const state = reactive({
      currentRecord: {},
      mode: "view",
    });

    var params = new URLSearchParams(window.location.href?.split("?")[1]);
    const code = params.get("loadCode");

    onMounted(() => {
      getContainerByCode({ code }).then((data) => {
        state.currentRecord = data;
        containerRef.value.$children[2].handleEdit(data, "view");
      });
    });

    return {
      state,
      containerRef,
    };
  },
});
</script>
