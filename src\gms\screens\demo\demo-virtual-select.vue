<template>
  <div>
    <p class="tw-mb-4">单选：虚拟滚动(3000条数据可筛选)：</p>
    <virtual-select
      v-model="state.selected"
      class="tw-w-1/3 tw-mb-4"
      :options="state.options"
      option-key="value"
      filterable
      clearable
    ></virtual-select>
    <p class="tw-mb-4">已选项：{{ state.selected }}</p>
    <p class="tw-mb-4">多选：虚拟滚动(3000条数据可筛选)：</p>
    <virtual-select
      v-model="state.multiSelected"
      class="tw-w-1/3 tw-mb-4"
      :options="state.options"
      option-key="value"
      filterable
      multiple
      clearable
    ></virtual-select>
    <p class="tw-mb-4">已选项：{{ state.multiSelected }}</p>
  </div>
</template>

<script>
import { reactive } from "vue";
import VirtualSelect from "gms-components/virtual-select";

const options = Array(3000)
  .fill({})
  .map((item, index) => {
    return {
      label: `选项${index + 1}`,
      value: index + 1,
      disabled: index % 2 === 0,
    };
  });

export default {
  name: "DemoVirtualSelect",
  components: { VirtualSelect },
  setup() {
    const state = reactive({
      selected: 20,
      multiSelected: [20, 30],
      options: options,
    });

    return {
      state,
    };
  },
};
</script>

<style scoped></style>
