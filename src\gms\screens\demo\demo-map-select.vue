<template>
  <div class="tw-flex tw-w-full tw-gap-4">
    <div class="tw-basis-1/2">
      <div class="tw-flex tw-items-center">
        <p>地图选点（多选）：</p>
        <map-select :multiple="true" :ws-url="state.wsUrl" :value="state.mapValue" @change="handleChange" />
      </div>
      <p class="tw-title tw-mt-4">地图选点结果：</p>
      <pre class="tw-mt-4 tw-p-2 tw-bg-background-dark tw-text-text-primary-dark">{{ displayMapValue }}</pre>
      <gp-divider direction="horizontal" />
      <div class="tw-flex tw-items-center">
        <p>地图选点（单选）：</p>
        <map-select :ws-url="state.wsUrl" :value="state.singleMapValue" @change="handleSingleChange" />
      </div>
      <p class="tw-title tw-mt-4">地图选点结果：</p>
      <pre class="tw-mt-4 tw-p-2 tw-bg-background-dark tw-text-text-primary-dark">{{ displaySingleMapValue }}</pre>
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, computed } from "vue";
import MapSelect from "gms-components/business/map-select";
import { prettyJSON } from "gms-utils";

export default defineComponent({
  name: "DemoMapSelect",
  components: { MapSelect },
  setup() {
    const state = reactive({
      originalMapValue: [],
      mapValue: "",
      originalSingleMapValue: "",
      singleMapValue: "",
      wsUrl: `ws://${window.location.host}/athena-monitor`,
    });

    const handleChange = (value) => {
      state.originalMapValue = value;
      state.mapValue = value
        ?.map((item) => {
          return item.cellCode;
        })
        .join(",");
    };

    const handleSingleChange = (value) => {
      state.originalSingleMapValue = value;
      state.singleMapValue = value;
    };

    const displayMapValue = computed(() => {
      return state.originalMapValue?.length > 0 ? prettyJSON(state.originalMapValue) : "暂无数据";
    });

    const displaySingleMapValue = computed(() => {
      return state.originalSingleMapValue ? prettyJSON(state.originalSingleMapValue) : "暂无数据";
    });

    return {
      state,
      handleChange,
      handleSingleChange,
      prettyJSON,
      displayMapValue,
      displaySingleMapValue,
    };
  },
});
</script>

<style scoped></style>
