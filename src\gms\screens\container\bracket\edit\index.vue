<template>
  <div class="tw-flex-grow tw-flex tw-flex-col tw-gap-5 tw-h-full" :class="cn.bracket_root">
    <ConfigForm
      v-bind="$props"
      :class="cn.flex_center_height"
      :step.sync="state.step"
      :form-values.sync="state.formValues"
      :form-group="state.formGroup"
      :is-submiting="state.isSubmiting"
      :form-group-list.sync="state.formGroupList"
      :handle-submit="handleSubmit"
      @update:formValues="emit('update:formValues', $event)"
    />
    <section class="tw-mt-2 tw-text-center tw-h-8">
      <template v-if="$props.mode !== 'view'">
        <gp-button type="primary" plain @click="handleCancel">
          {{ $t("geekplus.gms.client.commons.btn.cancel") }}
        </gp-button>
        <gp-button v-loading="loading" type="primary" :disabled="$props.mode === 'view'" @click="handleSubmit">{{
          $t("geekplus.gms.client.commons.btn.save")
        }}</gp-button>
      </template>
      <gp-button v-else @click="handleCancel">{{
        $t("geekplus.gms.client.screen.container.btns.cancelView")
      }}</gp-button>
    </section>
  </div>
</template>

<script>
import { cloneDeep } from "lodash";
import { Message } from "geekplus-ui";
import { defineComponent, reactive, onMounted, computed, ref } from "vue";
import { bracketElementType, containerModel, containerTypeShape } from "gms-constants";
import { createFormItemGroup } from "@srpe-fe/uic-el-form-wrapper-vue2";
import { useI18n } from "@/hooks";
import { addContainerType } from "gms-apis/container";
import ConfigForm from "./config-form";
import {
  commonValues,
  standardModel,
  getFormItemCfgs,
  getFormValues,
  getSubmitMappingData,
  getExpandModelMapping,
} from "./state";
const commonKeys = Object.keys(commonValues);

const initialFormValues = { ...commonValues, ...standardModel };

export default defineComponent({
  name: "BracketEditWrapper",
  components: { ConfigForm },
  props: {
    record: { type: Object, default: () => ({}) },
    mode: { type: String, default: "add" },
  },
  emits: ["save"],
  setup(props, { emit }) {
    const t = useI18n();
    const loading = ref(false);
    const state = reactive({
      formValues: cloneDeep(initialFormValues),
      formGroup: {},
      formGroupList: [],
      step: 1,
      shape: containerTypeShape.PALLET_RACKING,
    });

    const modelInfoKeys = computed(() => Object.keys(state.formValues.modelInfo));

    const handleGroupChange = ({ name, value: val }) => {
      let value = val;
      if (name === state.formGroup?.modelingMethod?.name) {
        state.formValues[name] = value;
        init();
      }
      // 更新commonValues
      commonKeys.forEach((key) => {
        if (name === key) {
          state.formValues[name] = value;
        }
      });
      // 更新formValues.modelInfo
      modelInfoKeys.value?.forEach((key) => {
        if (name === key) {
          state.formValues.modelInfo[name] = value;
        }
      });
      state.formGroup[name].value = value;
    };

    function resetFormValues() {
      state.formValues = cloneDeep(initialFormValues);
    }

    const handleSubmit = () => {
      state.formGroup.$validate((isValid) => {
        if (isValid) {
          if (state.formValues.modelingMethod === containerModel.NON_STANDARD) {
            if (state.formValues.modelInfo.filter((v) => v.type === bracketElementType.LEG)?.length < 2) {
              Message.error(t("geekplus.gms.client.screen.container.tips.atLeast2Legs"));
              return;
            }
          }
          loading.value = true;
          addContainerType(getSubmitMappingData(state.formValues))
            .then(() => {
              Message.success(t("lang.ark.fed.savedSuccessfully"));
              emit("update:mode", "list");
              emit("save", true);
              resetFormValues();
            })
            .finally(() => (loading.value = false));
        }
      });
    };

    const handleCancel = () => {
      emit("update:mode", "list");
    };

    const initFormGroup = () => {
      const formItemCfgs = getFormItemCfgs(state.formValues, props.mode);
      state.formGroup = createFormItemGroup(formItemCfgs, {
        handleGroupChange,
        mode: props.mode === "view" ? "view" : "edit",
      });
    };

    const initFormValues = () => {
      state.formValues = getFormValues(state.formValues);
    };

    const init = () => {
      initFormValues();
      initFormGroup();
    };

    onMounted(() => {
      if (props.mode !== "add") {
        state.formValues = getExpandModelMapping(props.record);
        initFormGroup();
      } else {
        init();
      }
    });

    const updateType = (containerType) => {
      state.formValues = { ...state.formValues, containerType };
      init();
    };

    return { state, loading, emit, updateType, handleSubmit, handleCancel };
  },
});
</script>
<style lang="scss" module="cn">
.bracket_root {
  .flex_center_height {
    height: calc(100vh - 166px);
    min-height: 620px;
  }
}
</style>
