<template>
  <div
    class="tw-flex-grow tw-flex tw-h-full tw-items-stretch tw-justify-between tw-p-4 tw-pr-0 xl:tw-gap-8 sm:tw-gap-4"
    :class="cn.form_wrapper"
  >
    <ConfigResult
      class="tw-flex-1 tw-border tw-border-gray-400 tw-border-solid"
      :form-values="$props.formValues"
      :active="activeItem"
      @update:active="updateActiveItem"
      @update:formValues="$emit('update:formValues', $event)"
    />
    <gp-form @click.native="handleFormClick" @blur.native="handleFormBlur" @mouseleave.native="handleFormBlur">
      <div class="tw-w-80 tw-h-full tw-max-h-full tw-overflow-y-scroll">
        <section class="tw-flex tw-flex-col" :class="gapClass">
          <h3 class="tw-relative tw-font-bold tw-text-l">
            {{ $t("geekplus.gms.client.screen.container.form.BasicInformation") }}
          </h3>
          <form-item v-bind="$props.formGroup.type" />
          <form-item v-bind="$props.formGroup.name" />
        </section>
        <ModelInfo v-bind="$props" :active.sync="activeItem" @update:formValues="$emit('update:formValues', $event)" />
      </div>
    </gp-form>
  </div>
</template>

<script>
import { defineComponent, ref, computed } from "vue";
import { FormItem } from "@srpe-fe/uic-el-form-wrapper-vue2";
import { containerModel } from "gms-constants";
import ModelInfo from "../model-info";
import ConfigResult from "../config-result";

export default defineComponent({
  name: "ConfigForm",
  components: { FormItem, ModelInfo, ConfigResult },
  props: {
    formValues: { type: Object, default: () => ({}) },
    formGroup: { type: Object, default: () => ({}) },
    record: { type: Object, default: () => ({}) },
    mode: { type: String, default: "add" },
    step: { type: Number, default: 2 },
    handleSubmit: { type: Function, default: () => {} },
  },
  emit: ["update:formValues"],
  setup(props) {
    const activeItem = ref(null);

    const isStandard = computed(() => props.formValues.modelingMethod === containerModel.STANDARD);
    const gapClass = computed(() => [props.mode !== "view" ? "tw-gap-4" : ""]);

    const updateActiveItem = ($event) => {
      activeItem.value = $event;
    };

    const handleFormClick = (e) => {
      if (isStandard.value) {
        activeItem.value = e.target.name;
      }
    };

    const handleFormBlur = () => {
      if (isStandard.value) {
        activeItem.value = "";
      }
    };

    return { activeItem, gapClass, updateActiveItem, handleFormBlur, handleFormClick };
  },
});
</script>
<style lang="scss" module="cn">
.form_wrapper {
  min-height: 660px;
  overflow-x: scroll;
  ::-webkit-scrollbar {
    width: 0;
  }
}
</style>
