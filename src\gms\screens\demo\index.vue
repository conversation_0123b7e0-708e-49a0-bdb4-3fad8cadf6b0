<template>
  <gp-container class="container">
    <gp-aside width="200px" class="sidebar">
      <gp-menu class="sidebar-menu" :default-active="activePath" router>
        <gp-menu-item index="/demo/map-select">选点组件</gp-menu-item>
        <gp-menu-item index="/demo/position-select">选点组件 - 实施调试</gp-menu-item>
        <gp-menu-item index="/demo/virtual-select">虚拟滚动</gp-menu-item>
        <gp-menu-item index="/demo/editable-list">可编辑列表</gp-menu-item>
        <gp-menu-item index="/demo/edit-table">可编辑表格</gp-menu-item>
      </gp-menu>
    </gp-aside>
    <gp-main class="main-wrap">
      <router-view />
    </gp-main>
  </gp-container>
</template>

<script>
import { defineComponent, ref } from "vue";
import { useRoute } from "vue-router/composables";

export default defineComponent({
  name: "DemoPage",
  setup() {
    const route = useRoute();
    const activePath = ref(route.path);

    return {
      activePath,
    };
  },
});
</script>

<style lang="scss" scoped>
.container {
  height: 100vh;
  overflow: hidden;
  .sidebar {
    height: 100%;
    border-right: 1px solid #ccc;
    overflow: hidden;
    &-menu {
      height: 100%;
      overflow-y: auto;
      overflow-x: hidden;
    }
  }
  .main-wrap {
    overflow-y: auto;
  }
}
</style>
