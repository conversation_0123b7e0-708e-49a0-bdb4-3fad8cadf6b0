import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";
import { useLoadingState } from "./use-loading-state";

const fackRequest = (delay) => () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve("data");
    }, delay);
  });
};

describe("hook: useLoadingState", () => {
  beforeEach(() => {
    vi.useFakeTimers();
  });
  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("test loading state: response return before delay time", async () => {
    const fn = useLoadingState(fackRequest(300), { delay: 500 });
    fn();
    expect(fn.loading).toBe(false);
    await vi.advanceTimersByTimeAsync(400);
    expect(fn.loading).toBe(false);
    await vi.advanceTimersByTimeAsync(400);
    expect(fn.loading).toBe(false);
  });

  it.only("test loading state: response return after delay time", async () => {
    const fn = useLoadingState(fackRequest(500), { delay: 300 });
    fn();
    expect(fn.loading).toBe(false);
    await vi.advanceTimersByTimeAsync(100);
    expect(fn.loading).toBe(false);
    await vi.advanceTimersByTimeAsync(300);
    expect(fn.loading).toBe(true);
    await vi.advanceTimersByTimeAsync(200);
    expect(fn.loading).toBe(false);
  });
});
