<template>
  <div>
    <section v-if="isStandard" class="tw-flex tw-gap-4 tw-flex-col tw-mt-6">
      <h3 class="tw-relative tw-font-bold tw-text-l">
        {{ $t("geekplus.gms.client.screen.container.form.modelInformation") }}
      </h3>
      <div class="tw-flex tw-justify-between tw-items-center tw-gap-2 tw--mt-4">
        <form-item v-bind="formGroup.modelingMethod" />
        <gp-button type="text" @click="exampleVisible = true">{{
          $t("geekplus.gms.client.screen.container.form.checkExample")
        }}</gp-button>
      </div>
      <div class="tw-border tw-border-solid tw-border-gray-300 tw-p-2 tw-rounded">
        <h3>{{ $t("geekplus.gms.client.screen.bracket.form.bracketSize") }}</h3>
        <div class="tw-flex tw-flex-col" :class="viewClass">
          <div class="tw-flex tw-items-center tw-gap-2">
            <form-item v-bind="formGroup.surfaceLength" />
            <span>mm</span>
          </div>
          <div class="tw-flex tw-items-center tw-gap-2">
            <form-item v-bind="formGroup.surfaceWidth" />
            <span>mm</span>
          </div>
        </div>
      </div>
      <div class="tw-border tw-border-solid tw-border-gray-300 tw-p-2 tw-rounded">
        <h3>{{ $t("geekplus.gms.client.screen.bracket.form.bracketLeg") }}</h3>
        <div class="tw-flex tw-flex-col" :class="viewClass">
          <div class="tw-flex tw-items-center tw-gap-2">
            <form-item v-bind="formGroup.legLength" />
            <span>mm</span>
          </div>
          <div class="tw-flex tw-items-center tw-gap-2">
            <form-item v-bind="formGroup.legWidth" />
            <span>mm</span>
          </div>
          <div class="tw-flex tw-items-center tw-gap-2">
            <form-item v-bind="formGroup.legHeight" />
            <span>mm</span>
          </div>
        </div>
      </div>
      <div class="tw-border tw-border-solid tw-border-gray-300 tw-p-2 tw-rounded">
        <div class="tw-flex tw-items-center tw-gap-1">
          <h3>{{ $t("geekplus.gms.client.screen.bracket.form.offset") }}</h3>
          <gms-tooltip effect="light" placement="left">
            <template #content>
              <p class="tw-leading-6">
                {{ $t("geekplus.gms.client.screen.bracket.form.offset.tooltip.desc") }}
              </p>
              <p class="tw-leading-6 tw-mt-2">
                {{ $t("geekplus.gms.client.screen.bracket.form.offset.tooltip.tips") }}
              </p>
              <img :src="yAxisOffsetImg" alt="yAxisOffset" class="tw-w-full" />
            </template>
          </gms-tooltip>
        </div>
        <div class="tw-flex tw-gap-4 tw-flex-col">
          <div class="tw-flex tw-items-center tw-gap-2">
            <form-item v-bind="formGroup.offsetY" />
            <span>mm</span>
          </div>
        </div>
      </div>
    </section>
    <NoStandardModel
      v-if="!isStandard"
      v-bind="$attrs"
      :form-group="$props.formGroup"
      :form-values="$props.formValues"
      :mode="$props.mode"
      @update:formValues="$emit('update:formValues', $event)"
      @update:active="$emit('update:active', $event)"
    />
    <ExampleModeExplain :visible.sync="exampleVisible" />
  </div>
</template>
<script>
import { defineComponent, computed, ref } from "vue";
import { FormItem } from "@srpe-fe/uic-el-form-wrapper-vue2";
import { containerModel } from "gms-constants";
import getContainerImage from "@/utils/containerImages";
const yAxisOffsetImg = getContainerImage("yAxis-offset.png");
import ExampleModeExplain from "../../example-model-explain.vue";
import NoStandardModel from "./no-standard-model";
import GmsTooltip from "gms-components/gms-tooltip.vue";

export default defineComponent({
  name: "NoPalletModel",
  components: { NoStandardModel, FormItem, ExampleModeExplain, GmsTooltip },
  props: {
    formGroup: { type: Object, default: () => ({}) },
    formValues: { type: Object, default: () => ({}) },
    mode: { type: String, default: "add" },
  },
  emit: ["update:formValues", "update:active"],
  setup(props) {
    const exampleVisible = ref(false);
    const isStandard = computed(() => props.formValues.modelingMethod === containerModel.STANDARD);
    const viewClass = computed(() => [props.mode !== "view" ? "tw-gap-4" : ""]);

    return { yAxisOffsetImg, exampleVisible, isStandard, viewClass };
  },
});
</script>
