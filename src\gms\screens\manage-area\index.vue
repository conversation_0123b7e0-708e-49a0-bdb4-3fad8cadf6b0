<template>
  <div class="tw-p-4 tw-bg-background-light">
    <page-header-with-action :title="t('geekplus.gms.client.screen.areaEditIndex.area')" />
    <gp-tabs v-model="activeTab" class="tw-mt-1" @tab-click="handleClick">
      <gp-tab-pane v-for="item in tabs" :key="item.value" :label="item.label" :name="item.value">
        <component :is="item.component" v-if="activeTab === item.value" ref="listRefs" />
      </gp-tab-pane>
    </gp-tabs>
  </div>
</template>
<script>
import { defineComponent, ref } from "vue";
import { PageHeaderWithAction } from "gms-components/page-header";
import { STORAGE_CREATE_TYPE } from "./common";
// 存储区
import StorageList from "./storage-list/index.vue";
// 排队区
import QueueList from "./queue-list/index.vue";
import { useI18n } from "@/hooks";
export default defineComponent({
  name: "ManageArea",
  components: {
    PageHeaderWithAction,
    StorageList,
    QueueList,
  },
  setup() {
    STORAGE_CREATE_TYPE.initI18n();
    const activeTab = ref(STORAGE_CREATE_TYPE.STORAGE);
    const tabs = STORAGE_CREATE_TYPE.filter((item) => {
      return item.value !== STORAGE_CREATE_TYPE.ALL;
    });
    const t = useI18n();
    const handleClick = (tab) => {
      console.log("tab========", tab);
      activeTab.value = tab.name;
    };
    return {
      tabs,
      t,
      activeTab,
      handleClick
    };
  },
});
</script>
