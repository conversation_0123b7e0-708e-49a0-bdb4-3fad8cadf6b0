{"geekplus.gms.client.screen.palletModel.palletModelCode": "Pallet rack model code", "geekplus.gms.client.screen.palletModel.palletModelName": "<PERSON><PERSON><PERSON> Model Name", "geekplus.gms.client.screen.palletModel.palletModelType": "Pallet rack model type", "geekplus.gms.client.screen.palletModel.containerType": "Container category", "geekplus.gms.client.screen.palletModel.numOfTiers": "Layer", "geekplus.gms.client.screen.palletModel.addPalletModel": "New pallet rack model", "geekplus.gms.client.screen.palletModel.editPalletModel": "Edit pallet rack model", "geekplus.gms.client.screen.palletModel.viewPalletModel": "View pallet rack model", "geekplus.gms.client.screen.palletModel.movable": "Support mobile", "geekplus.gms.client.screen.palletModel.canMove": "Portability", "geekplus.gms.client.screen.palletModel.cannotMove": "Immovable"}