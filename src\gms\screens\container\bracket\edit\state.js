import { uniqueId, cloneDeep } from "lodash";
import { bracketModelType, bracketElementType, geometricShape, containerParts, bracketShapeType } from "gms-constants";
import { validators } from "@srpe-fe/uic-el-form-wrapper-vue2";
import { useI18n } from "@/hooks";
const t = useI18n();

export const commonValues = {
  name: "",
  type: bracketShapeType.GROUND_SUPPORT, // 容器形态
  modelingMethod: bracketModelType.STANDARD, // 标准模型
};

/**
 * 标准模型 模型信息
 */
export const standardModel = {
  modelInfo: {
    surfaceLength: 1316,
    surfaceWidth: 1316,
    legLength: 40,
    legWidth: 40,
    legHeight: 300,
    offsetY: 0,
  },
};

/**
 * 非标准模型 模型信息
 */
export const nonStandardModel = {
  shelves: bracketElementType.LEG,
  modelInfo: [
    {
      key: uniqueId(),
      type: bracketElementType.FACE,
      length: 1316,
      width: 1316,
      x: 0,
      y: 0,
      faceX: 0,
      faceY: 0,
    },
    {
      key: uniqueId(),
      type: bracketElementType.LEG,
      length: 40,
      width: 40,
      height: 300,
      x: 600,
      y: 600,
    },
    {
      key: uniqueId(),
      type: bracketElementType.LEG,
      length: 40,
      width: 40,
      height: 300,
      x: -600,
      y: 600,
    },
    {
      key: uniqueId(),
      type: bracketElementType.LEG,
      length: 40,
      width: 40,
      height: 300,
      x: -600,
      y: -600,
    },
    {
      key: uniqueId(),
      type: bracketElementType.LEG,
      length: 40,
      width: 40,
      height: 300,
      x: 600,
      y: -600,
    },
  ],
};

export const baseFormItemCfgs = (values, mode) => ({
  type: {
    type: "el-select",
    labelText: t("geekplus.gms.client.screen.bracket.columns.containerShape"),
    value: values.type ?? "",
    disabled: true,
    options: bracketShapeType.toLabelValueList(),
    validators: [validators.required],
  },
  name: {
    type: "el-input",
    labelText: t("geekplus.gms.client.screen.bracket.columns.containerName"),
    value: values.name ?? "",
    disabled: mode === "view",
    validators: [validators.required],
    maxlength: 32,
  },
});

export const standModelFormItemCgs = (values, mode) => ({
  modelingMethod: {
    type: "el-select",
    labelText: t("geekplus.gms.client.screen.bracket.columns.containerModel"),
    value: values.modelingMethod ?? "",
    disabled: mode !== "add",
    options: bracketModelType.toLabelValueList().filter((v) => v.value !== bracketModelType.FORKLIFT),
    validators: [validators.required],
  },
  surfaceLength: {
    type: "el-input-number",
    labelText: t("geekplus.gms.client.screen.bracket.form.bracketFaceLength"),
    value: values.modelInfo.surfaceLength ?? "",
    disabled: mode === "view",
    validators: [validators.required],
    min: 0,
    max: 5000,
    labelWidth: "120px",
    labelPosition: "right",
  },
  surfaceWidth: {
    type: "el-input-number",
    labelText: t("geekplus.gms.client.screen.bracket.form.bracketFaceWidth"),
    value: values.modelInfo.surfaceWidth ?? "",
    disabled: mode === "view",
    validators: [validators.required],
    min: 0,
    max: 5000,
    labelWidth: "120px",
    labelPosition: "right",
  },
  legLength: {
    type: "el-input-number",
    labelText: t("geekplus.gms.client.screen.container.form.length"),
    value: values.modelInfo.legLength ?? "",
    disabled: mode === "view",
    validators: [validators.required],
    min: 0,
    max: 5000,
    labelWidth: "120px",
    labelPosition: "right",
  },
  legWidth: {
    type: "el-input-number",
    labelText: t("geekplus.gms.client.screen.container.form.width"),
    value: values.modelInfo.legWidth ?? "",
    disabled: mode === "view",
    validators: [validators.required],
    min: 0,
    max: 5000,
    labelWidth: "120px",
    labelPosition: "right",
  },
  legHeight: {
    type: "el-input-number",
    labelText: t("geekplus.gms.client.screen.container.form.height"),
    value: values.modelInfo.legHeight ?? "",
    disabled: mode === "view",
    validators: [validators.required],
    min: 0,
    max: 5000,
    labelWidth: "120px",
    labelPosition: "right",
  },
  offsetY: {
    type: "el-input-number",
    labelText: t("geekplus.gms.client.screen.bracket.form.offsetY"),
    value: values.modelInfo.offsetY ?? "",
    disabled: mode === "view",
    validators: [validators.required],
    min: -5000,
    max: 5000,
    labelWidth: "120px",
    labelPosition: "right",
  },
});

export const isFace = (type) => type === bracketElementType.FACE;

export const noStandModelFormItemCgs = (values, mode) => ({
  modelingMethod: {
    type: "el-select",
    labelText: t("geekplus.gms.client.screen.bracket.columns.containerModel"),
    value: values.modelingMethod ?? "",
    disabled: mode !== "add",
    validators: [validators.required],
    options: bracketModelType.toLabelValueList().filter((v) => v.value !== bracketModelType.FORKLIFT),
  },
  shelves: {
    type: "el-select",
    labelText: t("geekplus.gms.client.screen.bracket.form.bracketElements"),
    value: values.shelves ?? "",
    disabled: true,
    validators: [validators.required],
    options: bracketElementType.toLabelValueList().filter((v) => !isFace(v.value)),
  },
});

export const getFormItemCfgs = (formValues, mode) => {
  if (formValues.modelingMethod === bracketModelType.STANDARD) {
    return { ...baseFormItemCfgs(formValues, mode), ...standModelFormItemCgs(formValues, mode) };
  }
  return { ...baseFormItemCfgs(formValues, mode), ...noStandModelFormItemCgs(formValues, mode) };
};

export const getFormValues = (formValues) => {
  if (formValues.modelingMethod === bracketModelType.STANDARD) {
    return { ...formValues, ...cloneDeep(standardModel) };
  }
  return { ...formValues, ...cloneDeep(nonStandardModel) };
};

// 标准货架
const standardMapping = (formValues) => ({
  parts: [
    {
      type: containerParts.SURFACE,
      geometricShape: {
        shapeType: geometricShape.RECTANGLE,
        length: formValues.modelInfo.surfaceLength,
        width: formValues.modelInfo.surfaceWidth,
        centerPoint: {
          y: formValues.modelInfo.offsetY,
        },
      },
    },
    {
      type: containerParts.LEG,
      geometricShape: {
        shapeType: geometricShape.RECTANGLE,
        length: formValues.modelInfo.legLength,
        width: formValues.modelInfo.legWidth,
        height: formValues.modelInfo.legHeight,
      },
    },
  ],
});

const noStandardMapping = (formValues) => {
  return {
    parts: formValues.modelInfo.map((v) => {
      const parts = {
        type: v.type === bracketElementType.FACE ? containerParts.SURFACE : containerParts.LEG,
        geometricShape: {
          shapeType: geometricShape.RECTANGLE,
          length: v.length,
          width: v.width,
          height: v.height,
        },
      };
      if (v.type === bracketElementType.LEG) {
        parts.geometricShape.centerPoint = {
          x: v.x,
          y: v.y,
        };
      }
      return parts;
    }),
  };
};

export const getSubmitMappingData = (formValues) => {
  const data = {
    id: formValues.id,
    name: formValues.name,
    type: formValues.type,
    modelingMethod: formValues.modelingMethod,
  };
  if (formValues.modelingMethod === bracketModelType.STANDARD) {
    return { ...data, ...standardMapping(formValues) };
  }
  return { ...data, ...noStandardMapping(formValues) };
};

const standExpandMapping = (record) => {
  const formValues = { modelInfo: {} };
  const face = record.parts.find((v) => v.type === containerParts.SURFACE);
  formValues.modelInfo.surfaceLength = face.geometricShape.length;
  formValues.modelInfo.surfaceWidth = face.geometricShape.width;
  formValues.modelInfo.offsetY = face.geometricShape?.centerPoint?.y;

  const leg = record.parts.find((v) => v.type === containerParts.LEG);
  formValues.modelInfo.legLength = leg?.geometricShape?.length;
  formValues.modelInfo.legWidth = leg?.geometricShape?.width;
  formValues.modelInfo.legHeight = leg?.geometricShape?.height;

  return formValues;
};

const noStandardExpandMapping = (record) => {
  return {
    shelves: bracketElementType.LEG,
    modelInfo: record.parts.map((v) => {
      const model = {
        key: uniqueId(),
      };
      if (v.type === containerParts.SURFACE) {
        model.type = bracketElementType.FACE;
        model.length = v.geometricShape.length;
        model.width = v.geometricShape.width;
        model.offsetY = v.geometricShape?.centerPoint?.y;
        model.x = 0;
        model.y = 0;
        model.faceX = 0;
        model.faceY = 0;
      }
      if (v.type === containerParts.LEG) {
        model.type = bracketElementType.LEG;
        model.length = v.geometricShape.length;
        model.width = v.geometricShape.width;
        model.height = v.geometricShape.height;
        model.x = v.geometricShape.centerPoint.x;
        model.y = v.geometricShape.centerPoint.y;
      }
      return model;
    }),
  };
};

export const getExpandModelMapping = (record) => {
  const formValues = {
    id: record.id,
    name: record.name,
    type: record.type,
    modelingMethod: record.modelingMethod,
    modelInfo: {},
  };
  if (record.modelingMethod === bracketModelType.STANDARD) {
    return { ...formValues, ...standExpandMapping(record) };
  }
  return { ...formValues, ...noStandardExpandMapping(record) };
};
