/**
 *   呼叫器页面国际化字符集
 *   key 必须以 'geekplus.gms.client.screen.callmaker.' 开头
 */

{
  "geekplus.gms.client.screen.callmaker.callmakerConfig": "呼叫器配置",
  "geekplus.gms.client.screen.callmaker.intervalSetting": "任务触发间隔时间设置",
  "geekplus.gms.client.screen.callmaker.callmakerName": "呼叫器名称",
  "geekplus.gms.client.screen.callmaker.callmakerId": "呼叫器编号",
  "geekplus.gms.client.screen.callmaker.callmakerModel": "呼叫器型号",
  "geekplus.gms.client.screen.callmaker.model4keys": "4键呼叫器",
  "geekplus.gms.client.screen.callmaker.IpAddress": "ip地址",
  "geekplus.gms.client.screen.callmaker.status": "状态",
  "geekplus.gms.client.screen.callmaker.creator": "创建人",
  "geekplus.gms.client.screen.callmaker.createTime": "创建时间",
  "geekplus.gms.client.screen.callmaker.interval": "任务触发间隔时间",
  "geekplus.gms.client.screen.callmaker.createCallmaker": "新建呼叫器",
  "geekplus.gms.client.screen.callmaker.callmakerDetail": "呼叫器详情",
  "geekplus.gms.client.screen.callmaker.trigger": "触发",
  "geekplus.gms.client.screen.callmaker.btn.disabled": "未启用",
  "geekplus.gms.client.screen.callmaker.btn.enabled": "已启用",
  "geekplus.gms.client.screen.callmaker.label.unconfigured": "未配置",
  "geekplus.gms.client.screen.callmaker.label.configured": "已配置",
  "geekplus.gms.client.screen.callmaker.label.buttonEnabled": "按钮启用",
  "geekplus.gms.client.screen.callmaker.label.buttonFunction": "按钮功能",
  "geekplus.gms.client.screen.callmaker.label.buttonType": "按钮类型",
  "geekplus.gms.client.screen.callmaker.label.buttonCode": "按钮编号",
  "geekplus.gms.client.screen.callmaker.label.buttonType.selfRecovery": "自复位",
  "geekplus.gms.client.screen.callmaker.label.buttonType.selfLock": "自锁式",
  "geekplus.gms.client.screen.callmaker.label.flowControl": "流程控制",
  "geekplus.gms.client.screen.callmaker.label.flow": "流程",
  "geekplus.gms.client.screen.callmaker.label.nodeControl": "节点控制",
  "geekplus.gms.client.screen.callmaker.label.systemControl": "系统控制",
  "geekplus.gms.client.screen.callmaker.label.operationCommand": "操作指令",
  "geekplus.gms.client.screen.callmaker.label.operationCommand.start": "开始",
  "geekplus.gms.client.screen.callmaker.label.triggerMethod": "触发方式",
  "geekplus.gms.client.screen.callmaker.label.codePoint": "点位",
  "geekplus.gms.client.screen.callmaker.label.pointCode": "点位编码",
  "geekplus.gms.client.screen.callmaker.label.destType": "目的地类型",

  // tips
  "geekplus.gms.client.screen.callmaker.tip.intervalSetting": "单个按钮，在任务触发间隔时间内，再次按下，则不会触发任务，避免短时间内多次操作。",
  "geekplus.gms.client.screen.callmaker.tip.callmakerId": "呼叫器盒子的唯一编号，可通过外包装盒子获得，或者通过按钮日志中获取到",
  "geekplus.gms.client.screen.callmaker.tip.confirmDelete": "确定删除{name}吗？",
  "geekplus.gms.client.screen.callmaker.tip.operationCommand": "操作指令的帮助文档",
  "geekplus.gms.client.screen.callmaker.tip.triggerMethod": "触发方式的帮助文档",
  "geekplus.gms.client.screen.callmaker.tip.targetPoint": "目标点的帮助文档",
}
