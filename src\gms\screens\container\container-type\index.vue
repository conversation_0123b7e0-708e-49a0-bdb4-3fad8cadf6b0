<template>
  <div class="tw-flex-grow tw-flex tw-flex-col tw-gap-5" :class="cn.root">
    <SelectShape
      v-if="state.step === 1 && mode !== 'view'"
      :step.sync="state.step"
      :type="state.formValues.type"
      :mode="$props.mode"
      :class="cn.select"
      @update:type="updateType"
      @update:mode="$emit('update:mode', $event)"
    />
    <ConfigForm
      v-if="state.step === 2 || mode === 'view'"
      :step.sync="state.step"
      :form-values.sync="state.formValues"
      :form-group="state.formGroup"
      :mode="$props.mode"
      :handle-submit="handleSubmit"
      :class="cn.form"
      @update:formValues="emit('update:formValues', $event)"
    />
    <section class="tw-text-center tw-h-8 tw--mb-2">
      <template v-if="$props.mode !== 'view'">
        <gp-button v-if="showCancelButton" @click="handleCancel">
          {{ $t("geekplus.gms.client.commons.btn.cancel") }}
        </gp-button>
        <gp-button v-if="state.step === 2 && mode === 'add'" @click="handlePrev">
          {{ $t("geekplus.gms.client.commons.btn.prevStep") }}
        </gp-button>
        <gp-button v-if="state.step === 1" type="primary" @click="handleSubmit">
          {{ $t("geekplus.gms.client.commons.btn.nextStep") }}
        </gp-button>
        <gp-button
          v-if="state.step === 2"
          :loading="loading"
          type="primary"
          :disabled="loading || $props.mode === 'view'"
          @click="handleSubmit"
        >
          {{ $t("geekplus.gms.client.commons.btn.save") }}
        </gp-button>
      </template>
      <gp-button v-else @click="handleCancel">{{
        $t("geekplus.gms.client.screen.container.btns.cancelView")
      }}</gp-button>
    </section>
  </div>
</template>

<script>
import { cloneDeep } from "lodash";
import { Message } from "geekplus-ui";
import { defineComponent, reactive, onMounted, computed, ref } from "vue";
import { containerTypeShape, containerModel } from "gms-constants";
import { createFormItemGroup } from "@srpe-fe/uic-el-form-wrapper-vue2";
import { useI18n } from "@/hooks";
import { putContainerType, addContainerType } from "gms-apis/container";
import SelectShape from "./select-shape.vue";
import ConfigForm from "./config-form";
import {
  commonValues,
  standardModel,
  getFormItemCfgs,
  getFormValues,
  pallets,
  getSubmitMappingData,
  getExpandModelMapping,
} from "./state";
import { shelfElementType } from "@/common/constants";
const commonKeys = Object.keys(commonValues);

const initialFormValues = { ...commonValues, ...standardModel };

export default defineComponent({
  name: "ContainerType",
  components: { SelectShape, ConfigForm },
  props: {
    record: { type: Object, default: () => ({}) },
    mode: { type: String, default: "add" },
  },
  emits: [],
  setup(props, { emit }) {
    const loading = ref(false);
    const t = useI18n();
    const state = reactive({
      formValues: cloneDeep(initialFormValues),
      formGroup: {},
      formGroupList: [],
      step: 1,
      shape: containerTypeShape.PALLET_RACKING,
    });

    const initFormGroup = () => {
      const formItemCfgs = getFormItemCfgs(state.formValues, props.mode);
      state.formGroup = createFormItemGroup(formItemCfgs, {
        handleGroupChange,
        labelWidth: "120px",
        labelPosition: "right",
        mode: props.mode === "view" ? "view" : "edit",
      });
    };

    const initFormValues = () => {
      state.formValues = getFormValues(state.formValues);
    };

    const init = () => {
      initFormValues();
      initFormGroup();
    };

    const modelInfoKeys = computed(() => Object.keys(state.formValues.modelInfo));

    const isNoStandard = computed(
      () => state.formValues.modelingMethod === containerModel.NON_STANDARD && !pallets.includes(state.formValues.type)
    );

    const resetForkpallet = () => {
      const {
        id,
        code,
        name,
        modelInfo: { modelingMethod, palletStructure, handleableRobotModel },
      } = state.formValues;
      initFormValues();

      state.formValues = {
        ...state.formValues,
        id,
        code,
        name,
        modelInfo: {
          ...state.formValues.modelInfo,
          modelingMethod,
          palletStructure,
          handleableRobotModel,
        },
      };
    };

    function handleGroupChange({ name, value }) {
      // 更新commonValues
      commonKeys.forEach((key) => {
        if (name === key) {
          state.formValues[name] = value;
        }
      });
      // 更新formValues.modelInfo
      modelInfoKeys.value?.forEach((key) => {
        if (name === key) {
          state.formValues.modelInfo[name] = value;
        }
      });
      if (name === state.formGroup?.modelingMethod?.name) {
        init();
        if (state.formValues.type === containerTypeShape.FORKLIFT_PALLET) {
          state.formValues.modelInfo[name] = value;
        }
        state.formValues[name] = value;
      } else if (name === state.formGroup.handleableRobotModel?.name) {
        if (state.formValues.type === containerTypeShape.FORKLIFT_PALLET) {
          resetForkpallet();
        }
        initFormGroup();
      }
      state.formGroup.$setData({ [name]: value });
    }

    const handlePrev = () => {
      state.step = 1;
    };

    const handleSubmit = () => {
      if (state.step == 1) {
        // 请选择容器形态
        state.step = 2;
        return;
      }
      state.formGroup.$validate((isValid) => {
        if (isValid) {
          if (
            isNoStandard.value &&
            state.formValues.modelInfo.filter((v) => v.type === shelfElementType.LEG).length < 2
          ) {
            Message.error(t("geekplus.gms.client.screen.container.tips.atLeast2Legs"));
            return;
          }
          loading.value = true;
          putContainerType(getSubmitMappingData(state.formValues))
            .then(() => {
              Message.success(t("lang.ark.fed.savedSuccessfully"));
              emit("update:mode", "list");
              resetFormValues();
            })
            .finally(() => (loading.value = false));
        }
      });
    };

    const showCancelButton = computed(() => {
      if (props.mode === "add" && state.step === 1) return true;
      if (props.mode !== "add" && state.step === 2) return true;
      return false;
    });

    function handleCancel() {
      emit("update:mode", "list");
      resetFormValues();
    }

    function resetFormValues() {
      state.formValues = cloneDeep(initialFormValues);
    }

    onMounted(() => {
      init();

      if (props.mode !== "add") {
        const record = cloneDeep(props.record);
        state.formValues = getExpandModelMapping(record);
        initFormGroup();

        // 编辑和查看详情模式下，都不需要显示第一页
        if (["edit", "view"].includes(props.mode)) {
          state.step = 2;
        }
      }
      if (props.mode === "add") {
        addContainerType({
          code: "",
          name: "",
          type: containerTypeShape.filter((v) => v.value !== containerTypeShape.GROUND_SUPPORT).map((v) => v.value),
        }).then((data) => {
          state.formGroup.code.value = data.code;
          state.formValues.code = data.code;
          state.formValues.id = data.id;
        });
      }
    });

    const updateType = (type) => {
      const { code, id } = state.formValues;
      resetFormValues();
      state.formValues.type = type;
      state.formValues.code = code;
      state.formValues.id = id;
      init();
    };

    return {
      state,
      loading,
      showCancelButton,
      emit,
      handlePrev,
      handleCancel,
      updateType,
      handleSubmit,
    };
  },
});
</script>

<style lang="scss" module="cn">
.root {
  .select {
    height: calc(100vh - 254px);
    min-height: 300px;
    overflow-y: scroll;
  }
  .form {
    height: calc(100vh - 264px);
    min-height: 650px;
  }
}
</style>
