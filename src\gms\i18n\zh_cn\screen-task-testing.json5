/**
 *   任务调试页面国际化字符集
 *   key 必须以 'geekplus.gms.client.screen.taskTesting.' 开头
 */
{
  "geekplus.gms.client.screen.taskTesting.pageTitle": "任务调试",
  "geekplus.gms.client.screen.taskTesting.pageDesc": "可对配置的流程和点位进行调试和批量测试",
  "geekplus.gms.client.screen.taskTesting.placeholderTaskName": "任务名称",

  "geekplus.gms.client.screen.taskTesting.menu.routeTest": "路线点位调试",
  "geekplus.gms.client.screen.taskTesting.menu.workflowTest": "流程调试",

  "geekplus.gms.client.screen.taskTesting.btn.addRouteTest": "新增路线点位调试",
  "geekplus.gms.client.screen.taskTesting.btn.addWorkflowTest": "新增流程调试",

  "geekplus.gms.client.screen.taskTesting.btn.batchTest": "批量任务测试",
  "geekplus.gms.client.screen.taskTesting.btn.saveBinSpaceCofnig": "保存点位参数",
  "geekplus.gms.client.screen.taskTesting.btn.startSelected": "一键开始",
  "geekplus.gms.client.screen.taskTesting.btn.pauseSelected": "一键暂停",
  "geekplus.gms.client.screen.taskTesting.btn.deleteSelected": "一键删除",

  "geekplus.gms.client.screen.taskTesting.link.batchConfig": "批量配置 >",

  "geekplus.gms.client.screen.taskTesting.column.id": "任务ID",
  "geekplus.gms.client.screen.taskTesting.column.name": "名称",
  "geekplus.gms.client.screen.taskTesting.column.type": "类型",
  "geekplus.gms.client.screen.taskTesting.column.status": " 状态",
  "geekplus.gms.client.screen.taskTesting.column.progress": "执行进度",
  "geekplus.gms.client.screen.taskTesting.column.createTime": "创建时间",
  "geekplus.gms.client.screen.taskTesting.column.cargoCode": "货位编码",
  "geekplus.gms.client.screen.taskTesting.column.cargoOrder": "货位顺序",
  "geekplus.gms.client.screen.taskTesting.column.dockHeight": "对接高度",
  "geekplus.gms.client.screen.taskTesting.column.dropHeight": "放料高度",
  "geekplus.gms.client.screen.taskTesting.column.pickHeight": "取料高度",
  "geekplus.gms.client.screen.taskTesting.column.equipType": "设备类型",

  "geekplus.gms.client.screen.taskTesting.listFilterPlaceholder.taskType": "任务类型",
  "geekplus.gms.client.screen.taskTesting.listFilterPlaceholder.taskStatus": "任务状态",

  "geekplus.gms.client.screen.taskTesting.msg.stopConfirm": "您只能中止掉还未生成的任务， 若您希望取消正在执行的任务，您可以前往任务监控页面进行取消操作。确认是否中止？",
  "geekplus.gms.client.screen.taskTesting.msg.nodeListEmpty": "请先指定流程模板",
  "geekplus.gms.client.screen.taskTesting.msg.taskStartSucceed": "任务开始成功",
  "geekplus.gms.client.screen.taskTesting.msg.taskPauseSucceed": "任务暂停成功",
  "geekplus.gms.client.screen.taskTesting.msg.taskSuspendSucceed": "任务中止成功",
  "geekplus.gms.client.screen.taskTesting.msg.taskRemoveSucceed": "任务删除成功",
  "geekplus.gms.client.screen.taskTesting.msg.cargoInfoSaveSucceed": "点位参数保存成功",
  "geekplus.gms.client.screen.taskTesting.msg.cargoInfoNotFound": "暂未查询到选定的点位的参数配置，您可先进行批量配置",
  "geekplus.gms.client.screen.taskTesting.msg.flowTemplateExpired": "当前模板数据发生变化，请重新选择",
  "geekplus.gms.client.screen.taskTesting.msg.flowTemplateExpiredWhenStart": "当前模板数据发生变化，请重新编辑",
  "geekplus.gms.client.screen.taskTesting.msg.flowPositionValueMismatch": "地图点位数据发生变化，请重新配置",

  "geekplus.gms.client.screen.taskTesting.header.routeTestNew": "新建路线点位调试",
  "geekplus.gms.client.screen.taskTesting.header.routeTestEdit": "编辑路线点位调试",
  "geekplus.gms.client.screen.taskTesting.header.routeTestView": "路线点位调试详情",
  "geekplus.gms.client.screen.taskTesting.header.routeTestCopy": "复制路线点位调试",
  "geekplus.gms.client.screen.taskTesting.header.flowTestNew": "新建流程调试",
  "geekplus.gms.client.screen.taskTesting.header.flowTestCopy": "复制流程调试",
  "geekplus.gms.client.screen.taskTesting.header.flowTestEdit": "编辑流程调试",
  "geekplus.gms.client.screen.taskTesting.header.flowTestView": "流程调试详情",
  "geekplus.gms.client.screen.taskTesting.sectionTitle.basicInfo": "基础信息",
  "geekplus.gms.client.screen.taskTesting.sectionTitle.runningPlan": "执行计划配置",
  "geekplus.gms.client.screen.taskTesting.sectionTitle.flowTemplate": "流程配置",
  "geekplus.gms.client.screen.taskTesting.sectionTitle.nodeConfig": "点位配置",

  "geekplus.gms.client.screen.taskTesting.form.labelTaskName": "任务名称",
  "geekplus.gms.client.screen.taskTesting.form.labelRobotType": "机器人类型",
  "geekplus.gms.client.screen.taskTesting.form.labelRobotAssignMode": "指定模式",
  "geekplus.gms.client.screen.taskTesting.form.labelLoadType": "空负载",
  "geekplus.gms.client.screen.taskTesting.form.labelTestCount": "执行次数",
  "geekplus.gms.client.screen.taskTesting.form.labelTestMode": "调试模式",
  "geekplus.gms.client.screen.taskTesting.form.labelFlowTemplate": "流程模板",
  "geekplus.gms.client.screen.taskTesting.form.labelTemplateType": "模板类型",
  "geekplus.gms.client.screen.taskTesting.form.labelPickDrop": "取放",
  "geekplus.gms.client.screen.taskTesting.form.labelEmptyOrFull": "空满",
  "geekplus.gms.client.screen.taskTesting.form.labelRobotOrient": "机器人面朝向",
  "geekplus.gms.client.screen.taskTesting.form.labelWorkflowEnd": "是否流程终点",
  "geekplus.gms.client.screen.taskTesting.form.labelAssignFlow": "指定流程",
  "geekplus.gms.client.screen.taskTesting.form.labelAssignFlowTemplate": "指定流程模板",
  "geekplus.gms.client.screen.taskTesting.form.labelStartPointCode": "起点编码",
  "geekplus.gms.client.screen.taskTesting.form.labelTargetPointCode": "目标点编码",
  "geekplus.gms.client.screen.taskTesting.form.labelMiddlePointCode": "中间点编码",
  "geekplus.gms.client.screen.taskTesting.form.labelFlowAssignMode": "指定模式",
  "geekplus.gms.client.screen.taskTesting.form.labelDeviceCode": "机台/缓存架编码",
  "geekplus.gms.client.screen.taskTesting.form.labelStationStatus": "机台状态",
  "geekplus.gms.client.screen.taskTesting.form.labelRollLength": "卷料长度",
  "geekplus.gms.client.screen.taskTesting.form.labelRollType": "卷料类型",
  "geekplus.gms.client.screen.taskTesting.form.labelSafetyHeight": "安全高度",
  "geekplus.gms.client.screen.taskTesting.form.labelExtraParam15": "参数15",
  "geekplus.gms.client.screen.taskTesting.form.labelExtraParam16": "参数16",
  "geekplus.gms.client.screen.taskTesting.form.labelExtraParam17": "参数17",

  "geekplus.gms.client.screen.taskTesting.form.testModeFlow": "指定流程模板",
  "geekplus.gms.client.screen.taskTesting.form.testModeBus": "按业务场景组合",
  "geekplus.gms.client.screen.taskTesting.form.assignModeById": "指定机器人ID",
  "geekplus.gms.client.screen.taskTesting.form.assignModeByModel": "指定机器人型号",
  "geekplus.gms.client.screen.taskTesting.form.placeholderTaskName": "请输入任务名称",
  "geekplus.gms.client.screen.taskTesting.form.tipLoadType": "负载取放测试, 会执行排队模式, 按选点顺序执行, 到终点后自动把容器再送回起点.",
  "geekplus.gms.client.screen.taskTesting.form.namePick": "指定流程模板",

  "geekplus.gms.client.screen.taskTesting.robotType.cantilever": "悬臂机器人",
  "geekplus.gms.client.screen.taskTesting.robotType.topModule": "举升机器人",
  "geekplus.gms.client.screen.taskTesting.robotType.carrier": "潜伏机器人",
  "geekplus.gms.client.screen.taskTesting.robotType.roller": "辊筒机器人",
  "geekplus.gms.client.screen.taskTesting.robotType.forklift": "叉车机器人",

  "geekplus.gms.client.screen.taskTesting.flowOrTempOpts.flow": "流程",
  "geekplus.gms.client.screen.taskTesting.flowOrTempOpts.temp": "流程模板",

  "geekplus.gms.client.screen.taskTesting.dialogCargo.title": "选择目标点",
}
