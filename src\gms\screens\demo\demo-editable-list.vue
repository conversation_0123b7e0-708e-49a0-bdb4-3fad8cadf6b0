<template>
  <div v-loading="!positionHelper.optionsLoaded" class="tw-p-4">
    <section :class="cn.section">
      <p>
        默认使用 <code>input</code> 作为编辑控件。默认使用虚拟滚动，使用
        <code>scroller-height</code> 设定列表区域的高度。
      </p>
      <div class="tw-mt-4">
        <editable-list :initial-items="initialItems" scroller-height="150px" @change="handleChangeList" />
      </div>
    </section>

    <gp-divider direction="horizontal" />

    <section :class="cn.section">
      <p>可以使用 view 和 edit 插槽自定义内容</p>
      <div class="tw-my-4">
        <gp-button type="primary" @click="handleAddCustomItem">增加元素</gp-button>
        <gp-button type="primary" @click="handleAddMultiCustomItems">批量导入</gp-button>
        <gp-button type="primary" @click="clearAllItems">清空元素</gp-button>
        <gp-input v-model="customState.query" clearable placeholder="请输入搜索关键词" :class="cn.search_input" />
      </div>
      <div class="tw-mt-4">
        <editable-list
          ref="customEditableListRef"
          :initial-items="customInitialItems"
          :before-save="beforeSaveEditableItem"
          :scroller-height="'250px'"
          :keyword="customState.query"
          :filter-method="customFilterMethod"
          @change="handleChangeCustomList"
        >
          <template #view="{ index, item }">
            {{ index + 1 }}: 第一个点[{{ item.first.label }}]，第二个点[{{ item.second.label }}]
          </template>
          <template #edit="{ index, item, handleChange }">
            <div class="tw-flex tw-items-center tw-gap-2">
              <div class="tw-shrink-0">{{ index + 1 }}:</div>
              <PositionSelect
                :class="cn.select"
                :value="item?.first?.value"
                :groups="customState.propGroups"
                :clearable="customState.clearable"
                :filterable="true"
                @change="
                  (value) =>
                    handleChange({ value: { ...item, first: { value, label: positionHelper.getLabel(value) } } })
                "
              />
              <PositionSelect
                :class="cn.select"
                :value="item?.second?.value"
                :groups="customState.propGroups"
                :clearable="customState.clearable"
                :filterable="true"
                @change="
                  (value) =>
                    handleChange({ value: { ...item, second: { value, label: positionHelper.getLabel(value) } } })
                "
              />
            </div>
          </template>
        </editable-list>
      </div>
    </section>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted, watch } from "vue";
import { Message } from "geekplus-ui";
import EditableList from "gms-components/business/editable-list/index.vue";
import PositionSelect from "gms-components/business/position-select";
import positionSelectHelper from "@/gms/commons/position-select-helper";

const NUM_ITEMS = 2000;

export default defineComponent({
  name: "DemoEditableList",
  components: {
    EditableList,
    PositionSelect,
  },

  setup() {
    const positionHelper = reactive(positionSelectHelper);
    onMounted(async () => {
      await positionHelper.loadOptions();
    });

    // 默认的表单控件
    const initialItems = ref(["ABC", "Foobar"]);
    function handleChangeList(value) {
      initialItems.value = value;
    }

    const defaultPositionData = [
      [
        ["GENERAL_POINT", "00150195"],
        ["GENERAL_POINT", "00150205"],
      ],
      [
        ["WORKSTATION", "S1318"],
        ["GENERAL_POINT", "00150245"],
      ],
    ];

    // 自定表单控件
    const customInitialItems = ref([]);
    watch(
      () => positionHelper.optionsLoaded,
      (value) => {
        // 点位加载完毕后，生成标签
        if (value) {
          customInitialItems.value = defaultPositionData.map(([first, second], index) => ({
            id: index + 1,
            first: {
              value: first,
              label: positionHelper.getLabel(first),
            },
            second: {
              value: second,
              label: positionHelper.getLabel(second),
            },
          }));
        }
      }
    );

    const customEditableListRef = ref(null);
    const customState = reactive({
      propGroups: ["GENERAL_POINT", "WORKSTATION"],
      clearable: true,
      query: "",
    });

    function customFilterMethod(item, keyword) {
      if (item.first === undefined || item.second === undefined) return false;
      return item?.first?.label?.includes(keyword) || item?.second?.label?.includes(keyword);
    }

    function handleAddCustomItem() {
      customEditableListRef.value.addItem();
    }

    function handleAddMultiCustomItems() {
      const manyItems = [];
      const values = [
        ["GENERAL_POINT", "00150195"],
        ["GENERAL_POINT", "00150205"],
      ];
      const labels = values.map((item) => positionHelper.getLabel(item));
      for (let i = 0; i < NUM_ITEMS; i++) {
        manyItems.push({
          id: i + 1,
          first: {
            value: values[0],
            label: labels[0],
          },
          second: {
            value: values[1],
            label: labels[1],
          },
        });
      }

      customInitialItems.value = manyItems;
    }

    function clearAllItems() {
      customInitialItems.value = [];
    }

    function handleChangeCustomList(value) {
      customInitialItems.value = value;
    }

    function beforeSaveEditableItem(item) {
      if (!item?.first) {
        Message.error("第一个点位不能为空");
        return false;
      }

      if (!item?.second) {
        Message.error("第二个点位不能为空");
        return false;
      }

      return true;
    }

    return {
      initialItems,
      customInitialItems,
      customEditableListRef,
      customState,
      positionHelper,
      customFilterMethod,
      beforeSaveEditableItem,
      handleAddCustomItem,
      handleAddMultiCustomItems,
      clearAllItems,
      handleChangeList,
      handleChangeCustomList,
    };
  },
});
</script>

<style lang="scss" module="cn">
.section {
  width: 640px;
}

.select {
  width: 200px;
}

.search_input {
  width: 200px;
  margin-left: 8px;
}
</style>
