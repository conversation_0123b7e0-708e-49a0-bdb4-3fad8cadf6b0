/**
 * 流程页国际化字符集
 * key 必须以 'geekplus.gms.client.screen.flowConfig.' 开头
 */

 {
  "geekplus.gms.client.screen.flowConfig.title": "流程",
  "geekplus.gms.client.screen.flowConfig.subtitle": "可以将不同的业务场景编排成机器人工作流程，通过流程驱动机器人执行搬运任务",
  "geekplus.gms.client.screen.flowConfig.manualConfigFlow": "手动配置流程",
  "geekplus.gms.client.screen.flowConfig.templateBasedFlow": "流程模板生成流程",
  "geekplus.gms.client.screen.flowConfig.notCheckIfIdleRobots": "不判断是否空闲机器人",
  "geekplus.gms.client.screen.flowConfig.taskQueueIfNoIdleRobots": "无可用空闲机器人任务排队",
  "geekplus.gms.client.screen.flowConfig.taskFailIfNoIdleRobots": "无可用空闲机器人任务失败",
  "geekplus.gms.client.screen.flowConfig.taskLaunchRule": "任务发起策略",

  "geekplus.gms.client.screen.flowConfig.nodeForm.workstationCode": "工作站编码",
  "geekplus.gms.client.screen.flowConfig.nodeForm.areaCode": "区域编码",
  "geekplus.gms.client.screen.flowConfig.nodeForm.flow": "流程",
  "geekplus.gms.client.screen.flowConfig.nodeForm.equipmentPoint": "设备点位",
  "geekplus.gms.client.screen.flowConfig.nodeForm.pallet": "托盘位",
  "geekplus.gms.client.screen.flowConfig.nodeForm.mapCode": "点位编码",
  "geekplus.gms.client.screen.flowConfig.nodeForm.queueAreaActions": "排队区交互",
  "geekplus.gms.client.screen.flowConfig.nodeType.equipment": "设备点位",
  "geekplus.gms.client.screen.flowConfig.dragItem.equipment": "设备点位",
  "geekplus.gms.client.screen.flowConfig.dragItem.mapPoint": "地图点位",
  "geekplus.gms.client.screen.flowConfig.dragItem.pallet": "托盘位",
  "geekplus.gms.client.screen.flowConfig.nodeForm.nodeType": "点位类型",
  "geekplus.gms.client.screen.flowConfig.nodeForm.nodeCode": "点位编码",
}
