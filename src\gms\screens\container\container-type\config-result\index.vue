<template>
  <div>
    <!-- 搬运托盘 -->
    <PalletAnnotation v-if="isMovePallet" v-bind="$props" />
    <!-- 料箱/料车 -->
    <CabinetsAnnotation
      v-if="isMaterialBox"
      v-bind="$props"
      @update:active="$emit('update:active', $event)"
      @update:formValues="$emit('update:formValues', $event)"
    />
    <!-- 笼车 -->
    <TrolleyAnnotation
      v-if="isTrolley"
      v-bind="$props"
      @update:active="$emit('update:active', $event)"
      @update:formValues="$emit('update:formValues', $event)"
    />
    <!-- 货架 -->
    <ShelfAnnotation
      v-if="isShelf"
      v-bind="$props"
      @update:active="$emit('update:active', $event)"
      @update:formValues="$emit('update:formValues', $event)"
    />
    <!-- 叉车托盘 -->
    <ForkPalletAnnotation v-if="isForkPallet" v-bind="$props" />
  </div>
</template>
<script>
import { defineComponent, computed } from "vue";
import { containerTypeShape } from "gms-constants";
import ShelfAnnotation from "./shelf-annotation.vue";
import ForkPalletAnnotation from "./fork-pallet-annotation.vue";
import PalletAnnotation from "./pallet-annotation.vue";
import CabinetsAnnotation from "./cabinets-annotation.vue";
import TrolleyAnnotation from "./trolley-annotation.vue";
const materialBoxs = [containerTypeShape.CONTAINER_BIN, containerTypeShape.MATERIAL_BOX];

export default defineComponent({
  name: "ConfigResult",
  components: { ShelfAnnotation, ForkPalletAnnotation, PalletAnnotation, CabinetsAnnotation, TrolleyAnnotation },
  props: {
    formValues: { type: Object, default: () => ({}) },
    active: { type: String, default: "" },
  },
  emits: ["update:active"],
  setup(props) {
    const isShelf = computed(() => props.formValues.type === containerTypeShape.PALLET_RACKING);
    const isMovePallet = computed(() => props.formValues.type === containerTypeShape.PALLET);
    const isForkPallet = computed(() => props.formValues.type === containerTypeShape.FORKLIFT_PALLET);
    const isTrolley = computed(() => props.formValues.type === containerTypeShape.TROLLEY);
    const isMaterialBox = computed(() => materialBoxs.includes(props.formValues.type));

    return { isShelf, isForkPallet, isMovePallet, isMaterialBox, isTrolley };
  },
});
</script>
