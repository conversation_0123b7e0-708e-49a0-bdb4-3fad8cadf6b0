<template>
  <gp-select v-bind="$attrs" filterable :placeholder="$t('lang.ark.fed.common.placeholder.select')" v-on="$listeners">
    <gp-option
      v-for="item in $attrs.options"
      :key="item.value"
      :label="item.label"
      :value="item.value"
      :disabled="item.disabled"
    />
  </gp-select>
</template>
<script>
import { defineComponent } from "vue";

export default defineComponent({
  name: "MSelect",
});
</script>
