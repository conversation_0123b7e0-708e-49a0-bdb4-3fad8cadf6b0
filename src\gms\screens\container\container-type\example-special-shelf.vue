<template>
  <gms-dialog :visible="visible" width="50%" :is-need-footer="false" @closed="$emit('update:visible', false)">
    <template #title>
      <h3 :class="cn.big_title">{{ $t("geekplus.gms.client.screen.container.form.viewExample") }}</h3>
    </template>
    <div :class="cn.dialog_body">
      <section :class="cn.section_wrapper">
        <h3 style="text-align: center">
          {{ $t("geekplus.gms.client.screen.container.form.onlyRectangularSupported") }}
        </h3>
        <h3>{{ $t("geekplus.gms.client.screen.container.form.referActualShelfFigure") }}</h3>
        <div :class="cn.red_tips">
          <p>{{ $t("geekplus.gms.client.screen.container.form.notes") }}</p>
          <p>{{ $t("geekplus.gms.client.screen.container.form.shelfSurfaceApplied") }}</p>
          <p>{{ $t("geekplus.gms.client.screen.container.form.shelfLegsApplied") }}</p>
          <p>{{ $t("geekplus.gms.client.screen.container.form.regularRectangleApplied") }}</p>
          <p>{{ $t("geekplus.gms.client.screen.container.form.aboutMeasurement") }}</p>
        </div>
        <img :src="trayImg" alt="" />
      </section>
      <section :class="cn.section_wrapper">
        <h3>{{ $t("geekplus.gms.client.screen.container.form.configurationFaceLegs") }}</h3>
        <p>{{ $t("geekplus.gms.client.screen.container.form.measureDistanceLeg") }}</p>
        <img :src="shelfSurfaceImg" alt="" />
      </section>
    </div>
  </gms-dialog>
</template>

<script>
import { defineComponent } from "vue";
import getContainerImage from "@/utils/containerImages";
const trayImg = getContainerImage("tray.png");
const shelfSurfaceImg = getContainerImage("shelf-surface.png");

export default defineComponent({
  props: {
    visible: { type: Boolean, default: false },
  },
  setup() {
    return {
      trayImg,
      shelfSurfaceImg,
    };
  },
});
</script>
<style lang="scss" module="cn">
.dialog_body {
  margin-top: -30px;
  font-size: 14px;
  .section_wrapper {
    margin: 30px 20px;
    &:first-child {
      margin-top: 0;
    }
    .red_tips {
      color: #cb2421;
      margin: 20px 0;
      font-size: 14px;
    }
  }
}
h3,
.title {
  font-size: 14px;
  font-weight: 700;
  line-height: 2;
}
.big_title {
  font-size: 18px;
}
</style>
