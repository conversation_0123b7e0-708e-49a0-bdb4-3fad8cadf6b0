<template>
  <gms-dialog
    :visible="visible"
    width="45%"
    :title="title || $t('geekplus.gms.client.screen.container.tips.operationFailed')"
    @closed="$emit('update:visible', false)"
  >
    <div v-for="item in list" :key="item.containerCode">
      <p class="tw-leading-loose">
        {{ $t("geekplus.gms.client.screen.container.columns.containerCode") }}：{{ item.loadCarrierCode }} &nbsp;
        {{ $t("geekplus.gms.client.screen.container.tips.failReason") }}：{{ $t(item.message) }}
      </p>
    </div>
    <template #footer>
      <gp-button type="primary" @click="$emit('update:visible', false)">
        {{ $t("geekplus.gms.client.commons.btn.confirm") }}
      </gp-button>
    </template>
  </gms-dialog>
</template>
<script>
import { defineComponent } from "vue";

export default defineComponent({
  name: "DialogOperateResult",
  props: {
    visible: { type: Boolean, default: false },
    title: { type: String, default: "" },
    list: { type: Array, default: () => [] },
  },
  emits: ["update:visible"],
  setup() {},
});
</script>
