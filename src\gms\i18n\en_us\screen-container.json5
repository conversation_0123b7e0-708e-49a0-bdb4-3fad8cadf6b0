{
  "geekplus.gms.client.screen.container.tab.bracketModel": "Bracket type",
  "geekplus.gms.client.screen.container.containerManagement": "Container Management",
  "geekplus.gms.client.screen.container.containerType": "Container type",
  "geekplus.gms.client.screen.container.containerCode": "Container code",
  "geekplus.gms.client.screen.container.btns.addContainerType": "New container type",
  "geekplus.gms.client.screen.container.btns.addContainer": "New Container",
  "geekplus.gms.client.screen.container.btns.editContainer": "Edit container",
  "geekplus.gms.client.screen.container.btns.containerEntry": "Entry",
  "geekplus.gms.client.screen.container.btns.containerLeave": "Leave",
  "geekplus.gms.client.screen.container.btns.batchAdd": "Add in batch",
  "geekplus.gms.client.screen.container.btns.saveAndEntry": "Save and enter",
  "geekplus.gms.client.screen.container.btns.cancelView": "Cancel viewing",
  "geekplus.gms.client.screen.container.btns.viewContainerType": "View container specifications",
  "geekplus.gms.client.screen.container.form.searchParams": "Container code",
  "geekplus.gms.client.screen.container.form.containerTitle": "Container",
  "geekplus.gms.client.screen.container.form.containerSubTitle": "The container is the carrier for the robot to handle goods, while the bracket is a support structure on the ground for goods picking and placement. Different types of containers and brackets can be created based on business scenarios. The list of containers is used to manage existing container resources and control container entry and removal.",
  "geekplus.gms.client.screen.container.form.pleaseSelectContainerTypeName": "Please select the container type.",
  "geekplus.gms.client.screen.container.columns.containerStatus": "Container status",
  "geekplus.gms.client.screen.container.columns.status": "State",
  "geekplus.gms.client.screen.container.columns.containerCode": "Container code",
  "geekplus.gms.client.screen.container.columns.code": "Code",
  "geekplus.gms.client.screen.container.columns.containerTypeName": "Container type name",
  "geekplus.gms.client.screen.container.columns.containerAngle": "Container angle",
  "geekplus.gms.client.screen.container.columns.angle": "Angle",
  "geekplus.gms.client.screen.container.columns.materialType": "Type of material",
  "geekplus.gms.client.screen.container.columns.containerLoadingStatus": "Empty container status",
  "geekplus.gms.client.screen.container.columns.loadingStatus": "Empty/full status",
  "geekplus.gms.client.screen.container.columns.containerCurrentPosition": "Current position of container",
  "geekplus.gms.client.screen.container.columns.currentPosition": "Current position",
  "geekplus.gms.client.screen.container.columns.belongsArea": "Affiliated region",
  "geekplus.gms.client.screen.container.columns.belongsGroup": "Affiliated group",
  "geekplus.gms.client.screen.container.columns.entryEndPoint": "Destination for entry",
  "geekplus.gms.client.screen.container.columns.containerAmount": "Container qty",
  "geekplus.gms.client.screen.container.columns.containerEntryAngle": "Angle for container entry",
  "geekplus.gms.client.screen.container.tips.confirmLeave": "Confirm removal",
  "geekplus.gms.client.screen.container.tips.confirmEntry": "Confirm entry",
  "geekplus.gms.client.screen.container.tips.leaveTipMessage": "The empty/full status of the container will be cleared after removal. Are you sure to confirm removal?",
  "geekplus.gms.client.screen.container.tips.containerDeleteTipMessage": "It cannot be recovered after deletion. Are you sure to delete it?",
  "geekplus.gms.client.screen.container.tips.containerEntry": "Container Entry",
  "geekplus.gms.client.screen.container.tips.canDelete": "The following containers can be deleted.",
  "geekplus.gms.client.screen.container.tips.canEnter": "The following containers can enter.",
  "geekplus.gms.client.screen.container.tips.canLeave": "The following containers can be removed.",
  "geekplus.gms.client.screen.container.tips.cannotDelete": "The following containers have entered or are at work, and cannot be deleted.",
  "geekplus.gms.client.screen.container.tips.cannotEnter": "The following containers have entered or are at work, and cannot enter again.",
  "geekplus.gms.client.screen.container.tips.cannotLeave": "The following containers have been removed or are at work, and cannot be removed again.",
  "geekplus.gms.client.screen.container.tips.index": "SN",
  "geekplus.gms.client.screen.container.tips.editContainerCodeTip": "Just a new container can be added by specifying the container code ({code}). Are you sure to add it?",
  "geekplus.gms.client.screen.container.tips.noData": "No data",
  "geekplus.gms.client.screen.container.tips.entryFailed": "Entry failed",
  "geekplus.gms.client.screen.container.tips.operationFailed": "Operation failed",
  "geekplus.gms.client.screen.container.tips.atLeast2Legs": "Please add the information of at least two legs.",
  "geekplus.gms.client.screen.container.form.stepOne": "Step 1",
  "geekplus.gms.client.screen.container.form.stepTwo": "Step 2",
  "geekplus.gms.client.screen.container.form.pleaseSelectContainerShape": "Please select the container form",
  "geekplus.gms.client.screen.container.form.container": "Container",
  "geekplus.gms.client.screen.container.form.containerInfo": "Container information",
  "geekplus.gms.client.screen.container.form.modelInformation": "Specification",
  "geekplus.gms.client.screen.container.form.containerModel": "Container type",
  "geekplus.gms.client.screen.container.form.standardModel": "Standard container",
  "geekplus.gms.client.screen.container.form.noStandardModel": "Special-shaped container",
  "geekplus.gms.client.screen.container.form.containerSurfaceLong": "Length",
  "geekplus.gms.client.screen.container.form.containerSurfaceWide": "Container side: width",
  "geekplus.gms.client.screen.container.form.containerLegsLong": "Container leg: length",
  "geekplus.gms.client.screen.container.form.containerLegsWide": "Container leg: width",
  "geekplus.gms.client.screen.container.form.BasicInformation": "Basic info",
  "geekplus.gms.client.screen.container.form.codeType": "Code type",
  "geekplus.gms.client.screen.container.form.codeName": "Code name",
  "geekplus.gms.client.screen.container.form.containerForm": "Container form",
  "geekplus.gms.client.screen.container.form.shelves": "Shelf",
  "geekplus.gms.client.screen.container.form.feedTruck": "Trolley",
  "geekplus.gms.client.screen.container.form.bin": "Tote",
  "geekplus.gms.client.screen.container.form.cages": "Cage car",
  "geekplus.gms.client.screen.container.form.handlingPalletM": "Handling pallet (M series)",
  "geekplus.gms.client.screen.container.form.forkliftPalletF": "Forklift pallet (F series)",
  "geekplus.gms.client.screen.container.form.palletSpecifications": "Pallet specifications",
  "geekplus.gms.client.screen.container.form.standardPalletRegular": "Standard pallet - regular (recommended)",
  "geekplus.gms.client.screen.container.form.standardPalletSpecial": "Standard pallet - special",
  "geekplus.gms.client.screen.container.form.palletStructure": "Tray structure",
  "geekplus.gms.client.screen.container.form.twoHoleTray": "Double hole tray",
  "geekplus.gms.client.screen.container.form.singleHoleTray": "Single hole tray",
  "geekplus.gms.client.screen.container.form.identifyDistances": "Recognition distance",
  "geekplus.gms.client.screen.container.form.palletMaterial": "Tray material",
  "geekplus.gms.client.screen.container.form.wood": "Wooden",
  "geekplus.gms.client.screen.container.form.plastics": "Plastic",
  "geekplus.gms.client.screen.container.form.other": "Others",
  "geekplus.gms.client.screen.container.form.trayColor": "Tray Color",
  "geekplus.gms.client.screen.container.form.woody": "Wood color",
  "geekplus.gms.client.screen.container.form.blue": "Blue",
  "geekplus.gms.client.screen.container.form.black": "Black",
  "geekplus.gms.client.screen.container.form.palletModels": "Pallet truck model",
  "geekplus.gms.client.screen.container.form.palletSideLong": "Pallet length",
  "geekplus.gms.client.screen.container.form.palletSideWide": "Pallet width",
  "geekplus.gms.client.screen.container.form.edgeColumnWide": "Edge Width",
  "geekplus.gms.client.screen.container.form.middleColumnWide": "Mid-column width",
  "geekplus.gms.client.screen.container.form.holesHigh": "Hole height",
  "geekplus.gms.client.screen.container.form.holesWide": "Hole width",
  "geekplus.gms.client.screen.container.form.shelfElements": "Container elements",
  "geekplus.gms.client.screen.container.form.shelvesLegs": "Shelf - leg",
  "geekplus.gms.client.screen.container.form.shelfFace": "Shelf - side",
  "geekplus.gms.client.screen.container.form.sizeLong": "Container length",
  "geekplus.gms.client.screen.container.form.sizeWide": "Container width",
  "geekplus.gms.client.screen.container.form.sizeHeight": "Container height",
  "geekplus.gms.client.screen.container.form.xAxisFromCenter": "Distance from center X",
  "geekplus.gms.client.screen.container.form.yAxisFromCenter": "Distance from center Y",
  "geekplus.gms.client.screen.container.form.shelfCenterOriginX": "Center origin: x",
  "geekplus.gms.client.screen.container.form.shelfCenterOriginY": "Center origin: y",
  "geekplus.gms.client.screen.container.form.shelfCenter": "Container center point",
  "geekplus.gms.client.screen.container.form.rightConfig": "Please select the configuration on the right first.",
  "geekplus.gms.client.screen.container.form.checkExample": "Example",
  "geekplus.gms.client.screen.container.form.onlyRectangularSupported": "The shelf configuration with four sides in irregular rectangle is only available at present.",
  "geekplus.gms.client.screen.container.form.referActualShelfFigure": "I. Find the shelf sides and legs with reference to the actual shelf, as shown in the figure:",
  "geekplus.gms.client.screen.container.form.notes": "Precautions:",
  "geekplus.gms.client.screen.container.form.shelfSurfaceApplied": "1. Container side: It is used to calculate the rotation range during container handling by the robot.",
  "geekplus.gms.client.screen.container.form.shelfLegsApplied": "2. Container leg: The height of the container legs (including wheels) that can be scanned at laser height shall be the cross section for laser scanning. These container legs shall be configured for container recognition before the robot handles the container.",
  "geekplus.gms.client.screen.container.form.regularRectangleApplied": "3. About measurement: For irregular rectangular side and legs, the dimensions of the maximum rectangular surface in the side and leg shall prevail for the measurement of the container side. The dimensions of the container side and leg shall include the dimensions occupied by rotating wheels to prevent collision of rotating shelf with people or goods.",
  "geekplus.gms.client.screen.container.form.exampleDiagram": "Example:",
  "geekplus.gms.client.screen.container.form.configurationFaceLegs": " II. Configuration description of side and leg",
  "geekplus.gms.client.screen.container.form.measureDistanceLeg": "Measure leg-side margins 1 and 2 in mm.",
  "geekplus.gms.client.screen.container.form.containersRobotsInclude": "Common containers that can be handled by robots include the shelf, trolley, material cabinet, cage trolley, and pallet.",
  "geekplus.gms.client.screen.container.form.containerModelExplanation": "Terminology of container models",
  "geekplus.gms.client.screen.container.form.mainlyConfiguresInformation": 'The container model is mainly provided with the information of the "Container side" and "Container leg".',
  "geekplus.gms.client.screen.container.form.containerSurface": "Container side:",
  "geekplus.gms.client.screen.container.form.usedEnvelopeHandling": "for envelope calculation during handling.",
  "geekplus.gms.client.screen.container.form.containerLegs": "Container leg:",
  "geekplus.gms.client.screen.container.form.usedRobotOntology": "for container recognition by the robot.",
  "geekplus.gms.client.screen.container.form.standardContainerModel": "Definition: The container with its sides and four legs (each located at each angle) in regular rectangle and the point for docking with the robot located at the center of the rectangle is called standard container.",
  "geekplus.gms.client.screen.container.form.noStandardContainerModel": "Definition: The container showing irregular rectangular sides or more than four legs from its top view is called special-shaped container.",
  "geekplus.gms.client.screen.container.form.containerLegsConfigured": "Precaution: The container legs (including wheels) that can be scanned at laser height shall be configured.",
  "geekplus.gms.client.screen.container.form.laserScanningReferenceDiagram": "Reference diagram for laser scanning:",
  "geekplus.gms.client.screen.container.form.exampleShapedContainer": "Example of special-shaped container:",
  "geekplus.gms.client.screen.container.form.viewExample": "View the example",
  "geekplus.gms.client.screen.container.form.containerFace": "Container side",
  "geekplus.gms.client.screen.container.form.containerLeg": "Container leg",
  "geekplus.gms.client.screen.container.form.surfaceLength": "Container length",
  "geekplus.gms.client.screen.container.form.surfaceWidth": "Container width",
  "geekplus.gms.client.screen.container.form.surfaceHeight": "Container height",
  "geekplus.gms.client.screen.container.form.length": "Length",
  "geekplus.gms.client.screen.container.form.width": "Width",
  "geekplus.gms.client.screen.container.form.height": "High",
  "geekplus.gms.client.screen.container.form.shelfHeight": "Container height",
  "geekplus.gms.client.screen.container.form.codeRule": "Coding rule",
  "geekplus.gms.client.screen.container.form.codePrefix": "Code prefix",
  "geekplus.gms.client.screen.container.form.containerMaterialInfo": "Information of materials carried by container",
  "geekplus.gms.client.screen.container.form.shelfLeg": "Shelf leg",
  "geekplus.gms.client.screen.container.form.shelfOverLookDiagram": "Top view of upper plane of container",
  "geekplus.gms.client.screen.container.form.bracket.shelfOverLookDiagram": "Top view of upper plane of bracket",
  "geekplus.gms.client.screen.container.form.measureYAxisMethod": "(Measured by taking the robot entry direction as Y-axis)",
  "geekplus.gms.client.screen.container.form.specialShelfModelConfig": "Special-shaped container model configuration",
  "geekplus.gms.client.screen.container.form.bracket.specialShelfModelConfig": "Special-shaped bracket model configuration",
  "geekplus.gms.client.screen.container.form.shelf3DDiagram": "Stereoscopic schematic diagram of container",
  "geekplus.gms.client.screen.container.form.bracket.shelf3DDiagram": "Stereoscopic schematic diagram of bracket",
  "geekplus.gms.client.screen.container.form.containerType.step2": "Step 2: Edit specifications",
  "geekplus.gms.client.screen.container.form.containerList": "List of containers",
  "geekplus.gms.client.screen.container.form.containerSize": "Container dimensions",
  "geekplus.gms.client.screen.container.placeholder.upTo4Bits": "Please enter a maximum of four letters.",
  "geekplus.gms.client.screen.container.columns.typeCode": "Type code",
  "geekplus.gms.client.screen.container.columns.typeName": "Type name",
  "geekplus.gms.client.screen.container.columns.details": "Details",
  "geekplus.gms.client.screen.container.columns.containerType": "Container type",
  "geekplus.gms.client.screen.container.columns.containerShape": "Container form",
  "geekplus.gms.client.screen.container.columns.containerModel": "Container type",
  "geekplus.gms.client.screen.container.tips.failReason": "Failure reason",
  "geekplus.gms.client.screen.container.tips.containerAmount": "Container quantity; adding in batch is supported; up to 50 containers can be added at a time.",
  "geekplus.gms.client.screen.container.tips.containerCode": "Unique identifier of container; when the system generates codes automatically, generating for multiple containers in batch is supported; however, if the code is specified for containers, a container can be generated at a time.",
  "geekplus.gms.client.screen.container.tips.materialType": "The data are from the material management menu and are used to distinguish different materials carried by containers.",
  "geekplus.gms.client.screen.container.tips.codeRule": "The container code is automatically generated by default. To modify it, please directly modify it into the specified container code in the input box.",
  "geekplus.gms.client.screen.container.tips.containerNum": "Container quantity; adding in batch is supported; up to 50 containers can be added at a time; preset number: 1.",
  "geekplus.gms.client.screen.container.tips.recognizableDistance": "The distance for robot laser to recognize the pallet is mainly used for algorithm R&D.",
  "geekplus.gms.client.screen.container.tips.palletModel": "For vehicle models that handle pallets, the requirement of the minimum column width is different.",
  "geekplus.gms.client.screen.container.tips.containerFace": "for envelope calculation during handling.",
  "geekplus.gms.client.screen.container.tips.containerLeg": "for container recognition by the robot.",
  "geekplus.gms.client.screen.container.tips.specification.face": "Pallet side: no limit for both the length and width",
  "geekplus.gms.client.screen.container.tips.specification.material": "Material: wooden/plastic/others",
  "geekplus.gms.client.screen.container.tips.specification.color": "Color: wood color/blue/others",
  "geekplus.gms.client.screen.container.tips.specification.pillarSize": "Column size:",
  "geekplus.gms.client.screen.container.tips.specification.palletModel1": "F12ML/F14L/F16L/F35C: Both the edge column and the middle column shall be more than or equal to 60 mm wide.",
  "geekplus.gms.client.screen.container.tips.specification.palletModel2": "F20T/F20MT: Both the edge column and the middle column shall be more than or equal to 80 mm wide.",
  "geekplus.gms.client.screen.container.tips.specification.special.usageCases": "The column size is applicable to the site with just one type of pallet and smaller than the width of the edge column and middle column of standard pallet.",
  "geekplus.gms.client.screen.container.tips.specification.special.material": "Material: wooden/plastic/metal/others",
  "geekplus.gms.client.screen.container.tips.specification.special.color": "Color: wood color/blue/black/gray/others",
  "geekplus.gms.client.screen.container.tips.specification.special.palletModel1": "F12ML/F14L/F16L/F35C: Both the edge column and the middle column shall be more than or equal to 50 mm wide.",
  "geekplus.gms.client.screen.container.tips.specification.special.palletModel2": "F20T/F20MT: Both the edge column and the middle column shall be more than or equal to 70 mm wide.",
  "geekplus.gms.client.screen.bracket.form.offset.tooltip.desc": "The ground bracket is fixed on the ground. The robot may not stop at the geometric center point due to center-of-gravity shift of the pallet above the bracket or other reasons. In such case, it is necessary to configure front and rear offsets of Y-axis.",
  "geekplus.gms.client.screen.bracket.form.offset.tooltip.tips": "Note: Configuration shall be made by taking the robot entry direction as Y-axis.",
}
