/**
 *   v3.4.0后端接口提供的国际化内容
 */
{
  "lang.gles.baseData.warehouse": "仓库",
  "lang.ark.fed.common.deleteTipMsg": "删除后将无法恢复，确认删除吗？",
  "lang.ark.fed.detour": "绕行",
  "lang.ark.fed.cutting": "裁切",
  "lang.ark.fed.flowAndFlowTemplate": "流程/流程模板",
  "lang.ark.fed.deviceAccessType.fixation": "固定设备",
  "lang.ark.fed.addNew": "新增",
  "lang.ark.fed.shelfAttribute.MOVE": "MOVE",
  "lang.ark.fed.minutesLater": "分钟后",
  "lang.ark.workflow.paramValueCode.floor": "floor",
  "lang.ark.fed.trafficAndStopNotwManage": "已有交通管制区或者急停区功能，不支持添加管理区功能",
  "lang.ark.fed.verySerious": "非常严重",
  "lang.ark.fed.queuePointLevel": "排队点优先级",
  "lang.ark.trafficControl.robotRange": "机器人调度区域",
  "lang.ark.fed.chargingTime": "充电时长",
  "lang.ark.fed.theMapHasBeenSavedAndYouCanEditItNow": "地图已保存，您可以去编辑地图.",
  "lang.ark.fed.locationOfRobotCharging": "机器人充电的位置",
  "lang.ark.fed.side": "面",
  "lang.ark.fed.component.workflow.label.specifyNodeType": "指定节点类型",
  "lang.ark.shelfTypeRefShelf": "删除失败，已被货架使用！货架号:{0}",
  "lang.ark.apiContainerCode.containerNotExistsByLocation": "locationCode:{0}上容器不存在",
  "lang.ark.apiCallbackReg.controlSendFrequency": "是否控制发送频率",
  "lang.ark.workflow.containerLevel2Classifica": "容器二级分类",
  "lang.ark.fed.pleaseAddCacheArea": "节点未配置缓存区",
  "lang.ark.fed.nodeConfirmedLeave.type": "确认离开中",
  "lang.ark.fed.screen.equipmentAssociatedInfo.dialogDetail.labelPosition": "点位",
  "lang.ark.workflow.completeBizAutoTriggerSimple": "业务选择",
  "lang.ark.waveType.alikeProduct": "相同产线",
  "lang.ark.fed.pickingUpRack": "正在取货架",
  "lang.ark.fed.leftBracket": "+左括号",
  "lang.mwms.fed.simulation": "模拟控制",
  "lang.ark.workflowConfig.cellFunctions.turn": "转面功能",
  "lang.ark.fed.rmsRange": "rms同步区域管制",
  "lang.ark.fed.sendMaterialType": "送料目的点匹配方式",
  "lang.ark.fed.pleaseChangeBins": "请切换货位",
  "lang.ark.action.interface.conditionExtraParam9": "extraParam9",
  "lang.ark.workflow.B": "B",
  "lang.ark.action.interface.conditionExtraParam8": "extraParam8",
  "lang.ark.action.interface.conditionExtraParam7": "extraParam7",
  "lang.ark.action.interface.conditionExtraParam6": "extraParam6",
  "lang.ark.fed.makeSure": "确 定",
  "lang.ark.action.interface.conditionExtraParam5": "extraParam5",
  "lang.ark.workflow.F": "F",
  "lang.ark.action.interface.conditionExtraParam4": "extraParam4",
  "lang.ark.action.interface.conditionExtraParam3": "extraParam3",
  "lang.ark.externalDevice.instructionRule4": "机台偏移量±50mm外（不包含50）",
  "lang.ark.action.interface.conditionExtraParam2": "extraParam2",
  "lang.ark.externalDevice.instructionRule3": "机台偏移量±50mm内（包含50）",
  "lang.ark.action.interface.conditionExtraParam1": "extraParam1",
  "lang.gles.strategy.robotGoodsPosition": "机器人货位策略",
  "lang.ark.fed.screen.hybridRobot.installEquipmentTip": "数据来自dmp的上装设备",
  "lang.ark.fed.liveNoSaveGoods": "存在未保存的物料明细，请先保存！",
  "lang.ark.workflow.L": "L",
  "lang.ark.workflow.R": "R",
  "lang.ark.workflow.template.validate.templateOrderNodeMustGreaterThan1": "动态模板业务节点数量必须大于1",
  "lang.ark.fed.uninstallSuccess": "卸载成功",
  "lang.ark.fed.redistribution": "重新分配",
  "lang.authManage.web.others.expand": "展开",
  "lang.ark.singleCellStation": "单点工位",
  "lang.ark.fed.containerTypeExternalNo": "类型编码",
  "lang.ark.fed.selectPoints": "选择点位",
  "lang.ark.fed.templateCode": "模板编码",
  "lang.gles.receipt.adjustOrder.adjustOrder": "库存调整单",
  "lang.mwms.monitorRobotMsg12009": "机器人取货架动作超时",
  "lang.ark.fed.goodsLocation": "货位",
  "lang.mwms.monitorRobotMsg12006": "路径资源可能正被占用",
  "lang.ark.operatelog.operatetype.fetch": "取",
  "lang.mwms.monitorRobotMsg12007": "无路径可以规划",
  "lang.mwms.monitorRobotMsg12004": "任务起点或终点是障碍",
  "lang.ark.fed.emergencyStopError": "系统急停失败!",
  "lang.mwms.monitorRobotMsg12005": "途中有障碍机器人或障碍货架",
  "lang.mwms.monitorRobotMsg12013": "机器人达到低电比",
  "lang.ark.workflow.buttonAlreadyConfigured": "删除失败！存在物理按钮配置信息，请先在“按钮配置”页面进行删除操作",
  "lang.ark.fed.uploadViewSuccess": "上传成功",
  "lang.mwms.monitorRobotMsg12014": "没有匹配到充电站，充电任务无法执行",
  "lang.ark.task.log.export.title.end.time": "结束时间",
  "lang.mwms.monitorRobotMsg12011": "长时间未到达等待点",
  "lang.mwms.monitorRobotMsg12012": "机器人电量长时间不上涨",
  "lang.ark.fed.chargingCapacity": "充电电量",
  "lang.mwms.monitorRobotMsg12010": "未到达货架位置取货架",
  "lang.ark.warehouse.containerConfigEmpty": "容器配置信息为空",
  "lang.ark.record.rms.sendRobotTask": "发送机器人任务",
  "lang.ark.fed.screen.flowNodeConfig.ifExecuteTask": "若执行的任务为",
  "lang.ark.workflowConfig.cellFunctions.restart": "重启功能",
  "lang.ark.fed.screen.workflowInfo.requestParamDetail": "请求报文详情",
  "lang.ark.workflow.exceptionHandler.idlePriority": "空闲优先",
  "lang.ark.workflow.task.status.create": "创建",
  "lang.ark.fed.inWarehouse": "人工入库",
  "lang.ark.fed.lineName": "产线名称",
  "lang.mwms.monitorRobotMsg12019": "跨楼层充电失败",
  "lang.mwms.monitorRobotMsg12017": "充电站单元格被其他机器人占用",
  "lang.mwms.monitorRobotMsg12018": "跨区域充电失败",
  "lang.ark.warehouse.noMatchZagvdbm": "未匹配到上料点",
  "lang.mwms.monitorRobotMsg12015": "没有空闲充电站，充电任务无法执行",
  "lang.mwms.monitorRobotMsg12016": "没有可用充电站，充电任务无法执行",
  "lang.mwms.monitorRobotMsg12024": "找不到停靠位",
  "lang.ark.workflow.arrive.action.goTurnOfAngle.pos180": "180°",
  "lang.mwms.monitorRobotMsg12022": "充电时间过长",
  "lang.mwms.monitorRobotMsg12023": "机器人电池温度过高",
  "lang.mwms.monitorRobotMsg12020": "当前任务阻塞，无法执行充电任务",
  "lang.mwms.monitorRobotMsg12021": "当前任务未完成，无法执行充电任务",
  "lang.ark.fed.whetherToWait": "是否等待",
  "lang.ark.fed.goodsExComplete": "异常完成",
  "lang.ark.fed.triggerCompletion": "完成触发",
  "lang.ark.fed.goToNextFlowNode": "前往下一个节点",
  "lang.ark.fed.south": "南",
  "lang.ark.dynamicTemplate.cellCodeNotMatch": "自动赋值节点未匹配到点，请检查配置！",
  "lang.ark.fed.shelfLock": "货架锁",
  "lang.ark.fed.pickingTask": "领料任务",
  "lang.ark.fed.screen.hybridRobot.binInfo": "货位信息",
  "lang.ark.workflow.area.queueRange": "排队区",
  "lang.ark.fed.nodeType": "节点类型",
  "lang.mwms.fed.warehouseInit": "仓库搭建",
  "lang.ark.fed.common.placeholder.select": "请选择",
  "lang.ark.workflow.robotWaitFlag": "是否原地等待",
  "lang.ark.fed.waveStrategyName": "策略名称",
  "lang.mwms.fed.shelfIn": "货架入库",
  "lang.ark.fed.tripTo": "去程",
  "lang.ark.interface.notExist": "该日志不存在",
  "lang.mwms.fed.stocktakeException": "盘点异常",
  "lang.ark.fed.end": "结束",
  "lang.ark.fed.selectTriggerEvent": "选择触发事件",
  "lang.ark.fed.belongsToArea": "所属区域",
  "lang.ark.fed.shelfAttribute.RECALL": "RECALL",
  "lang.ark.interface.interfaceDesc.edit": "编辑接口信息",
  "lang.mwms.monitorRobotMsg12002": "路径规划失败",
  "lang.mwms.monitorRobotMsg12003": "机器人不在地图",
  "lang.ark.fed.blankingTimeout": "下料超时",
  "lang.mwms.monitorRobotMsg12000": "机器人无法链接",
  "lang.mwms.monitorRobotMsg12001": "子任务发送超时",
  "lang.ark.fed.areDeletionsConfirmed": "是否确认删除？",
  "lang.ark.fed.taskFrom.putTask": "放货任务",
  "lang.ark.fed.beforeExecuteSaveDayLog": "每次执行前，保留最近{0}天日志",
  "lang.ark.fed.customStartAndendNode": "支持指定起点和终点",
  "lang.ark.fed.dataUpdate": "数据更新",
  "lang.ark.workflow.template.validate.templateNotExist": "模板不存在",
  "lang.ark.fed.actionsErrorNeedRemoveRobot": "{0}节点交互配置错误，需释放机器人！",
  "lang.authManage.web.common.pleaseSelect": "请选择",
  "lang.ark.fed.demandQuantity": "需求数量",
  "lang.mwms.fed.arrangePlan": "理货计划管理",
  "lang.ark.robot.go.fetch.pallet": "去取托盘",
  "lang.ark.fed.takeTheRack": "取货架",
  "lang.ark.fed.robotApplications": "机器人种类",
  "lang.ark.fed.screen.LoginLog.roleName": "角色",
  "lang.ark.fed.floorCode": "层编码",
  "lang.ark.fed.screen.flowNodeConfig.offsetValue": "偏移值",
  "lang.ark.fed.scrollIsExistsInBoundRecord": "卷轴号存在库存记录,入库失败",
  "lang.ark.bussinessModel.wave": "波次",
  "lang.ark.fed.lackRobotQueue": "任务排队",
  "lang.ark.fed.require": "需求叫料",
  "lang.ark.fed.robotStatus": "执行结果",
  "lang.ark.fed.screen.equipmentAssociatedInfo.dialogDetail.labelEntryType": "料口类型",
  "lang.ark.rollerCellStation.canNotDelete": "辊筒工位不允许删除",
  "lang.ark.fed.thereIsARequiredItemNotFilled": "，存在必填项未填写！",
  "lang.ark.fed.menu.systemSetting": "系统设置",
  "lang.ark.fed.numberOfControllers": "控制器个数",
  "lang.ark.action.interface.extraParam18": "extraParam18",
  "lang.ark.fed.abnormalCompleteSuccessfully": "异常完成成功",
  "lang.ark.action.interface.extraParam17": "extraParam17",
  "lang.ark.fed.sendMaterialPayAttention": "按物料自动匹配：根据绑定产线工位的物料信息，确定物料配送的工位。 手动选择目的点：手动选择上料点可配送目的点，根据选择目的点配送物料。  领单配送不受该配置项影响。",
  "lang.ark.fed.configurationValue": "配置值",
  "lang.ark.action.interface.extraParam19": "extraParam19",
  "lang.ark.action.interface.extraParam14": "extraParam14",
  "lang.ark.action.interface.extraParam13": "extraParam13",
  "lang.ark.interface.messageNameDesc": "接口名称",
  "lang.ark.action.interface.extraParam16": "extraParam16",
  "lang.ark.action.interface.extraParam15": "extraParam15",
  "lang.ark.action.interface.extraParam10": "extraParam10",
  "lang.ark.fed.waitStatus": "等待状态",
  "lang.ark.action.interface.extraParam12": "extraParam12",
  "lang.ark.action.interface.extraParam11": "extraParam11",
  "lang.ark.workflow.wareHouseStationConfig": "按工作站",
  "lang.ark.fed.exceptionHandler": "异常处理",
  "lang.ark.apiRuleCode.defaultRuleNotExists": "默认规则不存在",
  "lang.ark.operatelog.operatetype.send": "送",
  "lang.authManage.web.common.toLoginPage": "返回登录页",
  "lang.ark.fed.templateType": "模板类型",
  "lang.ark.common.failed": "调用失败",
  "lang.ark.fed.interruptInstruct": "中断等待指令",
  "lang.ark.fed.hybridRobot.hybridRobotType.singleSpiralArm": "单悬臂",
  "lang.ark.workflowPeriod.one": "仅一次",
  "lang.authManage.web.common.creator": "创建人",
  "lang.ark.waveStatus.waveTaskCreateFail": "任务创建失败",
  "lang.ark.fed.loadingMount": "上料数量",
  "lang.ark.fed.mapsAreBeingUpdated": "正在更新地图",
  "lang.ark.fed.menu.buttonFunctionConfiguration": "按钮功能配置",
  "lang.ark.fed.sendMaterialDestination": "选择可配送目的点",
  "lang.ark.fed.selfCharging": "自行充电",
  "lang.ark.fed.contents.flowConfig.recycleType.autoRecoveryRule.alert": "提示：可设置多个预设地点，系统按优先级匹配",
  "lang.ark.warehouse.materialPreparePointCellCode": "上料点位",
  "lang.ark.workflow.wareHouseManuallyCreate": "手动创建",
  "lang.ark.warehouse.containerBin": "容器货位",
  "lang.ark.fed.serverAddress": "服务器地址",
  "lang.ark.fed.screen.flowNodeConfig.turnSideTo": "旋转至",
  "lang.ark.fed.originAngle": "原始角度",
  "lang.ark.fed.editNode": "编辑节点",
  "lang.ark.workflow.completeTrigger": "完成自动触发后续任务",
  "lang.ark.fed.shelfAttribute": "请选择物料类别",
  "lang.ark.record.dmp.receiveCallBack": "收到dmp回调",
  "lang.ark.interface.sendSucceed": "该日志已经发送成功，无法再次发送",
  "lang.ark.fed.secondsAndTime": "秒/次",
  "lang.ark.fed.sound": "声音",
  "lang.ark.fed.deleteActionFailedRef": "交互配置无法删除，存在引用的流程",
  "lang.ark.fed.queuePoint": "排队点",
  "lang.ark.warehouse.materialPreparePointName": "上料点名称",
  "lang.ark.fed.receiveMaterial": "领取上料",
  "entry.shelf.failed": "货架入场失败,请稍后再试!",
  "lang.ark.interface.interfaceName": "接口名称",
  "lang.ark.externalDevice.device_own_type.robotDeviceComponent": "机器人设备组件",
  "lang.mwms.fed.pickException": "拣货异常",
  "lang.ark.fed.firstDay": "第一天",
  "lang.ark.robot.go.drop": "卸货",
  "lang.ark.fed.roadWidth": "道路宽度:",
  "lang.ark.waveTaskStatus.disCanceled": "取消",
  "lang.ark.fed.firstDrawWorkStop": "请先画流程首节点，首节点的类型是：工作站，停靠点",
  "lang.ark.trafficControl.trafficFunction": "交通管制区功能",
  "lang.ark.fed.source": "来源",
  "lang.ark.fed.backButton": "退回按钮",
  "lang.ark.fed.manualClean": "手动清除",
  "lang.ark.fed.confirmGoods": "确认物料",
  "lang.ark.fed.menu.taskManagement": "任务监控",
  "lang.ark.workflow.init": "待确认",
  "lang.ark.button.type.selfRecovery": "自复位",
  "lang.ark.action.interface.fixedValue": "固定值",
  "lang.ark.fed.passbyPointType": "经过点类型:",
  "lang.ark.fed.screen.flowNodeConfig.ifNextPoint": "若下一个点",
  "lang.ark.fed.selectionWorkflow": "选择流程",
  "lang.ark.fed.currentLocation": "当前位置",
  "lang.ark.fed.uploadFileLimit500": "只能上传excel文件(xls/xlsx)，且不超过500kb",
  "lang.ark.fed.containPoints": "包含点位",
  "lang.ark.warehouse.poleCabinet": "带杆料柜",
  "lang.ark.fed.chargingStrategy": "充电策略",
  "lang.ark.loadCarrier.loadCarrierModelCodeGenerateErr": "容器模型编码生成错误！",
  "lang.authManage.web.others.subsystem": "授权子系统",
  "lang.ark.fed.stopCharging": "停止充电",
  "lang.ark.fed.dateRange": "日期范围",
  "lang.ark.fed.pleaseCreateARule": "请创建规则",
  "lang.ark.fed.imageTypeJudge": "图片类型要求：jpeg、jpg、png",
  "lang.ark.fed.north": "北",
  "lang.ark.interface.apiStart": "流程启动",
  "lang.ark.fed.rollerRobot": "辊筒机器人",
  "lang.ark.fed.robotWaitFlag": "原地等待",
  "lang.ark.fed.specifyRobot": "指定机器人",
  "lang.ark.warehouse.materialPreparePointOrder": "上料顺序",
  "lang.ark.fed.fullStation": "FULL版工作站",
  "lang.ark.workflowConfig.status.uninstalled": "已卸载",
  "lang.ark.workflow.canDeleteshelfFlag": "移除货架",
  "lang.ark.fed.screen.flowNodeConfig.tip.onlyWaitPoint": "仅适用于等待点",
  "lang.ark.fed.image": "图片",
  "lang.ark.workTask.export.title.fileName": "任务查询",
  "lang.ark.fed.pleaseEnterANumber": "请输入数字",
  "lang.ark.fed.menu.workstationEditController": "工作站",
  "lang.authManage.web.others.activePermission": "启用权限",
  "lang.ark.fed.isClearTrigger": "确定取消当前触发器？",
  "lang.ark.fed.selectArea": "选择区域",
  "lang.ark.fed.byRackType": "按货架类型",
  "lang.ark.action.interface.paramValue": "参数值",
  "lang.ark.fed.shangliao": "上料超时",
  "lang.ark.fed.taskTriggerCycle": "触发器周期",
  "lang.ark.apiCommonCode.locationFromNotMatchStart": "locationFrom:{0}分别按照任意点、工作站、区域在系统内都没有匹配到对应的位置",
  "lang.ark.fed.surlpusGoods": "剩余{0}个",
  "lang.ark.common.exportTaskDetail": "导入任务查看",
  "lang.ark.fed.currentTask": "当前任务",
  "lang.ark.fed.taskOverTime": "任务完成时间",
  "lang.ark.fed.DingTalk": "钉钉",
  "lang.ark.workflow.queue.noAvailableStopPoint": "无可用点位",
  "lang.ark.fed.allowInterruptionOfCharging": "允许中断充电",
  "lang.ark.robot.Task.send.failed": "机器人任务发送失败",
  "lang.ark.fed.order": "单据信息",
  "lang.ark.workflowTrigger.logType.taskRecordLog": "任务执行日志",
  "lang.ark.fed.pleaseSelectCellCode": "请先选择上料点",
  "lang.ark.fed.disCharingCell": "下料工位",
  "lang.ark.fed.shelfModel": "货架模型",
  "lang.ark.fed.container.leaveFailure": "以下容器离场失败，请重试!",
  "lang.ark.warehouse.demandProductionLine": "需求产线",
  "lang.mwms.rf.rfVersion": "PDA版本控制",
  "lang.ark.fed.virtualNode": "虚拟节点",
  "lang.ark.workflow.nodeStatus.empty": "空(未取到物流容器)",
  "lang.ark.fed.goBack": "退回",
  "lang.ark.fed.afterGoing": "前往",
  "lang.ark.fed.container": "容器",
  "lang.ark.fed.resume": "恢复运行",
  "lang.ark.fed.screen.workflowInfo.dmpTaskId": "dmp任务Id",
  "lang.ark.workflow.deviceTaskNotExistOrCompleted": "设备任务不存在或已完成不允许关闭",
  "lang.ark.fed.externalInterfaceInteraction": "外部接口交互",
  "lang.ark.workflow.authRoleHasUsed": "下列角色已经设置过工作站权限",
  "lang.ark.fed.optionalDockPoints": "可选停靠点",
  "lang.gles.receipt.tallyList": "理货单",
  "lang.ark.fed.shelfAttribute.ENO": "ENO",
  "lang.ark.fed.screen.workflowInfo.responseParam": "回调报文",
  "lang.mwms.fed.shelfStrategy": "上架策略",
  "lang.ark.warehouse.estimateUseTimeUnit": "单位",
  "lang.ark.fed.pauseEnter": "暂停进入",
  "lang.ark.fed.standbyPoint": "待命点",
  "lang.ark.fed.arriveOperation": "到达动作",
  "lang.ark.fed.orderReceive": "已领取",
  "lang.ark.apiNodeActionCode.successHandlerNotEmpty": "节点交互配置的成功处理逻辑为空",
  "lang.ark.task.log.export.title.fileName": "流程任务日志",
  "lang.ark.apiRobotTaskCode.waitPointExecutingTaskNotExists": "处于等待点执行中的任务不存在",
  "lang.ark.fed.ruleOperator": "运算符",
  "lang.ark.trafficControl.enterType.singleFactorySingleEnter": "单厂家单入口",
  "lang.ark.workflow.externalInteraction": "到达后外部交互",
  "lang.ark.fed.uploadImageCutText": "为保证条码识别准确率，请上传图片后先剪裁图片至仅有条码大小",
  "lang.ark.robot.classfy.noaction": "不动",
  "lang.ark.fed.screen.flowNodeConfig.ifExecuteRule": "若执行的指令规则为",
  "lang.ark.fed.noLoad": "空载",
  "lang.ark.workflow.containerNeedOrientation": "容器需要朝向",
  "lang.ark.fed.materialCode": "物料编码",
  "lang.ark.apiContainerCode.isEmptyIsBlank": "isEmpty为空",
  "lang.ark.fed.workstationConfig": "工作站配置",
  "lang.ark.manual.pick.up": "手动取走",
  "lang.ark.interface.enableSuccess": "启用成功",
  "lang.ark.waveStatus.create": "波次创建",
  "lang.ark.binStopPointRelation.binCellCode": "货位点编码",
  "lang.ark.trafficControl.triggerLock": "外部触发锁定",
  "lang.ark.fed.siteManagement": "场地管理",
  "lang.ark.workflow.workflowTaskSendPause": "无法操作暂停中的流程",
  "lang.authManage.web.permission.roleDesc": "角色说明",
  "lang.ark.fed.conButtonLogPieceData": "条数据",
  "lang.ark.fed.inventoryType": "库存类型",
  "lang.ark.workflow.workflowStartNode": "流程起点",
  "lang.ark.existWorkflowConfigUseTheArea": "有流程配置正在使用该区域!",
  "lang.ark.fed.others": "其他",
  "lang.ark.fed.defaultSite": "默认场地",
  "lang.ark.workflowConfig.cellFunctions.avoid": "避让功能",
  "lang.ark.fed.obstacleAvoidance": "前后避障",
  "lang.ark.warehouse.containerStockEdit": "该类型的容器存在库存，无法编辑",
  "lang.ark.common.ok": "调用成功",
  "lang.ark.fed.pointPosition": "点位",
  "lang.ark.fed.waveTaskCreateFail": "任务生成失败",
  "lang.ark.apiCommonCode.instructionNotBlank": "instruction不能为空",
  "lang.ark.fed.containerLevelTwo": "容器二级分类",
  "lang.ark.fed.pleaseEnterAPositiveInteger": "请输入正整数",
  "lang.ark.interface.apiDelete": "流程实例删除",
  "lang.ark.fed.rackState": "容器状态",
  "lang.ark.fed.cancelWaveSure": "取消波次，波次单据无法继续配送，需重新组波，确定取消？",
  "lang.ark.fed.sendingNode": "送节点",
  "lang.ark.warehouse.buttonOperationEndSystemUni": "存在相同的按钮配置且操作指令与当前录入的操作指令不匹配",
  "lang.ark.fed.mobileLocation": "移动位置",
  "lang.ark.fed.manageAreaNoTrayPointMsg0": "暂不支持同时存在托盘位和点位！",
  "lang.ark.api.workflowConfigNoMatched": "无匹配流程",
  "lang.ark.fed.menu.vens.dmpDeviceModel": "设备模型",
  "lang.ark.fed.businessRuleFlow": "业务规则流转",
  "lang.ark.warehouse.Baiting": "下料",
  "lang.ark.fed.move": "搬运",
  "lang.ark.workflow.denseStorageTaskUpperLimit": "起点是密集存储区域, 区域接受任务已达上限",
  "lang.ark.fed.conButtonLogIP": "IP",
  "lang.ark.fed.escDrawsBranchLines": "ESC 绘制分支线路",
  "lang.ark.fed.waitLeave": "等待离开",
  "lang.ark.fed.mergeNode": "连接已有节点",
  "lang.ark.fed.responseParameterValue": "返参处理",
  "lang.ark.fed.linkDisconnect": "链接断开",
  "lang.ark.workflow.areaLocked": "交通管制区被锁定，无法删除",
  "lang.ark.robot.classfy.cage": "牵引",
  "lang.ark.fed.pleaseCheckTheRequiredItems": "请检查必填项",
  "lang.mwms.fed.strategyAllocate": "命中策略管理",
  "lang.ark.fed.logManagement": "日志管理",
  "lang.ark.action.interface.extraParam20": "extraParam20",
  "lang.ark.fed.nextStep": "下一步",
  "lang.ark.fed.editingWorkflow": "流程编辑",
  "lang.ark.fed.conButtonLogID": "ID",
  "lang.ark.fed.circulationStrategy": "流转策略",
  "lang.authManage.web.others.disabledPermisssion": "禁用权限",
  "lang.ark.fed.startTime": "开始时间",
  "lang.ark.fed.containerManage": "容器管理",
  "lang.ark.apiCallbackReg.timeInterval": "时间间隔",
  "lang.ark.fed.noGoodsChangeLocation": "无该物料上料记录，请切换货位",
  "lang.ark.fed.waitingEnter": "等待进入",
  "lang.ark.fed.sourceProductionLine": "来源产线",
  "lang.ark.fed.taskTypeName": "任务类型",
  "lang.ark.fed.containerBinUsed": "任务占用",
  "lang.ark.element.has.bound": "该元素已经绑定了工作流",
  "lang.ark.fed.menu.workstationaddress": "访问地址",
  "lang.ark.fed.editComponentInterface": "编辑组件指令",
  "lang.ark.workflowTriggerMonitorStatus.create": "创建",
  "lang.ark.operation.workflow.recoveryWorkflow": "恢复任务",
  "lang.ark.fed.noGoodsInfo": "暂无物料",
  "lang.ark.fed.nodeEditing": "节点编辑",
  "lang.ark.fed.menu.robotTypeManagement": "机器人配置",
  "lang.ark.fed.menu.templateInstance": "模板实例",
  "lang.ark.robot.robotBindDeviceExist": "对应设备已绑定机器人",
  "lang.ark.fed.length": "长",
  "lang.ark.systemErrCannot_operate": "系统异常，不可操作",
  "lang.ark.fed.Wechat": "企业微信",
  "lang.ark.fed.estimate": "预计",
  "lang.mwms.fed.SNPoolCharts": "SN序号",
  "lang.ark.fed.productType": "产品型号",
  "lang.mwms.fed.monitoring": "仓库监控",
  "lang.authManage.web.common.isDelRol": "是否确定删除角色？",
  "lang.ark.apiCommonCode.needTimeFormatError": "needTime格式错误",
  "lang.ark.fed.optionalRackPoints": "可选货架点",
  "lang.ark.fed.autoSetNodeValue": "自动赋值点",
  "lang.mwms.monitorRobotMsg.norobot": "无可用机器人",
  "lang.ark.robot.go.return.pallet": "去还托盘",
  "lang.ark.fed.screen.flowNodeConfig.pleaseSelectCondition": "请选择判断条件",
  "lang.ark.fed.higherSpeed": "快速",
  "lang.ark.loadCarrier.loadCarrierModelSyncErr": "容器模型同步rms错误！",
  "lang.ark.workflow.condition.unEqual": "不等于",
  "lang.ark.record.robotCallback.fetched": "机器人取到货架",
  "lang.ark.fed.remap": "重新绘制地图",
  "lang.ark.fed.expression": "表达式",
  "lang.ark.fed.interfaceSetting": "接口设置",
  "lang.ark.fed.orderCreate": "创建",
  "lang.ark.fed.pleaseSelectTheTaskRecord": "请选择任务记录",
  "lang.ark.fed.menu.authManage": "权限",
  "lang.ark.workflow.startNode": "起始点",
  "lang.ark.trafficControl.shelfRange": "货架区域",
  "lang.mwms.rf.receiveWithoutTask": "无单入库",
  "lang.ark.fed.screen.flowNodeConfig.isDoubleLiftRobot": "是否双举升机器人",
  "lang.authManage.web.common.editor": "编辑人",
  "lang.ark.interface.apiLocationList": "点位查询",
  "lang.ark.fed.GRAVE": "严重",
  "lang.ark.fed.createTriggerTask": "创建触发任务",
  "lang.ark.workflow.task.status.node.backing": "退回中",
  "lang.ark.fed.name": "名称:",
  "lang.ark.fed.contents.flowConfig.recycleType": "取消时货物送回位置",
  "lang.mwms.fed.pickWorkManualCreate": "拣货工作手动生成",
  "lang.ark.task.log.export.title.robot.number": "机器人编号",
  "lang.ark.fed.component.workflow.nodeType.equipment": "设备点位",
  "lang.ark.binStopPointRelation.binOrder": "货位顺序号",
  "lang.authManage.web.others.missPage": "抱歉，你访问的页面不存在",
  "lang.ark.fed.menu.robotControlStrategy": "机器人策略",
  "lang.ark.workflow.recoveryAreaType.workflowStart": "流程起点",
  "lang.ark.waveGeneratePattern.documentAuto": "按单据自动组波",
  "lang.ark.fed.menu.monitoringAndManagement": "监控",
  "lang.ark.fed.waitSendGoods": "待送料",
  "lang.ark.robot.robotModelExist": "机器人类型已存在，不允许重复添加！",
  "lang.ark.fed.productDate": "生产日期",
  "lang.ark.fed.waveTaskCode": "波次任务编码",
  "lang.ark.workflow.workflowRuleNameExist": "规则名称重复",
  "lang.ark.fed.completionTime": "完成时间",
  "lang.ark.fed.expiryDate": "失效日期",
  "lang.ark.warehouse.canNotUseNodeToNode": "多目标点时不能使用点到点模板",
  "lang.ark.action.interface.integer": "int",
  "lang.ark.fed.orderAbnormalTip": "操作成功{successNum}条，失败{faildNum}条，任务异常挂起状态的单据才可操作",
  "lang.ark.fed.containerForm": "容器形态",
  "lang.ark.api.template.startNodeIsBlank": "任务起始点为空",
  "lang.ark.fed.processGroupNumber": "流程组编号",
  "lang.ark.fed.viewTheWholeProcess": "查看全流程",
  "lang.authManage.web.common.oldPassword": "旧密码",
  "lang.ark.interface.apiContainerCategoryList": "容器类型查询",
  "lang.ark.fed.contents.flowConfig.recycleType.manual": "手动选择送回位置",
  "lang.ark.workflow.area.autoRealease": "机器人自动释放",
  "lang.ark.fed.conditionNumber": "条件值",
  "lang.ark.fed.batteryVoltage": "电池电压",
  "lang.ark.fed.show": "显示",
  "lang.ark.fed.redistributionSuccessfully": "重新分配成功",
  "lang.ark.fed.personalizationOptionsTitle": "开启后，叫料工作站支持用户输入需求物料的预计用料时间",
  "lang.ark.fed.sourcePoint": "来源工位",
  "lang.ark.fed.pickListsPending": "待处理领料单",
  "lang.mwms.fed.user.add": "添加用户",
  "lang.ark.robot.go.deliver.pallet": "去送托盘",
  "lang.ark.fed.activeDistribution": "主动配送",
  "lang.ark.warehouse.getTaskHashCancle": "领料单已取消",
  "lang.gles.workflow.receiptMonitor": "单据监控",
  "lang.ark.fed.screen.hybridRobot.pleaseInputNumber": "请输入，仅支持数字",
  "lang.authManage.web.others.pwsavesuccess": "重置密码成功，新密码为{0}",
  "lang.ark.fed.logControllerConfigId": "控制器配置ID",
  "lang.authManage.web.common.edit": "编辑",
  "lang.ark.fed.openAutoFlow": "展开自动流程",
  "lang.ark.fed.controllerNumber": "控制器编号",
  "lang.ark.fed.enableEdit": "可编辑",
  "lang.ark.fed.seriNum": "节点编码",
  "lang.ark.fed.conditionalCoding": "条件编码",
  "lang.ark.fed.width": "宽",
  "lang.ark.fed.editExtendDevice": "编辑外部设备",
  "lang.ark.apiStationCode.stationStopPointNotOnlyOne": "工作站内的停靠点不唯一,必须指定具体点",
  "lang.ark.action.interface.string": "string",
  "lang.ark.fed.scopestartEnd": "范围{start}~{end}",
  "lang.ark.fed.isFastStartFlow": "再次启动上一流程：{0}",
  "lang.ark.fed.cellCode": "点位编码",
  "lang.ark.fed.pleaseInputGoodsCode": "请输入或扫描物料编码",
  "lang.ark.fed.receivingPoint": "领单工位",
  "lang.ark.fed.waveStrategyTrigger": "触发条件",
  "lang.ark.workflow.shelfCodeAlreadyExists": "容器类型编码已存在",
  "lang.ark.workstationIsExist": "工作站已存在",
  "lang.ark.fed.taskFrom.fetchTask": "取货任务",
  "lang.ark.fed.workstationPage": "工作站页面",
  "lang.ark.areaCellCodeUsed": "点位不能同时属于选择了排队策略的区域和工作站，请删除:{0}!",
  "lang.ark.workflow.arrive.action.component.commandPhaseIllegal": "指令执行阶段非法",
  "lang.ark.fed.materialDetails": "物料明细",
  "lang.ark.fed.contents.flowConfig.recycleType.autoRecoveryRule.columns.position": "地点",
  "lang.ark.workflow.containerSelectedByTask": "容器已被任务占用",
  "lang.ark.workflow.noLongerWaiting": "当前任务不再是等待进入状态，无法暂停，请刷新",
  "lang.ark.fed.citeFlow": "引用流程",
  "lang.ark.fed.screen.flowNodeConfig.executeByNormal": "正常执行",
  "lang.ark.fed.trafficAreaControlManagement": "管制区域监控",
  "lang.ark.fed.outGoodsNumMoreThanRemain": "货位{0}:最多可出库{1}个",
  "lang.ark.fed.contents.flowConfig.autoRecyclePosition": "预设地点",
  "lang.authManage.web.others.pinlessIp": "绑定设备信息",
  "lang.ark.workflow.workflowNotCancel": "当前流程取消均不满足取消规则",
  "lang.ark.fed.containBinIsOccupy": "货位已占用",
  "lang.ark.fed.orderSend": "配送中",
  "lang.ark.fed.modificationTime2": "修改时间",
  "lang.ark.fed.getMaterialDescribe": "适用上料点主动配送场景，即上料点主动配送。",
  "lang.ark.fed.taskManagement": "任务监控",
  "lang.authManage.web.others.filterKey": "请输入关键字进行过滤",
  "lang.ark.archiveType.containerChangeLog": "容器修改日志",
  "lang.mwms.fed.inventoryAge": "库龄报表",
  "lang.ark.base.license.instanceIdIsNull": "项目仓库编码为空!",
  "lang.ark.fed.billDashboard": "单据看板",
  "lang.ark.fed.restart": "重启",
  "lang.ark.fed.newWorkflow": "新建流程",
  "lang.ark.fed.operationDurationGreaterThan": "操作时长>=",
  "lang.ark.fed.nodeConfirmedLeave": "节点确认离开",
  "lang.ark.fed.fullGoods": "有货",
  "lang.ark.interface.dmpHandleDoor": "开关门",
  "lang.ark.fed.paramValueCode": "参数值编码",
  "lang.ark.base.license.customerIdIsNull": "客户编码为空!!",
  "lang.ark.fed.placeItHere": "在此处放置",
  "lang.ark.fed.interactiveList": "交互列表",
  "lang.ark.fed.processNumber": "工序号",
  "lang.ark.fed.unit": "单位",
  "lang.ark.workflow.robotAllocationStrategy": "机器人规则",
  "lang.mwms.fed.user.edit": "编辑用户",
  "lang.ark.fed.totalStock": "库存/物料总量",
  "lang.ark.fed.deliveryTime": "配送时间",
  "rms.system.container.entry.failed": "容器入场失败",
  "lang.mwms.monitorRobotMsg21085": "驱动器指令错误",
  "lang.mwms.monitorRobotMsg21084": "任务步骤stage错误",
  "lang.mwms.monitorRobotMsg21083": "任务结束时给出错误指令",
  "lang.mwms.monitorRobotMsg21082": "任务状态机切换错误",
  "lang.ark.fed.executionCondition": "执行条件",
  "lang.mwms.monitorRobotMsg21089": "驱动器欠电压",
  "lang.ark.warehouseTask.loadTaskOverTime": "工位上料时长预警，已超{0}分钟",
  "lang.mwms.monitorRobotMsg21088": "驱动器反馈错误",
  "lang.mwms.monitorRobotMsg21087": "驱动器跟踪错误",
  "lang.ark.fed.functionType": "功能类型",
  "lang.mwms.monitorRobotMsg21086": "驱动器电机相位错误",
  "lang.ark.fed.takeNode": "取节点",
  "lang.gles.baseData.productionLine": "产线",
  "lang.ark.fed.manualSelectGoodsAndDest": "手动选择目的点",
  "lang.mwms.monitorRobotMsg21081": "主控与工控通信中断",
  "lang.mwms.monitorRobotMsg21080": "称重传感器数据丢失",
  "lang.ark.fed.obstacleAvoidanceArea": "避障区",
  "lang.ark.action.interface.conditionType": "条件值类型",
  "lang.mwms.fed.warehouse": "仓库",
  "lang.ark.fed.nodeControl": "节点控制",
  "lang.ark.warehouse.demandLineStation": "需求点位",
  "lang.ark.workflow.hitStrategy": "命中策略",
  "lang.ark.fed.screen.container.belongsToGroup": "所属分组",
  "lang.ark.fed.commandDetail": "指令描述",
  "lang.ark.action.interface.conditionContainerCode": "containerCode",
  "lang.ark.workflow.customCellNotMatchCell": "自定义回收区没有匹配到空闲点位,取消失败!",
  "lang.mwms.monitorRobotMsg21079": "绝对值编码器电池低电量",
  "lang.mwms.monitorRobotMsg21096": "驱动器温度过低",
  "lang.mwms.monitorRobotMsg21095": "驱动器温度过高",
  "lang.mwms.monitorRobotMsg21094": "驱动器电机温度过低",
  "lang.mwms.monitorRobotMsg21093": "驱动器电机温度过高",
  "lang.mwms.monitorRobotMsg21099": "组件发货异常",
  "lang.mwms.monitorRobotMsg21098": "驱动器地址错误",
  "lang.mwms.monitorRobotMsg21097": "驱动器超速报警",
  "lang.ark.fed.component.workflow.label.specifyEquip": "指定设备",
  "lang.mwms.monitorRobotMsg21092": "驱动器电流短路",
  "lang.mwms.monitorRobotMsg21091": "驱动器过电流",
  "lang.mwms.monitorRobotMsg21090": "驱动器过电压",
  "lang.ark.warehouse.hasSameCellCode": "存在相同工位点位",
  "lang.ark.fed.systemexceptionPleaseContactTheAdministrator": "系统异常，请联系管理员",
  "lang.ark.fed.orderNo": "订单号",
  "lang.ark.fed.deliveryCompletionTime": "配送完成时间",
  "lang.ark.fed.robotDeviceComponent": "机器人组件设备",
  "lang.ark.fed.selected.containerCode": "选择点位或容器编码",
  "lang.ark.fed.screen.equipmentAssociatedInfo.dialogDetail.titleView": "查看设备关联信息",
  "lang.ark.fed.logicOr": "+或运算符(||)",
  "lang.mwms.monitorRobotMsg21063": "充电无电流",
  "lang.mwms.monitorRobotMsg21062": "相机上传的图像数据解析头尾校验和失败",
  "lang.mwms.monitorRobotMsg21061": "相机上传的图像数据解析头尾校验失败",
  "lang.ark.fed.thereAreNoProcessNodesPleaseClickAdd": "暂无流程节点，请点击添加~",
  "lang.ark.warehouse.transfer": "搬运",
  "lang.mwms.monitorRobotMsg21060": "机器人避障",
  "lang.ark.fed.screen.hybridRobot.offsetValueL": "L面偏移值",
  "lang.ark.fed.errorSide": "容器面错误，请确认条码",
  "lang.mwms.monitorRobotMsg21067": "驱动轮电机过流",
  "lang.ark.thisWorkstationStopPointNotOnlyOne": "该工作站不存在点位或不唯一",
  "lang.mwms.monitorRobotMsg21066": "驱动轮充电过流",
  "lang.ark.fed.containerCode": "容器编码",
  "lang.mwms.monitorRobotMsg21065": "2s内避障原始数据未更新",
  "lang.mwms.monitorRobotMsg21064": "充电传感器故障",
  "lang.ark.warehouse.workflowTemplateDescription": "工作站绑定流程模板，即工作站送料、搬运（管理模式）可按绑定模板的交互方式执行送料和搬运任务",
  "lang.ark.fed.screen.hybridRobot.offsetValueR": "R面偏移值",
  "lang.ark.fed.deliveryOrder": "配送顺序",
  "lang.ark.fed.isDefaultAction": "默认交互",
  "lang.mwms.monitorRobotMsg21059": "后防撞条触发",
  "lang.mwms.monitorRobotMsg21058": "货架二维码解码错误。如：已解出黑框，但是码值错误",
  "lang.mwms.monitorRobotMsg21057": "前防撞条触发",
  "lang.ark.fed.interfaceLocation": "接口地址",
  "lang.ark.fed.goodsCode": "物料编码",
  "lang.ark.warehouse.estimateUseTimeUnit.hour": "小时",
  "lang.ark.action.interface.failure": "失败",
  "lang.mwms.monitorRobotMsg21074": "激光雷达数据丢失，或器件故障",
  "lang.mwms.monitorRobotMsg21073": "向上货架二维码解码超时",
  "lang.ark.fed.tuesday": "周二",
  "lang.mwms.monitorRobotMsg21072": "向上货架二维码解码超时",
  "lang.mwms.monitorRobotMsg21071": "货架二维码解码失败。如：连黑框都未解析出来",
  "lang.ark.fed.addStation": "新增工位",
  "lang.mwms.monitorRobotMsg21078": "电机模组不可恢复故障（换件）",
  "lang.mwms.monitorRobotMsg21077": "电机模组可恢复故障",
  "lang.mwms.monitorRobotMsg21076": "电池过温保护",
  "lang.mwms.monitorRobotMsg21075": "电池数据丢失",
  "lang.ark.fed.menu.apiSchema": "接口配置",
  "lang.ark.fed.syncInformSuccess": "信息同步成功",
  "lang.mwms.monitorRobotMsg21070": "DSP数据反馈错误",
  "lang.ark.workflow.area.queueStrategy": "排队策略",
  "lang.ark.trafficControl.enterType": "通行方式",
  "lang.ark.fed.uncheckedDataToBeDeleted": "未勾选需要删除的数据！",
  "lang.mwms.monitorRobotMsg21069": "DSP丢失心跳，在正确二维码位置没有反馈",
  "lang.mwms.monitorRobotMsg21068": "提升电机过流",
  "lang.ark.areaCode.not.exist.stop.range": "该区域不存在急停区",
  "lang.mwms.monitorRobotMsg21041": "驱动器失联",
  "lang.ark.roller.docking.feeding": "辊筒对接上料",
  "lang.ark.fed.menu.robotUpgradeLog": "升级日志",
  "lang.mwms.monitorRobotMsg21040": "CAN2通讯故障。如：400ms内未收到数据",
  "lang.ark.fed.selectMaterialAndQuantity": "选择物料和数量",
  "lang.mwms.monitorRobotMsg21045": "陀螺前后两次标定的基准值变化过大",
  "lang.ark.workflow.workflowTaskFetchDuplicate": "取操作重复",
  "lang.mwms.monitorRobotMsg21044": "陀螺温度变化过大",
  "lang.mwms.fed.user.disable": "启用|禁用用户",
  "lang.mwms.monitorRobotMsg21043": "DSP失联。如：40s内未收到DSP数据",
  "lang.mwms.monitorRobotMsg21042": "编码器失联",
  "lang.ark.fed.pleaseSelectTheControlButtonToDelete": "请选择要删除的控制按钮",
  "lang.ark.workflow.arrive.action.goTurnOfAngle.pos270": "270°",
  "lang.mwms.monitorRobotMsg21038": "电源盒收到错误的电池数据",
  "lang.mwms.monitorRobotMsg21037": "电源盒与主控通讯链路异常",
  "lang.mwms.monitorRobotMsg21036": "避障盒/上位机收到错误避障传感器数据",
  "lang.mwms.monitorRobotMsg21035": "避障盒与主控通讯链路异常",
  "lang.ark.fed.fiveLight": "五色灯",
  "lang.ark.fed.successHandler": "成功处理",
  "lang.ark.fed.normal": "正常",
  "lang.mwms.monitorRobotMsg21039": "CAN1通讯故障。如：400ms内未收到数据",
  "lang.mwms.monitorRobotMsg21052": "保存图像时出错，如：地面二维码校准误差>10°，侧边误差>4cm",
  "lang.mwms.monitorRobotMsg21051": "直线运动时右轮打滑",
  "lang.mwms.monitorRobotMsg21050": "直线运动时左轮打滑",
  "lang.ark.fed.component.workflow.label.equipType": "设备类型",
  "lang.ark.fed.serialNumber": "序列号",
  "lang.mwms.monitorRobotMsg21056": "驱动轮编码器脉冲数溢出，即计数超过上限",
  "lang.mwms.monitorRobotMsg21055": "驱动轮编码器脉冲数未更新。如：直线或弧线运动下，编码器故障",
  "lang.mwms.monitorRobotMsg21054": "提升电机无法举升。如：在举升状态下20s内没有举升托盘即报错",
  "lang.mwms.monitorRobotMsg21053": "非二维码区域图像反馈，如：丢码时报错",
  "lang.ark.api.flowStrategyIsNull": "flowStrategy不能为空",
  "lang.ark.fed.operationalProcessConfiguration": "操作流程配置",
  "lang.ark.apiCommonCode.locationToNotExists": "locationCode:{0}不存在",
  "lang.ark.fed.noAvailableRobotsWereFound": "未找到可用机器人",
  "lang.ark.fed.menu.interfaceLog": "接口日志",
  "lang.ark.fed.orderTaskHang": "单据挂起",
  "lang.ark.fed.trafficControl": "交通管制",
  "lang.ark.fed.stationExistPassWorkflowInstance": "存在经过该工作站的流程实例，请稍后再试",
  "lang.ark.waveStatus.distributed": "配送完成",
  "lang.mwms.monitorRobotMsg21049": "驱动轮锁死故障。如：在直线、弧线、旋转等运动模式下，超过2秒没有运动，即认为驱动轮锁死",
  "lang.mwms.monitorRobotMsg21048": "200s之内没收到电池数据更新",
  "lang.mwms.monitorRobotMsg21047": "2s之内没收到避障数据更新",
  "lang.mwms.monitorRobotMsg21046": "旋转时驱动轮打滑",
  "lang.mwms.monitorRobotMsg.targetnotfree": "目标点(目标区域)不空闲",
  "lang.ark.illegal_containerType_code": "容器编码格式不正确",
  "lang.ark.fed.menu.editWorkstationConfig": "工作站功能设置",
  "lang.mwms.monitorRobotMsg21023": "获取dsp数据ceil rect超时",
  "lang.mwms.monitorRobotMsg21022": "丢失地面二维码超过2个",
  "lang.mwms.monitorRobotMsg21021": "放货架时货架二维码位置很差就不往下放，防止撞到旁边的货架",
  "lang.mwms.monitorRobotMsg21020": "旋转时调整货架拍到货架位置比较差",
  "lang.ark.container.entry.failed": "容器入场失败",
  "lang.ark.apiRobotTaskCode.robotIdNotEmpty": "机器人id不能为空",
  "lang.ark.apiContainerCode.containerCategoryOnlyOneNotMatch": "没有匹配到唯一的containerCategory",
  "lang.ark.hitStrategy.shelfDenseStorage": "支架长廊密集存储",
  "lang.ark.action.interface.containerCode": "containerCode",
  "lang.ark.fed.completed": "已完成",
  "lang.mwms.monitorRobotMsg21016": "充电后退方向不对",
  "lang.ark.apiRobotTaskCode.robotTaskNotCreate": "机器人任务没有生成",
  "lang.mwms.monitorRobotMsg21015": "充电站位置xy偏差整点坐标20mm",
  "lang.mwms.monitorRobotMsg21014": "机器人走到充电站位置偏差xy坐标20mm",
  "lang.ark.fed.excel.deviceCodeNotExist": "{0}行设备编码不存在，请检查修改后再上传",
  "lang.ark.fed.warehouse.goods": "物料",
  "lang.mwms.monitorRobotMsg21013": "机器人停止模式下编码器角度变化检测。如：机器人停止状态下被外力转动则可能报错",
  "lang.ark.fed.ruleExpression": "表达式",
  "lang.mwms.monitorRobotMsg21019": "机器人当前点与路径起始位置超过限制",
  "lang.mwms.monitorRobotMsg21018": "下降时高度参数不对",
  "lang.mwms.monitorRobotMsg21017": "举升时高度参数不对",
  "lang.mwms.monitorRobotMsg21030": "运动时车轮打滑",
  "lang.ark.workflow.task.status.InterruptWaiting": "中断等待",
  "lang.mwms.monitorRobotMsg21034": "避障盒/上位机未收到避障传感器数据",
  "lang.mwms.monitorRobotMsg21033": "进入了手动模式",
  "lang.mwms.monitorRobotMsg21032": "急停开关触发",
  "lang.mwms.monitorRobotMsg21031": "货架模型识别异常",
  "lang.ark.fed.addRack": "添加货架",
  "lang.ark.fed.areYouSureToDeleteTheListInformation": "确定删除列表信息么？",
  "lang.ark.fed.interfaceTaskCode": "外部任务号",
  "lang.ark.fed.severityLevel": "严重级别",
  "lang.mwms.monitorRobotMsg21027": "充电失败",
  "lang.mwms.monitorRobotMsg21026": "手动模式收到了去执行任务错误",
  "lang.mwms.monitorRobotMsg21025": "规划小路径时角度错误",
  "lang.ark.fed.nodeActionName": "节点交互配置名称",
  "lang.mwms.monitorRobotMsg21024": "获取dsp数据ceil decode超时",
  "lang.mwms.monitorRobotMsg21029": "切换二维码导航后踩不到第1个地面二维码",
  "lang.ark.waveTaskStatus.distributing": "配送中",
  "lang.mwms.monitorRobotMsg21028": "行走过程中，向上货架二维码不在视野范围内",
  "lang.ark.fed.hybridRobot.hybridRobotType.singleLift": "单举升",
  "lang.ark.fed.receiveCallTask": "接收叫料任务",
  "lang.mwms.monitorRobotMsg21001": "直线行走角度偏差超过3°",
  "lang.ark.fed.waveOrders": "单据数",
  "lang.mwms.monitorRobotMsg21000": "末端二维码定位侧向偏差>20mm；角度大于2°",
  "lang.ark.fed.deliverOrder": "配送顺序",
  "lang.ark.fed.leftRotation": "左旋转",
  "lang.ark.fed.waveTriggerCondition": "触发条件",
  "lang.ark.fed.queueUp": "排队",
  "lang.ark.hitStrategy.cyclicSave": "循环存放",
  "lang.ark.fed.menu.vens.equipmentAssociatedInfo": "提升机配置",
  "lang.ark.fed.optionalDockPointPosition": "可选点位",
  "lang.ark.fed.second": "秒",
  "lang.ark.fed.interfaceInstructMsg1": "请至少新增一条传参赋值！",
  "lang.ark.workflow.area.noorder": "不排队",
  "lang.ark.fed.interfaceInstructMsg2": "请至少新增一条返参处理！",
  "lang.ark.fed.menu.apiPlatform": "系统接口",
  "lang.ark.fed.interfaceInstructMsg0": "传参赋值或返参处理中有数据未保存！",
  "lang.mwms.monitorRobotMsg21012": "机器人停止模式下图像融合角度变化检测。如：机器人停止状态下被外力转动则可能报错",
  "lang.mwms.monitorRobotMsg21011": "机器人停止模式下陀螺积分检测。如：机器人停止状态下被外力转动则可能报错",
  "lang.ark.fed.rackName": "货架名称",
  "lang.mwms.monitorRobotMsg21010": "举升货架时举歪",
  "lang.ark.fed.day": "天",
  "lang.ark.workflow.task.status.assign": "任务已下发",
  "lang.authManage.web.common.modifySuccess": "修改成功",
  "lang.ark.interface.interfaceDesc": "接口描述",
  "lang.ark.apiContainerCode.mapRemoveContainerFail": "从地图中移除容器失败",
  "lang.ark.fed.confirmCallRobot": "确认呼叫机器人",
  "lang.authManage.web.common.dataPermission": "数据权限",
  "lang.ark.fed.copySuccess": "复制成功！",
  "lang.ark.fed.shelfEditor": "货架编辑",
  "lang.ark.fed.chromeForbidScan": "2、浏览器开启了摄像头扫码，摄像头被禁止，点击浏览器地址栏安全警示标记，按照浏览器引导开启摄像头。",
  "lang.ark.workflow.goShift": "偏移",
  "lang.mwms.monitorRobotMsg21005": "控制指令方向和实际角度旋转方向不一致。如：可能指令错误，可能负载过大",
  "lang.mwms.monitorRobotMsg21004": "旋转时驱动轮打滑",
  "lang.mwms.monitorRobotMsg21003": "机器人中心位置偏移超限制",
  "lang.mwms.monitorRobotMsg21002": "旋转积分角度和编码器角度的差值超限制",
  "lang.mwms.monitorRobotMsg21009": "货架二维码和地面二维码相对误差超限制",
  "lang.ark.workflow.existsAttachTaskNotComplete": "当前流程存在子节点流程未完成，不可执行完成操作",
  "lang.ark.fed.unloading": "未上料",
  "lang.mwms.monitorRobotMsg21008": "旋转货架角度超过180°",
  "lang.mwms.monitorRobotMsg21007": "进田字格场景，目标姿态和停止姿态误差超限制",
  "lang.mwms.monitorRobotMsg21006": "根据点拟合圆弧时，轨迹平滑度累计误差超限制",
  "lang.ark.robot.robotExist": "机器人已存在，不允许重复添加！",
  "lang.ark.fed.personalizationOptions": "个性化选项",
  "lang.ark.fed.executionStatus": "执行状态",
  "lang.ark.fed.cancelWave": "取消波次",
  "lang.ark.fed.noWorkflowPleaseCreateANewWorkflow": "暂无流程，请新建流程",
  "lang.ark.fed.rackEntry": "货架录入",
  "lang.ark.fed.workstation.noMoreWorkstations": "没有可切换的工作站",
  "lang.ark.fed.slight": "轻微",
  "lang.ark.fed.childWorkflowUnpublished": "子流程{0}未发布，请先发布子流程！",
  "lang.ark.fed.removeFromTheSystem": "从系统移除",
  "lang.authManage.web.others.importLicense": "导入证书",
  "lang.ark.fed.theConnectionBetweenTheDockingPointAndWorkstationCannotBe": "点位／工作站 有关联关系的 不能互相连线",
  "lang.gles.strategy": "策略管理",
  "lang.ark.fed.curvePathOfRobotWalkable": "机器人可行走的曲线路径",
  "lang.ark.apiContainerCode.containerCategoryMustSelect": "必须指定容器类型",
  "lang.ark.fed.menu.physicalButtonLog": "呼叫器日志",
  "lang.mwms.fed.taskResolve": "任务拆分",
  "lang.ark.workflow.taskNodeWaiting": "节点等待",
  "lang.ark.fed.roller": "辊道",
  "lang.mwms.pickingException": "盘点异常",
  "lang.ark.fed.screen.equipmentAssociatedInfo.rowDeleteConfirm": "删除后将无法恢复，确认删除吗？",
  "lang.ark.fed.pleaseSelectTheRobotBeforeDrawingTheMap": "绘制地图前，请先选择机器人",
  "lang.ark.record.robotCallback.fbShift": "机器人{0}前后偏移",
  "lang.ark.apiCommonCode.robotTypeNotEmpty": "robotType参数不能为空",
  "lang.ark.waveStatus.disCanceled": "波次取消",
  "lang.ark.fed.theAudioCacheing": "试听音频缓冲中，稍后尝试~",
  "lang.ark.fed.baseWorkStation": "标准工作站",
  "lang.ark.fed.thereIsNoWorkflowAtThisDockPoint": "此停靠点暂无流程",
  "lang.ark.countNum": "总数:",
  "lang.ark.fed.allowPutDown": "{0}只允许配置一条放下",
  "lang.ark.workflow.recycleTypeNoConfig": "取消时货物送回位置未配置",
  "lang.ark.fed.containerType": "容器类型",
  "lang.ark.fed.upload": "上传",
  "lang.ark.fed.priority": "优先级",
  "lang.mwms.monitorRobotMsg.sendtaskfail": "任务发送RMS异常，请检查RMS运行状态",
  "lang.authManage.web.common.creatTime": "创建时间",
  "lang.ark.fed.unloadWholeOrder": "整单下料",
  "lang.ark.warehouse.hasSameProductionLineName": "存在相同产线名称",
  "lang.ark.fed.cycleTimes": "循环次数",
  "lang.ark.fed.cancel": "取消",
  "lang.ark.fed.screen.hybridRobot.robotBindDevice": "机器人与上装绑定",
  "lang.ark.fed.startPointName": "起点名称",
  "lang.ark.fed.materialNo": "单据行号",
  "lang.ark.fed.workstationOnlyOneUser": "（同一工作站不允许多个用户同时登录）",
  "lang.ark.fed.cellCodeType": "编码类型",
  "lang.ark.workflow.template.type.nodeToNode": "点到点任务",
  "lang.ark.fed.goodsLocationLimtMax": "货位物料数量上限",
  "lang.ark.fed.screen.hybridRobot.robotBodyTip": "数据来自rms机器人信息",
  "lang.ark.interface.interfaceDesc.type": "字段类型",
  "lang.ark.externalDevice.cautionContent": "所选设备类型对应设备交互动作不完全相同，要求必须相同，请检查并修改！",
  "lang.ark.apiStationCode.stationStopPointNotMatch": "工作站内停靠点不匹配",
  "lang.ark.workflow.arrive.action.goTurnOfAngle.neg360": "-360°",
  "lang.ark.fed.pleaseUploadImage": "请先上传正确的条码图片",
  "lang.ark.fed.fullBinsChange": "货位已满，请切换货位",
  "lang.ark.fed.destGroup": "节点分组",
  "lang.ark.fed.lower": "下",
  "lang.ark.fed.operationMode": "运行模式",
  "lang.ark.fed.dynamicNotAllowBranch": "动态目标点不允许使用分支",
  "lang.ark.fed.beforeExecute": "每次执行前，保留最近",
  "lang.ark.fed.automaticExecutionExpected": "自动执行中，预计",
  "lang.ark.fed.areUSureStop": "确定急停吗？",
  "lang.ark.fed.optionsSetting": "选项设置",
  "lang.ark.dynamicTemplate.nextPoint": "下一个点",
  "lang.ark.fed.timeRange": "时间范围",
  "lang.ark.fed.runMode.load": "负载",
  "lang.ark.fed.confirmShelfLeave": "容器离场库存状态清空，确认离场？",
  "lang.ark.fed.creator": "创建人",
  "lang.ark.fed.containsIllegalCharacters": "含有非法字符",
  "lang.ark.fed.menu.vens.dmpHeartbeat": "设备心跳",
  "lang.ark.workflow.notAllowCancelIfTaskPhase": "当前阶段已不支持取消！",
  "lang.ark.fed.notEnabled": "未启用",
  "lang.ark.fed.singleFactoryMultipleEntrances": "单厂家多入口",
  "lang.authManage.web.permission.roleName": "角色名称",
  "lang.ark.fed.productLineAutoSelect": "按物料自动分配",
  "lang.ark.fed.stationCode": "工作站编码",
  "lang.ark.fed.actionsNotAllowAddContainer": "{0}节点交互配置错误，不可添加容器！",
  "lang.ark.warehouse.lineStationNotEmpty": "产线工位不能为空",
  "lang.ark.button.operation.command.send": "送",
  "lang.ark.fed.abnormalInformation": "异常信息",
  "lang.mwms.fed.warehouseVolumeCharts": "库容报表",
  "lang.ark.workflow.platformPriority": "有物流容器（货架）优先",
  "lang.ark.fed.shelfAttribute.RETURN": "RETURN",
  "lang.ark.goodsTask.export.title.fileName": "领料单",
  "lang.ark.fed.systemVersion": "系统版本号",
  "lang.ark.interface.apiNext": "流程继续执行",
  "lang.ark.fed.illegalProcess": "不合法流程",
  "lang.ark.workflow.buttonAlreadyExists": "此控制器按钮已存在，不能重复添加",
  "lang.ark.fed.theConnectionIsClosedAndReconnectedToTheServer": "连接被关闭，重新连接服务器中..",
  "lang.ark.robotDeviceComponent.robotType": "机器人型号",
  "lang.ark.fed.amount": "取放数量",
  "lang.mwms.fed.roleManage": "角色管理",
  "lang.ark.workflow.arrive.action.goTurnOfSide.side": "旋转至",
  "lang.ark.warehouse.zagvdbmNotexits": "上料点:{0}不存在",
  "lang.ark.workflow.area.releaseStrategy": "放行策略",
  "lang.ark.fed.remark": "备注",
  "lang.ark.fed.confirmMoving": "是否确认移动？",
  "lang.ark.workflow.task.status.canceled": "取消完成",
  "lang.ark.fed.workFlowNodeNotEdit": "当前节点无编辑项",
  "lang.ark.workflowConfig.beginOrEndNodeCanNotEmpty": "流程需要配置开始节点和结束节点",
  "lang.ark.fed.goodsLocationInfo": "货位信息",
  "lang.ark.fed.robotStrategy": "机器人策略",
  "lang.ark.workflow.condition.in": "包含",
  "lang.ark.fed.allType": "全部类型",
  "lang.ark.warehouse.materialPointOrderNotNull": "上料顺序不能为空",
  "lang.ark.api.goturn.turnangleError": "容器旋转参数turnAngle值不为90倍数",
  "lang.ark.fed.pickUpTaskNumber": "领料单号",
  "lang.ark.equipment.notSelectExistEquipment": "不能新增已经存在的设备，已有的设备名称：{0}",
  "lang.ark.warehouse.hasSameSort": "存在相同配送顺序",
  "lang.ark.interface.interfaceDesc.format": "字段长度",
  "lang.ark.workflow.wareHouseAllType": "全部",
  "lang.ark.fed.ruleName": "规则名称",
  "lang.ark.fed.screen.equipmentAssociatedInfo.columnEquipType": "设备类型",
  "lang.ark.fed.retryDuration": "重试时间（单位分钟）",
  "lang.mwms.fed.excelImport": "Excel导入查询",
  "lang.ark.fed.describe": "描述",
  "lang.ark.fed.applicationEntryTime": "申请/进入时间",
  "lang.ark.fed.number": "编号",
  "lang.ark.workflow.putDownStuff": "放货",
  "lang.ark.fed.mapManagement": "地图管理",
  "lang.ark.fed.screen.hybridRobot.binOrderTip": "机台的货位顺序号",
  "lang.ark.action.interface.conditionValue": "条件值",
  "lang.ark.fed.interactionOfInternalInterface": "内部接口交互",
  "lang.ark.operatelog.operatetype.auto.group": "自动流程（流程组）",
  "lang.ark.fed.demandForMaterialsTitle": "需求叫料：经过工作站的所有流程，该工作站都能发起",
  "lang.ark.fed.screen.equipmentAssociatedInfo.dialogDetail.btnAddEquip": "新增设备",
  "lang.ark.fed.uploadImageMessage2": "上传的图片文件最好可以在白色底色下清晰可见",
  "lang.mwms.fed.putaway": "上架管理",
  "lang.ark.fed.cancelMiddlePoint": "取消途经点",
  "lang.ark.fed.updateMap": "更新地图",
  "lang.ark.fed.screen.hybridRobot.robotPointOffset": "机器人点位偏移量",
  "lang.ark.robot.go.receive": "自动收货",
  "lang.gles.logisticsConfig.stationDefaultConfig": "默认配置",
  "lang.authManage.web.others.packup": "收起",
  "lang.ark.fed.uploadImageMessage1": "上传的图片文件最好可以在深灰底色下清晰可见",
  "lang.ark.fed.uploadImageMessage0": "上传的图片文件最好可以在浅蓝底色下清晰可见",
  "lang.ark.fed.twoPointMode": "双点模式",
  "lang.ark.fed.goodsPlaceType.put": "放料口",
  "lang.ark.fed.lowVolumeCharging": "低电量充电",
  "lang.ark.fed.emptyContainerArea": "空箱返还区",
  "lang.ark.robot.go.return.box": "去还箱子",
  "lang.ark.workflow.enterMapDestNotExistReturnFail": "货架不存在入场终点，空箱返还失败！",
  "lang.ark.robotDeviceComponent.recordAlreadyExist": "记录已存在，不能重复添加！",
  "lang.ark.workflow.sendOperationFailed": "送操作失败",
  "lang.ark.fed.screen.hybridRobot.robotTypeNotAddedTip": "该型号未在复合机器人类型中添加，请先添加",
  "lang.ark.workflow.function.type.recycleArea": "回收区",
  "lang.ark.workflowConfig.cellFunctions.skip": "跳过功能",
  "lang.ark.sys.config.group.other": "其他配置",
  "lang.ark.fed.homePageBackgroundImageUploadViewSuccess": "首页背景图上传成功！",
  "lang.authManage.web.common.userName": "用户名",
  "lang.ark.fed.byRackCode": "按货架编码",
  "lang.ark.fed.areUSureClearLock": "确定解锁吗？",
  "lang.ark.workflowRule.referenced.cannot.be.delete": "规则配置被流程配置引用，不能删除",
  "lang.ark.fed.materialStation": "可配料工位",
  "lang.ark.workflow.cycleType.infiniteLoop": "无限循环",
  "lang.ark.fed.rackEntrance": "容器入场",
  "lang.ark.fed.closeAutoFlow": "关闭自动流程",
  "lang.ark.workflow.areaInUsed": "区域编码({})已存在，请检查！",
  "lang.ark.fed.robot": "机器人",
  "lang.ark.fed.sourceOrDestination": "来源/去向",
  "lang.ark.fed.externalDeviceType": "设备类型",
  "lang.ark.fed.enabledShelfAttribute": "是否需选择物料类别",
  "lang.ark.fed.dataType.value": "参数类型",
  "lang.ark.apiContainerCode.containerCodeNotBlank": "必须指定容器号",
  "lang.ark.fed.messagePush": "消息推送",
  "lang.ark.fed.fullWorkStation": "FULL版工作站",
  "lang.ark.fed.excel.deviceCodeRepeat": "{0}行数据的参数{1}重复，请检查修改后再上传",
  "lang.ark.fed.issuedQuantity": "实发数量",
  "lang.ark.fed.flowTemplate": "流程模板",
  "lang.gles.stock": "库存管理",
  "lang.ark.fed.taskStartTime": "任务开始时间",
  "lang.ark.workflow.shelfEntryNotSupported": "不支持容器入场",
  "lang.ark.fed.unsupportCamera": "您的浏览器未开启摄像头扫码，请使用上传图片进行解析条码",
  "lang.ark.fed.serious": "严重",
  "lang.ark.fed.piece": "条",
  "lang.ark.fed.dockPoint": "停靠点",
  "lang.mwms.fed.putawayRuleDesignatedShelf": "上架规则指定货架",
  "lang.mwms.fed.freeze": "库存冻结",
  "lang.ark.action.interface.locationTo": "locationTo",
  "lang.ark.fed.allValuesInTheCollectionAndRelationships": "集合内所有值为且关系",
  "lang.ark.fed.thereIsNoOptionalRobot": "暂无可选择的机器人！",
  "web.c.RoleAPI.item0090": "角色名称已存在，请使用其它名称！",
  "lang.ark.fed.previousStation": "起始工位",
  "lang.ark.fed.pleaseEnterANonzeroPositiveInteger": "请输入非0正整数",
  "web.c.RoleAPI.item0092": "角色关键字不能为空！",
  "web.c.RoleAPI.item0091": "角色添加失败！",
  "web.c.RoleAPI.item0094": "修改失败！",
  "lang.ark.fed.currentPoint": "当前点",
  "lang.ark.fed.authWorkStation": "工作站用户权限",
  "web.c.RoleAPI.item0093": "角色名称不能为空！",
  "web.c.RoleAPI.item0096": "请选择一个角色！",
  "lang.ark.fed.taskDashboard": "任务看板",
  "web.c.RoleAPI.item0095": "删除失败！",
  "lang.ark.fed.pleaseSelectWorkStation": "请至少选择一个工作站！",
  "web.c.RoleAPI.item0089": "还没有角色数据",
  "lang.ark.fed.import": "导入",
  "lang.ark.fed.workOrientation": "工作朝向:",
  "lang.ark.fed.floorNo": "第{0}层",
  "lang.ark.fed.destPoint": "目标点位",
  "lang.ark.workflow.actionNameExists": "交互名称已存在！",
  "lang.ark.targetNotFree": "目标点处于非空闲状态!",
  "lang.ark.robot.robotModelBindTopModulesNotAllowDelete": "已与上装绑定的复合机器人类型不允许删除",
  "lang.ark.fed.autoRefreshEvery20Seconds": "监控记录每20s自动刷新",
  "lang.ark.fed.rackStatusRecovery": "货架状态恢复",
  "lang.ark.fed.email": "邮箱",
  "lang.ark.fed.SMPAlter": "告警通知",
  "lang.ark.fed.triggerContent": "触发内容",
  "lang.ark.fed.chargingStation": "充电站",
  "lang.ark.workflow.noContainersWereFoundThatCouldBeMoved": "没发现可以移动的容器!",
  "lang.ark.fed.selectMaterial": "选择物料",
  "lang.ark.fed.boxMoving": "容器搬运",
  "lang.ark.fed.monitor.taskDashboard": "任务看板",
  "lang.ark.singleCellStation.canNotEdit": "单点工位不允许编辑",
  "lang.ark.fed.required": "必填",
  "lang.ark.fed.wareHouseSetting": "出入库设置",
  "lang.ark.warehouse.configEnableDescription": "关闭启用方可修改配置，请确保在非工作时段修改",
  "lang.ark.fed.goodsNonExistent": "当前物料不存在，请切换物料",
  "lang.ark.fed.exceptionDescription": "异常描述",
  "lang.ark.fed.deliveryCompleteTime": "配送完成时间",
  "lang.ark.workflow.archiveType": "归档类型",
  "lang.ark.interface.apiWorkflowList": "流程查询",
  "lang.ark.fed.failedRetryTime": "轮询时间",
  "lang.ark.fed.shelfAttribute.P1": "P1",
  "lang.ark.fed.shelfAttribute.P3": "P3",
  "lang.ark.fed.soon": "即将",
  "lang.ark.fed.pleaseEnterContent": "请输入内容",
  "lang.ark.button.operation.command.systemControl": "系统控制",
  "lang.ark.fed.menu.editingWorkflow": "流程",
  "lang.ark.fed.paramConfig": "参数配置",
  "lang.gles.baseData.warehouseArea": "库区",
  "lang.ark.fed.robotMonitoring": "机器人监控",
  "lang.ark.fed.warnType": "告警方式",
  "lang.ark.interface.apiCancel": "任务撤销",
  "lang.mwms.fed.index": "首页",
  "lang.ark.shelf.status.noValiable": "无可用货架",
  "lang.ark.fed.interface": "接口指令",
  "lang.ark.workflow.task.status.cancelToNewAssignation": "送到指定点",
  "lang.ark.fed.waitingToSend": "等待发送",
  "lang.ark.fed.moveTo": "移动",
  "lang.ark.fed.forceDeleteConfirmMsg1": "流程取消失败, 是否进行流程强制删除",
  "lang.ark.workflow.shelfExchange": "货架交换",
  "lang.ark.fed.id": "编码:",
  "lang.mwms.fed.receive": "到货验收",
  "lang.ark.fed.onePicking": "一车领料",
  "lang.ark.fed.palletPoint": "托盘位",
  "lang.ark.fed.batchCancellation": "批量取消",
  "lang.ark.interface.businessStatus": "任务状态",
  "lang.ark.fed.value": "值",
  "lang.ark.robot.invalid": "非法模式",
  "lang.ark.fed.reqNum": "原单号",
  "lang.ark.fed.startScanningTheMap": "开始扫描地图",
  "lang.ark.fed.file": "文件",
  "lang.ark.workflowOuterCode.exists": "流程外部编码不能重复",
  "lang.ark.fed.dayLogo": "天日志",
  "lang.ark.fed.menu.operationLog": "操作日志",
  "lang.ark.fed.task": "任务",
  "lang.ark.loadCarrier.inUsing": "容器使用中,请空闲后再试",
  "lang.ark.fed.normalStation": "普通工作站",
  "lang.ark.fed.operationInformation": "操作信息",
  "lang.ark.interface.response": "响应",
  "lang.ark.record.interface.sendTask": "发送接口指令任务",
  "lang.ark.fed.allOptions": "全部",
  "lang.ark.fed.hostCellCode": "外部编号",
  "lang.ark.fed.lowerSpeed": "慢速",
  "lang.ark.record.robotCallback.arrivePassWaitPoint": "机器人到达途经点",
  "lang.mwms.monitorRobotMsg10069": "驱动器指令错误",
  "lang.ark.fed.menu.mapController": "场地",
  "lang.mwms.monitorRobotMsg10068": "任务步骤stage错误",
  "lang.ark.equipment.notFoundEquipment": "没有找到相应的设备",
  "lang.mwms.monitorRobotMsg10076": "驱动器电流短路",
  "lang.mwms.monitorRobotMsg10075": "驱动器过电流",
  "lang.ark.workflow.workflowTaskStatusError": "流程任务状态不正确",
  "lang.mwms.monitorRobotMsg10078": "驱动器电机温度过低",
  "lang.ark.base.license.sysParamForInstanceIdIsNull": "证书验证的仓库编码为空",
  "lang.mwms.monitorRobotMsg10077": "驱动器电机温度过高",
  "lang.mwms.monitorRobotMsg10072": "驱动器反馈错误",
  "lang.mwms.monitorRobotMsg10071": "驱动器跟踪错误",
  "lang.mwms.monitorRobotMsg10074": "驱动器过电压",
  "lang.mwms.monitorRobotMsg10073": "驱动器欠电压",
  "lang.mwms.monitorRobotMsg10070": "驱动器电机相位错误",
  "lang.ark.fed.segmentName": "链接线名称",
  "lang.ark.fed.menu.config": "配置",
  "lang.ark.workflow.condition.equal": "等于",
  "lang.ark.fed.funcComponent": "功能组件",
  "lang.ark.fed.addInterface": "新增接口指令",
  "lang.ark.fed.component.workflow.tooltip.specifiedEquip": "不配时，默认所选类型下的全部料口都可命中",
  "lang.mwms.monitorRobotMsg10079": "驱动器温度过高",
  "lang.ark.record.upstream.callback": "通知上游",
  "lang.ark.workflow.area.range": "区域范围",
  "lang.ark.fed.delete": "删除",
  "lang.ark.api.workflow.break.failure": "中止失败，机器人任务取消失败",
  "lang.mwms.monitorRobotMsg10082": "驱动器地址错误",
  "lang.ark.fed.containerPoint": "容器点",
  "lang.mwms.monitorRobotMsg10081": "驱动器超速报警",
  "lang.mwms.monitorRobotMsg10080": "驱动器温度过低",
  "lang.authManage.web.common.status": "状态",
  "lang.authManage.web.others.customer": "客户编码",
  "lang.ark.fed.common.btn.edit": "编辑",
  "lang.ark.interface.shelfMovingCallbackMsg": "容器位置变更回调",
  "lang.ark.fed.pleaseSelectTheConsole": "请选择操作端",
  "lang.ark.fed.siteMainInterface": "场地主界面",
  "lang.mwms.monitorRobotMsg10047": "进田字格场景，目标姿态和停止姿态误差超限制",
  "lang.mwms.monitorRobotMsg10046": "根据点拟合圆弧时，轨迹平滑度累计误差超限制",
  "lang.ark.fed.pinkingFinish": "上料完成",
  "lang.mwms.monitorRobotMsg10049": "货架二维码和地面二维码相对误差超限制",
  "lang.ark.workflowConfig.configInUse": "有正在进行中的流程实例，请稍后重试",
  "lang.ark.fed.hour": "小时",
  "lang.mwms.monitorRobotMsg10048": "旋转货架角度超过180°",
  "lang.mwms.monitorRobotMsg10054": "走到充电站位置偏差xy坐标20mm",
  "lang.ark.fed.no": "否",
  "lang.ark.fed.goodsPlaceType": "料口类型",
  "lang.mwms.monitorRobotMsg10053": "停止模式下编码器角度变化检测。如：停止状态下被外力转动则可能报错",
  "lang.mwms.monitorRobotMsg10056": "充电后退方向不对",
  "lang.mwms.monitorRobotMsg10055": "充电站位置xy偏差整点坐标20mm",
  "lang.ark.fed.timeoutInfoTip": "任务看板中的单据看板，显示超时信息",
  "lang.mwms.monitorRobotMsg10050": "举升货架时举歪",
  "lang.ark.workflow.mandatoryAllocation": "强制",
  "lang.ark.workflow.nodeClassification": "节点分类",
  "lang.mwms.monitorRobotMsg10052": "停止模式下图像融合角度变化检测",
  "lang.ark.fed.billCreateFail": "单据创建失败",
  "lang.mwms.fed.strategyOrderTime": "截单时间计算策略",
  "lang.mwms.monitorRobotMsg10051": "停止模式下陀螺积分检测。如：停止状态下被外力转动则可能报错",
  "lang.mwms.fed.sysconfig": "系统配置",
  "lang.ark.warehouse.materialPointNameNotNull": "名称不能为空",
  "lang.ark.agv.instructionRule.executeByNormal": "正常执行",
  "lang.ark.fed.goodsPlaceType.fetch": "取料口",
  "lang.ark.fed.closeMoreGoodsInfo": "收起更多物料信息",
  "lang.mwms.fed.dictSet": "数据字典",
  "lang.ark.fed.allDay": "全天",
  "lang.ark.fed.newAddedSystem": "新加入系统",
  "lang.ark.fed.servicePoints": "服务点位",
  "lang.mwms.monitorRobotMsg10058": "下降时高度参数不对",
  "lang.ark.fed.externalDevice": "设备名称",
  "lang.mwms.monitorRobotMsg10057": "举升时高度参数不对",
  "lang.ark.fed.screen.area.groupName": "分组名称",
  "lang.mwms.monitorRobotMsg10059": "当前点与路径起始位置超过限制",
  "lang.mwms.monitorRobotMsg10065": "规划小路径时角度错误",
  "lang.mwms.monitorRobotMsg10064": "获取dsp数据ceildecode超时",
  "lang.ark.action.interface.robotType": "robotType",
  "lang.mwms.monitorRobotMsg10067": "任务结束时给出错误指令",
  "lang.mwms.monitorRobotMsg10066": "任务状态机切换错误",
  "lang.ark.fed.menu.destPoint": "PDA配置",
  "lang.mwms.monitorRobotMsg10061": "放货架时货架二维码位置很差就不往下放，防止撞到旁边的货架",
  "lang.mwms.monitorRobotMsg10060": "旋转时调整货架拍到货架位置比较差",
  "lang.mwms.monitorRobotMsg10063": "获取dsp数据ceilrect超时",
  "lang.mwms.monitorRobotMsg10062": "丢失地面二维码超过2个",
  "lang.ark.fed.modeTitle": "管理模式：开启，即工作站叫料、送料、搬运业务开启管理模式，可代叫料和送料，以及指定任意点发起搬运任务。",
  "lang.ark.fed.oneWayEntrance": "单行入口",
  "lang.ark.workflow.cycleType.timeLoop": "按次数循环",
  "lang.mwms.monitorRobotMsg22010": "充电模块错误状态",
  "lang.mwms.monitorRobotMsg22011": "充电站上报充电站不可用状态",
  "lang.ark.fed.alarmLevel": "告警级别",
  "lang.ark.fed.excel.data.isBlank": "{0}行数据不全，请补充完整后重新上传",
  "lang.ark.loadCarrier.loadCarrierIsInUse": "容器处于被使用中！",
  "lang.ark.fed.drawTheMap": "进行地图绘制了",
  "lang.ark.fed.shelfHeat": "货架热度",
  "lang.mwms.monitorRobotMsg10029": "驱动器过温",
  "lang.mwms.monitorRobotMsg10028": "货架二维码解码超时",
  "lang.mwms.monitorRobotMsg10025": "DSP丢失心跳，在正确二维码位置没有反馈",
  "lang.mwms.monitorRobotMsg10024": "提升电机过流",
  "lang.ark.fed.classCode": "类别编码",
  "lang.mwms.monitorRobotMsg10027": "货架二维码解码失败。如：连黑框都未解析出来",
  "lang.mwms.monitorRobotMsg10026": "DSP数据反馈错误",
  "lang.mwms.monitorRobotMsg10032": "避障盒/上位机收到错误避障传感器数据",
  "lang.ark.fed.deleteSuccessfully": "删除成功",
  "lang.ark.fed.pleaseEnterAWorkflowName": "请输入流程名称",
  "lang.mwms.monitorRobotMsg10031": "避障盒与主控通讯链路异常",
  "lang.mwms.monitorRobotMsg10034": "电源盒收到错误的电池数据",
  "lang.mwms.monitorRobotMsg10033": "电源盒与主控通讯链路异常",
  "lang.ark.workflow.lastNodeMustConnectionEndNode": "最后一个非子流程节点未连接结束节点！",
  "lang.mwms.monitorRobotMsg10030": "避障盒/上位机未收到避障传感器数据",
  "lang.ark.interface.id": "序号",
  "lang.ark.workflow.template.type": "流程模板类型",
  "lang.mwms.monitorRobotMsg22007": "充电模块过温",
  "lang.mwms.monitorRobotMsg22008": "自动模式充电电流为0",
  "lang.mwms.monitorRobotMsg22009": "充电模块警告状态",
  "lang.mwms.monitorRobotMsg22003": "RMS数据异常",
  "lang.ark.manual.place": "手动放置",
  "biz.UserServiceImpl.updateUserAndRoleRelation.msg1": "admin用户不可编辑",
  "lang.mwms.monitorRobotMsg22004": "RMS命令异常",
  "lang.mwms.monitorRobotMsg22005": "叉车充电站按下急停",
  "lang.ark.fed.enterCode": "输入工位编码或产线编码",
  "lang.mwms.monitorRobotMsg22006": "叉车充电站没检测到传感器",
  "lang.ark.fed.singlePointMode": "单点模式",
  "lang.ark.waveTaskStatus.distributed": "配送完成",
  "lang.mwms.monitorRobotMsg10039": "DSP失联。如：40s内未收到DSP数据",
  "lang.ark.fed.workflowStatus": "流程状态",
  "lang.mwms.monitorRobotMsg10036": "CAN2通讯故障。如：400ms内未收到数据",
  "lang.mwms.monitorRobotMsg10035": "CAN1通讯故障。如：400ms内未收到数据",
  "lang.gles.receipt.warehousingOrder": "入库单",
  "lang.ark.workflow.action.command.paramSourceType": "取值来源",
  "lang.mwms.monitorRobotMsg10038": "编码器失联",
  "lang.mwms.monitorRobotMsg10037": "驱动器失联",
  "lang.mwms.monitorRobotMsg10043": "中心位置偏移超限制",
  "lang.mwms.monitorRobotMsg10042": "旋转积分角度和编码器角度的差值超限制",
  "lang.ark.fed.shelfAttribute.IA": "IA",
  "lang.mwms.monitorRobotMsg10045": "控制指令方向和实际角度旋转方向不一致。如：可能指令错误，可能负载过大",
  "lang.ark.workflow.robotLineUp": "机器人排队",
  "lang.mwms.monitorRobotMsg10044": "旋转时驱动轮打滑",
  "lang.ark.fed.noOperationAvailable": "无可用操作！",
  "lang.mwms.monitorRobotMsg10041": "直线行走角度偏差超过3°",
  "lang.mwms.monitorRobotMsg10040": "直线行走侧边偏差超过20mm",
  "lang.ark.fed.menu.logManagement": "日志",
  "lang.ark.apiCommonCode.robotStopFailed": "机器人急停失败",
  "lang.ark.workflow.workflowHaveFinished": "取消失败，流程已经完成",
  "lang.ark.fed.flowClass": "流程类别",
  "lang.ark.fed.goodsExistent": "当前物料已存在，请切换物料",
  "lang.ark.fed.doYouConfirmTheGeneration": "是否确认生成？",
  "lang.ark.workflow.wareHouseUnifiedConfig": "统一配置",
  "lang.mwms.monitorRobotMsg10": "任务异常",
  "lang.mwms.monitorRobotMsg10007": "直线运动时右轮打滑",
  "lang.mwms.monitorRobotMsg10006": "直线运动时左轮打滑",
  "lang.mwms.monitorRobotMsg10009": "非二维码区域图像反馈，如：丢码时报错",
  "lang.mwms.monitorRobotMsg10008": "保存图像时出错，如：地面二维码校准误差>10°，侧边误差>4cm（无应用）",
  "lang.mwms.monitorRobotMsg10003": "2s之内没收到避障数据更新",
  "lang.mwms.monitorRobotMsg10002": "旋转时驱动轮打滑",
  "lang.gles.baseData.baseFactoryPosition": "库位",
  "lang.mwms.monitorRobotMsg10005": "驱动轮锁死故障。如：在直线、弧线、旋转等运动模式下，超过2秒没有运动，即认为驱动轮锁死",
  "lang.mwms.monitorRobotMsg10004": "200s之内没收到电池数据更新",
  "lang.mwms.monitorRobotMsg10010": "提升电机无法举升。如：在举升状态下20s内没有举升托盘即报错",
  "lang.ark.fed.notification": "通知对象",
  "lang.mwms.monitorRobotMsg10012": "驱动轮编码器脉冲数溢出，即计数超过上限",
  "lang.mwms.monitorRobotMsg10011": "驱动轮编码器脉冲数未更新。如：直线或弧线运动下，编码器故障",
  "lang.gles.receipt.outWarehouseExternalOrder": "外部出库单",
  "lang.ark.fed.designatedRobotNew": "指定机器人",
  "lang.ark.fed.systemEmergencyStop": "系统急停",
  "lang.mwms.fed.base": "基础设置",
  "lang.mwms.monitorRobotMsg22000": "RMS通信中断",
  "lang.mwms.monitorRobotMsg22001": "CAN通信中断(充电模块)",
  "lang.mwms.monitorRobotMsg22002": "屏幕通信中断",
  "lang.ark.warehouse.materialPointOrderNoLessZero": "上料顺序必须大于0",
  "lang.ark.robot.robotNotExist": "机器人不存在",
  "lang.ark.fed.menu.taskLog": "任务日志",
  "lang.mwms.monitorRobotMsg10018": "相机上传的图像数据解析头尾校验和失败",
  "lang.mwms.monitorRobotMsg10017": "提升电机无法下降。如：40s内放不下货架",
  "lang.ark.fed.childWorkflowInUse": "流程{0}执行中，无法卸载！",
  "lang.ark.fed.noMatchCellCode": "目标工位",
  "lang.mwms.monitorRobotMsg10019": "充电无电流",
  "lang.ark.fed.saturday": "周六",
  "lang.mwms.monitorRobotMsg10014": "货架二维码解码错误",
  "lang.mwms.monitorRobotMsg10013": "前防撞条触发",
  "lang.ark.fed.endTime": "结束时间",
  "lang.mwms.monitorRobotMsg10016": "避障触发",
  "lang.mwms.monitorRobotMsg10015": "后防撞条触发",
  "lang.mwms.monitorRobotMsg10021": "2s内避障原始数据未更新",
  "lang.mwms.monitorRobotMsg10020": "充电传感器故障",
  "lang.mwms.monitorRobotMsg10023": "驱动轮电机过流",
  "lang.ark.workflow.action.command.robot.MediaStop": "停止语音播放",
  "lang.mwms.monitorRobotMsg10022": "驱动轮充电过流，非故障",
  "lang.authManage.fed.screen.creditCardLogin.pleaseBrushCard": "请刷工卡",
  "lang.ark.fed.areaGroupCode": "分组编码",
  "lang.ark.fed.take": "取",
  "lang.ark.fed.pleaseSelectATarget": "请选择目的地",
  "lang.ark.interface.responseStatus": "通讯状态",
  "lang.ark.interface.startPoint": "起点编码",
  "lang.ark.fed.materialPreparationPoint": "上料点",
  "lang.ark.fed.agvIsMovingWaitLock": "有AGV运动，等待锁定",
  "lang.ark.warehouse.stationOtherCellCodeNotFree": "工作站中其它点位有任务,不能发起叫料任务",
  "lang.ark.interface.responseDate": "响应时间",
  "lang.ark.fed.offShelves": "去货架",
  "lang.ark.logType.rmsCallbackInfo": "rms回调信息",
  "lang.ark.fed.to": "至",
  "lang.ark.fed.dmpTaskUnComplete": "存在设备交互任务未完成",
  "lang.ark.mechanical.arm.pick.up": "机械臂取走",
  "lang.authManage.web.common.realName": "姓名",
  "lang.ark.fed.enabledScanValidate": "扫码校验容器",
  "lang.ark.robotDeviceComponent.robotId": "机器人id",
  "robot.task.already.send": "已下发机器人，不允许人工完成!",
  "lang.ark.fed.specifications": "规格",
  "lang.ark.fed.throughEscSwitchLineDrawingModeDoublePointSwitchSinglePoint": "通过ESC切换画线模式，双点切换单点，需要点击两次 1. 单点模式（A-B-C顺次点）2. 双点模式（A-B B-C 模式）",
  "lang.ark.fed.triggerWorkflow": "触发流程",
  "lang.ark.fed.screen.equipmentAssociatedInfo.columnEquipName": "设备名称",
  "lang.ark.fed.robotWillArriveText": "机器人: {0}即将到达，剩余距离",
  "lang.ark.deliverOrder.defaultOrder": "产线配送顺序",
  "lang.ark.workflowgroup.triggerpoint.begin": "开始",
  "lang.mwms.homePage": "首页",
  "lang.ark.fed.trafficLockMsg": "区域手动锁定后，需要再次手动释放才能被机器人申请进入，继续锁定？",
  "lang.ark.fed.taskType": "任务类型",
  "lang.mwms.monitorRobotMsg10001": "陀螺前后两次标定的基准值变化过大",
  "lang.mwms.monitorRobotMsg10000": "陀螺温度变化过大",
  "lang.ark.warehouseTask.cuttingTaskOverTime": "工位下料时长预警，已超{0}分钟",
  "lang.ark.interface.interfaceDesc.numbers": "数字",
  "lang.ark.apiCommonCode.locationToNotEmpty": "目标点不能为空",
  "lang.ark.fed.recoverRefresh": "恢复刷新",
  "lang.ark.waitForAssign": "等待分配",
  "lang.ark.fed.revoke": "撤销",
  "lang.ark.fed.arrivedTime": "目标工位停留时长",
  "lang.ark.warehouse.manualOperateTypeIn": "人工入库",
  "lang.ark.workflow.chooseStrategy.exception": "异常完成",
  "lang.ark.fed.scrollNumber": "卷轴号",
  "lang.ark.fed.sureClickConfirm": "确认执行保存操作？",
  "lang.ark.fed.uploadImage": "上传图片",
  "lang.ark.fed.defaultName": "默认名称",
  "lang.ark.fed.liveNotSaveParamConfig": "存在未保存的参数配置，请先保存！",
  "lang.mwms.fed.wareMutex": "物料互斥配置",
  "lang.ark.fed.prepareToExecuteTheWorkflowAutomatically": "准备自动执行流程",
  "lang.ark.fed.operationSuccessfully": "操作成功",
  "lang.ark.fed.noSelectGoods": "暂无已选物料",
  "lang.ark.fed.screen.flowNodeConfig.pleaseInputCellCode": "请输入点位编码",
  "lang.ark.warehouse.goodsManagement": "货位管理",
  "lang.ark.fed.contents.flowConfig.recycleType.autoRecoveryRule.columns.priority": "优先级",
  "lang.mwms.fed.outWarehouseCollect": "出库汇总",
  "lang.ark.fed.theSpaceIsTaken": "货位已占用",
  "lang.ark.fed.stationNumber": "工位数",
  "lang.ark.fed.notMergeNode": "没有可以连接的节点！",
  "lang.authManage.web.common.delete": "删除",
  "lang.ark.fed.reasonsForFailure": "失败原因",
  "lang.ark.fed.status": "状态",
  "lang.ark.fed.arriveRobotActions": "到达后机器人动作",
  "lang.ark.workflow.arrive.action.goTurnOfAngle.pos360": "360°",
  "lang.ark.fed.leavingSoon": "即将离开",
  "lang.ark.fed.tray": "托盘",
  "lang.ark.workflowConfig.cellFunctions.notAllowTurn": "禁止转弯功能",
  "lang.ark.apiContainerCode.containerAlreadyRemoved": "容器{}已离场",
  "lang.ark.fed.config": "配置",
  "lang.ark.warehouse.goodsTaskHasHang": "流程异常取消,单据挂起待处理",
  "lang.gles.workflow.abuttingJoint": "外部对接位",
  "lang.auth.UserAPI.item0306": "启用",
  "lang.ark.fed.workflowGroupConfiguration": "流程组编辑",
  "lang.auth.UserAPI.item0305": "禁用",
  "lang.ark.workflow.initiateNextTaskSimple": "下一步",
  "lang.ark.button.node.type.startPoint": "启动点",
  "lang.ark.workflow.wareHouseAutoCreate": "自动创建",
  "lang.ark.workflow.workflowTaskHasCompleted": "工作流任务已完成",
  "lang.ark.apiContainerCode.angleValueIllegal": "容器角度的取值范围为-180.0到180.0",
  "lang.ark.trafficControl.artificialControlFunction": "人工管制区功能",
  "lang.ark.action.interface.conditionLocationTo": "locationTo",
  "lang.ark.fed.robotInstruct": "机器人指令",
  "lang.ark.warehouse.setPointNumber": "节点编号",
  "lang.ark.workflow.wareHouseConfigMethod": "配置方式",
  "lang.ark.fed.addParam": "新增参数",
  "lang.ark.fed.yes.generateCode": "是-系统生成容器号",
  "lang.ark.fed.component.workflow.label.hoisterFlow": "提升机流程",
  "lang.ark.apiStationCode.stationCodeNotBlank": "stationCode不能为空",
  "lang.ark.fed.targetProductionLine": "需求产线",
  "lang.ark.action.interface.extraParam": "extraParam",
  "lang.ark.fed.endDate": "结束日期",
  "lang.ark.fed.autoDeliver": "主动配送",
  "lang.authManage.web.others.project": "项目ID",
  "lang.ark.workflowConfig.status.error": "异常",
  "lang.ark.fed.deleteAll": "全删",
  "lang.ark.record.dmp.sendTask": "发送设备任务",
  "lang.ark.workflow.arrive.action.goTurnOfAngle.angle": "旋转角度",
  "lang.ark.robot.robotModelNotExist": "对应复合机器人类型不存在",
  "lang.ark.fed.menu.robotParamConfig": "机器人参数",
  "lang.ark.fed.pleaseSelectCycle": "请选择频率",
  "lang.ark.fed.pleaseAtLeastOneCellInfo": "请至少新增一条点位信息",
  "lang.ark.fed.areaCode": "区域编码",
  "lang.ark.workflow.arrive.action.goTurnOfAngle.neg270": "-270°",
  "lang.ark.fed.menu.applyMaintenance": "应用维护",
  "lang.ark.fed.senior": "高级",
  "lang.ark.trafficControl.enterPattern.serialPass": "连续通过",
  "lang.mwms.fed.asn": "入库单",
  "lang.ark.warehouse.theMatraialHaveAnyWave": "物料点存在多个待配送波次",
  "lang.ark.fed.selectRobotsThatNeedToBeDisplayedAndManipulated": "选择需要显示并操作的机器人",
  "lang.auth.UserAPI.item0302": "该用户不存在，请联系管理员！",
  "lang.auth.UserAPI.item0301": "该工卡号已经绑定{0}用户",
  "lang.ark.fed.createNewExcute": "添加条件",
  "lang.ark.fed.workstation": "工作站",
  "lang.ark.workflow.arrive.action.goTurnOfSide": "组件按面旋转",
  "lang.ark.fed.screen.area.grouping": "分组",
  "lang.ark.fed.beforeExecuteSaveListLog": "每次执行前，保留最近{0}条日志",
  "lang.ark.fed.dealSuccessCode": "成功后处理逻辑",
  "lang.ark.fed.systemControl": "系统控制",
  "lang.ark.fed.pleaseSelectTheLastNode": "请选择最后一个节点",
  "lang.authManage.web.common.noPermission": "抱歉，你无{0}系统的权限！",
  "lang.ark.fed.screen.LoginLog.userName": "用户名",
  "lang.ark.fed.padFlowStartAgain": "PAD-流程再启动",
  "lang.ark.api.goturn.isForbidden": "任务在当前状态下不能执行容器旋转",
  "lang.ark.fed.childFlowOnlySaveType": "子流程只允许连接到子流程!",
  "lang.ark.fed.screen.hybridRobot.bindReason": "绑定原因：由于上游只能返给我们货位信息，无法返回我们认识的点位信息，所以需要将货位与停靠位点位进行绑定，当上游返回货位信息时，gms可反查对应的点位，确保对接机台正常",
  "lang.ark.fed.currentNode": "当前点位",
  "lang.ark.fed.monitoringObjects": "监控对象",
  "lang.ark.fed.theTotalNumberOfTrays": "货位总数",
  "lang.ark.fed.isOpenAllQueue": "是否全部开启排队",
  "lang.ark.base.license.licenseExpiredWarningMsg": "证书已过期，系统允许继续使用",
  "lang.ark.fed.pullLoadMore": "上拉加载更多",
  "lang.ark.fed.locked": "已锁定",
  "lang.ark.fed.menu.workflowTrigger": "触发器配置",
  "lang.ark.button.node.type.middlePoint": "中间点",
  "lang.ark.fed.switchingStandPoint": "正在切换点位...",
  "lang.ark.paramNameExist": "名称不能重复",
  "lang.ark.trafficControl.areaLockStatus": "区域状态",
  "lang.ark.fed.screen.flowNodeConfig.offsetParam": "参数值",
  "lang.ark.fed.nodePoint": "节点点位",
  "lang.ark.fed.scrollCode": "卷轴号",
  "lang.ark.apiRobotTaskCode.waitPointTaskContinueFailed": "处于等待点的任务，执行继续失败",
  "lang.authManage.fed.instanceId": "仓库编码",
  "lang.authManage.fed.remainingDays": "剩余天数",
  "lang.ark.agv.instructionRule1": "±50外（不包含50）",
  "lang.authManage.web.others.license": "证书",
  "lang.ark.fed.batchNumber": "批次号",
  "lang.ark.fed.CuttingFinish": "下料完成",
  "lang.ark.workflow.arriveOperation": "到达后机器人动作",
  "lang.ark.fed.resetAll": "全部重置",
  "lang.ark.workflowConfig.status.designing": "设计中",
  "lang.ark.fed.screen.equipmentAssociatedInfo.columnUpdater": "编辑人",
  "lang.ark.fed.pleaseEnterAPositiveNumber": "请输入非负数",
  "lang.ark.fed.menu.flowTemplate": "流程模板",
  "lang.gles.receipt.upAndDownMaterialExternalOrder": "外部上下料单",
  "lang.ark.fed.orderWaveSucess": "组波成功",
  "lang.ark.fed.contents.flowConfig.recycleType.auto": "自动送回预设地点",
  "lang.ark.fed.material": "上料点",
  "lang.gles.material": "物料管理",
  "lang.ark.fed.waitSend": "待配送",
  "lang.ark.fed.batteryTemperature": "电池温度",
  "lang.ark.fed.orderCollection": "领单配送",
  "lang.ark.plugin.pluginType.fetchContainer.way.full": "取满容器",
  "lang.ark.workflowConfig.status.released": "已发布",
  "lang.ark.fed.screen.hybridRobot.pleaseInputIntOffset": "请输入偏移值",
  "lang.ark.workflowTriggerType.workflow": "触发流程",
  "lang.ark.fed.screen.equipmentAssociatedInfo.labelEquipName": "设备名称",
  "lang.ark.fed.everyOnceHappen": "天发生一次",
  "lang.ark.fed.sendGoodsTitle": "送料：开启，即工作站支持手动选择物料发起送料任务，也可领取叫料任务配送。",
  "UserAPI.item0100": "该用户已被禁用，请联系管理员重新启用或者使用其它用户登录",
  "lang.ark.action.interface.instanceId": "instanceId",
  "lang.ark.workflow.action.commandExecutePhase.nextStart": "发起下一任务",
  "lang.ark.fed.secondClassification": "二级分类",
  "lang.ark.element.shelf.point.belong.to.area": "该货架点属于区域",
  "lang.ark.fed.enable": "启用",
  "lang.ark.workflowTriggerStatus.create": "创建",
  "lang.ark.notManualTrigger": "目标点不是手动触发,不支持该操作!",
  "lang.ark.fed.theSameLevelAlterMessageCanNotBeRepeat": "同级别同类型的预警通知不可重复",
  "lang.ark.fed.cellIdleTrigger": "节点空闲触发",
  "lang.ark.fed.addGoods": "添加物料",
  "lang.ark.fed.lineDrawingMode": "画线模式",
  "lang.ark.warehouse.goodsEditError": "物料编辑错误",
  "lang.ark.fed.goodsComplete": "配送完成",
  "lang.ark.interface.requestDate": "请求时间",
  "lang.ark.fed.robotConfigurationEditingPage": "机器人配置编辑页面",
  "lang.ark.workflow.waitRelease": "等待释放",
  "lang.ark.workflowTrigger.logType.all": "全部",
  "lang.ark.fed.taskList": "任务列表",
  "lang.gles.logisticsConfig.workPosition": "工位配置",
  "lang.ark.fed.uploadFailed": "上传失败",
  "lang.ark.workflowConfig.cellFunctions.blockedCell": "BLOCKED_CELL",
  "lang.ark.fed.screen.workflowInfo.requestParam": "请求报文",
  "lang.ark.fed.uninstall": "卸载",
  "lang.ark.fed.signOut": "退出",
  "lang.ark.fed.common.validator.required": "必填",
  "lang.ark.interface.containerAmountNumberTip": "单次最多增加5000，仅支持数字",
  "lang.ark.fed.isForceDelete": "是否强制删除？此操作存在风险，请谨慎操作！",
  "lang.mwms.fed.reportManagement": "报表管理",
  "lang.ark.fed.sendGoods": "送料",
  "lang.ark.fed.warehouse": "仓库管理",
  "lang.ark.fed.calledGoods": "已叫料",
  "lang.ark.workflow.positionIsOccupied": "位置已被占用",
  "lang.ark.workflow.rollOver": "翻转",
  "lang.ark.fed.theRobotIsNotHere": "机器人不在此处",
  "lang.ark.workflowConfig.cellFunctions.omniDirCell": "OMNI_DIR_CELL",
  "lang.ark.fed.liveNotSaveExternalInteraction": "存在未保存的外部交互配置，请先保存！",
  "lang.ark.record.nextTask": "生成下一步任务",
  "lang.ark.fed.addNewRobotInstruct": "新增机器人指令",
  "lang.ark.fed.extraParam1": "extraParam1",
  "lang.ark.workflowConfig.cellFunctions.elevatorCell": "ELEVATOR_CELL",
  "lang.ark.fed.extraParam9": "extraParam9",
  "lang.ark.fed.extraParam8": "extraParam8",
  "lang.ark.fed.extraParam7": "extraParam7",
  "lang.ark.workflow.workflowTaskSendRecover": "流程正在运行，无法恢复",
  "lang.ark.fed.extraParam6": "extraParam6",
  "lang.ark.fed.extraParam5": "extraParam5",
  "lang.ark.fed.extraParam4": "extraParam4",
  "lang.ark.fed.extraParam3": "extraParam3",
  "lang.ark.fed.extraParam2": "extraParam2",
  "lang.authManage.web.common.logout": "退出登录",
  "lang.ark.fed.productLineDetail": "产线详情",
  "lang.ark.fed.containerInfo": "容器信息",
  "lang.ark.fed.screen.equipmentAssociatedInfo.dialogDetail.titleNew": "新增设备关联信息",
  "lang.ark.warehouse.noMatchCellCode": "未匹配到物料目标工位",
  "lang.ark.workflow.template.type.dynamiNodeUnit": "动态单元任务",
  "lang.gles.workflow.MonitorManagement": "监控管理",
  "lang.ark.base.license.sysParamForCustomerIdIsNull": "证书验证的客户编码为空",
  "lang.ark.fed.executeSuccessfully": "执行送成功",
  "lang.slam.api.menu.item0001": "PC",
  "lang.ark.fed.containerTypeName": "容器类型名称",
  "lang.ark.fed.activeDistributionTitle": "主动配送：工作站只能发起从该工作站开始的流程",
  "lang.ark.fed.notParams": "暂未配置参数！",
  "lang.mwms.fed.qrCodeAnalysis": "二维码解析策略",
  "lang.ark.fed.time": "时间",
  "lang.ark.base.license.licensePreAlertMsg": "证书将于{0}天后过期!",
  "lang.ark.fed.setStartPoint": "设为起点",
  "lang.ark.fed.wave": "波次",
  "lang.ark.workflow.task.status.deviceExecuting": "外部设备运输中",
  "lang.mwms.fed.category": "货物类别",
  "lang.ark.workflow.containerEnterMapDest": "容器入场终点",
  "lang.ark.fed.processOperation": "流程操作",
  "lang.ark.fed.screen.flowNodeConfig.judgingByPath": "路径判断",
  "lang.ark.fed.orderDetail": "单据详情",
  "lang.ark.fed.login": "登录",
  "lang.ark.fed.screen.equipmentAssociatedInfo.dialogDetail.titleEdit": "编辑设备关联信息",
  "lang.ark.fed.timeOfReceipt": "领单时长",
  "geekplus.moving.uic.elTableWrapperVue2.column.action": "操作",
  "lang.ark.fed.device.ruleMatch": "根据点位设备绑定关系匹配",
  "lang.ark.fed.instructList": "指令列表",
  "lang.ark.waveGenerateScope.materialDocument": "按备料点",
  "lang.ark.workflowConfig.cellFunctions.stayBlocking": "停留阻塞功能",
  "lang.ark.interface.apiStationQueueStop": "关闭排队接货",
  "lang.ark.fed.startOrEndNodeNotExist": "操作失败，没有开始或结束节点！",
  "lang.ark.fed.containerEntryWay": "入场方式",
  "lang.ark.auth.otherUserHaveLoginThisStation": "已有用户 {0} 在{1}工作站登录，请联系该用户退出登录后，可再次切换!",
  "lang.ark.fed.cancelEndPoint": "取消终点",
  "lang.gles.workflow.workReceipt": "作业单据",
  "lang.ark.fed.screen.flowNodeConfig.assignOffset": "下发机器人点位偏移值",
  "lang.gles.strategy.shelf": "上架策略",
  "lang.ark.fed.release": "释放",
  "lang.ark.fed.menu.nodeMapRelation": "传感器触发任务",
  "lang.ark.fed.whatAreYouGoingToDoWithTheCurrentAreaOrShelf": "当前的区域或者是货架，准备执行什么处理？",
  "lang.ark.fed.lengthLang": "长度",
  "lang.ark.fed.putDownOrTurnSide": "{0}组件动作已存在放下，不允许配置转面！",
  "lang.ark.workflow.condition.notIn": "不包含",
  "lang.ark.fed.twoDimensionalCodeMode": "二维码模式",
  "lang.ark.fed.sourceProductionLineOrWorkshop": "来源产线/车间",
  "lang.ark.fed.displayOrder": "分组内点位显示顺序",
  "lang.ark.waveTriggerCondition.wireless": "物理按钮",
  "lang.ark.fed.notSameStartAndEnd": "起点和终点位置不能相同",
  "lang.ark.workflow.enterMapDest": "入场终点",
  "lang.ark.fed.buttonCommand": "按钮指令",
  "lang.ark.fed.dataTimeRange": "日期时间范围",
  "lang.ark.fed.areYouSureYouWantToDeleteThisWorkflow": "是否确定删除此流程吗！",
  "lang.ark.workflow.workflowTaskNotExists": "工作流任务不存在",
  "lang.ark.workflow.noPause":"最后一项子任务不可暂停",
  "lang.mwms.rf.outboundWithoutTask": "无单出库",
  "lang.ark.workflow.paramValueCode.taskId": "taskId",
  "lang.ark.workflow.fetchStuff": "取货",
  "lang.ark.workflow.pickupCompleted": "完成取货",
  "lang.ark.fed.hostSeriNum": "外部编码",
  "lang.ark.fed.screen.hybridRobot.hybridRobotType": "复合机器人类型",
  "lang.ark.fed.StationNum": "领料单号",
  "lang.ark.action.interface.exceptionResponse": "异常响应时",
  "lang.ark.fed.goodsCoding": "货位编码",
  "lang.mwms.fed.workStationEfficiencyCharts": "工位效率报表",
  "lang.ark.fed.light": "灯光",
  "lang.ark.fed.operatingTime": "操作时间",
  "lang.mwms.fed.shelfRuleManagement": "上架规则管理",
  "lang.ark.dynamicTemplate.dynamicControlLogic": "控制逻辑",
  "lang.ark.fed.taskDashboardColumn": "任务看板显示列",
  "lang.ark.robotUsage.sorting": "分拣",
  "lang.ark.fed.disCharingMount": "下料数量",
  "lang.ark.workflow.autoOperationFailed": "自动执行失败",
  "lang.ark.fed.areasThatCanBeSpeciallyControlledForRobotMovements": "可以对机器人动作进行特殊控制的区域",
  "lang.ark.fed.floors": "层数",
  "lang.ark.task.log.export.title.workflow.name": "流程名称",
  "lang.ark.workflowAction.noDefault": "非默认",
  "lang.ark.fed.redraw": "重新绘制",
  "lang.ark.fed.recoverSuccess": "恢复成功",
  "lang.ark.fed.triggerName": "触发器名称",
  "lang.ark.fed.dragPictureFileHereOr": "将图片文件拖到此处，或",
  "lang.ark.fed.menu.nodeConfig": "等待点交互",
  "lang.ark.apiContainerCode.containerCodeGenerateFail": "容器号生成失败",
  "lang.ark.workflow.lackRobotQueue": "机器人不足时任务排队",
  "lang.ark.fed.selectedGoods": "已选物料",
  "lang.ark.apiStationCode.stationQueueAlreadyEnable": "工作站排队控制已开启，不能重复开启",
  "lang.ark.workflow.workflowTask": "流程任务",
  "lang.ark.fed.copyError": "复制失败！",
  "lang.ark.fed.logicalNodeInfo": "逻辑点信息",
  "lang.ark.fed.grabShelvesFromWorkstations": "从工作站抓取货架",
  "lang.ark.workflow.workflowConfigNotExists": "工作流配置不存在",
  "lang.ark.warehouse.hasSameStationNumber": "存在相同工序号",
  "lang.ark.fed.backPre": "退",
  "lang.ark.fed.deleteNode": "删除节点",
  "lang.ark.fed.receivingWorkstation": "领单工作站",
  "lang.ark.fed.creationTime": "创建时间",
  "lang.ark.fed.goodsIsNotExists": "物料信息不存在",
  "lang.ark.fed.pleaseSelectAtLeastOneProcess": "请至少选择一项流程",
  "lang.ark.fed.pleaseSelectGoods": "请选择物料",
  "lang.ark.common.invalidParameter": "非法参数",
  "lang.mwms.fed.pickWorkCreate": "拣货工作生成",
  "lang.ark.robot.go.rest": "去固定位置",
  "lang.mwms.fed.codeRule": "条码规则",
  "lang.ark.fed.username": "用户名",
  "lang.ark.workflowConfig.cellFunctions.recycleid": "回收点功能",
  "lang.ark.fed.menu.siteMonitoring": "场地监控",
  "lang.ark.fed.areaStop": "区域急停",
  "lang.ark.workflowgroup.triggerpoint.end": "结束",
  "lang.ark.warehouse.noMatchToloc": "未匹配到产线",
  "lang.ark.interface.apiCallback": "任务回调",
  "lang.ark.fed.cancelStartPoint": "取消起点",
  "lang.ark.fed.flowBelongClass": "流程所属类别",
  "lang.ark.hitStrategy.queue": "先进先出",
  "lang.ark.existsDefaultNodeAction": "已存在默认配置！",
  "lang.ark.fed.wall": "墙",
  "lang.ark.fed.systemCustomization": "系统定制",
  "lang.ark.fed.originalLocation": "原始位置",
  "lang.ark.workflow.workflowTaskSendDuplicate": "送操作重复",
  "lang.mwms.fed.internalManager": "库内管理",
  "lang.ark.fed.areaTypeExistence": "当前区域类型已存在，同种类型只能新增一条！",
  "lang.ark.fed.priorityGoDown": "优先级下降",
  "lang.ark.workflow.noAvailableEndNode": "无法选择可用目标节点",
  "lang.ark.fed.pleaseSelectAConditionValueCollectionFile": "请选择条件值集合文件",
  "lang.ark.task.log.export.title.startNode.name": "起点名称",
  "lang.ark.fed.demandFactory": "需求工厂",
  "lang.ark.lift.up": "顶升",
  "lang.ark.fed.sendMaterialForStations": "该上料点可给哪些工位备料",
  "lang.ark.trafficControl.enterStrategy.byOccupancy": "先占先过",
  "lang.mwms.fed.mergeInventory": "合并库存",
  "lang.ark.fed.middlePoint": "中间点",
  "lang.ark.fed.workstationManage": "工作站管理",
  "lang.ark.fed.demandStation": "需求工位",
  "lang.ark.fed.collectionAnd": "集合(且)",
  "lang.ark.fed.execution": "到达操作",
  "lang.ark.loadCarrier.loadCarrierReqParamsErr": "容器请求参数错误！",
  "lang.gles.workPositionMaterialConfig": "工位物料配置",
  "lang.ark.fed.destCode": "节点编码",
  "lang.ark.fed.lastDay": "最后一天",
  "lang.mwms.fed.inManage": "入库管理",
  "lang.ark.fed.menu.parameterConfigOuter": "调度",
  "lang.ark.fed.isSureDelFlowList": "确定删除流程么？",
  "lang.ark.fed.batchSave": "批量保存",
  "lang.ark.containerType.exists": "容器分类名称已经存在",
  "lang.ark.fed.deleteTaskProcessConfirmText": "删除该流程实例下所有任务，确定删除？",
  "lang.ark.apiNodeActionCode.commonNodeActionNotExists": "节点通用交互配置不存在",
  "lang.ark.effectiveTimeCannotLessThanEffectiveTime": "失效时间不能小于生效时间",
  "lang.ark.fed.moveBins": "货架移除，上料取消请重新领取",
  "lang.ark.interface.moving": "点到点搬运",
  "lang.ark.fed.belongNodeAlreadyExistInOtherAreaConfig": "选择的所属节点列表中,有在其它区域配置中存在的节点",
  "lang.mwms.fed.robotCharts": "机器人报表",
  "lang.ark.workflow.authUserHasUsed": "下列用户已经设置过工作站权限",
  "lang.ark.api.template.startNodeNotMatch": "指定起点[{}]和模板起点[{}]不匹配",
  "lang.ark.workflow.queue.noAvailableRobot": "无可用机器人",
  "lang.ark.interface.resentFail": "调用失败",
  "lang.ark.fed.singleGeneration": "单个生成",
  "lang.ark.fed.screen.equipmentAssociatedInfo.dialogDetail.labelMaxTaskCont.tooltip": "一个提升机同一时段能处理的最大任务量，如果超出，不影响任务下发，会默认处理成任务排队，等上一个任务完成了，再给提升机分配新任务，直到下发的任务都执行完。",
  "lang.ark.fed.emptyShelves": "空货架",
  "lang.ark.fed.isOpenCamera": "检测是否开启摄像头...",
  "lang.ark.fed.containerPosition": "容器位置",
  "lang.ark.workflow.noLongerPause": "当前任务不再是暂停进入状态，无法恢复，请刷新",
  "lang.ark.container,syncContainerErr": "同步容器信息失败",
  "lang.ark.logType.trafficControlTask": "交通管制任务",
  "lang.ark.fed.triggerMode": "触发方式",
  "lang.ark.fed.interactiveActionName": "交互动作",
  "lang.ark.fed.leftAndRightObstacleAvoidance": "左右避障",
  "lang.ark.fed.pleaseEnterARuleName": "请输入规则名称",
  "lang.ark.fed.workflowTrigger": "任务触发器",
  "lang.ark.fed.obstacleAvoidanceRange": "避障范围",
  "lang.ark.fed.area": "区域",
  "lang.ark.fed.oneByOne": "逐个通过",
  "lang.ark.warehouse.getTaskFailed": "该叫料任务已被其它备料点领取",
  "lang.ark.fed.cancelCall": "取消叫料",
  "lang.ark.fed.workFlowType": "流程类别",
  "lang.ark.workflow.area.releaseTime": "自动释放时间",
  "lang.ark.fed.menu.areaManage": "区域管理",
  "lang.ark.warehouse.Feeding": "上料",
  "lang.ark.fed.trafficArea": "管制区",
  "lang.ark.fed.interactiveMode": "交互方式",
  "lang.ark.fed.executing": "执行中",
  "lang.ark.fed.received": "已领取",
  "lang.ark.fed.siteName": "场地名称",
  "lang.ark.fed.menu.workflowConfiguration": "任务",
  "lang.ark.fed.pleaseAddAreaFun": "请新增至少一个功能类型",
  "lang.ark.workflow.containerNotExists": "容器不存在",
  "lang.ark.fed.passbyPoint": "经过点",
  "lang.ark.fed.theFirstNodeCanNotBeDeleted": "首节点不可以删除！",
  "lang.ark.apiNodeActionCode.nodeActionNotExists": "节点交互配置不存在",
  "lang.ark.workflow.arrive.action.goTurnOfAngle.pos90": "90°",
  "lang.mwms.fed.kpi": "员工效率管理",
  "lang.ark.fed.nodeConfig": "节点通用配置",
  "lang.mwms.fed.outWarehouse": "出库日报表",
  "lang.ark.action.interface.conditionExtraParam": "extraParam",
  "lang.ark.fed.endType": "终点类型",
  "lang.ark.fed.shelfSwap": "货架互换",
  "lang.ark.fed.selectProductLineOrCellcode": "选择产线或工位",
  "lang.ark.fed.waveStatus": "波次状态",
  "lang.ark.fed.menu.callbackAddressConfig": "回调地址配置",
  "lang.ark.fed.drawANewMapToCoverTheCurrentMap": "绘制一个新的地图，覆盖当前地图",
  "lang.ark.workflow.recycleAreaNoConfigAction": "取消失败，流程未配置回收区交互！",
  "lang.ark.workflow.exceptionHandler.cache": "去排队区",
  "lang.ark.fed.batchNo": "批次号",
  "lang.ark.warehouse.binNoShelf": "物料所在货架不存在,不能执行该操作",
  "lang.ark.api.workflowTask.notExistOrCompleted": "任务不存在或已结束，无法继续执行操作",
  "lang.ark.container.containerCodeTooLong": "类型编码长度不得超过64",
  "lang.ark.fed.pleaseSelectALanguage": "请选择语言",
  "lang.ark.fed.cageTrolley": "笼车",
  "lang.ark.fed.common.btn.cancel": "取消",
  "lang.ark.api.workflow.locationToIsNull": "目标点位编码不能为空",
  "lang.auth.UserAPI.item2001": "到期日期不正确",
  "lang.ark.common.exportExcelFile": "模板导出",
  "lang.auth.UserAPI.item2000": "账户即将在{0}天内到期",
  "lang.ark.workflowTriggerType.clear.log": "清除日志",
  "lang.ark.fed.unknowError": "未知错误",
  "lang.ark.fed.workflowName": "流程名称",
  "lang.ark.fed.wednesday": "周三",
  "lang.ark.fed.restartAngle": "重启角度",
  "lang.ark.fed.mediumSpeed": "中速",
  "lang.ark.workflow.arrive.action.goTurnOfAngle.neg180": "-180°",
  "lang.mwms.fed.inWarehouseCollect": "入库汇总",
  "lang.ark.fed.modifier": "修改人：",
  "lang.ark.fed.layer": "层",
  "lang.ark.apiCommonCode.locationToNotMatchDest": "locationTo:{0}分别按照任意点、工作站、区域在系统内都没有匹配到对应的位置",
  "lang.ark.fed.mainProcessInstance": "主流程实例",
  "lang.mwms.fed.containerInfo": "容器管理",
  "lang.ark.interface.interfaceDesc.required": "是否必填",
  "lang.ark.fed.taskCycle": "任务循环",
  "lang.ark.fed.noWorkFlowData": "无流程信息",
  "lang.ark.fed.help": "帮助",
  "lang.ark.fed.screen.workflowInfo.workflowTaskId": "一级任务Id",
  "lang.mwms.fed.kpiCharts": "员工效率报表",
  "lang.ark.fed.taskDetail": "任务详情",
  "lang.ark.shelfTypeNotExist": "容器类型不存在",
  "lang.ark.apiStationCode.stationNotSupportLogin": "此工作站不支持登录",
  "lang.ark.fed.manualTrigger": "人工触发",
  "lang.ark.trafficControl.trafficLightRange": "十字路口(红绿灯)",
  "lang.ark.fed.dataChangeRefreshPage": "数据变更，请刷新页面~",
  "lang.ark.fed.logining": "正在登录中...",
  "lang.ark.fed.common.btn.confirm": "确定",
  "lang.auth.UserAPI.item0203": "{0}用户异常",
  "lang.ark.exceptionHandle": "不可操作，目的点存在DMP配置",
  "lang.auth.UserAPI.item0202": "没有选中一个用户",
  "lang.ark.fed.materialName": "物料名称",
  "lang.ark.fed.menu.configManage": "配置管理",
  "lang.auth.UserAPI.item0201": "没有修改用户的状态",
  "lang.ark.fed.screen.flowNodeConfig.checkMsg.robot": "保存失败，请选择机器人状态",
  "lang.auth.UserAPI.item0200": "修改用户异常",
  "lang.ark.fed.actionsErrorCommandDetailError": "交互配置保存失败，指令明细保存失败",
  "lang.authManage.web.others.noAllow": "抱歉，你无权访问该页面",
  "lang.ark.fed.groupDisplayOrder": "分组顺序",
  "lang.ark.workflow.putDown": "放下",
  "lang.ark.workflowConfig.segmentWithoutRule": "连接线未配置规则，请检查",
  "lang.ark.fed.taskIsSavedSuccessfully": "任务保存成功",
  "lang.ark.fed.repeatWeek": "周",
  "lang.ark.fed.shelfType": "货架类型",
  "lang.ark.fed.pickUpARack": "取货架",
  "lang.ark.apiContainerCode.codeAndNumberAreNull": "containerCodes和numberOfContainer不能同时为空",
  "lang.ark.fed.menu.robotSoftwareManagement": "软件版本",
  "lang.mwms.fed.systemLack": "系统报缺异常处理",
  "lang.ark.warehouse.uploadFileFormatError": "上传文件格式错误",
  "lang.ark.fed.copy": "复制",
  "lang.ark.apiContainerCode.containerCategoryMustUnique": "系统内容器类型不唯一,必须指定containerCategory",
  "lang.ark.button.operation.command.end": "结束",
  "lang.ark.fed.binFree": "可用",
  "lang.ark.fed.operationFailed": "操作失败",
  "lang.ark.fed.closeotherTabs": "关闭其它",
  "lang.ark.fed.batchEditing": "批量编辑",
  "lang.ark.fed.shelfAttribute.BALANCE": "BALANCE",
  "lang.ark.fed.wfTaskInfo": "领料单",
  "lang.ark.fed.modifier2": "修改人",
  "lang.ark.loadCarrier.loadCarrierModelCodeIsEmpty": "容器模型编码不能为空！",
  "lang.ark.operation.workflow.adjustPriority": "调整任务优先级，将任务优先级调整为{0}",
  "lang.ark.fed.forceDeleteSuccess": "强制删除成功！",
  "lang.ark.fed.listLogo": "条日志",
  "lang.ark.fed.send": "送",
  "lang.ark.fed.menu.containerManagement": "容器",
  "lang.ark.fed.screen.equipmentAssociatedInfo.btnAddInfo": "新增设备关联信息",
  "lang.ark.fed.disable": "停用",
  "lang.ark.fed.container.confirmLeave": "容器离场确认",
  "lang.ark.fed.download": "下载",
  "lang.ark.fed.idleCharging": "空闲充电",
  "lang.ark.fed.startType": "起点类型",
  "lang.ark.fed.date": "日期",
  "lang.ark.fed.autoUpdatePage": "自动更新翻页",
  "lang.ark.groupStrategy.closestDistance": "最近距离",
  "lang.ark.groupStrategy.sequentialSelection": "顺序选择",
  "lang.ark.workflow.removeShelfFailed": "移除容器失败",
  "lang.ark.fed.waveTasks": "波次任务数",
  "lang.ark.fed.queuingAtTheWorkstation": "去工作站排队",
  "lang.ark.workflow.nodeStatus": "当前点状态",
  "lang.ark.pda.function.container.entry": "容器入场",
  "lang.ark.workflow.template.validate.templateError": "动态模板配置错误",
  "lang.ark.fed.menu.containerType": "容器类型",
  "lang.ark.fed.screen.flowNodeConfig.judgingByRobotStatus": "按机器人状态判断",
  "lang.ark.fed.drivingRestrictions": "行驶限制:",
  "lang.ark.fed.getGoodsTitle": "叫料：开启，即工作站支持选择物料发起叫料任务。",
  "lang.authManage.web.auth.roler": "角色",
  "lang.ark.fed.multiNodeCustom": "点到多点定制任务",
  "lang.ark.fed.suggestedTreatment": "建议处理方式",
  "lang.ark.record.robotCallback.move": "机器人开始移动",
  "lang.ark.fed.normalWork": "工作正常",
  "lang.ark.shelfNameExist": "容器类型名称已存在",
  "lang.ark.fed.robotTaskFlow": "机器人任务流转",
  "lang.ark.fed.editor": "编辑人",
  "lang.ark.workflow.paramValueCode.locationFromFloor": "locationFromFloor",
  "lang.mwms.fed.sysMonitor": "系统监控",
  "lang.ark.fed.component": "组件",
  "lang.mwms.monitorRobotMsg.scanabnormal.notMatch": "识别的容器号与下发的容器号不一致",
  "lang.ark.workflow.autoSkip": "完成后自动跳过",
  "lang.ark.workflow.exceptionHandler.taskQueue": "任务排队",
  "lang.ark.fed.rackManagement": "货架管理",
  "lang.ark.workflow.cycleType": "循环类型",
  "lang.ark.loadCarrier.loadCarrierModelTypeIsEmpty": "容器模型形态不能为空！",
  "lang.ark.fed.screen.workflowInfo.commandTaskId": "接口指令任务Id",
  "lang.ark.warehouse.deliveryMaterial": "配送物料",
  "lang.ark.element.stop.point.belong.to.workstation": "该点位属于工作站",
  "lang.auth.PwdMgrAPI.item0009": "密码将于{0}天过期",
  "lang.auth.PwdMgrAPI.item0008": "新密码不能跟旧密码一样",
  "lang.ark.apiStationCode.stationQueueNotExists": "工作站排队控制不存在",
  "lang.auth.PwdMgrAPI.item0005": "添加密码管理异常",
  "lang.auth.PwdMgrAPI.item0004": "密码警告天数有则需设置为正整数，且少于密码效期",
  "lang.auth.PwdMgrAPI.item0007": "删除密码管理异常",
  "lang.ark.fed.flowTemplateSelTypeOrRobot": "机器人型号和指定机器人必须填写其中一个",
  "lang.auth.PwdMgrAPI.item0006": "没有找到密码管理ID",
  "lang.auth.PwdMgrAPI.item0001": "密码少于规则长度{0}",
  "lang.auth.PwdMgrAPI.item0003": "密码效期有则需设置为正整数",
  "lang.auth.PwdMgrAPI.item0002": "密码不符合规则:{0}",
  "lang.ark.fed.waitExecute": "未执行",
  "lang.ark.workflow.TimeTriggerSimple": "自动",
  "lang.ark.fed.shelfAttribute.name": "物料类别",
  "lang.ark.fed.orderComplete": "配送完成",
  "geekplus.moving.uic.elTableWrapperVue2.column.index": "序号",
  "lang.ark.interface.apiWorkflowInstanceList": "流程实例查询",
  "lang.ark.workflow.recoveryAreaType": "回收区类型",
  "lang.ark.warehouse.manualOperateTypeOut": "人工出库",
  "lang.ark.fed.menu.workstationManage": "工作站",
  "lang.auth.PwdMgrAPI.item0012": "您的账号{0}分钟内禁止登录！",
  "lang.ark.action.interface.compareValue": "对比条件值",
  "lang.auth.PwdMgrAPI.item0011": "由于密码输入错误次数过多，帐号{0}已被禁止登录！",
  "lang.ark.warehouse.goodsNumberExists": "物料编码已存在",
  "lang.auth.PwdMgrAPI.item0013": "您还剩{0}次重试的机会",
  "lang.ark.fed.screen.equipmentAssociatedInfo.dialogDetail.labelEquipName": "设备名称",
  "lang.authManage.web.others.login": "登录",
  "lang.auth.PwdMgrAPI.item0010": "当前登录密码已过期,请修改后重新登录",
  "lang.ark.fed.containerEntryAndLeave": "容器出入场",
  "lang.ark.fed.machineUse": "机器用途",
  "lang.ark.fed.description": "描述",
  "lang.mwms.fed.sku": "产品档案",
  "lang.ark.fed.waveTask": "波次任务",
  "lang.ark.fed.startDate": "开始日期",
  "lang.ark.workflow.exceptionHandler.robotQueue": "机器人排队",
  "lang.ark.archiveType.workStationOperateLog": "工作站操作日志",
  "lang.mwms.fed.customer": "客户档案",
  "lang.ark.workflow.area.factory": "厂家",
  "lang.ark.warehouse.theMatrialPointIsNotUni": "上料点位不唯一，组波失败",
  "lang.gles.baseData": "基础数据",
  "lang.ark.warehouse.thePointExistsAnyOrderPleaseCancel": "存在多个产线领料单组波失败，请取消多余单据。",
  "lang.ark.fed.workflowNumber": "流程编号",
  "lang.ark.trafficControl.blockRange": "地图区块",
  "lang.ark.fed.demandProductionLine": "需求产线",
  "lang.ark.trafficControl.enterStrategy.byOccupancyPriority": "先占先过（优先级优先）",
  "lang.ark.fed.goodsInfo": "物料信息",
  "lang.ark.fed.deliveryTimeV2": "配送时长",
  "lang.ark.fed.switchingWorkstation": "切换工作站",
  "lang.ark.fed.defaultSet": "默认布局",
  "lang.ark.workflow.condition.lessThanOrEqual": "小于等于",
  "lang.ark.workflow.sendTask": "发送指令",
  "lang.ark.fed.partition": "隔板",
  "lang.common.invalidParameters": "输入参数不对",
  "lang.ark.fed.notNow": "暂不",
  "lang.ark.fed.component.workflow.msg.inconsistentEquipIds": "设备存在不属于同一设备的情况，请检查设备相关配置",
  "lang.ark.interface.disabledSuccess": "停用成功",
  "lang.ark.fed.reeditTheCurrentMapBackground": "对当前地图背景进行重新编辑",
  "lang.ark.fed.robotModel": "机器人型号",
  "lang.ark.fed.adjustment": "调整",
  "lang.ark.fed.editingMapBackground": "编辑地图背景",
  "lang.ark.interface.interfaceDesc.phase": "回调时机",
  "lang.ark.removeContainerFail": "移除容器失败!",
  "lang.ark.fed.strategyManagement": "策略管理",
  "lang.ark.fed.modificationTime": "修改时间：",
  "lang.ark.fed.collectionOr": "集合(或)",
  "lang.ark.trafficControl.trafficFunctionType": "功能类型",
  "lang.ark.areaCodeExist": "区域编码不能重复",
  "lang.ark.fed.screen.flowNodeConfig.executeDeviceInstruct": "则执行该设备指令",
  "lang.ark.externalDevice.device_own_type.externalDevice": "外部设备",
  "lang.ark.workflow.area.increaseStrategy": "递进策略",
  "lang.ark.action.interface.applicationType": "返参应用",
  "lang.ark.operatelog.operatetype.auto": "自动流程",
  "lang.ark.warehouse.quadrupleContainerSide": "四面",
  "lang.ark.fed.RightBracket": "+右括号",
  "lang.ark.workflow.task.status.wait.queue.robot": "等待排队机器人",
  "lang.ark.fed.goodsManagement": "货位管理",
  "lang.ark.robotDeviceComponent.deviceName": "设备名称",
  "lang.ark.fed.expressionError": "表达式格式错误",
  "lang.ark.fed.waitingSeconds": "等待秒数",
  "lang.mwms.fed.arrangePlanDetails": "理货计划明细查询",
  "lang.ark.equipment.equipmentTypeCannotNull": "设备类型不能为空",
  "lang.ark.equipment.error.usedByWorkflow": "当前提升机已被流程/流程模板使用，请先卸载流程/流程模板再修改提升机料口配置",
  "lang.ark.fed.configurationParameter": "配置参数",
  "lang.ark.fed.editBackgroundMap": "编辑背景图",
  "lang.ark.workflow.paramValueCode.constant": "constant",
  "lang.ark.fed.containerOrientation": "容器朝向",
  "lang.ark.fed.screen.hybridRobot.binBindStopPoint": "货位与地图点位绑定",
  "lang.ark.fed.pauseSuccess": "暂停成功",
  "lang.ark.interface.endpoint": "终点编码",
  "lang.ark.workflow.notAllowFinishIfUnArrivedEnd": "任务未到达终点不允许完成",
  "lang.ark.fed.batchGeneration": "批量生成",
  "lang.ark.fed.inAutomaticExecution": "自动执行中...",
  "lang.ark.fed.chinese": "中文",
  "lang.ark.workflowTriggerMonitorStatus.cancel": "取消",
  "lang.ark.workStatus.execute": "执行中",
  "lang.ark.waveTaskStatus.executing": "执行中",
  "lang.ark.fed.taskQueue": "任务队列",
  "lang.ark.fed.triggerTimer": "触发时长",
  "lang.ark.workflow.area.artificialControl": "人工管制区",
  "lang.ark.workflow.task.status.node.pause": "任务暂停",
  "lang.ark.fed.unknownArea": "未知区域",
  "lang.ark.fed.workflowConfiguration": "流程管理",
  "lang.ark.robot.classfy.lift": "举升",
  "lang.ark.workflow.task.status.exception.completed": "异常完成",
  "lang.ark.fed.nonumberWorkstation": "{number} 号工作站",
  "lang.ark.fed.inventoryAdjustment": "库存调整",
  "lang.ark.fed.robotWaitFlagnew": "机器人原地等待",
  "lang.ark.interface.apiRemoveContainer": "容器出场",
  "lang.ark.fed.noRmsTask": "未生成搬运任务",
  "lang.auth.DataAPI.item0001": "获取用户数据权限异常，异常信息为：{0}",
  "lang.ark.fed.screen.flowNodeConfig.conditionTips": "提示：若选择多个条件，则所选条件需同时满足才会执行",
  "lang.ark.fed.managementMode": "管理模式",
  "lang.ark.fed.distributionMode": "配送模式",
  "lang.ark.workflow.arrive.action.goTurnOfAngle": "组件按角度旋转",
  "lang.ark.fed.screen.flowNodeConfig.ifRobotStatus": "若机器人状态为",
  "lang.ark.fed.taskQuery": "任务查询",
  "lang.ark.fed.lift": "举升",
  "lang.ark.fed.buttonFunctionConfiguration": "按钮功能配置",
  "lang.ark.fed.pleaseSelectTheProcessNodeYouWantToEdit": "请选择所需要编辑的流程节点！",
  "lang.ark.fed.uploadCutImage": "上传并剪裁条码",
  "lang.ark.fed.floorStage.differentFloor": "不同楼层",
  "lang.ark.fed.editing": "编辑",
  "lang.ark.fed.getGoodsMESLocation": "获取物料MES请求地址",
  "lang.ark.fed.systemConfiguration": "系统配置",
  "lang.ark.element.workstation.atLeast.one": "工作站必须含有至少一个点位",
  "lang.ark.bussinessModel.workflow": "流程",
  "lang.mwms.fed.arrangeTaskSplit": "理货任务拆分策略",
  "lang.ark.workflow.canNotContinue": "流程状态异常！",
  "lang.ark.container.containerAmountNumberCurrentScope": "指定容器编码{}最多新增{}个容器，确定添加？",
  "lang.ark.fed.exportSuccessFailNum": "数据导入完成，导入失败{0}",
  "lang.ark.apiCommonCode.flowStrategyNotSupported": "flowStrategy:{0}不支持",
  "lang.ark.fed.productionLineNoGoodsInfo": "产线工位未绑定物料信息",
  "workflow.task.cancel.robot.task.failure": "删除失败！原因：rms反馈消息",
  "lang.ark.fed.callMaterialTask": "发起叫料任务",
  "lang.ark.auth.userLoginNotSessionOtherStation": "当前用户 {0} 已经在{1}工作站登录，退出登录后，才可以登录不同工作站!",
  "lang.mwms.rf.multiSkuBoard": "拉动式看板",
  "lang.ark.warehouse.materialPointCellCodeRepeat": "{0}已使用",
  "lang.ark.fed.pleaseGoToTheLoginPageAndSelectTheSerialPassword": "请到登录页面选择串口号",
  "lang.ark.fed.goToCharge": "充电",
  "lang.ark.apiCommonCode.binCodeAndOrderExist": "该货位点同货位顺序号已存在，不允许重复添加！",
  "lang.ark.fed.startPoint": "起点",
  "lang.ark.fed.everyOnce": "每隔",
  "lang.gles.interface.interfaceConfig": "接口配置",
  "lang.ark.robotDeviceComponent.deviceType": "设备类型",
  "lang.ark.fed.startFlowSuccess": "流程发起成功",
  "lang.ark.button.operation.command.cleanWaitPoint": "清除等待点",
  "lang.ark.fed.pleaseChooseAreaCode": "请选择区域对应节点",
  "lang.ark.workflowTriggerType.clear.workflow": "清除指定流程",
  "lang.ark.fed.taskCancelTime": "任务取消时间",
  "lang.ark.fed.rackCode": "容器编码",
  "lang.auth.fed.password.ruleDesc": "密码长度至少9位，必须是数字、大小写字母的组合",
  "lang.ark.warehouse.noMatchMatnr": "未匹配到物料",
  "lang.ark.fed.noWorkflowNodePleaseClickToSelect": "暂无流程节点，请点击选择！",
  "lang.ark.action.interface.saveValue": "存入条件值",
  "lang.ark.fed.screen.area.addGroup": "新增分组",
  "lang.auth.role.edit.sysName.gms": "GMS",
  "lang.common.success": "操作成功",
  "lang.ark.deliverOrder.invertedSequence": "产线工序倒序",
  "lang.ark.fed.wirelessSignal": "无线信号",
  "lang.ark.logType.warehouseInterfaceLog": "仓库接口日志",
  "lang.ark.fed.handleRefresh": "手动刷新",
  "lang.ark.warehouse.cellCodeHasTask": "工位有未完成任务,请完成后操作",
  "lang.ark.fed.playVoice": "播发语音",
  "lang.ark.fed.entryStartPoint": "入场起点",
  "lang.ark.fed.xialiao": "下料超时",
  "lang.ark.fed.cycleNum": "循环次数",
  "lang.ark.apiCallbackReg.sendInterval": "逐条/全部",
  "lang.ark.fed.existBinCode": "该货位序列号已存在",
  "lang.ark.loadCarrier.loadCarrierModelUsed": "删除失败，容器模型已被使用！",
  "lang.ark.workflow.area.releaseOrder": "放行顺序",
  "lang.ark.fed.menu.workstationAndqueueController": "工作站排队策略",
  "lang.ark.fed.noWorkflowConfiguration": "无可用工作流配置",
  "lang.ark.fed.startFlow": "启动",
  "lang.ark.fed.shelfAttribute.msgType": "消息类型。",
  "lang.common.cancel": "取消",
  "lang.ark.fed.containerChangeLogType": "容器操作日志任务类型",
  "lang.mwms.fed.innerException": "库内异常",
  "lang.ark.fed.add": "添加",
  "lang.mwms.exceptionHandling": "异常处理",
  "lang.ark.fed.firstDrawWorkStop1": "请先画流程首节点，首节点的类型是：工作站，点位",
  "lang.ark.workflow.brotherNodeNotWorkflowNode": "继承机器人的节点和它的兄弟节点必须为子流程节点!",
  "lang.ark.fed.bindWorkstation": "绑定工作站",
  "lang.ark.fed.nodeNumber": "节点编号",
  "lang.ark.fed.japanese": "日语",
  "lang.ark.fed.screen.LoginLog.loginTime": "登录时间",
  "lang.ark.fed.screen.systemConfig.businessGroup": "业务功能",
  "lang.ark.fed.savedSuccessfully": "保存成功",
  "lang.ark.fed.trafficRangeRun": "管制区运行",
  "lang.ark.hitStrategy.shortestDistance": "最短距离",
  "lang.ark.fed.sureCutting": "以下物料未下完，确定完成下料？",
  "lang.ark.workflow.task.status.fetched": "取到容器",
  "lang.ark.warehouse.policyHaveSameTriggerCondition": "相同触发条件策略已存在",
  "lang.mwms.fed.batchAdjustmentManager": "批次调整单管理",
  "lang.ark.fed.angle": "角度",
  "lang.ark.fed.sureFeeding": "以下物料未上满，确定完成上料？",
  "lang.ark.fed.workstationType": "类型",
  "lang.ark.fed.waveGeneratePattern": "波次生成方式",
  "lang.ark.fed.isExcetuTrigger": "是否执行当前触发器？",
  "lang.ark.fed.node": "节点",
  "lang.ark.fed.successfulLogin": "登录成功",
  "lang.ark.base.license.exceptionForCannotFindServerUUID": "设备信息不匹配!",
  "lang.ark.operation.workflow.deleteExecution": "删除任务{0}",
  "lang.ark.fed.inventoryStatus": "库存状态",
  "lang.ark.loadCarrier.batchAddAmountTransfinite": "批量操作数据超限，最多5000条！",
  "lang.ark.fed.options": "选项",
  "lang.ark.fed.homepage": "首页",
  "lang.ark.fed.taskSource.station": "工作站",
  "lang.ark.fed.targetPointCode": "目标点编码",
  "lang.ark.fed.cycle": "循环",
  "lang.ark.fed.workflowGroupName": "流程组名称",
  "lang.ark.fed.edit": "编辑",
  "lang.ark.fed.circle": "绕圈",
  "lang.ark.ruleStage.sameFloor": "相同楼层",
  "lang.ark.fed.working": "工作中",
  "lang.ark.auth.otherUserHaveLoginOtherStation": "{0} 用户已经登录于该工作站，请联系该用户退出登录后，可再次登录!",
  "lang.ark.fed.waveStrategy": "波次策略",
  "lang.ark.fed.appointmentTip": "预约开始时间需大于当前时间！",
  "lang.ark.fed.bindTemplate": "绑定模板",
  "lang.ark.bin.binNotExist": "货位点不存在",
  "lang.ark.binStopPoint.offsetX": "上装X轴偏移量",
  "lang.ark.fed.excel.data.binOrder.offsetXNumericError": "第{0}行上装X轴偏移量不能为负数，请修改后重新导入",
  "lang.ark.fed.menu.vens.dmpTaskManage": "设备任务",
  "lang.authManage.fed.expiryDate": "到期日期",
  "lang.ark.fed.morePickingTitle": "多车领料：即叫料任务需多个上料点备料时，确认好上料点组合后，根据不同上料点物料生成多个领料单。分配推至各上料点，领取任务备料后送回需求工位。",
  "lang.ark.workflow.area.factoryFollowControl": "不允许跟随其他厂家进入的厂家",
  "lang.ark.workStatus.exception": "异常完成",
  "lang.ark.fed.fullBins": "货位已满，最多可放7个",
  "lang.ark.fed.upper": "上",
  "lang.ark.fed.useBeforeTask": "是否查询上段任务",
  "lang.ark.fed.screen.hybridRobot.binCellCodeTip": "机台的货位编码",
  "lang.ark.fed.syncProductGoodsInfo": "同步产线工位的物料信息",
  "lang.ark.workflow.template.validate.templateMidNodeMustUnique": "模板中间点必须唯一",
  "lang.ark.workflow.action.commandExecutePhase.undoBackArrived": "撤销/退回到达",
  "lang.ark.fed.menu.strategyCenter": "系统配置",
  "lang.ark.fed.startDrawing": "开始绘制",
  "lang.ark.warehouse.triggerPoint": "启动工位",
  "lang.ark.fed.offRefresh": "关闭刷新",
  "lang.ark.interface.resent": "重发",
  "lang.ark.fed.areaAreaOrAreaShelfOrShelfShelfIllegalProcess": "区域-区域或者区域-货架或者货架-货架，不合法的流程",
  "lang.ark.operation.workflow.pauseExecution": "暂停任务",
  "lang.ark.workflow.condition.greaterThan": "大于",
  "lang.ark.robot.firmware.update": "固件升级",
  "lang.ark.fed.goodsName": "物料名称",
  "lang.ark.loadCarrier.alreadyRemoved": "容器已离场",
  "lang.ark.workflow.existMultipleNodeExtendRobot": "存在多个节点继承机器人！",
  "lang.mwms.rf.receive": "入库",
  "lang.ark.fed.eitherOrRobotAndTypeNew": "型号与机器人只能二选一",
  "lang.ark.taskCannotOperate": "不能执行该操作!",
  "lang.ark.workflow.rollOverBack": "翻转-复位",
  "lang.ark.equipment.equipmentCellCodeExists": "点位 {0} 已被设备占用",
  "lang.ark.fed.waveType": "组波条件",
  "lang.ark.fed.alarmType": "上报类型",
  "lang.ark.fed.pointsList": "点位列表",
  "lang.ark.workflow.area.vertexInfo": "顶点信息",
  "lang.ark.fed.addContainerAmount": "批量新增容器数量，默认为1，最多5000",
  "lang.ark.fed.types": "类型",
  "lang.ark.apiCommonCode.instructionNotSupported": "执行指令类型{0}不支持",
  "lang.mwms.fed.businessRule": "业务规则",
  "lang.ark.fed.abnormal": "异常",
  "lang.ark.fed.orderAbnormalFailed": "操作失败，任务异常挂起状态的单据才可操作",
  "lang.ark.fed.autoCancleTask": "自动取消任务",
  "lang.ark.fed.menu.palletPositionManage": "托盘位信息",
  "lang.ark.workflow.controllerOutOfBounds": "控制器的数量要在1-65536之间",
  "lang.ark.fed.emptyIt": "清空",
  "lang.ark.fed.theMaterialsOfTime": "用料时间",
  "lang.ark.fed.numberLang": "数量",
  "lang.ark.fed.robotID": "机器人ID",
  "lang.ark.fed.nodeConfirmedLeave.tip": "需配合地图途经点使用",
  "lang.ark.fed.distributionMaterials": "配送物料",
  "lang.auth.UserAPI.item0195": "改角色不存在",
  "lang.auth.UserAPI.item0194": "角色修改失败",
  "lang.auth.UserAPI.item0197": "真实姓名不能为空",
  "lang.ark.fed.taskStartingPoint": "任务起点",
  "lang.auth.UserAPI.item0196": "用户基本信息修改失败",
  "lang.authManage.web.existLoginUser": "已经别处登录,请正常退出后再登录！",
  "lang.auth.UserAPI.item0191": "添加用户失败",
  "lang.auth.UserAPI.item0190": "您输入的原密码错误",
  "lang.auth.UserAPI.item0193": "用户名已存在",
  "lang.ark.apiStationCode.stationQueueUnDefinite": "工作站排队控制状态未定义",
  "lang.auth.UserAPI.item0192": "添加用户异常",
  "lang.ark.fed.waveConfig": "波次配置",
  "lang.ark.addContainerFail": "添加容器失败!",
  "lang.ark.fed.distribution": "分配",
  "lang.ark.fed.showByFlowClass": "根据流程类别显示",
  "lang.auth.UserAPI.item0199": "没有要修改的用户",
  "lang.mwms.monitorRobotMsg.notfree": "货架处于非空闲状态",
  "lang.auth.UserAPI.item0198": "用户名不能为空",
  "lang.ark.fed.containerTypeInfo": "容器类型信息",
  "lang.ark.fed.scanExceptionProcess": "扫码异常处理",
  "lang.ark.fed.shelfAttribute.REPLEN": "REPLEN",
  "lang.ark.fed.onTheWay": "正在路上",
  "lang.ark.api.moving.startIsEmpty": "任务起点为空",
  "lang.ark.fed.ContainerIsWorkingCanNotEmptyInventory": "容器工作中，不可清空库存",
  "lang.ark.workflow.area.append": "队尾补缺",
  "lang.ark.fed.amounts": "需求数量",
  "lang.mwms.fed.taskMonitor": "任务监控",
  "lang.ark.fed.SIMPPushTip": "选择SIMP配置规则，支持邮箱、企业微信、钉钉等方式推送",
  "lang.ark.externalDevice.caution": "请注意",
  "lang.ark.fed.flowCreateTitle": "流程创建：创建流程页面，流程显示呈现样式",
  "lang.ark.fed.pleaseAtLeastOneGoodsInfo": "请至少新增一条物料信息",
  "lang.ark.fed.query": "查询",
  "lang.ark.api.none": "未知含义",
  "lang.ark.fed.pleaseEnterANonnegativeNumber": "请输入非负数！",
  "lang.ark.fed.intelligentMovingSystem": "智能搬运系统！",
  "lang.ark.fed.chooseGoods": "可配物料",
  "lang.auth.UserAPI.item0175": "没有获取到当前登录用户",
  "lang.auth.UserAPI.item0174": "没有权限",
  "lang.ark.fed.deviceNotExists": "设备不存在",
  "lang.ark.fed.sourcesOfTheFactory": "来源工厂",
  "lang.mwms.fed.stocktake": "库存盘点",
  "lang.auth.UserAPI.item0177": "该用户已被禁用，请联系管理员重新启用或者使用其它用户登录",
  "lang.auth.UserAPI.item0176": "数据错误",
  "lang.auth.UserAPI.item0179": "密码错误",
  "lang.auth.UserAPI.item0178": "账户不存在，请重新输入",
  "lang.ark.record.interface.createTask": "创建接口指令任务",
  "lang.ark.fed.executeNotAllowed": "正在执行中,不允许操作",
  "lang.common.failed": "操作失败",
  "lang.ark.fed.flowRule": "流转规则",
  "lang.ark.api.cellNotExists": "单元格不存在",
  "lang.ark.workflow.includesSubflowCancellationNotAllowed": "当前流程包含子流程不允许取消",
  "lang.ark.workflow.pathDecision": "路径判断",
  "lang.authManage.web.others.relUser": "关联用户",
  "lang.ark.fed.materialConsumptionTime": "用料时间",
  "lang.auth.UserAPI.item0184": "已经没有用户数据了",
  "lang.auth.UserAPI.item0183": "退出异常",
  "lang.auth.UserAPI.item0186": "您输入的密码与原来的密码不匹配",
  "lang.ark.fed.notAllowOperation": "终点编码已存在,不允许操作",
  "lang.auth.UserAPI.item0185": "查询用户异常",
  "lang.auth.UserAPI.item0180": "数据错误",
  "lang.auth.UserAPI.item0182": "请输入用户名",
  "lang.ark.workflow.canNotPublish": "流程异常，无法发布",
  "lang.auth.UserAPI.item0181": "请输入密码",
  "lang.ark.trafficControl.enterStrategy": "放行顺序",
  "lang.auth.UserAPI.item0188": "密码修改失败",
  "lang.auth.UserAPI.item0187": "用户旧密码校验异常",
  "lang.ark.systemParamCannotEdit": "系统默认，不可编辑！",
  "lang.ark.fed.queue": "排队逻辑",
  "lang.auth.UserAPI.item0189": "修改密码异常",
  "lang.ark.fed.screen.equipmentAssociatedInfo.columnUpdateTime": "编辑时间",
  "lang.ark.backTaskNotAllowedUndo": "回退任务不允许撤销!",
  "lang.ark.fed.processDescription": "流程描述",
  "lang.ark.fed.goSomewhere": "去某个地方",
  "lang.ark.fed.trafficControlManage": "交通管制管理",
  "lang.ark.fed.citeNode": "引用节点",
  "lang.ark.fed.waveGenerateScope": "组波范围",
  "lang.authManage.web.others.number": "序号",
  "lang.ark.fed.targetStation": "需求工位",
  "lang.ark.waveStatus.distributing": "配送中",
  "lang.gles.strategy.tallyStrategy": "理货策略",
  "lang.ark.fed.createAMap": "创建地图",
  "lang.ark.fed.delNodeConfig": "确定删除当前节点交互配置么？",
  "lang.ark.fed.multipleChoice": "多选",
  "lang.ark.apiContainerCode.locationCodeExistsUsingContainer": "locationCode:{0}存在使用中的容器",
  "lang.ark.apiCallbackReg.single": "逐条",
  "lang.ark.getDMPErr": "获取设备信息错误",
  "lang.ark.fed.workstationUrl": "工作站URL",
  "lang.ark.fed.taskFrom": "取放类型",
  "lang.ark.fed.menu.chargeInfo": "充电站监控",
  "lang.gles.interface.interfaceLog": "接口日志",
  "lang.ark.workflow.template.type.dynamiNode": "动态点任务",
  "lang.ark.fed.chooseFreeMaterial": "选择可配物料",
  "lang.ark.fed.GENERAL": "普通",
  "lang.ark.workflowTriggerStatus.unEnable": "禁用",
  "lang.ark.workflow.Full": "已满/已占用",
  "lang.ark.fed.playVoiceTime": "播放时间",
  "lang.ark.fed.shelfLeaveSuccess": "容器离场成功",
  "lang.ark.fed.common.btn.delete": "删除",
  "lang.ark.fed.deliverType": "配送方式",
  "lang.ark.workflow.canDeleteContainerFlag": "移除容器",
  "lang.ark.fed.screen.workflowInfo.workflowExeTaskId": "二级任务Id",
  "lang.ark.noOperation": "无可执行操作",
  "lang.ark.workflowTrigger.logType.interface": "接口日志",
  "lang.ark.base.license.errorLicenseCustomerIdOrInstanceId": "证书客户编码或仓库编码 与证书不一致",
  "lang.ark.fed.firstNode": "首节点",
  "lang.ark.fed.cleanAll": "全部清除",
  "lang.ark.fed.feedingNode": "上料节点",
  "lang.ark.fed.ordinary": "普通",
  "lang.ark.api.goturn.neededsidesError": "容器旋转参数neededSides值不为F、B、L、R其中一个",
  "lang.mwms.monitorRobotMsg.scanabnormal": "容器号识别失败",
  "lang.ark.fed.forklift": "叉车",
  "lang.ark.fed.theCargoSpaceIsLocked": "货位已锁定",
  "lang.auth.UserAPI.item0115": "用户名已存在 ",
  "lang.auth.UserAPI.item0114": "添加用户异常",
  "lang.ark.workflow.template.validate.templateFinishNodeMustUnique": "模板终点必须唯一",
  "lang.auth.UserAPI.item0117": "该角色不存在",
  "lang.auth.UserAPI.item0116": "角色修改失败",
  "lang.auth.UserAPI.item0111": "您输入的原密码错误",
  "lang.auth.UserAPI.item0110": "密码修改失败",
  "lang.auth.UserAPI.item0113": "添加用户失败",
  "lang.auth.UserAPI.item0112": "修改密码异常",
  "lang.ark.workflow.arrive.action.command.executeFailed": "指令执行失败",
  "lang.auth.UserAPI.item0108": "您输入的密码与原来的密码不匹配",
  "lang.auth.UserAPI.item0107": "查询用户异常",
  "lang.ark.interface.oneToSixTeenLettersAndNumbers": "支持1-16位的字母与数字！",
  "lang.ark.workflow.cellCode": "点位编号",
  "lang.auth.UserAPI.item0109": "用户旧密码校验异常",
  "lang.ark.workflow.extendRobotFalse": "否",
  "lang.ark.interface.checkout": "查看",
  "lang.auth.UserAPI.item0120": "用户名不能为空",
  "lang.gles.systemManage.baseDict": "数据字典",
  "lang.ark.fed.interfaceSetNodeValue": "接口赋值点",
  "lang.ark.fed.outWarehouse": "人工出库",
  "lang.ark.fed.conButtonLogResult": "执行结果",
  "lang.auth.UserAPI.item0126": "已经没有用户数据了",
  "lang.ark.fed.linkName": "环节名称",
  "lang.auth.UserAPI.item0125": "{0}用户异常",
  "lang.auth.UserAPI.item0122": "修改用户异常",
  "lang.ark.fed.rotateMap": "旋转",
  "lang.ark.fed.theFirstNodeMustBeWorkstationOrDockPoint": "首节点必须为工作站或点位",
  "lang.ark.fed.screen.hybridRobot.stopPointCode": "地图点位编码",
  "lang.auth.UserAPI.item0121": "没有要修改的用户",
  "lang.auth.UserAPI.item0124": "没有选中一个用户",
  "lang.auth.UserAPI.item0123": "没有修改用户的状态",
  "lang.ark.fed.suspend": "暂停",
  "lang.ark.fed.processInstance": "流程实例",
  "lang.ark.fed.screen.area.ynGroup": "是否分组",
  "lang.auth.UserAPI.item0119": "真实姓名不能为空",
  "lang.ark.fed.entryPoint": "录入点",
  "lang.wms.biz.UserServiceImpl.deleteAdminAlert": "不能刪除管理員",
  "lang.auth.UserAPI.item0118": "用户基本信息修改失败",
  "lang.ark.controlNodeType.station": "工作站",
  "lang.ark.workflow.completeBizAutoTrigger": "完成后业务自动选择分支",
  "lang.ark.apiNodeActionCode.componentCommandIsEmpty": "节点交互配置的组件指令为空",
  "lang.ark.warehouse.goodsTaskCantNotExecute": "该任务已经被其它上料点领取,不能在该点位执行",
  "lang.ark.fed.common.checkNumberFormatMsg0": "请输入长度在{0}以内的数字",
  "lang.ark.workflow.area.stragingRange": "存储区",
  "lang.ark.fed.common.checkNumberFormatMsg1": "请输入长度在{0}至{1}以内的数字",
  "lang.ark.fed.orderAbnormalSure": "即异常情况下手动完成单据，确定完成？",
  "lang.ark.fed.menu.flowNodeConfig": "交互配置",
  "lang.ark.fed.factory": "工厂",
  "lang.authManage.web.auth.roleList": "角色管理",
  "lang.ark.recycleAreaTaskNotAllowedOperate": "回收区任务不允许执行此操作",
  "lang.ark.fed.location": "货位",
  "lang.ark.fed.defaultType": "默认类型",
  "lang.mwms.fed.inventoryNum": "库存余量",
  "lang.ark.shelfCodeErr": "容器类型编号格式不正确，请输入六位数字",
  "lang.ark.fed.sure": "确定",
  "lang.ark.fed.reflectCell": "区域对应节点",
  "lang.ark.workflow.paramValueCode.count": "count",
  "lang.ark.fed.friday": "周五",
  "lang.ark.task.log.export.title.workflow.instance": "流程实例",
  "lang.ark.fed.descriptionMessage": "描述信息",
  "lang.auth.UserAPI.item0104": "请输入用户名",
  "lang.ark.fed.station": "工位",
  "lang.auth.UserAPI.item0103": "请输入密码",
  "lang.auth.UserAPI.item0106": "已经没有用户数据了",
  "lang.auth.UserAPI.item0105": "退出异常",
  "lang.auth.UserAPI.item0100": "该用户已被禁用，请联系管理员重新启用或者使用其它用户登录",
  "lang.ark.fed.thisWorkflowHasBeenSelected": "已选择{str},这个工作流",
  "lang.auth.UserAPI.item0102": "密码错误",
  "lang.auth.UserAPI.item0101": "账户不存在，请重新输入",
  "lang.ark.fed.rackType": "货架类型",
  "lang.authManage.web.permission.permissiontype": "权限类型",
  "lang.ark.workflow.recoveryAreaType.customCell": "自定义回收区",
  "lang.ark.fed.arrivalOrientation": "到达朝向",
  "lang.ark.loadCarrier.loadCarrierModelFormErr": "容器模型数据不符合要求！",
  "lang.ark.fed.whereRobotsCanWalk": "机器人可行走的地方",
  "lang.ark.fed.thursday": "周四",
  "lang.mwms.fed.charts": "统计报表",
  "lang.ark.workflow.template.validate.templateTypeNotBlank": "模板类型不能为空",
  "lang.ark.fed.oneWayExit": "单行出口",
  "lang.authManage.web.common.modifyPw": "修改密码",
  "lang.ark.action.interface.responseParamType": "返参类型",
  "lang.ark.robot.classfy.mix": "复合",
  "lang.ark.fed.cancelledSuccessfully": "取消成功",
  "lang.authManage.web.others.toPrePage": "返回上一页",
  "lang.ark.firstSendNodeUnsupportedOperation": "第一个送节点不支持该操作!",
  "lang.ark.fed.expiringDate": "失效日期",
  "lang.ark.fed.pleaseSelectARobot": "请选择一个机器人",
  "lang.ark.fed.goToWork": "去工作",
  "lang.ark.workflow.function.type.functionArea": "功能区",
  "lang.ark.fed.closeRightTabs": "关闭右侧",
  "lang.ark.fed.component.workflow.label.nonSpecified": "不指定",
  "lang.ark.fed.firstClassification": "一级分类",
  "lang.ark.workstationNotExists": "工作站不存在!",
  "lang.ark.fed.screen.equipmentAssociatedInfo.dialogDetail.labelFloor": "楼层",
  "lang.ark.fed.basicData": "基础数据",
  "lang.ark.fed.pleaseSelectAtLeastOneAction": "请至少选择一项操作",
  "lang.ark.fed.waiting": "等待中",
  "lang.ark.fed.orientation": "方向",
  "lang.ark.fed.emptyMoving": "机器人空载",
  "lang.ark.container.containerEntryWay.manual": "人工入场",
  "lang.auth.PermissionAPI.item0009": "保存页面权限页面权限所属不同子系统间互斥验证不通过",
  "lang.ark.workflowConfig.configErr": "流程配置错误，请检查",
  "lang.auth.PermissionAPI.item0008": "保存页面权限子系统id与页面权限所属子系统id不一致",
  "lang.gles.receipt.receiptUpAndDownMaterialOrder": "上下料单",
  "lang.auth.PermissionAPI.item0005": "查询所有权限发生异常：{0}",
  "lang.auth.PermissionAPI.item0004": "保存页面权限发生异常：{0}",
  "lang.auth.PermissionAPI.item0007": "保存页面权限子系统数目验证不通过",
  "lang.auth.PermissionAPI.item0006": "角色名重复",
  "lang.authManage.web.common.newPassword": "新密码",
  "lang.ark.fed.flowStartAgain": "流程再启动",
  "lang.ark.fed.arrived": "已到达",
  "lang.ark.fed.cellNodeDeviceExists": "点位设备信息已存在",
  "lang.gles.containerManage": "容器管理",
  "lang.ark.fed.screen.equipmentAssociatedInfo.dialogDetail.labelEquipType": "设备类型",
  "lang.ark.fed.containerDeal": "容器处理",
  "lang.ark.fed.monday": "周一",
  "lang.ark.hitStrategy.denseStorage": "托盘密集存储",
  "lang.ark.fed.conditionalValue": "条件值",
  "lang.ark.workflow.palletLatticeNotExist": "目标点位不存在托盘位",
  "lang.ark.fed.shelfLocation": "货架位置",
  "lang.ark.fed.greaterThan": "大于",
  "lang.ark.fed.please": "请",
  "lang.ark.workflow.rollBack": "复位",
  "lang.mwms.monitorRobotMsg81000": "全场机器人电量偏低",
  "lang.ark.fed.rackPoint": "货架点",
  "lang.ark.fed.delRowData": "确定删除本行数据么?",
  "lang.ark.workflowTrigger.logType.task": "任务日志",
  "lang.ark.robot.manual": "遥控模式",
  "lang.ark.workflowConfig.cellFunctions.wait": "等待功能",
  "lang.mwms.fed.kanban": "电子看板",
  "lang.ark.fed.modificationRecord": "修改记录",
  "lang.ark.fed.recommendedSize": "推荐尺寸为",
  "lang.mwms.monitorRobotMsg16000": "机器人异常",
  "lang.ark.fed.sacnFailAndCancelTaskV2": "容器编码识别失败，异常故障码，{0}，等待上游业务系统处理",
  "lang.ark.workflow.executingCancelOperation": "正在执行取消操作",
  "lang.ark.workflow.controllerCodeOutOfBounds": "控制器编号要在0-65535之间",
  "lang.ark.fed.triggerHandler": "触发时机",
  "lang.ark.interface.interfaceDesc.name": "字段名称",
  "lang.ark.equipment.equipmentAlreadyRelevanceWorkflow": "设备已经关联流程，请先卸载流程",
  "lang.ark.workflow.paramValueCode.locationToFloor": "locationToFloor",
  "lang.ark.fed.receiptTimeout": "领单超时",
  "lang.ark.fed.licenseEditController": "License信息",
  "lang.ark.element.area.atLeast.one": "区域必须含有至少一个货架点",
  "lang.ark.cellCodeConfig.exists": "保存失败，该点位的交互配置已存在!",
  "lang.ark.unsupportedRunMode": "不支持的运行模式!",
  "lang.ark.fed.appointMentStatus": "预约状态",
  "lang.gles.baseData.baseContainerType": "容器类型",
  "lang.ark.fed.stopPointIdNonExistent": "当前点位不存在，请切换点位",
  "lang.ark.fed.serialNumPlaceholder": "选物料后出，焦点自动选中扫描框，扫描料杆序列号",
  "lang.ark.fed.distributionRobot": "分配机器人",
  "lang.ark.fed.setEndPoint": "设为终点",
  "lang.ark.fed.standardStation": "标准工作站",
  "lang.ark.fed.triggerTime": "触发时间",
  "lang.ark.fed.noAvailableRobots": "暂无可用机器人",
  "lang.ark.fed.ruleCode": "条件编码",
  "lang.ark.fed.cellName": "点位名称",
  "lang.ark.fed.orderExComplete": "异常完成",
  "lang.ark.fed.rack": "货架",
  "lang.ark.fed.enlarge": "放大",
  "lang.ark.ruleStage.differentFloor": "不同楼层",
  "lang.ark.fed.fixedRollerTrackOnTheGroundToConnectWithRobot": "地面上固定的可与机器人进行对接的辊道",
  "lang.ark.workflow.cycleType.noLoop": "不循环",
  "lang.ark.workflow.denyFollowFactoryNullError": "不允许跟随进入厂家为空，请填入厂家",
  "lang.ark.fed.waveStrategyCode": "策略编码",
  "lang.ark.workflow.paramValueCode.skuCode": "skuCode",
  "lang.ark.fed.workstation.msg.logout": "退出当前工作站？",
  "lang.ark.fed.autoRefresh": "自动刷新",
  "lang.auth.PermissionAPI.item0001": "查询页面权限发生异常：{0}",
  "lang.auth.PermissionAPI.item0003": "编辑页面权限失败,不能把其它权限保存为页面权限",
  "lang.ark.fed.contents.flowConfig.recycleType.api": "上游指定位置",
  "lang.auth.PermissionAPI.item0002": "编辑页面权限失败,{0}角色不存在",
  "lang.ark.syncRobotInfoError": "获取机器人信息失败",
  "lang.ark.fed.speedLimitZone": "限速区",
  "lang.ark.interface.clientCode": "客户端代码",
  "lang.ark.workflow.canAddshelfFlag": "添加货架",
  "lang.ark.fed.AutoIn": "自动中...",
  "lang.ark.robot.map.init": "地图初始化",
  "lang.ark.fed.remainingDistance": "温馨提示：就近AGV距您剩余距离：",
  "lang.ark.fed.targetFactory": "需求工厂",
  "lang.ark.fed.collectionTime": "领单时间",
  "lang.ark.fed.buttonType": "按钮类型",
  "lang.ark.workflow.arrive.action.component.robotComponentExecuteFailed": "机器人执行节点交互配置的组件指令失败（{0}）",
  "lang.ark.fed.waveRanage": "组波范围",
  "lang.ark.fed.screen.flowTemplate.specialNodeRepetitionTip": "已存在相同的节点类型和节点编码数据，请修改后再提交！",
  "lang.ark.fed.orderCreateFail": "创建失败",
  "lang.ark.workflowTriggerMonitorStatus.success": "成功",
  "lang.ark.fed.logType": "日志类型",
  "lang.ark.fed.robotLog": "机器人日志",
  "lang.ark.fed.pickUpTaskDetail": "领料单详情",
  "lang.ark.fed.getGoods": "叫料",
  "lang.authManage.web.common.requiredInput": "请输入必填项",
  "lang.ark.workflowTriggerMonitorStatus.failed": "失败",
  "lang.ark.workflow.denseStorageTemplateAreaEmpty": "起点是密集存储区域，且区域没有可用容器",
  "lang.ark.workflow.wareHouseStationBusinessConfig": "工作站业务",
  "lang.ark.loadCarrier.loadCarrierModelRequired": "容器模型不能为空，请选择容器模型！",
  "lang.ark.fed.screen.flowTemplate.LogicNodeRepetitionTip": "逻辑点：已存在相同的节点类型和节点编码数据，请修改后再提交！",
  "lang.ark.workflow.invalidStopPointStatus": "点位状态异常",
  "lang.mwms.fed.stockRotation": "库存周转规则",
  "lang.ark.fed.deleteTaskConfirmText": "删除任务，任务恢复到流程上一中断挂起，挂起恢复后重新下发，确定删除？",
  "lang.ark.workflowConfig.cellCodeDoNotExists": "节点编码未配置",
  "lang.ark.fed.containerSide": "容器面",
  "lang.ark.fed.screen.hybridRobot.stopPointTip": "机器人到达机台前的物理点位编码",
  "lang.ark.fed.cuttingGoods": "下料",
  "lang.ark.fed.component.workflow.label.nodeComponent": "节点组件",
  "lang.ark.fed.rotateLeft": "左旋转",
  "lang.ark.fed.nonEmptyShelf": "非空货架",
  "lang.ark.workflow.end": "结束",
  "lang.ark.workflow.template.validate.templateCodeNotBlank": "模板编码不能为空",
  "lang.ark.fed.component.workflow.label.specified": "指定",
  "lang.ark.fed.unlockSure": "确定解锁货位",
  "lang.ark.fed.liveAllNodeUnallowAdd": "已新增全部节点，不可重复添加！",
  "lang.gles.receipt.stockAdjustOrder": "库存调整单",
  "lang.ark.fed.onEnter": "正在进入",
  "lang.ark.fed.floorStage.sameFloor": "相同楼层",
  "lang.auth.UserAPI.item0098": "没有获取到当前登录用户",
  "lang.auth.UserAPI.item0097": "没有权限",
  "lang.ark.apiContainerCode.codeRightNotNumber": "startContainerCode最右一位或者几位需为数字",
  "lang.ark.fed.screen.area.maxTaskSize": "同时任务命中量",
  "lang.ark.taskStatusCannotCancel": "当前任务处于{0}状态,不允许删除!",
  "lang.ark.fed.createNew": "新建",
  "lang.ark.workflow.task.status.node.wait": "节点等待中",
  "lang.auth.UserAPI.item0099": "数据错误",
  "lang.mwms.fed.classifyMutex": "分类互斥配置",
  "lang.ark.fed.triggerNameRepeat": "触发器名称重复",
  "lang.authManage.web.common.item0027": "编辑",
  "lang.mwms.monitorRobotMsg13003": "结束充电站指令发送超时",
  "lang.authManage.web.common.item0028": "新增",
  "lang.mwms.monitorRobotMsg13001": "充电站离线(长时间失联)",
  "lang.mwms.monitorRobotMsg13002": "开始充电站指令发送超时",
  "lang.ark.fed.reset": "重置",
  "lang.mwms.fed.move": "库存移动",
  "lang.ark.area.lockJobInsertError": "区域预约锁定时间有重叠，请检查！",
  "lang.mwms.monitorRobotMsg13000": "充电站掉线(短暂网络断)",
  "lang.ark.button.command.reset": "弹起",
  "lang.ark.waveTaskStatus.create": "创建",
  "lang.ark.workflow.workflowRuleExpressionErr": "保存失败，请配置完整的表达式！",
  "lang.ark.fed.create": "创建",
  "lang.ark.sendRmsTaskError": "rms恢复异常，请稍后重试！原因：{0}",
  "lang.authManage.web.common.item0023": "查询",
  "lang.ark.api.globalConfigCancelOff": "未开启删除配置",
  "lang.ark.workflow.endSimple": "结束",
  "lang.ark.pda.function.container.leave": "容器离场",
  "lang.ark.fed.robotLoadStatus": "机器人状态判断",
  "lang.ark.warehouse.materialPointNameRepeat": "名称已存在",
  "lang.mwms.fed.ownerMutex": "货主互斥配置",
  "lang.ark.fed.pleaseSelectADestinationOnTheMapElementsOnly": "请在地图上选择目的地(仅限地图元素)",
  "lang.ark.fed.welcomePage": "欢迎页面",
  "lang.ark.fed.screen.hybridRobot.setPointOffset": "设置点位偏移值",
  "lang.ark.recycleFunctionSwitchIsClosed": "功能未开启，请联系系统人员开启此功能",
  "lang.ark.fed.menu.dockModel": "托盘架模型",
  "lang.common.ok": "确定",
  "lang.ark.fed.alarmTypeDetail": "领单超时、上料超时、下料超时",
  "lang.ark.fed.dispatchQueue": "配送顺序",
  "lang.ark.fed.cancelButton": "取消按钮",
  "lang.ark.fed.shelfAttribute.RTV": "RTV",
  "lang.mwms.fed.batchProperty": "批次属性",
  "lang.ark.interface.apiStationQueueStart": "启动排队接货",
  "lang.ark.fed.selectTaskType": "请选择任务类型",
  "lang.ark.loadCarrier.loadCarrierModelNameIsEmpty": "容器模型名称不能为空！",
  "lang.authManage.web.others.deviceInfo": "设备信息",
  "lang.ark.button.type.selfLocking": "自锁式",
  "lang.ark.fed.strategyCenter": "策略中心",
  "lang.authManage.api.menu.userList": "用户管理",
  "lang.authManage.web.auth.passwordPolicy": "密码策略",
  "lang.ark.fed.theTotalNumberOfContainers": "容器总数",
  "lang.ark.fed.buttonNumber": "按钮编号",
  "lang.ark.fed.unMatchGoodsLocation": "货架不匹配，送料货架位{0}，请回复货架后操作",
  "lang.ark.record.dmp.createTask": "创建dmp任务",
  "lang.ark.fed.continuityPassage": "连续通过",
  "lang.ark.fed.stationLineUpControl": "工作站排队控制",
  "lang.ark.fed.rackTypeManagement": "货架类型管理",
  "lang.ark.fed.containBinIsNotEmpty": "货位不为空",
  "lang.ark.fed.baseInfoV2": "基本信息",
  "lang.ark.api.template.unclearTargetBusinessType": "未明确目标点业务类型！",
  "lang.ark.fed.rmsTaskPhase": "执行阶段",
  "lang.mwms.fed.adjustments": "库存调整",
  "lang.ark.task.log.export.title.task.status": "任务状态",
  "lang.ark.workflowConfig.cellFunctions.beep": "鸣笛功能",
  "lang.ark.fed.notAllowedEditContainer": "容器已经入场，无法编辑",
  "lang.ark.trafficControl.manageArea": "管理区",
  "lang.ark.fed.bezier": "曲线",
  "lang.ark.fed.onePickingTitle": "一车领料：即叫料任务需多个上料点备料时，第一个上料点可领取任务领取完成下一步到达第二个上料点；上料点全部走完送回需求工位下料。",
  "lang.ark.fed.excel.data.repeat": "{0}行与{1}行点位重复，请检查修改后再上传",
  "lang.ark.apiNodeActionCode.componentCommandNotSupported": "节点交互配置的组件指令不支持",
  "lang.ark.operation.workflow.recoveryExecution": "恢复任务",
  "lang.authManage.web.common.editTime": "编辑时间",
  "lang.ark.fed.password": "密码",
  "lang.ark.fed.component.workflow.msg.duplicatedMaterialEntryType": "料口类型一致，请检查设备相关配置",
  "lang.authManage.web.common.cancel": "取消",
  "lang.mwms.fed.seedManager": "播种墙管理",
  "lang.ark.interface.messageName": "接口名称",
  "lang.ark.workflow.template.validate.templateStartNodeMustUnique": "模板起点必须唯一",
  "lang.ark.fed.changePassword": "修改密码",
  "lang.ark.fed.releaseSuccess": "发布成功",
  "lang.ark.workflow.ruleConfig": "规则配置",
  "lang.ark.fed.taskName": "任务名称",
  "lang.ark.base.license.errorLicenseInfoStr": "证书验证未通过!",
  "lang.ark.fed.theBranchMustStartWithTheMainLineNode": "分支必须以 主线节点为起始点",
  "lang.ark.fed.select": "选择",
  "lang.wms.station.web.UserAPI.item0305": "禁用",
  "lang.ark.workflow.lastTaskArrived": "上一个任务到达",
  "lang.ark.warehouse.productLineInfoException": "产线信息不存在，请确认",
  "lang.ark.warehouse.stationBusinessConfigDescription": "仅对FULL版工作站生效，FULL版工作站为单独入口，需访问地址请联系我们",
  "lang.ark.fed.areanodeidDidNotSelectShelfPoint": "区域{nodeId}未选择货架点;",
  "lang.ark.fed.orderStatus": "单据状态",
  "lang.ark.fed.menu.moduleInformationConfiguration": "按钮模型配置",
  "lang.mwms.fed.logistics": "物流管理",
  "lang.ark.interface.messageBody": "消息报文",
  "lang.authManage.fed.import": "导入",
  "lang.ark.fed.operateSwitch": "操作开关",
  "lang.ark.workflow.priorityAllocation": "优先",
  "lang.ark.workflow.arrive.action.goTurnOfAngle.neg90": "-90°",
  "lang.ark.fed.orderHang": "挂起",
  "lang.ark.shelfFirCodeErr": "容器类型编号格式不正确，托盘首字母为P，货架首字母为S",
  "lang.ark.record.rms.sendCommandTask": "发送机器人指令",
  "lang.ark.fed.period": "频率",
  "lang.ark.workflow.action.commandExecutePhase.previousArrived": "上一任务到达",
  "lang.common.internalException": "服务器内部异常",
  "lang.ark.record.breakTask": "中断原有任务创建新任务",
  "lang.ark.fed.containBinIsNotExists": "货位不存在",
  "lang.ark.fed.workTriggerManage": "触发器管理",
  "lang.ark.workflow.exceptionHandler.queueUp": "排队等待",
  "lang.ark.workflow.workflowTaskHasArrivedTargetLocation": "子任务暂停失败，机器人已到达目标点无法暂停！",
  "lang.ark.fed.flowName": "流程名称",
  "lang.ark.workflowConfig.cellFunctions.backup": "后退功能",
  "lang.ark.workflow.task.status.manual.completed": "人工完成",
  "lang.ark.fed.saveFailed": "保存失败",
  "lang.ark.workflowgroup.selector.ok": "是",
  "lang.ark.interface.interfaceDesc.english": "英文",
  "lang.ark.fed.nowYouCanDrawAMap": "现在您可以绘制地图",
  "lang.ark.fed.listInformation": "列表信息：",
  "lang.ark.fed.editingTime": "编辑时间",
  "lang.gles.stockInStore.fixedGoodsPositionStock": "固定货位库存",
  "lang.ark.fed.returnShelf": "归还货架",
  "lang.ark.workflow.notAllowCancel": "机器人取放操作中不允许取消",
  "lang.gles.baseData.baseDevice": "设备",
  "lang.mwms.monitorRobotMsg3": "规划路径的起点和终点是障碍",
  "lang.mwms.monitorRobotMsg4": "途中有障碍机器人或障碍货架",
  "lang.ark.warehouse.singleContainerSide": "单面",
  "lang.mwms.monitorRobotMsg5": "此时的路径资源正被其他机器人占据",
  "lang.ark.workflow.task.status.executing": "执行中",
  "lang.ark.fed.locationOfShelvesWithXyCoordinateAttributes": "可以安放货架的位置，具有xy坐标属性",
  "lang.ark.fed.configEnableTip": "关闭启用方可修改配置，请确保在非工作时间段修改",
  "lang.mwms.monitorRobotMsg6": "无路径可以规划",
  "lang.mwms.monitorRobotMsg7": "机器人无法链接",
  "lang.mwms.monitorRobotMsg8": "子任务发送超时",
  "lang.ark.record.clearWaitPoint": "清除等待点",
  "lang.mwms.monitorRobotMsg9": "指令发送超时",
  "lang.ark.fed.confirm": "确认",
  "lang.ark.robot.Task.pausing": "流程已暂停，请恢复后再试！",
  "lang.ark.fed.cancelFinish": "取消完成",
  "geekplus.moving.uic.elTableWrapperVue2.column.datetime": "日期时间",
  "lang.ark.workflowgroup.selector.no": "否",
  "lang.ark.workflow.areaLockedEdit": "交通管制区被锁定，无法编辑",
  "lang.ark.warehouse.importFileFormatError": "文件格式错误，请下载模板导入",
  "lang.ark.fed.front": "前",
  "lang.ark.fed.rackPositionRecovery": "货架位置恢复",
  "lang.ark.workflow.area.trafficRange": "交通管制区",
  "lang.ark.fed.updateAngle": "更新角度",
  "lang.ark.fed.addTaskTrigger": "新增任务触发器",
  "lang.ark.fed.manuallyControlArea": "人工管制区",
  "lang.ark.fed.screen.equipmentAssociatedInfo.dialogDetail.labelMaxTaskCont": "物理最大任务量",
  "lang.ark.api.moving.destIsEmpty": "任务目标点为空",
  "lang.ark.workflowPeriod.oneDay": "每天一次",
  "lang.ark.fed.cannotDeleteFetchNodesAlone": "不能单独删除取节点",
  "lang.ark.interface.apiStationList": "工作站查询",
  "lang.ark.fed.details": "详情",
  "lang.ark.fed.trafficDeleteMsg": "移除后不再跟踪该机器人本次在管制区运行情况，且其他机器人可能进入该管制区，确定移除?",
  "lang.ark.fed.welcomeToUse": "欢迎使用",
  "lang.ark.fed.robotType": "机器人类型",
  "lang.ark.interface.apiLastStep": "任务退回",
  "lang.ark.fed.workFlowConfigIsNotModel": "工作流程不是模板流程",
  "lang.ark.fed.conButtonLogFailed": "失败",
  "lang.mwms.monitorRobotMsg1": "规划路径失败",
  "lang.mwms.monitorRobotMsg2": "机器人不在地图",
  "lang.ark.fed.taskInterVal": "任务间隔",
  "lang.mwms.fed.priorityManager": "优先级管理",
  "lang.ark.workflow.autoSkipSimple": "自动跳过",
  "lang.ark.fed.isForbidTrigger": "是否禁用此触发器？",
  "lang.ark.fed.rice": "米",
  "lang.ark.fed.workFlowRule": "流转规则",
  "lang.ark.warehouse.hasSameStationGoods": "相同产线工位不可添加重复物料",
  "lang.ark.workflow.TimeTrigger": "完成后自动触发",
  "lang.ark.fed.automaticTrigger": "时间触发",
  "lang.ark.workflowTrigger.logType.controllerButtonLog": "物理按钮日志",
  "lang.ark.fed.productionLine": "产线管理",
  "lang.auth.RoleAPI.item0007": "保存数据权限发生异常：{0}",
  "lang.ark.fed.abnormalCancelTime": "异常取消时间",
  "lang.ark.fed.production": "产线",
  "lang.ark.fed.Entrance": "进入口",
  "lang.auth.RoleAPI.item0008": "roleId 参数不能为空",
  "lang.auth.RoleAPI.item0005": "编辑数据权限失败,角色不存在",
  "lang.ark.workflowConfig.cellFunctions.palletPackCell": "PALLET_PACK_CELL",
  "lang.auth.RoleAPI.item0006": "编辑数据权限失败,不能把其它权限保存为数据权限",
  "lang.ark.fed.interactiveActionNameConfig": "交互动作配置",
  "lang.ark.api.workflowWorkIdIsNull": "workflowWorkId不能为空",
  "lang.auth.RoleAPI.item0009": "删除角色失败,角色不存在",
  "lang.auth.RoleAPI.item0003": "激活或禁用角色权限发生异常：{0}",
  "lang.auth.RoleAPI.item0004": "激活角色权限发生异常：{0}",
  "lang.auth.RoleAPI.item0001": "激活或者禁用角色权限失败! 参数为空",
  "lang.ark.fed.common.btn.save": "保存",
  "lang.auth.RoleAPI.item0002": "激活或者禁用角色权限发生异常 参数不符合标准：status {0}",
  "lang.ark.fed.workstationAndqueueController": "排队控制",
  "lang.ark.fed.workTaskDetails": "流程任务详情",
  "lang.mwms.fed.strategyWave": "波次策略",
  "lang.ark.record.robotCallback.arrived": "机器人已到达",
  "lang.authManage.web.others.operation": "操作",
  "lang.auth.RoleAPI.item0016": "使用中角色不能删除",
  "lang.auth.RoleAPI.item0017": "该角色(roleId = 1)不可编辑",
  "lang.auth.RoleAPI.item0010": "删除角色发生异常：{0}",
  "lang.ark.fed.nodeInteractiveMode": "设备交互方式",
  "lang.auth.RoleAPI.item0011": "{0}角色权限发生异常 参数为空：roleId {1} roleName {2} ",
  "lang.auth.RoleAPI.item0014": "{0}角色权限发生异常： 更新失败 roleId{1}",
  "lang.auth.RoleAPI.item0015": "查询角色的数据权限发生异常：{0}",
  "lang.auth.RoleAPI.item0012": "{0}角色权限发生异常：查询出多条记录 roleId {1} roleName {2}",
  "lang.auth.RoleAPI.item0013": "{0}角色权限发生异常：参数id和name与db记录不匹配 ",
  "lang.gles.workflow.abnormalTask": "异常任务清理",
  "lang.mwms.fed.strategicCenter": "策略中心",
  "lang.ark.fed.productionDate": "生产日期",
  "lang.ark.fed.ensure": "确认",
  "lang.ark.fed.rackAngle": "容器角度",
  "lang.gles.strategy.hit": "命中策略",
  "lang.ark.apiContainerCode.containerCategoryNoMatch": "没有匹配到containerCategory:{0}",
  "lang.ark.fed.templateName": "模板名称",
  "lang.wms.station.web.UserAPI.item0306": "启用",
  "lang.ark.container.containerEntryWay.moving": "机器人搬运入场",
  "lang.ark.warehouse.noShelf": "当前点位无可用货架",
  "lang.ark.workstationDoesNotExistNode": "{0}号工作站中不存在{1}停靠点!",
  "lang.ark.fed.alreadyIssued": "已下发",
  "lang.ark.action.interface.referenceValue": "引用取值",
  "lang.ark.fed.taskAbnormal": "任务异常",
  "lang.ark.fed.publish": "发布",
  "lang.ark.action.interface.internal": "间隔:(秒)",
  "lang.ark.warehouse.supportBusinessDescription": "叫料：开启，即工作站支持选择物料发起叫料任务。 送料：开启，即工作站支持手动选择物料发起送料任务，也可领取叫料任务配送。 搬运：开启，即工作站支持选择流程发起搬运任务。 管理模式：开启，即工作站叫料、送料、搬运业务开启管理模式，可代叫料和送料，以及指定任意点发起搬运任务。",
  "lang.ark.workflow.manulSimple": "手动触发",
  "lang.ark.fed.conButtonLogDayData": "天数据",
  "lang.ark.fed.manualProcess": "人工处理",
  "lang.ark.fed.successfulConnectionToTheServer": "与服务器连接成功",
  "lang.ark.fed.notAllowMergeGet": "取节点不支持连接到其他节点！",
  "lang.ark.fed.menu.scaffold": "支架",
  "lang.ark.fed.model": "型号",
  "lang.ark.fed.zoneLockReleaseTime": "区域锁定/释放时间",
  "lang.ark.fed.createFlow": "创建流程",
  "lang.ark.fed.selectPalletPosition": "选择托盘位",
  "lang.ark.fed.waitTime": "等待时间",
  "lang.ark.fed.firstAddSendPoint": "请先添加送节点！",
  "lang.ark.workflow.staging": "送往暂存区",
  "lang.ark.fed.enterCodeOrName": "输入物料编码或物料名称",
  "lang.ark.button.operation.command.systemEmergencyStop": "系统急停",
  "lang.ark.fed.loadingCell": "上料工位",
  "lang.ark.fed.actionTriggerHandlerError": "请填写触发时机",
  "lang.ark.fed.pointContainNoStation": "触发点位/容器号/工作站",
  "lang.ark.fed.demandProductionLineOrWorkshop": "需求产线/车间",
  "lang.ark.waveStatus.preDistribute": "待配送",
  "lang.ark.interface.errorMessage": "异常原因",
  "lang.ark.fed.screen.systemConfig.sysGroupName": "分类标记",
  "lang.ark.fed.materialQuantity": "品类数",
  "lang.ark.workflowTriggerType.workflowGroup": "触发流程组",
  "lang.ark.fed.defaultCacheArea": "默认缓存区",
  "lang.ark.fed.enabled": "已启用",
  "lang.ark.workflow.recycleAreaIsFull": "取消失败,回收区已满,请稍后再试!",
  "lang.ark.fed.leaving": "正在离开",
  "lang.ark.fed.screen.workflowInfo.executeAction": "执行动作",
  "lang.ark.fed.endPoint": "终点",
  "lang.ark.apiContainerCode.locationCodeExistsContainer": "locationCode:{0}已经存在容器",
  "lang.ark.fed.operationDuration": "操作时长",
  "lang.ark.fed.deliveryInfo": "配送信息",
  "lang.ark.fed.closeLoop": "闭环流程",
  "lang.ark.fed.cancelConfirmMsg3": "取消流程后流程结束, 且容器被送到指定回收区, 确定取消?",
  "lang.ark.fed.cancelConfirmMsg2": "取消该流程实例下所有任务, 确定取消?",
  "lang.ark.fed.cancelConfirmMsg1": "请指定流程取消后, 容器送往的区域",
  "lang.ark.fed.total": "总数",
  "lang.ark.fed.goodsWaitSend": "待配送",
  "lang.ark.workflow.acceptTask": "接收指令",
  "lang.mwms.monitorTaskPhaseMsg21": "到达目的地",
  "lang.ark.theRunningTaskIsUsingThisArea": "不允许编辑,运行的任务正在使用该区域!",
  "lang.mwms.monitorTaskPhaseMsg22": "任务已经完成",
  "lang.ark.fed.menu.vensManagement": "设备",
  "lang.mwms.monitorTaskPhaseMsg20": "离开电梯",
  "lang.ark.workflowTrigger.logType": "日志类型",
  "lang.ark.api.requestExists": "requestId重复",
  "lang.ark.workflow.shelfAlreadyExists": "容器已存在",
  "lang.ark.fed.timingTrigger": "定时触发",
  "lang.ark.fed.areUSureLocked": "确定锁定吗？",
  "lang.ark.base.license.licenseInfoStrIsNull": "证书秘钥为空!",
  "lang.ark.task.exception.cellCode.not.null": "目标点:{}已存在容器号为：{}的容器",
  "lang.ark.fed.conButtonLogALLData": "全部数据",
  "lang.ark.fed.stationPosition": "工位点位",
  "lang.ark.fed.containerLevelOne": "容器一级分类",
  "lang.mwms.monitorTaskPhaseMsg10": "滚筒机器人到达取货位,准备取货",
  "lang.mwms.monitorTaskPhaseMsg11": "滚筒机器人到达卸货位,准备卸货",
  "lang.ark.fed.initiationTime": "发起时间",
  "lang.ark.fed.containerTypeNo": "容器类型编号",
  "lang.ark.workStatus.complete": "完成",
  "lang.mwms.monitorTaskPhaseMsg18": "到达电梯门口的等待点(电梯外),等待进入电梯",
  "lang.mwms.monitorTaskPhaseMsg19": "已经进入电梯",
  "lang.mwms.monitorTaskPhaseMsg16": "取箱子完成",
  "lang.mwms.monitorTaskPhaseMsg17": "送箱子到达",
  "lang.ark.fed.executeWorkflowFailed": "无可用容器,流程发起失败",
  "lang.mwms.monitorTaskPhaseMsg14": "滚筒机器人收货完成",
  "lang.ark.api.goturn.noTurnParameter": "容器旋转参数neededSides和turnAngle至少填一项",
  "lang.mwms.monitorTaskPhaseMsg15": "滚筒机器人卸货完成",
  "lang.ark.fed.excel.deviceMode": "{0}行设备交互方式信息不正确，请检查修改后再上传",
  "lang.ark.fed.inputParameterAssignment": "传参赋值",
  "lang.mwms.monitorTaskPhaseMsg12": "到达等待点",
  "lang.mwms.monitorTaskPhaseMsg13": "离开等待点",
  "lang.ark.workflow.occupiedByWorkflowGroup": "该流程已关联流程组，不能删除该流程或将首节点设为手动",
  "lang.ark.workflow.extendRobot": "强制继承机器人",
  "lang.ark.fed.waveTaskStatus": "波次任务状态",
  "lang.auth.UserAPI.item0001": "已登录{0}用户，请注销后登录",
  "lang.ark.fed.processDemonstration": "流程展示",
  "lang.ark.workflow.workflowEditFailed": "无法编辑使用中的流程或流程组",
  "lang.ark.fed.pointPositionName": "点位",
  "lang.ark.fed.component.workflow.label.materialEntryType": "料口类型",
  "lang.ark.fed.floorStage": "楼层流转策略",
  "lang.ark.fed.scrollIsNotMatchWithGoods": "卷轴号和物料信息不匹配",
  "lang.authManage.web.common.pagePermission": "页面权限",
  "lang.ark.no.operation": "无操作",
  "lang.ark.fed.autoClean": "自动清除",
  "lang.ark.warehouse.columnCountLimit26": "每层最多26列，请重新输入列数",
  "lang.ark.fed.component.workflow.edgeName.equipmentTask": "设备流转",
  "lang.ark.fed.stop": "暂停",
  "lang.ark.workflow.area.releaseCountDown": "自动释放倒计时",
  "lang.authManage.fed.screen.login.toModify": "去修改",
  "lang.ark.trafficControl.taskControlRange": "任务限制区域",
  "lang.ark.workflow.task.status.commandExecuting": "指令执行中",
  "lang.ark.fed.yes": "是",
  "lang.ark.fed.language": "语言",
  "lang.ark.fed.screen.hybridRobot.robotOffsetConfig": "机器人点位偏移量配置",
  "lang.ark.workflowConfig.cellFunctions.transCell": "TRANS_CELL",
  "lang.ark.warehouse.cellCodeNotexits": "用料点:{0}不存在",
  "lang.ark.fed.eachRow": "每行",
  "lang.ark.trafficControl.containerFunction": "存储区功能",
  "lang.ark.fed.lagTime": "滞后时间",
  "lang.ark.alreadyExpired": "当前时间已经超过所设置的有效期",
  "lang.ark.workflow.userNoAuthToLogin": "当前用户没有登录该工作站的权限",
  "lang.ark.fed.createFlowSuccess": "发起流程成功",
  "lang.ark.workflow.paramValueCode.extraParam15": "extraParam15",
  "lang.ark.action.interface.conditionExtraParam18": "extraParam18",
  "lang.ark.fed.uploadOnly": "只能上传",
  "lang.ark.trigger.missedEffectiveTime": "已经错过了生效时间",
  "lang.ark.workflow.paramValueCode.extraParam14": "extraParam14",
  "lang.ark.fed.noPermissionPage": "当前页面无权限",
  "lang.ark.action.interface.conditionExtraParam19": "extraParam19",
  "lang.ark.workflow.paramValueCode.extraParam13": "extraParam13",
  "lang.ark.action.interface.conditionExtraParam16": "extraParam16",
  "lang.ark.workflow.paramValueCode.extraParam12": "extraParam12",
  "lang.ark.action.interface.conditionExtraParam17": "extraParam17",
  "lang.ark.workflow.paramValueCode.extraParam19": "extraParam19",
  "lang.ark.action.interface.conditionExtraParam14": "extraParam14",
  "lang.ark.fed.waitingPoint": "等待点",
  "lang.ark.workflow.paramValueCode.extraParam18": "extraParam18",
  "lang.ark.action.interface.conditionExtraParam15": "extraParam15",
  "lang.ark.workflow.paramValueCode.extraParam17": "extraParam17",
  "lang.ark.action.interface.conditionExtraParam12": "extraParam12",
  "lang.ark.workflow.paramValueCode.extraParam16": "extraParam16",
  "lang.ark.action.interface.conditionExtraParam13": "extraParam13",
  "lang.ark.workflowConfig.cellFunctions.chargerCell": "CHARGER_CELL",
  "lang.ark.fed.containerLeave": "离场",
  "lang.ark.fed.enableSettings": "启用设置",
  "lang.ark.interface.interfaceDesc.content": "字段返回值",
  "lang.ark.interface.apiRecover": "流程恢复执行",
  "lang.ark.fed.menu.operator": "运营",
  "lang.ark.fed.sunday": "周日",
  "lang.ark.workflow.taskType": "任务类型",
  "lang.ark.action.interface.conditionExtraParam20": "extraParam20",
  "lang.ark.workflow.paramValueCode.extraParam20": "extraParam20",
  "lang.ark.fed.triggerType": "触发器类型",
  "lang.common.retry": "重试",
  "lang.gles.materialArchives": "物料档案",
  "lang.ark.warehouse.binShelfNotFree": "物料所在货架不空闲,不能执行该操作",
  "lang.ark.fed.sendComplete": "配送完成",
  "lang.ark.fed.siteMonitoring": "场地监控",
  "lang.ark.interface.movingMulti": "点到多点搬运",
  "lang.ark.fed.pleaseSelectAvailableRobot": "选择机器人 ：",
  "lang.ark.fed.deviceInfo": "设备信息",
  "lang.ark.warehouseTask.pickTaskOverTime": "领料单未领取，已超{0}分钟",
  "lang.ark.fed.robotNumber": "机器人编号",
  "lang.ark.fed.getGoodsTimeout": "上料超时",
  "lang.ark.action.interface.conditionExtraParam10": "extraParam10",
  "lang.ark.fed.moveTitle": "搬运：开启，即工作站支持选择流程发起搬运任务。",
  "lang.ark.action.interface.conditionExtraParam11": "extraParam11",
  "lang.ark.workflow.notAllowFinishIfEmptyLoad": "空载状态下不允许直接完成",
  "lang.authManage.web.common.search": "查询",
  "lang.ark.button.operation.command.addContainer": "容器入场",
  "lang.mwms.fed.putawayRuleDesignatedBin": "上架规则指定货位",
  "lang.ark.fed.processProcessGroup": "流程／流程组",
  "lang.ark.fed.insert": "插入",
  "lang.ark.workflow.acceptUpstreamParam": "接收上游参数",
  "lang.ark.fed.turningSurface": "转面",
  "lang.fed.ark.sure": "确认",
  "lang.ark.fed.taskOver": "撤销",
  "lang.mwms.fed.queryTask": "任务查询",
  "lang.ark.fed.pleaseSelectShelf": "请点击选择货架！",
  "lang.ark.fed.shelfAttribute.GMSInterfaceField": "GMS接口字段",
  "lang.ark.fed.editInterface": "编辑接口指令",
  "lang.ark.fed.completionOfDrawing": "完成绘制",
  "lang.ark.apiCommonCode.systemRecoverFailed": "系统恢复失败",
  "lang.mwms.monitorTaskPhaseMsg9": "正在充电",
  "lang.mwms.monitorTaskPhaseMsg8": "正在移动",
  "lang.mwms.monitorTaskPhaseMsg7": "货架旋转",
  "lang.authManage.web.common.abled": "启用",
  "lang.mwms.monitorTaskPhaseMsg2": "货架已经取到",
  "lang.mwms.monitorTaskPhaseMsg1": "正在取货架",
  "lang.mwms.fed.putawayRuleDesignatedArea": "上架规则指定区",
  "lang.mwms.monitorTaskPhaseMsg6": "货架归还中",
  "lang.mwms.monitorRobotMsg.noshelf": "无可用货架",
  "lang.mwms.monitorTaskPhaseMsg5": "货架已经抵达目的地",
  "lang.mwms.monitorTaskPhaseMsg4": "正在排队",
  "lang.mwms.monitorTaskPhaseMsg3": "正在搬运货架",
  "lang.ark.interface.interfaceDesc.desc": "字段描述",
  "lang.ark.workflow.template.type.nodeToMultiNode": "点到多点任务",
  "lang.ark.sys.config.group.common": "基础配置",
  "lang.gles.StockInTransit": "在途库存",
  "lang.ark.fed.publishAndSave": "发布并保存",
  "lang.ark.fed.refreshCycle": "刷新频率",
  "lang.ark.fed.operationSymbol": "运算符",
  "lang.mwms.fed.stationManager": "工作站管理",
  "lang.ark.fed.targetPointType": "目标点类型",
  "lang.ark.workflow.canAddContainerFlag": "添加容器",
  "lang.ark.fed.departure": "离场",
  "lang.ark.pda.function.area.lock": "区域锁定",
  "lang.ark.apiStationCode.stationQueueAlreadyDisable": "工作站排队控制已关闭，不能重复关闭",
  "lang.ark.fed.paramCode": "参数编码",
  "lang.ark.interface.apiContainerList": "容器查询",
  "lang.ark.fed.moduleInformationConfiguration": "模块信息配置",
  "lang.ark.fed.singleFactorySingleEntrances": "单厂家单入口",
  "lang.ark.fed.shelfCondition": "货架条件",
  "lang.ark.fed.basicInformation": "基础信息",
  "lang.ark.workflow.ruleDecision": "规则判断",
  "lang.ark.fed.unlock": "解锁",
  "lang.ark.fed.cloaseAll": "全部关闭",
  "lang.ark.fed.containerCodeAutoIncrement": "多个容器编码自增",
  "lang.ark.containerNotAtTheTriggerPoint": "容器{0}当前所在位置为{1},不在启动点{2}上!",
  "lang.ark.fed.palletBitNode": "托盘位节点",
  "lang.ark.fed.priorityGoUp": "优先级上升",
  "lang.ark.fed.flowNodeConfig": "交互配置",
  "lang.ark.systemParamCannotDelete": "系统默认，不可删除",
  "lang.ark.fed.rotateRight": "右旋转",
  "lang.ark.container.shelfTypeNotExit": "容器二级分类不存在",
  "lang.gles.logisticsConfig": "物流配置",
  "lang.ark.fed.dischargeLoadMore": "释放后加载",
  "lang.gles.baseData.area": "区域",
  "lang.common.abort": "中止",
  "lang.ark.fed.workstationManagement": "工作站管理",
  "lang.ark.fed.entryEndPoint": "入场终点",
  "lang.ark.interface.messageInstruction": "执行命令",
  "lang.ark.fed.specialAreaSavedSuccessfully": "特殊区",
  "lang.ark.fed.orderCancel": "取消",
  "lang.ark.fed.menu.replacementMaterial": "更换素材",
  "lang.ark.fed.userHelpDocumentDownload": "用户帮助文档下载",
  "lang.ark.record.robotCallback.leaveStart": "机器人离开",
  "lang.ark.fed.conButtonLogSuccess": "成功",
  "lang.ark.fed.currentContainer": "当前容器",
  "lang.mwms.fed.inventoryCharts": "库存报表",
  "lang.ark.fed.GoodsTask": "领料任务",
  "lang.ark.fed.actionsNotAllowToUp": "{0}节点交互配置错误，动作不可为顶升！",
  "lang.ark.fed.changeStopPoint": "切换停靠点",
  "lang.ark.fed.theDeliveryTime": "配送时间",
  "lang.ark.fed.orderOccupy": "先占先过",
  "lang.ark.containerAlreadyExists": "容器已存在!",
  "lang.ark.fed.workflowList": "流程列表",
  "lang.ark.workflow.paramValueCode.extraParam11": "extraParam11",
  "lang.ark.workflow.paramValueCode.extraParam10": "extraParam10",
  "lang.ark.record.robotCallback.arriveWaitPoint": "机器人到达等待点",
  "lang.auth.Audit.item0010": "禁用角色：{0}",
  "lang.auth.Audit.item0012": "设置角色权限：{0}",
  "lang.auth.Audit.item0011": "启用角色：{0}",
  "lang.ark.fed.shelfAttribute.upstreamInterfaceField": "上游接口字段",
  "lang.ark.fed.taskId": "任务号",
  "lang.auth.Audit.item0013": "删除角色：{0}",
  "lang.ark.workflowTrigger.logType.triggerExeLog": "触发器执行日志",
  "lang.ark.fed.pleaseChooseShelfTypeOrShelfCode": "请选择货架类型或者货架编码",
  "lang.ark.workflow.shelfNotExists": "容器不存在",
  "lang.ark.interface.request": "请求",
  "lang.ark.fed.robotsCanBeScheduledByTheSystemDuringChargingElectricityMust": "机器人在充电中（电量必须大于5%）可被系统调度",
  "lang.ark.fed.breakAndWait": "中断等待",
  "lang.ark.task.exception.work.not.null": "目标点:{}已存在任务实例：{}占用",
  "lang.ark.fed.areaMustContainNodes": "保存失败！区域必须包含一个以上的点位！",
  "lang.ark.workflow.workflowUndoing": "取消失败，流程正在取消中",
  "lang.ark.fed.raceway": "滚道",
  "lang.ark.fed.selectionRobot": "选择机器人",
  "lang.ark.fed.specialArea": "特殊区",
  "lang.mwms.monitorRobotMsg14004": "驱动器欠电压",
  "lang.ark.workflow.noAvailableStopPointBeginNode": "未找到可用的起点",
  "lang.mwms.monitorRobotMsg14000": "货架位置待确认",
  "lang.ark.workflow.condition.greaterThanOrEqual": "大于等于",
  "lang.gles.receipt.receiptWarehousingOrder": "入库单",
  "lang.ark.fed.target": "去向",
  "lang.ark.fed.minute": "分钟",
  "lang.ark.fed.menu.containerManage": "容器管理",
  "lang.ark.fed.addComponentInterface": "新增组件指令",
  "lang.ark.api.template.startNodeNotMatchForWave": "指定起点{0}和模板起点{1}不匹配",
  "lang.ark.element.no.element.selected": "没有选中元素",
  "lang.gles.receipt.outWarehouseOrder": "出库单",
  "lang.ark.trafficControl.enterStrategy.byTime": "先到先过",
  "lang.ark.workflowTrigger.logType.containerChangeLog": "容器日志",
  "lang.ark.fed.canDeleteshelfFlag": "移除货架",
  "lang.ark.fed.saveCurCustomConfig": "保存当前定制配置",
  "lang.ark.loadCarrier.loadCarrierModelNotExist": "容器模型不存在！",
  "lang.ark.fed.tow": "牵引",
  "lang.authManage.web.common.input": "请输入",
  "lang.ark.api.nodesNeedConfigureAction": "未找到与节点相匹配的交互配置",
  "lang.ark.fed.wholeTriggeWorkflow": "整体触发流程",
  "lang.ark.plugin.pluginType.returnContainer.way.empty": "送空容器",
  "lang.ark.fed.addExtendDevice": "新增外部设备",
  "lang.ark.fed.viewMap": "地图查看",
  "lang.ark.fed.shelfArriveOrientation": "货架需要朝向",
  "lang.ark.sys.config.group.switch": "系统开关",
  "lang.ark.button.operation.command.systemRecover": "系统恢复",
  "lang.ark.fed.implement": "执行",
  "lang.ark.workStatus.create": "创建",
  "lang.ark.fed.screen.hybridRobot.installEquipment": "上装设备",
  "lang.ark.fed.hybridRobot.hybridRobotType.doubleLift": "双举升",
  "lang.ark.fed.carryOutTheTask": "执行任务",
  "lang.ark.auth.userHaveLoginOtherPlace": "当前用户已在别处登录!",
  "lang.ark.fed.waitLockSureCancel": "等待锁定中，确定取消",
  "lang.ark.fed.right": "右",
  "lang.ark.fed.processControl": "流程控制",
  "lang.ark.fed.driving": "主动配送",
  "lang.ark.fed.goodsNum": "物料编码",
  "lang.ark.fed.twoDimensionalCodeFlowManagement": "二维码流程管理",
  "lang.ark.fed.waitCodeType": "等待指令",
  "lang.ark.workflow.currentOperateIsHappening": "请勿重复操作！",
  "lang.ark.warehouse.workstationPointIsMuchForWave": "波次业务模式下工作站不可以存在多个点位",
  "lang.ark.fed.renderingFlowChart": "正在渲染流程图",
  "lang.ark.fed.collect": "集合",
  "lang.ark.trafficControl.enterPattern": "逐个通过/连续通过",
  "lang.ark.fed.currentStopPointStatus": "当前节点状态",
  "lang.ark.fed.screen.workflowInfo.robotTaskId": "机器人任务Id",
  "lang.ark.fed.moreOperations": "更多操作",
  "lang.ark.fed.sureWantExecute": "确定要执行吗？",
  "lang.ark.fed.wfTaskNum": "领料单号",
  "lang.ark.fed.taskManagementMsg0": "任务监控: 任务监控页面是否显示以下按钮, 删除按钮初始化是勾选状态, 撤销和退回按钮初始化是非勾选状态",
  "lang.ark.area.areaAlreadyLock": "区域已锁定！",
  "lang.ark.interface.interfaceDesc.targetName": "上游字段别称",
  "lang.ark.action.interface.locationFrom": "locationFrom",
  "lang.ark.fed.arriveOrientation": "货架朝向",
  "lang.ark.fed.fieldInform": "场地信息",
  "lang.ark.fed.eraseNoise": "擦除噪点",
  "lang.ark.fed.recoveryToTargetSucceedStatus": "恢复到目标成功状态",
  "lang.ark.action.interface.extraParam1": "extraParam1",
  "lang.ark.fed.orderPass": "先到先过",
  "lang.ark.action.interface.extraParam2": "extraParam2",
  "lang.ark.action.interface.extraParam3": "extraParam3",
  "lang.ark.action.interface.extraParam4": "extraParam4",
  "lang.ark.action.interface.extraParam5": "extraParam5",
  "lang.ark.fed.robotWillArrive": "机器人即将到达，剩余距离：{0}",
  "lang.ark.fed.demandForMaterials": "需求叫料",
  "lang.ark.workflowTriggerStatus.enable": "启用",
  "lang.ark.auth.userHaveLoginCurrentPlace": "当前用户已经在此登录!",
  "lang.ark.fed.workflowEncoding": "流程编码",
  "lang.ark.apiRobotTaskCode.waitPointExecutingTaskNotOnlyOne": "处于等待点执行中的任务不唯一",
  "lang.ark.workflow.criteria": "条件",
  "lang.authManage.web.common.makeSure": "确定",
  "lang.ark.trafficControl.enterPattern.singlePass": "逐个通过",
  "lang.ark.fed.menu.taskExeRecord": "任务执行记录",
  "lang.ark.workflow.allType": "全部",
  "lang.ark.fed.orderWaitSend": "待配送",
  "lang.ark.fed.timingCharging": "定时充电",
  "lang.ark.fed.uploadFileLimit3M": "只能上传mp3、wma、wav、amr格式，单个文件大小不可超过",
  "lang.ark.workflow.chooseStrategy.cancel": "取消操作",
  "lang.ark.fed.technicalSupport": "技术支持",
  "lang.authManage.web.common.differentPassword": "两次输入密码不一致！",
  "lang.ark.trafficControl.enterType.singleFactoryMultiEnter": "单厂家多入口",
  "lang.ark.fed.loopSetup": "循环设置",
  "lang.auth.Audit.item0001": "用户：{0}登录了系统",
  "lang.auth.Audit.item0003": "新增用户：{0}",
  "lang.ark.fed.isSureStartFlow": "请确定是否发起该流程？",
  "lang.auth.Audit.item0002": "用户：{0}退出了系统",
  "lang.auth.Audit.item0005": "启用用户：{0}",
  "lang.ark.action.interface.extraParam6": "extraParam6",
  "lang.auth.Audit.item0004": "禁用用户：{0}",
  "lang.ark.action.interface.extraParam7": "extraParam7",
  "lang.ark.fed.pickingMethod": "领料方式",
  "lang.auth.Audit.item0007": "修改用户：{0}",
  "lang.ark.action.interface.extraParam8": "extraParam8",
  "lang.auth.Audit.item0006": "删除用户：{0}",
  "lang.ark.action.interface.extraParam9": "extraParam9",
  "lang.auth.Audit.item0009": "批量修改角色：{0}，关联用户权限",
  "lang.auth.Audit.item0008": "修改用户密码：{0}",
  "lang.ark.fed.createType": "生成方式",
  "lang.ark.workflowConfig.cellFunctions.stationCell": "STATION_CELL",
  "lang.ark.fed.componentInterface": "组件指令",
  "lang.ark.noContainerAvailableAtCurrentPoint": "当前点无可用容器！",
  "lang.ark.fed.pleaseSelect": "请选择",
  "lang.ark.fed.outerData": "外部数据",
  "lang.ark.fed.afterArrivingHere": "到达此处",
  "lang.ark.fed.shelfAttribute.SSR": "SSR",
  "lang.mwms.fed.innerFreezes": "库内冻结",
  "lang.ark.warehouse.containerWorkingEdit": "存在任务或可用库存，不可编辑货位",
  "lang.ark.apiCommonCode.notMasterServer!": "当前请求的服务端不是主服务！",
  "lang.ark.fed.currentRackInformation": "当前货架信息",
  "lang.ark.fed.changePointPosition": "切换点位",
  "lang.ark.fed.conButtonLogExecuteTime": "执行时间",
  "lang.ark.externalDevice.device_own_type_desc": "选择为机器人组件设备时，需要在机器人组件设备管理页面维护机器人组件设备！",
  "lang.ark.fed.binUsed": "在途(容器工作中)",
  "lang.ark.warehouse.theMatrialPointExistsAny": "存在多个产线配送波次，请取消多余波次",
  "lang.ark.fed.currentStatus": "当前状态",
  "lang.ark.fed.workflowNode": "流程节点",
  "lang.ark.fed.screen.workflowInfo.responseParamDetail": "回调报文详情",
  "lang.ark.fed.outer": "外部接口",
  "lang.ark.fed.waitPoint": "等待点",
  "lang.ark.apiCommonCode.robotRecoverFailed": "机器人恢复失败",
  "lang.ark.fed.menu.exceptionHandling": "机器人异常处理",
  "lang.ark.workflow.arriveOrientation": "货架需求朝向",
  "lang.ark.fed.movingShelvesToWorkstations": "移动货架到工作站",
  "lang.ark.fed.teakDetail": "任务明细",
  "lang.ark.fed.shelfAttribute.PNAE": "PNAE",
  "lang.ark.fed.maximumSpeed": "最大速度",
  "lang.ark.fed.english": "英文",
  "lang.ark.fed.unconnectedNodeExist": "操作失败，存在未连通节点！",
  "lang.ark.fed.pickUpTheTask": "领取任务",
  "lang.ark.fed.parameterGrouping": "参数分组",
  "lang.gles.baseData.baseGoodsPosition": "货位",
  "lang.mwms.fed.wcs": "控制",
  "lang.ark.fed.targetPoint": "目标点",
  "lang.ark.fed.areaName": "区域名称",
  "lang.ark.workflow.task.status.node.undoing": "撤销中",
  "lang.ark.fed.taskStage": "任务阶段",
  "lang.ark.fed.default": "默认",
  "lang.ark.workflow.task.status.moving": "机器人已上路",
  "lang.ark.workflow.manulChoice": "手动选择分支",
  "lang.ark.fed.automatic": "自动",
  "lang.ark.deliverOrder.positiveSequence": "产线工序正序",
  "lang.ark.apiRobotTaskCode.robotTaskIdNotEmpty": "机器人任务id不能为空",
  "lang.ark.fed.screen.systemConfig.commonGroup": "系统配置",
  "lang.ark.fed.openAll": "全部开启",
  "lang.ark.fed.condition": "条件",
  "lang.ark.fed.pullDownTheCargoPosition": "正常/锁定/任务占用",
  "lang.ark.fed.speed": "速度",
  "lang.ark.fed.oneway": "单向",
  "lang.ark.fed.passingPoint": "途经点",
  "lang.ark.workflow.autoReleaseFactoryNullError": "机器人自动释放厂家为空，请填入厂家",
  "lang.ark.fed.networkTimeout": "网络超时",
  "lang.ark.fed.backgroundMapEditing": "背景图编辑",
  "lang.ark.fed.arrivelExternalInteraction": "到达后外部交互",
  "lang.ark.fed.isStopAllQueue": "是否全部停止排队",
  "lang.ark.robot.classfy.forklift": "叉车",
  "lang.ark.workflow.queue.noAvailableShelf": "无可用货架点",
  "lang.ark.warehouse.shelfDifferent": "点位当前货架与领单时货架不一致",
  "lang.ark.fed.theWorkstationnodeidDidNotSelectAStopPoint": "工作站{nodeId}未选择停靠点;",
  "lang.ark.button.operation.command.removeContainer": "容器离场",
  "lang.ark.fed.screen.flowNodeConfig.checkMsg.rule": "保存失败，请选择指令规则",
  "lang.ark.fed.takeAway": "撤",
  "lang.ark.interface.apiAddContainer": "容器入场",
  "lang.ark.interface.apiPriorityAdjust": "流程实例优先级调整",
  "lang.ark.fed.pleaseSelectTheAlarmLightPortNumber": "请选择报警灯端口号",
  "lang.ark.apiStationCode.stationNotExists": "工作站不存在",
  "lang.ark.fed.noPointToDeleteWasSelected": "未选择需要删除的点",
  "lang.mwms.monitorRobotMsg.config": "流程配置异常，请检查流程配置",
  "lang.ark.fed.offsetInstructionMustSpecify": "偏移指令必须指定组件",
  "lang.ark.warehouse.waveStatusCannotbyCancel": "不可执行取消的波次状态",
  "lang.ark.interface.businessCode": "应答代码",
  "lang.ark.fed.excel.cellNotExist": "{0}行点位在系统中不存在，请检查修改后再上传",
  "lang.ark.fed.containerBinFree": "正常",
  "lang.ark.fed.hoursLater": "小时后",
  "lang.ark.fed.taskRunDetails": "任务执行详情",
  "lang.ark.fed.sendMaterial": "送料目的点",
  "lang.ark.warehouse.taskHasExecute": "任务已执行,请不要重复执行",
  "lang.ark.fed.overAlarmType": "预警类型",
  "lang.gles.StockInStore": "在库库存",
  "lang.ark.fed.switchToSuccess": "切换成{val}成功",
  "lang.authManage.web.common.disabled": "禁用",
  "lang.ark.workflowTriggerMonitorStatus.executing": "执行中",
  "lang.ark.action.interface.boolean": "bool",
  "lang.ark.fed.flowCategory": "流程分类",
  "lang.ark.warehouse.deliveryStationLine": "配送工位",
  "lang.ark.action.interface.taskCode": "taskCode",
  "lang.mwms.monitorRobotMsg21100": "组件收货异常",
  "lang.ark.fed.configCustomParams": "配置自定义参数",
  "lang.ark.workflow.targetNode": "目标点",
  "lang.ark.fed.back": "后",
  "lang.ark.workflow.task.status.completed": "完成",
  "lang.ark.fed.load": "负载",
  "lang.ark.workflow.condition.lessThan": "小于",
  "lang.ark.button.operation.command.start": "开始",
  "lang.ark.fed.multiplefactoryMultipleEntrances": "多厂家单入口",
  "lang.ark.fed.pleaseSelectTheParentNode": "请选择父节点！",
  "lang.ark.fed.noInstructUnderTheAction": "该交互动作下没有指令",
  "lang.ark.fed.interfaceInteraction": "接口交互",
  "lang.gles.materialClassify": "物料分类",
  "lang.ark.workflow.task.status.wait": "等待",
  "lang.ark.workflow.paramValueCode.extraParam8": "extraParam8",
  "lang.mwms.monitorRobotMsg21111": "定位丢失",
  "lang.ark.workflow.paramValueCode.extraParam7": "extraParam7",
  "lang.mwms.monitorRobotMsg21110": "秤重超载或偏载超限",
  "lang.auth.UserAPI.item1271": "启用",
  "lang.ark.fed.threeLight": "三色灯",
  "lang.ark.fed.locationOfRobotsWaitingToReceiveNewTasks": "机器人在等待接收新任务时可停靠的位置",
  "lang.ark.workflow.paramValueCode.extraParam9": "extraParam9",
  "lang.auth.UserAPI.item1270": "禁用",
  "lang.ark.workflow.paramValueCode.extraParam4": "extraParam4",
  "lang.ark.workflow.paramValueCode.extraParam3": "extraParam3",
  "lang.ark.workflow.paramValueCode.extraParam6": "extraParam6",
  "lang.ark.workflow.paramValueCode.extraParam5": "extraParam5",
  "lang.ark.workflow.paramValueCode.extraParam2": "extraParam2",
  "lang.ark.workflow.paramValueCode.extraParam1": "extraParam1",
  "lang.mwms.monitorRobotMsg21109": "电量耗尽",
  "lang.ark.workflow.wareHouseSupportBusiness": "业务",
  "lang.ark.task.log.export.title.endNode.name": "终点名称",
  "lang.mwms.monitorRobotMsg21104": "鱼眼相机数据丢失",
  "lang.mwms.monitorRobotMsg21103": "深度相机数据丢失",
  "lang.ark.fed.locking": "锁定",
  "lang.mwms.monitorRobotMsg21102": "组件自身故障",
  "lang.ark.auth.userHaveLoginOtherStation": "当前用户 {0} 已经在{1}工作站登录，请确保退出后，再次登录使用!",
  "lang.mwms.monitorRobotMsg21101": "组件通信中断",
  "lang.ark.workflow.notFirstStation": "工作站为主动配送模式时,非首节点工作站不能发起流程",
  "lang.ark.fed.makeADetour": "绕行",
  "lang.mwms.monitorRobotMsg21108": "按下解抱闸",
  "lang.ark.fed.logicAnd": "+与运算符(&)",
  "lang.mwms.monitorRobotMsg21107": "触发STO",
  "lang.mwms.monitorRobotMsg21106": "驱动器数据丢失，或器件故障",
  "lang.ark.fed.receiveGoodsNumLessThanRemain": "货位{0}:最多可入库{1}个",
  "lang.mwms.monitorRobotMsg21105": "网络中断",
  "lang.ark.workflow.workflowtypeNameOrCodeRepeat": "流程分类名称或者编码重复",
  "lang.ark.fed.mesInterfaceError": "MES接口返回异常，异常信息:{0}",
  "lang.mwms.fed.viewSet": "页面配置",
  "lang.mwms.fed.inWarehouse": "入库日报表",
  "lang.ark.hitStrategy.stack": "后进先出",
  "lang.ark.fed.detail": "详情",
  "lang.ark.fed.closeAllTabs": "关闭所有",
  "lang.ark.workflow.action.command.robot.FBShift": "前后偏移",
  "lang.ark.fed.morePicking": "多车领料",
  "lang.ark.fed.column": "列",
  "lang.ark.fed.orderSourceNum": "原单号",
  "lang.ark.fed.noRobotsHaveBeenAssignedYetPleaseClickToAdd": "暂未分配机器人，请点击添加！",
  "lang.ark.fed.ruleConfig": "规则配置",
  "geekplus.moving.uic.elTableWrapperVue2.actionBtn.confirmPopup.btnCancel": "取消",
  "lang.ark.fed.prompt": "提示",
  "lang.mwms.fed.supplier": "供应商管理",
  "lang.ark.warehouse.estimateUseTimeUnit.minute": "分钟",
  "lang.ark.fed.exportFile": "正在下载",
  "lang.ark.fed.selectRobot": "选择机器人",
  "lang.ark.fed.workflowId": "流程编号：",
  "lang.ark.warehouse.wave": "波次",
  "lang.ark.record.task.over": "任务结束",
  "lang.ark.stationCodeExist": "工作站编码不能重复",
  "lang.ark.fed.arriveEndCellCodeTime": "到达节点时间",
  "lang.ark.workflow.template.validate.equipmentNodeNotFoundData": "设备点位没有找到对应数据，请检查设备数据",
  "lang.ark.fed.occupied": "设备是否占用！",
  "lang.ark.fed.releaseFull": "放行",
  "lang.ark.fed.numberOfButtons": "按钮个数",
  "lang.ark.fed.screen.hybridRobot.pointInfo": "点位信息",
  "lang.ark.workflow.areaNotHaveIdlePoint": "区域[{}]没有空闲点位",
  "lang.ark.base.license.exceptionForLicenseValidating": "证书验证异常!",
  "lang.ark.fed.cancelWait": "取消等待",
  "lang.ark.fed.selectAll": "全选",
  "lang.ark.fed.missionEndpoint": "任务终点",
  "lang.ark.trafficControl.stopFunction": "急停区功能",
  "lang.ark.fed.pleaseSaveEditTable": "请检查当前列表是否有为空或未保存的数据",
  "lang.ark.fed.menu.heartbeat": "心跳管理",
  "lang.ark.fed.facilityType": "设施类型:",
  "lang.ark.fed.screen.area.containGroup": "包含分组",
  "lang.mwms.fed.operate": "地图操作",
  "lang.ark.fed.dashboardSetting": "看板设置",
  "lang.ark.taskRecord.param.notblank": "流程实例或任务号不能为空",
  "lang.ark.operation.workflow.pauseWorkflow": "暂停任务",
  "lang.ark.workflow.paramValueCode.export": "export",
  "lang.ark.fed.cancellationProcess": "取消流程",
  "lang.ark.fed.conButtonLogSocketPort": "端口号",
  "lang.ark.pda.function.transport.task": "搬运任务",
  "lang.mwms.fed.package": "包装",
  "lang.ark.apiCommonCode.systemStopFailed": "系统急停失败",
  "lang.ark.interface.interfaceDesc.phaseTypeDesc": "说明：字段值为枚举值时，返回给上游的参数值格式。如taskPhase字段，有多个枚举值状态，上游可能需要返回10、20、30对应不同的状态，也可能需要返回英文对应不同状态CREATE、EXECUTION、COMPLETED。此设置对本接口所有枚举值字段生效。",
  "lang.ark.trafficControl.queueFunction": "排队区功能",
  "lang.ark.fed.appointMentLock": "预约锁定",
  "lang.ark.fed.executeDetail": "执行详细",
  "lang.ark.fed.twoway": "双向",
  "lang.ark.fed.stageOfCompletion": "完成情况",
  "lang.ark.fed.ForkliftTray": "叉车托盘",
  "lang.ark.fed.jackUpLayDown": "顶升/放下",
  "lang.ark.fed.screen.flowTemplate.specialNode": "特殊点",
  "lang.ark.workflow.notSupportAgvClass": "不支持的机器人类型",
  "lang.ark.interface.interfaceDesc.detail": "接口详情",
  "lang.ark.interface.callbackAddress": "回调地址",
  "lang.ark.fed.flowCreate": "流程创建",
  "lang.ark.fed.editMap": "地图编辑",
  "lang.ark.fed.addProcess": "添加流程",
  "lang.mwms.fed.userManage": "用户管理",
  "lang.ark.fed.zoom": "缩放",
  "lang.ark.workflow.template.validate.templateCannotSelectBranchOrderNode": "动态模板根据连线方式无法选择分支节点",
  "lang.ark.fed.className": "类别名称",
  "lang.ark.executedNum": "已执行次数:",
  "lang.ark.waveTaskStatus.cancel": "取消",
  "lang.gles.receipt.tallyList.tallyList": "理货单",
  "lang.ark.fed.loadGoods": "上料",
  "lang.ark.warehouse.shelfNotFree": "当前点位货架不空闲",
  "lang.ark.fed.triggerOtherSettings": "触发其他设置",
  "lang.ark.fed.robotLessQueue": "机器人不足时任务排队",
  "lang.ark.warehouse.hasSameProductionLineCode": "存在相同产线编码",
  "lang.gles.logisticsConfig.workTemplate": "作业模板",
  "lang.ark.fed.goodsSend": "配送中",
  "lang.ark.fed.userAndRole": "用户/角色",
  "lang.authManage.web.others.addNewIp": "新增IP和PORT",
  "lang.authManage.fed.effectiveDate": "开始日期",
  "lang.ark.fed.robotManagement": "机器人管理",
  "lang.ark.fed.clearLock": "解锁",
  "lang.ark.fed.typeMustBeDockPointWorkstationRackPointArea": "类型必须是：停靠点，工作站，货架点， 区域",
  "lang.ark.task.log.export.title.start.time": "开始时间",
  "lang.ark.fed.taskLog": "任务日志",
  "lang.ark.fed.menu.hybridRobot": "复合机器人配置",
  "lang.ark.workflow.task.status.ready": "预备",
  "lang.ark.fed.east": "东",
  "lang.mwms.monitorRobotMsg91001": "急停按钮断开连接",
  "lang.ark.fed.confirmCharging": "是否确认充电？",
  "lang.ark.fed.homeViewUpload": "首页上传",
  "lang.mwms.monitorRobotMsg91000": "急停按钮触发",
  "lang.ark.interface.interfaceType": "接口类型",
  "lang.ark.fed.loadingPoint": "上料点",
  "lang.ark.fed.screen.flowNodeConfig.executeByCondition": "按条件判断后执行",
  "lang.ark.fed.serialLessMount": "物料数量不可小于0",
  "lang.ark.fed.orderPassPriority": "先到先过(优先级优先)",
  "lang.ark.fed.nomsgIsAvailableDoYouWantToContinueGeneratingTasks": "无可用{msg}，是否继续生成任务？",
  "lang.ark.fed.callStation": "叫料工作站",
  "lang.ark.fed.purpose": "用途",
  "lang.ark.fed.returnDistance": "返程",
  "lang.ark.fed.everyMonth": "每月{0}",
  "lang.ark.interface.requestSource": "请求主体",
  "lang.ark.fed.taskBoardWaringSetting": "看板警示设置",
  "lang.mwms.fed.skuOutInConfig": "物料出入库配置",
  "lang.ark.fed.pleaseEnter": "请输入",
  "lang.mwms.fed.printSet": "打印设置",
  "lang.ark.workflow.recycleTypeNoSupportManual": "接口取消不支持流程配置手动选择送回位置",
  "lang.ark.fed.revokeButton": "撤销按钮",
  "lang.ark.action.interface.paramName": "参数名称",
  "lang.ark.fed.generationTime": "生成时间",
  "lang.ark.fed.triggerTiming": "触发时机",
  "lang.ark.action.interface.assignType": "赋值方式",
  "lang.ark.logType.waitPointTaskLog": "等待点任务日志",
  "lang.ark.hand.push": "手动推至",
  "lang.ark.singleCellStation.canNotDelete": "删除失败，请从地图编辑中删除后同步数据！",
  "lang.ark.fed.pleaseSelectRobot": "请选择机器人",
  "lang.ark.fed.selected.cellCode": "选择点位、区域、分组、容器编码",
  "lang.ark.loadCarrier.loadCarrierParamsErr": "容器参数错误！",
  "lang.mwms.rf.outbound": "出库",
  "lang.ark.fed.batteryCurrent": "电池电流",
  "lang.ark.workflowConfig.nodeActionDoNotExists": "有未关联交互的节点",
  "lang.ark.fed.includePoints": "包含点位",
  "lang.ark.fed.isEnabledTrigger": "是否启用此触发器？",
  "lang.ark.fed.narrow": "缩小",
  "lang.ark.fed.allowLiftUp": "{0}只允许配置一条顶升",
  "lang.ark.fed.abnormalComplete": "异常完成",
  "lang.ark.workflow.wareHouseWorkflowType": "流程类型",
  "lang.ark.fed.orderReAssign": "重新分配",
  "lang.ark.fed.thereIsNoWorkflowAtThisDockPointPosition": "此点位无流程",
  "lang.ark.mechanical.arm.place": "机械臂放置",
  "lang.authManage.fed.screen.login.pwdExpireTipTitle": "密码过期提醒",
  "lang.ark.fed.abnormalCancel": "异常取消",
  "lang.ark.fed.oneClickExecution": "一键执行",
  "lang.ark.fed.robotCode": "机器人编码",
  "lang.ark.workflowCode.exists": "流程编码不能重复",
  "lang.mwms.fed.stationControl": "工作站监控",
  "lang.ark.station.rollerStation": "辊筒工位",
  "lang.ark.fed.extraParam18": "extraParam18",
  "lang.ark.fed.extraParam17": "extraParam17",
  "lang.ark.fed.extraParam19": "extraParam19",
  "lang.ark.hitStrategy.default": "默认策略",
  "lang.ark.fed.extraParam20": "extraParam20",
  "lang.ark.workflow.invalidWorkflowConfig": "无效工作流配置",
  "lang.ark.fed.deleteWaveRow": "确定删除策略吗?",
  "lang.ark.fed.notAllow": "未选择",
  "lang.ark.taskStatusCannotOperate": "任务处于{0}状态,不可操作!",
  "lang.ark.fed.unloadOrderDesc": "整单下料：工作站下料默认需要物料全部下架，不需要严格要求物料和货位情况操作下架。",
  "lang.ark.plugin.pluginType.returnContainer.way.full": "送满容器",
  "lang.ark.fed.extraParam10": "extraParam10",
  "lang.ark.fed.menu.systemConfiguration": "系统参数配置",
  "lang.ark.fed.extraParam12": "extraParam12",
  "lang.ark.fed.extraParam11": "extraParam11",
  "lang.ark.fed.extraParam14": "extraParam14",
  "lang.ark.record.createTask": "创建任务",
  "lang.ark.fed.extraParam13": "extraParam13",
  "lang.ark.fed.taskNumber": "任务编号",
  "lang.ark.fed.extraParam16": "extraParam16",
  "lang.ark.fed.extraParam15": "extraParam15",
  "lang.authManage.fed.effectiveDays": "有效期天数",
  "lang.ark.fed.screen.area.tipForEmptyGroup": "未添加分组，无法保存",
  "lang.gles.receipt.warehousingExternalOrder": "外部入库单",
  "lang.ark.fed.deleteFlow": "删除流程",
  "lang.ark.fed.numberOfCategories": "品类数",
  "lang.ark.fed.screen.LoginLog.realName": "姓名",
  "lang.ark.fed.mediaPlay": "语音播放",
  "lang.ark.fed.nearTheWorkstationRobotsCanBeParkedAndWaitedForAPosition": "工作站附近可以让机器人停靠并等待人员操作的位置",
  "lang.ark.apiContainerCode.containerCategoryNotExists": "容器类型不存在",
  "lang.authManage.fed.screen.auth.cardNo": "工卡号",
  "lang.ark.areaCode.not.exist": "区域编码全部或者部分不存在",
  "lang.ark.fed.processProcessNew": "流程",
  "lang.ark.workflow.action.command.robot.goTurnOfSide": "按面旋转",
  "lang.ark.task.log.export.title.task.number": "任务编号",
  "lang.ark.fed.taskNumberNew": "任务单号",
  "lang.ark.shelfType.referenceByShelf": "该容器类型已关联容器",
  "lang.ark.fed.updateLocation": "更新位置",
  "lang.gles.stockInStore.factoryPositionStock": "库位库存",
  "lang.ark.fed.executiveInstruction": "执行指令",
  "lang.ark.fed.baseInfo": "基础信息",
  "lang.ark.fed.endPointName": "终点名称",
  "lang.ark.interface.requestId": "消息编号",
  "lang.ark.fed.newTask": "新建任务",
  "lang.ark.fed.areYouSureToDeleteThisNodeWorkflow": "是否删除此节点{nodeId}和它的所有子节点？",
  "lang.ark.fed.sending": "配送中",
  "lang.authManage.web.common.roleDelSuc": "角色删除成功！",
  "lang.ark.fed.finish": "完成",
  "lang.ark.apiContainerCong.modifyAngleFail": "修改容器角度失败",
  "lang.ark.fed.pinking": "上料中",
  "lang.ark.workflow.noAvailableAreaOrShelfPoint": "未找到接收区域或货架点",
  "lang.ark.robot.unbind.device": "机器人未绑定上装设备",
  "lang.ark.fed.menu.containerEditor": "容器管理",
  "lang.gles.planTask": "计划任务",
  "lang.ark.containerNotExists": "容器不存在!",
  "lang.ark.warehouse.TriggerCellCodeCanNotFindUnWaveOrder": "无可用单据，组波失败",
  "lang.ark.loadCarrier.loadCarrierModelIdIsEmpty": "容器模型不能为空！",
  "lang.ark.workflow.lastTaskArrivedSimple": "上一步",
  "lang.ark.container.inUsing": "容器{}使用中,请空闲后再试",
  "lang.ark.dynamicTemplate.previousNode": "上一个点",
  "lang.ark.workflow.wareHouseWorkflowTemplate": "流程模板",
  "lang.ark.fed.priorityStickTop": "优先级置顶",
  "lang.ark.fed.nodeDeviceInfo": "点位设备信息",
  "lang.ark.common.importExcelFile": "模板导入",
  "lang.mwms.fed.accountManage": "账户管理",
  "lang.ark.fed.containerAmount": "容器数量",
  "lang.ark.workflow.workflowInstanceNotExists": "工作流实例不存在",
  "lang.ark.workflow.action.command.paramSourceType.clientAssign": "上游下发",
  "lang.ark.workflow.action.command.paramSourceType.outside": "从外部获取",
  "lang.ark.fed.stopPointIdExistent": "当前点位已存在，请切换点位",
  "lang.ark.fed.specialAreaName": "特殊区名称",
  "lang.ark.fed.materialNum": "物料数量",
  "lang.ark.fed.removeRack": "移除货架",
  "lang.ark.fed.menu.templateManage": "外部任务导入",
  "lang.ark.fed.noWorkflowNodePleaseReedit": "，无流程节点，请重新编辑",
  "lang.ark.fed.preStep": "上一步",
  "lang.ark.fed.height": "高",
  "lang.ark.existsInUsedContainer": "存在已经入场的容器",
  "lang.ark.warehouse.buttonExistsButNoMatch": "该按钮已配置其他操作指令，点位或容器号需保持一致",
  "lang.ark.fed.menu.areaEditController": "区域配置",
  "lang.ark.fed.batchImport": "批量导入",
  "lang.ark.canNotSubmitRepeat": "请勿重复点击",
  "lang.ark.fed.parentWorkflowContainThis": "流程{0}包含此流程，已将其变为异常状态！",
  "lang.ark.fed.workstationName": "工作站名称",
  "lang.ark.fed.cellCodeLock": "点位锁",
  "lang.ark.workflow.manulChoiceSimple": "手动选择",
  "lang.ark.fed.interfaceAccessType": "接口地址获取方式",
  "lang.ark.shelf.addShelfFailed": "添加容器失败",
  "lang.ark.workflow.mutiBeginOrEnd": "存在多个开始节点或结束节点",
  "lang.ark.fed.pleaseSelectOrder": "请选择单据",
  "lang.ark.fed.inTheTask": "任务中",
  "lang.ark.fed.endOfProcess": "流程结束",
  "lang.ark.fed.drawingAMap": "绘制地图",
  "lang.ark.waveTriggerCondition.all": "全部",
  "lang.mwms.fed.replenishmentWorkCreate": "补货工作生成",
  "lang.mwms.fed.pickWork": "拣货工作",
  "lang.ark.interface.apiAreaList": "区域查询",
  "lang.gles.systemManage.systemManage": "系统管理",
  "lang.ark.fed.loading": "系统加载中...",
  "lang.gles.baseData.workshop": "车间",
  "lang.ark.fed.updateTimeInterval": "更新时间间隔",
  "lang.ark.button.code.already.exist": "此控制器按钮已存在，不能重复添加",
  "lang.ark.workflow.area.occupiedTime": "连续占用时间上限",
  "lang.ark.fed.errorBins": "货位错误，请确认条码",
  "lang.ark.fed.save": "保存",
  "lang.ark.workflow.action.command.paramSourceType.manualConfiguration": "手动配置",
  "lang.ark.fed.menu.robotManagement": "机器人",
  "lang.ark.fed.facility": "设施:",
  "lang.ark.trafficControl.enterType.multiFactorySingleEnter": "多厂家单入口",
  "lang.mwms.fed.strategyOrderAllocate": "订单命中策略",
  "lang.ark.fed.nodeName": "节点名称",
  "lang.ark.fed.openMoreGoodsInfo": "查看更多物料信息",
  "lang.ark.fed.codeValue": "码值",
  "lang.ark.api.template.startNodeNotExist": "模板起点不存在",
  "lang.ark.fed.typeName": "类型名称",
  "lang.ark.fed.processMode": "流程模式",
  "lang.ark.fed.common.placeholder.input": "请输入",
  "lang.ark.apiRobotTaskCode.robotTaskNotExists": "机器人任务不存在",
  "lang.ark.fed.trigger": "触发",
  "lang.ark.fed.menu.editMap": "地图配置",
  "lang.authManage.web.common.loading": "加载中...",
  "lang.ark.apiStationCode.stationStopPointIsEmpty": "工作站内停靠点信息为空",
  "lang.ark.fed.ruleConfiguration": "规则配置",
  "lang.ark.fed.synchronizeAllRacks": "同步所有货架",
  "lang.ark.fed.singleCellStation": "单点工位",
  "lang.ark.apiContainerCode.containerCategoryNotMatch": "containerCategory:{0}与系统容器类型不匹配",
  "lang.ark.sameQueuePriorityNeedSameAndallowQueueRobotNumNeedSame": "相同的排队点需要相同的排队数量和排队优先级",
  "lang.mwms.fed.wave": "波次管理",
  "lang.ark.fed.triggerLocking": "外部触发锁定",
  "lang.ark.fed.robotDashboard": "机器人看板",
  "lang.ark.fed.west": "西",
  "lang.ark.workflow.shelfExistsAndOccupied": "当前点存在使用中的货架",
  "geekplus.moving.uic.elTableWrapperVue2.actionBtn.confirmPopup.btnConfirm": "确认",
  "lang.ark.fed.lingdan": "领单超时",
  "lang.ark.fed.dataStatus": "数据状态",
  "lang.ark.archiveType.interfaceRecord": "接口日志",
  "lang.ark.fed.havingSeparateBusinessAttributesAPhysicalOrLogical": "具有独立的业务属性，从物理上或逻辑上划分出的一块位置",
  "lang.ark.fed.robotStat": "机器人状态",
  "lang.ark.fed.typeMustBeDockPointWorkstationRackPointArea1": "类型必须是：点位，工作站，货架点， 区域 ",
  "lang.ark.fed.shelfFlow": "负载模式",
  "lang.ark.fed.areYouSureYouWantToRedrawTheMap": "确认要重新绘制地图？",
  "lang.ark.warehouse.goods": "送料",
  "lang.ark.interface.interfaceDesc.phaseType": "枚举值字段回调格式",
  "lang.ark.fed.productModel": "产品型号",
  "lang.gles.receipt.tallyList.externalTallyList": "外部理货单",
  "lang.mwms.fed.monitorAll": "整仓监控",
  "lang.ark.fed.manualConfig": "手动配置",
  "lang.ark.fed.redistributionSure": "新增一条相同单据记录重新配送，确定操作？",
  "lang.ark.workflowAction.default": "默认",
  "lang.ark.workflow.workflowNodeConfigNotExists": "工作流节点配置不存在",
  "lang.ark.lang.ark.controlNodeType.point": "点位",
  "lang.ark.workflow.subflowCancellationNotAllowed": "子流程不允许取消",
  "lang.mwms.fed.outManage": "出库管理",
  "lang.ark.workflowTrigger.logType.operation": "操作日志",
  "lang.ark.fed.recoveryToInitialStatus": "恢复到初始状态",
  "lang.ark.fed.callStationTaskDetails": "叫料任务单详情",
  "lang.ark.fed.containerEntry": "入场",
  "lang.ark.warehouse.stationLineDeliveryPriority": "配送优先级",
  "lang.ark.fed.pleaseSelectFlow": "请点击选择流程！",
  "lang.ark.workflow.manul": "手动触发自动判断分支",
  "lang.ark.fed.estimatedTime": "后到达；本趟次预计耗时",
  "lang.ark.fed.childFlow": "子流程",
  "lang.ark.fed.parameterType": "参数类型",
  "lang.ark.fed.addRackPoints": "添加货架点",
  "lang.ark.fed.controlArea": "管制区域",
  "lang.mwms.fed.inventoryManage": "库存管理",
  "lang.ark.rollerStation.canNotEdit": "辊筒工位不允许编辑",
  "lang.ark.workflow.extendRobotTrue": "是",
  "lang.ark.workflow.workflowDropNotSupport": "WORKFLOW_DROP指令不支持辊筒、复合,请使用WORKFLOW_CANCEL",
  "lang.ark.fed.setMiddlePoint": "设为途经点",
  "lang.ark.fed.lockingSure": "确定锁定货位",
  "lang.ark.fed.screen.flowNodeConfig.judgingByTask": "按任务判断",
  "lang.authManage.fed.preAlertDays": "预警天数",
  "lang.ark.workflowConfig.cellFunctions.rest": "停靠功能",
  "lang.ark.workflow.workflowNotExists": "取消失败，流程不存在",
  "lang.ark.curNodeExitsShelf": "当前点存在货架，不能操作无货架模式",
  "lang.ark.action.interface.retry": "重试",
  "lang.ark.loadCarrier.loadCarrierCodeGenerateErr": "容器编码生成错误！",
  "lang.mwms.fed.exception": "异常处理",
  "lang.ark.fed.signalDisplay": "信号展示",
  "lang.ark.fed.set": "设置",
  "lang.ark.warehouse.materialPreparePointType": "上料点节点类型",
  "lang.ark.fed.pleaseChooseRack": "请选择货架",
  "lang.ark.fed.start": "开始",
  "lang.ark.shelfTypeRefWorkflowNodeAction": "删除失败，已被交互配置使用！交互配置名称:{0}",
  "lang.mwms.fed.allocationRule": "拣货规则",
  "lang.ark.fed.businessModel": "业务模式",
  "lang.ark.fed.operationInstruction": "操作指令",
  "lang.ark.fed.wirelessCallModule": "无线呼叫模块",
  "lang.ark.base.license.nolicense": "请导入证书!",
  "lang.gles.receipt.upAndDownMaterialOrder": "上下料单",
  "lang.gles.baseData.baseContainerArchives": "容器档案",
  "lang.ark.fed.forkLiftSetCommponent": "叉车的指令列表中必须配置一条组件动作",
  "lang.ark.action.interface.retrySize": "重试次数",
  "lang.ark.fed.setTriggerTime": "设置触发时间",
  "lang.ark.workflow.recycleTypeNoConfigAction": "取消时货物送回位置未配置交互动作",
  "lang.ark.fed.sendMaterialRepertory": "该上料点可配送哪些物料",
  "lang.ark.fed.addPointPosition": "添加点位",
  "lang.ark.plugin.pluginType.fetchContainer.way.empty": "取空容器",
  "lang.ark.fed.empty": "空",
  "lang.ark.fed.bussinessModel": "业务模式",
  "lang.ark.workflow.area.ContinuousPassage": "逐个通过/连续通过",
  "lang.ark.workflow.area.end": "队尾排队",
  "lang.ark.action.interface.responseParamName": "返参名称",
  "lang.ark.fed.screen.area.groupCode": "分组编码",
  "lang.ark.fed.brushSize": "笔触大小",
  "lang.authManage.web.common.surePassword": "确认密码",
  "lang.ark.fed.startDeliveryTime": "开始配送时间",
  "lang.ark.fed.operationFlow": "操作流",
  "lang.ark.fed.workFlow": "工作流程",
  "lang.ark.fed.workflowTriggerMonitor": "触发器执行日志",
  "lang.ark.fed.leaved": "已离开",
  "lang.ark.fed.pleaseFlowNode": "请添加流程节点",
  "lang.ark.fed.cacheNodeActions": "排队区交互",
  "lang.ark.fed.reconnectToTheSystem": "再次连入系统",
  "lang.ark.fed.simulationButton": "按钮触发",
  "lang.ark.trafficControl.enterStrategy.byTimePriority": "先到先过（优先级优先）",
  "lang.ark.workflow.area.increase": "逐个递进",
  "lang.ark.fed.firstLetterUppercasePlus6Digits": "首字母大写加6位数字！",
  "lang.ark.fed.common.btn.detail": "详情",
  "lang.ark.fed.earlyWarningMode": "预警方式",
  "lang.ark.fed.menu.containerLog": "容器日志",
  "lang.ark.robot.classfy.roller": "辊筒机器人",
  "lang.ark.fed.carModel": "车型",
  "lang.ark.fed.youCanRemotelySelectTheRobot": "您可以遥控选择的机器人",
  "lang.ark.fed.strategy": "策略",
  "lang.ark.fed.dockPointName": "停靠点名称",
  "lang.ark.roller.docking.bating": "辊筒对接下料",
  "lang.mwms.fed.arrangeMoveNote": "移动单管理",
  "lang.ark.externalDevice.instructionRule": "指令规则",
  "lang.ark.fed.workingNotAllowClickCell": "存在正在执行的任务，请勿点击单元格",
  "lang.ark.fed.canAddshelfFlag": "添加货架",
  "lang.ark.fed.export": "导出",
  "lang.ark.fed.selectLocation": "选择货位",
  "lang.ark.workflow.area.factoryControl": "不允许跟随进入厂家",
  "lang.ark.fed.externalNumber": "外部单号",
  "lang.ark.fed.update": "更新",
  "lang.ark.fed.operationLog": "操作日志",
  "lang.ark.workflowConfig.cellFunctions.dropCell": "DROP_CELL",
  "lang.ark.fed.equipmentNode": "设备点位",
  "lang.ark.externalDevice.device_own_type": "设备归属",
  "lang.ark.fed.templateDownloading": "模板下载",
  "lang.ark.fed.contents.flowConfig.recycleType.autoRecoveryRule.add": "添加预设地点",
  "lang.ark.fed.menu.instance": "设备实例",
  "lang.ark.workflow.task.status.suspension": "挂起",
  "lang.ark.workflow.denseStorageTemplateAreaFull": "终点是密集存储区域，且区域已满",
  "lang.ark.groupStrategy": "选组策略",
  "lang.ark.fed.allValuesOrRelationshipsWithinTheCollection": "集合内所有值为或关系",
  "lang.ark.rpc.syncNodeErr": "获取节点信息失败！",
  "lang.ark.fed.commonMaterials": "常用物料",
  "lang.ark.fed.eitherOrRobotAndType": "机器人型号与指定机器人只能二选一",
  "lang.ark.fed.importFileFormatError": "导入模板错误，请下载指定模板后再上传",
  "lang.ark.fed.productPolice": "防呆报警",
  "lang.mwms.monitorRobotMsg.other": "其它异常",
  "lang.ark.fed.deleteAuthWorkStation": "确定删除权限？",
  "lang.ark.fed.generalNode": "地图点位",
  "lang.ark.base.license.licenseExpiredErrorMsg": "证书已过期，系统禁止使用!",
  "lang.ark.fed.stationConfig": "工位配置",
  "lang.ark.fed.screen.equipmentAssociatedInfo.labelEquipType": "设备类型",
  "lang.ark.container.containerBatchAddPartSuccessful": "成功新增{}个容器，失败{}个（容器编码{}已存在）",
  "lang.ark.fireStop.areaCodeAlreadyExistPleaseCheck": "区域编码{0}已存在，请检查!",
  "lang.ark.apiRobotTaskCode.robotTaskNotOnlyOne": "没有匹配到唯一的robotTaskId:{}",
  "lang.ark.fed.conButtonLogMessage": "报文内容",
  "lang.ark.fed.sacnFailAndCancelTask": "容器编码识别失败，任务取消",
  "lang.ark.workflow.workflowNotFound": "流程起点未找到",
  "lang.ark.immediate.bating": "直接下料",
  "lang.gles.receipt.receiptOutWarehouseOrder": "出库单",
  "lang.ark.waveTaskStatus.finished": "完成",
  "lang.ark.fed.component.workflow.tooltip.specifyNodeType": "当选择“不指定”时，默认包含全场所有点位类型，适用于只一个外部设备场景。",
  "lang.authManage.web.common.password": "密码",
  "lang.ark.fed.deviceAccessType": "设备名称获取方式",
  "lang.ark.fed.waitingTime": "等待时间",
  "lang.ark.fed.queueOrder": "排队顺序",
  "lang.ark.fed.deliveryMaterial": "配送物料",
  "lang.ark.base.license.ipStrIsNull": "IP端口号为空!",
  "lang.ark.workflowConfig.cellFunctions.firePass": "消防功能",
  "lang.ark.loadCarrier.loadCarrierModelCodeDuplicated": "容器模型编码重复！",
  "lang.mwms.fed.efficiency": "效率管理",
  "lang.ark.fed.workingNotAllowChangeFloor": "存在正在执行的任务，请勿切换楼层",
  "lang.ark.interface.containerNo": "容器号",
  "lang.ark.fed.businessCode": "业务代码",
  "lang.ark.fed.createManually": "手动创建",
  "lang.ark.fed.speedLimit": "速度限制",
  "lang.ark.fed.picking": "领料中",
  "lang.gles.workflow.receipt": "单据管理",
  "lang.mwms.fed.inventoryStockshot": "库存对账",
  "lang.authManage.web.menu.roleList": "角色列表",
  "lang.ark.record.robotCallback.turnOfSide": "机器人{0}转面",
  "lang.ark.fed.commandCode": "指令代码",
  "lang.ark.fed.commandName":"指令名称",
  "lang.ark.fed.conButtonLogExecuteContent": "执行内容",
  "lang.ark.fed.stationPoint": "工位节点编码",
  "lang.ark.fed.upTaskNumber": "外部任务号",
  "lang.ark.fed.queueRobotNum": "排队机器人数量",
  "lang.ark.api.areaNodeExit": "货架区不存在",
  "lang.ark.workflow.area.accessWay": "通行方式",
  "lang.ark.fed.pleaseSelectShelfType": "请选择货架类型或输入货架编码",
  "lang.ark.fed.currentWorkflow": "当前流程",
  "lang.ark.fed.showContents": "显示内容",
  "lang.ark.workflow.task.status.cancelExecuting": "取消中",
  "lang.ark.workflow.template.validate.templateCodeIsExist": "模板编码已存在",
  "lang.ark.fed.rotate": "旋转",
  "lang.ark.api.template.finishNodeNotExist": "模板终点不存在",
  "lang.ark.fed.orderNum": "单据号",
  "lang.ark.fed.setTop": "置顶",
  "lang.ark.fed.parameterClassification": "参数分类",
  "lang.ark.fed.operator": "操作人",
  "lang.ark.fed.addDockPoints": "添加停靠点",
  "lang.ark.fed.selectImage": "选择图片",
  "lang.ark.fed.clickUpload": "点击上传",
  "lang.ark.fed.menu.nodeDeviceInfo": "点位设备绑定",
  "lang.ark.fed.lineCoding": "产线编码",
  "lang.ark.fed.screen.flowNodeConfig.judgingByRule": "按规则判断",
  "lang.ark.fed.month": "月",
  "lang.ark.fed.electricQuantity": "电量",
  "lang.ark.workflow.area.order": "顺序排队",
  "lang.ark.fed.userEnquiry": "用户询问",
  "lang.ark.fed.idle": "空闲",
  "lang.gles.interface.interfaceManager": "接口管理",
  "lang.ark.fed.week": "星期",
  "lang.ark.fed.forbid": "禁用",
  "lang.ark.workflow.cancelLocationCodeInvalid": "取消点位类型非法",
  "lang.ark.apiContainerCode.numberLessThanZero": "numberOfContainer的值必须大于0",
  "lang.ark.workflow.successHandler": "成功后处理逻辑",
  "lang.ark.warehouse.warehouseHaveNoBinInfo": "无可用库存",
  "lang.ark.fed.alarmInfo": "报警信息",
  "lang.ark.fed.shelfAttribute.PNAC": "PNAC",
  "lang.authManage.web.common.newItem": "新增",
  "lang.ark.fed.serialExcessMount": "物料数量不可超过{0}，请切换物料",
  "lang.ark.warehouse.preparationEnableFailed": "上料点物料或可配送目的点为空，启用失败！",
  "lang.ark.fed.screen.hybridRobot.robotBody": "机器人本体",
  "lang.ark.workflow.notFoundDenseStorageTemplate": "起点或者终点是密集存储，未找到配置的密集存储动态点模板",
  "lang.ark.fed.waveTaskDashboard": "波次任务看板",
  "lang.ark.fed.syncInform": "信息同步",
  "lang.ark.fed.extendDevice": "外部设备",
  "lang.ark.workflow.chooseStrategy.normal": "正常完成",
  "lang.ark.fed.rotationAngle": "旋转角度",
  "lang.ark.fed.happensEveryOnceInAwhile": "每隔{0}天发生一次",
  "lang.ark.fed.triggerEntity": "触发器实例",
  "lang.ark.workflowTrigger.logType.robot": "机器人日志",
  "lang.ark.fed.makeSureToDeleteTheCurrentRule": "确定删除当前规则",
  "lang.ark.fed.abnormalCompletion": "异常完成",
  "lang.ark.fed.left": "左",
  "lang.ark.fed.stationCoding": "工位外部编码",
  "lang.ark.fed.screen.flowNodeConfig.instructExeCondition": "指令执行条件",
  "lang.ark.fed.closeLeftTabs": "关闭左侧",
  "lang.ark.fed.deviceCode": "设备编码",
  "lang.ark.workflow.area.stopRange": "急停区",
  "lang.ark.fed.triggerStopPointId": "启动工位",
  "lang.ark.element.has.boundNodeAction": "该元素已经绑定了交互配置",
  "lang.ark.workflow.stopPointNotExists": "点位不存在",
  "lang.ark.fed.unloadingPoint": "下料点",
  "lang.ark.pda.function.container.list": "容器列表",
  "lang.authManage.web.common.save": "保存",
  "lang.gles.baseData.factory": "工厂",
  "lang.ark.warehouse.doubleContainerSide": "双面",
  "lang.ark.fed.handlingRack": "搬运货架",
  "lang.ark.fed.chromeScanSetting": "1、浏览器地址栏输入chrome://flags， 搜索 unsafely-treat-insecure-origin-as-secure 选项，设置为Enabled，在选项输入框添加需要开启摄像头的域名或IP，修改之后重启浏览器。",
  "lang.ark.interface.apiPause": "流程暂停",
  "lang.ark.base.license.getHarewareFail": "获取设备信息失败,请检查IP配置及jar包启动情况!",
  "lang.ark.fed.taskJiXu": "点击恢复挂起任务,流程继续",
  "lang.ark.apiCallbackReg.all": "全部",
  "lang.ark.fed.ruleOpreator": "运算符",
  "lang.ark.fed.noItemSelected": "没有选中项",
  "lang.ark.fed.emergencyStopSuccess": "系统急停成功!",
  "lang.ark.fed.interfaceAccessType.fixation": "固定地址",
  "lang.ark.workflow.noAvailableNode": "未找到可用的节点",
  "lang.ark.fed.clearWay": "清除方式",
  "lang.ark.fed.menu.vens.dmpInstance": "设备实例",
  "lang.ark.fed.return": "返回",
  "lang.ark.fed.menu.deviceModel": "设备模型",
  "lang.ark.workflow.recoveryAreaType.appointCell": "指定回收区",
  "lang.ark.apiStationCode.stationTypeNotBlank": "工作站类型不能为空",
  "lang.ark.workflow.failure": "失败",
  "lang.ark.fed.screen.flowNodeConfig.checkMsg2": "保存失败，请选择任务",
  "lang.ark.operation.workflow.deleteWorkflow": "删除任务{0}",
  "lang.ark.fed.screen.flowNodeConfig.checkMsg0": "保存失败，请输入点位编码",
  "lang.ark.fed.screen.flowNodeConfig.checkMsg1": "保存失败，请选择路径判断条件",
  "lang.ark.fed.workstation.loading.logout": "正在退出...",
  "lang.ark.button.command.pressDown": "按下",
  "lang.ark.fed.asc": "升序",
  "lang.ark.workflow.template.validate.dynamicUnitTemplateFormatError": "动态单元任务样式错误",
  "lang.ark.fed.taskStatus": "任务状态",
  "lang.gles.logisticsConfig.tallyConfig": "理货物流配置",
  "lang.ark.fed.once": "一次",
  "lang.ark.workflow.dataArchiving": "数据归档",
  "lang.ark.fed.monitoringAndManagement": "监控管理",
  "lang.ark.fed.floor": "楼层",
  "lang.ark.fed.menu.robotInformation": "机器人监控",
  "lang.ark.fed.workstationstationid": "{stationId}号工作站",
  "lang.ark.workflow.deviceTaskNotExistOrUnException": "设备任务不存在或非异常状态不允许重试",
  "lang.ark.fed.runMode.unload": "空载",
  "lang.ark.trafficControl.noStayRange": "禁止停留区域",
  "lang.ark.button.operation.command.fetch": "取",
  "lang.mwms.api.menu.item0001": "RHINO-WEB",
  "lang.ark.fed.desc": "降序",
  "lang.authManage.web.common.reset": "重置",
  "lang.ark.fed.screen.flowNodeConfig.sourceTurnSide": "旋转面来源",
  "lang.ark.workflow.wareHouseConfigurationEnable": "配置启用",
  "lang.ark.workflow.notAllowBreak": "机器人取放操作中不允许中断",
  "lang.authManage.web.common.phone": "电话",
  "lang.ark.workflow.taskSplit": "任务拆分",
  "lang.ark.fed.configName": "配置名称",
  "lang.gles.batchProperty": "批属性",
  "lang.ark.fed.airRunProcess": "空跑流程",
  "lang.ark.fed.operatorPositionInProductionLine": "操作人员在产线上进行生产作业的位置(工位)",
  "lang.ark.fed.screen.flowNodeConfig.deviceInstruct": "设备指令",
  "lang.ark.fed.all": "全部",
  "lang.ark.workflow.paramValueCode.binCode": "binCode",
  "lang.ark.workflow.paramValueCode.offsetX": "offsetX",
  "lang.ark.apiNodeActionCode.successHandlerIsAuto": "节点交互配置的成功处理逻辑为自动触发",
  "lang.ark.workflow.noAvailableRobot": "无可用机器人",
  "lang.ark.waveTriggerCondition.workstation": "工作站",
  "lang.ark.workflow.arrive.action.robotGoTurnOfAngle": "本体按角度旋转",
  "lang.ark.fed.startCharging": "开始充电",
  "lang.ark.fed.theSaveSourceOnlySameTriggerHandle": "连接线的起始点相同时，只配置其中一条的触发时机即可！",
  "lang.ark.warehouse.policyNumberExists": "波次策略编码已存在",
  "lang.ark.workflowConfig.cellFunctions.shelfCell": "SHELF_CELL",
  "lang.ark.fed.emergencyStop": "已急停",
  "lang.ark.fed.screen.flowNodeConfig.SourceOffsetValue": "偏移值来源",
  "lang.ark.fed.sourceDocuments": "单据来源",
  "lang.mwms.fed.seedRule": "播种墙规格维护",
  "lang.ark.fed.nodeWaiting": "到达工位",
  "lang.ark.apiContainerCode.locationAndContainerAreEmpty": "containerCode和locationCode均为空，必须指定其中一项",
  "lang.ark.fed.emptyItAll": "全部清空",
  "lang.ark.fed.operation": "操作",
  "lang.ark.api.workflow.idOrTaskCodeIsNull": "任务编码或流程ID不能为空",
  "lang.ark.plugin.pluginType.returnContainer.way.manual": "手动选择空满",
  "lang.ark.fed.menu.vens.dmpTemplateInstance": "设备实例模板",
  "lang.mwms.fed.workStationCharts": "工位效率管理",
  "lang.ark.fed.theLogicalArea": "逻辑区",
  "lang.ark.fed.flowEdgeType": "流转类型",
  "lang.ark.warehouse.goodsNoMatchZagvdbm": "未匹配到上料点,物料编码:{0}",
  "lang.ark.workflow.initiateNextTask": "发起下一个任务",
  "lang.ark.fed.road": "道路",
  "lang.ark.fed.receiveSure": "确认领取",
  "lang.ark.workflow.childNodeListIsEmpty": "子节点集合为空",
  "lang.ark.workflow.denseStorageEndPointTaskUpperLimit": "终点是密集存储区域, 区域接受任务已达上限",
  "lang.gles.systemManage.systemParam": "系统参数",
  "lang.ark.workflowConfig.status.deleted": "已删除",
  "lang.ark.fed.waveSetting": "波次生成",
  "lang.ark.fed.waveNum": "波次号",
  "lang.ark.fed.cellType": "点位类型",
  "lang.ark.fed.menu.collapseMenu": "折叠边栏",
  "lang.ark.fed.contents.flowConfig.recycleType.autoRecoveryRule.alert.api": "提示：用于上游调接口取消的场景，选择该方式需上游传货物送回位置",
  "lang.ark.fed.contents.flowConfig.recycleType.autoRecoveryRule.alert.auto": "提示：可设置多个预设地点，系统按优先级匹配",
  "lang.ark.fed.contents.flowConfig.recycleType.autoRecoveryRule.alert.manual": "提示：用于在任务监控手动取消任务，上游调接口取消不支持手动选择送回位置",
  "lang.ark.fed.contents.flowConfig.recycleType.autoRecoveryRule.add.required": "请添加预设地点",
  "lang.ark.fed.contents.flowConfig.recycleType.recycleActionId.add.required": "请选择交互动作",
  "lang.ark.interface.config.field.locationToDesc": "目标点位编码\n如工作站（工位）只有一个点位，也可以传入工作站编号；\n匹配顺序：地图节点 > 工作站 > 区域",
  "lang.ark.interface.config.taskCallback.field.msgTypeDesc": "消息类型：值必须是MovingCallbackMsg",
  "lang.ark.interface.config.taskCallback.field.exceptionFailReasonDesc": "流程异常处理失败原因",
  "lang.ark.sys.config.values.show": "显示",
  "lang.ark.sys.config.values.rpcStop": "新版本rpc系统急停",
  "lang.ark.sys.config.values.sync": "同步",
  "lang.ark.externalDevice.instructionRule7": "双举升–非空满交换任务",
  "lang.ark.externalDevice.instructionRule6": "双举升–放料执行",
  "lang.ark.externalDevice.instructionRule5": "悬臂–取料执行",
  "lang.ark.interface.config.taskCallbackDesc": "1、除请求接口发起的任务会产生回调外，通过工作站端操作页面发起的任务也会产生回调。\n\n2、回调的任务阶段分为三级：第一级workflowPhase，第二级taskPhase，第三级robotPhase。\n\n3、注意点：在任务执行中，taskPhase中的“中断等待”和“指令执行中”为非机器人触发的任务阶段变化，所以，此时robotPhase为空。中断等待后续如有需要执行的事件列表，则状态变更为指令执行中，此时会根据机器人执行指令阶段的变化，配合产生robotPhase的回调。\n\n注意：此接口为回调接口，是GMS调用主机系统，此接口需要主机系统按照对应格式开发",
  "lang.ark.sys.config.rmsQueryCurrentMapAddress": "增加查询rms当前地图地址(暂时方案，后续替换为RPC接口)",
  "lang.ark.interface.config.taskCallback.field.parentInstanceIdDesc": "上级流程实例编号",
  "lang.ark.sys.config.values.noneStop": "都不使用",
  "lang.ark.sys.config.callbackMessageRetryInterval": "Socket模式下，给上游回调时，重试的时间间隔：单位（秒），默认10秒",
  "lang.ark.interface.config.field.containerCodeDesc": "容器编号",
  "lang.ark.fed.uploadFile": "上传文件",
  "lang.ark.sys.config.denseStorageTemplateForAreaToArea": "密集存储区域到区域的动态点模板编码",
  "lang.ark.sys.config.simpHost": "simp的服务器host",
  "lang.ark.interface.config.movingMulti.field.destsDesc": "目标点位集合",
  "lang.ark.fed.excel.data.null": "第{0}行数据不全，请补充完整后重新上传",
  "lang.ark.task.nodeDevice.export.param6.value": "参数6：参数值",
  "lang.ark.interface.config.field.priorityDesc": "执行任务的优先级，数字越小优先级越高。未填写则使用模板中的优先级，填写则使用填写的优先级",
  "lang.ark.interface.config.taskCallback.field.robotTaskIdDesc": "机器人任务编号",
  "lang.ark.binStopPoint.deviceType": "设备类型",
  "lang.ark.task.nodeDevice.export.param1.value": "参数1：参数值",
  "lang.ark.task.plugin.take.fullContainer": "取容器-取满",
  "lang.ark.sys.config.values.oldFormat": "老格式",
  "lang.ark.sys.config.strictOrderMode": "是否严格按顺序执行任务：严格按顺序执行，则同类型的任务前一任务执行成功后才会执行下一任务；不严格按顺序执行，则上一任务执行完，不论成功或失败都可以执行下一任务。",
  "lang.ark.sys.config.buttonFilterTime": "在多长时间内，连续拍多次物理按钮只触发一次，该时间可设置（单位：秒）",
  "lang.ark.sys.config.callbackMessageRetryTimes": "Socket模式下，给上游回调时，重试的次数：默认360次",
  "lang.ark.sys.config.values.close": "关闭",
  "lang.ark.sys.config.rmsWsAddress": "RMS Websocket地址",
  "lang.ark.task.plugin.take.emptyContainer": "取容器-取空",
  "lang.ark.interface.config.taskCallback.field.instancePriorityDesc": "流程实例（任务）的优先级，值越小优先级越高，最高优先级为1",
  "lang.ark.fed.containerBinStatus": "货位状态",
  "lang.ark.sys.config.rmsQueryAreaAddress": "从RMS系统获取各类区域数据的地址",
  "lang.ark.fed.binStopPoint.file.excel.name": "locationBaseInfo",
  "lang.ark.sys.config.modbusEnable": "物理按钮功能开关，打开则开启物理按钮功能，关闭则屏蔽物理按钮功能（即使在物理按钮配置界面配了也不生效）",
  "lang.ark.task.exception.startPoint.containerCode.mustOne": "第{}行起点编码和容器编码必填一项",
  "lang.ark.interface.config.movingMulti": "点到多点搬运",
  "lang.ark.interface.config.field.containerCategoryDesc": "容器类型编码",
  "lang.ark.interface.config.taskCallback.field.waitNextLocationDesc": "等待点的下一个点",
  "lang.ark.sys.config.robotMediaApi": "影音播报默认api",
  "lang.ark.task.exception.templateTask.empty": "查询模板信息为空",
  "lang.ark.sys.config.stationNoticeCycle": "工作站循环提醒的时间间隔，单位：秒",
  "lang.ark.interface.config.taskCallback.field.robotDesc": "机器人编号",
  "lang.ark.sys.config.values.exactMatch": "精准匹配",
  "lang.ark.interface.config.taskCallback.field.instanceIdDesc": "流程实例编号",
  "lang.ark.task.nodeDevice.export.param8.name": "参数8：参数名称",
  "lang.ark.sys.config.denseStorageTemplateForAreaToPoint": "密集存储区域到点的动态点模板编码",
  "lang.ark.sys.config.overtimeTaskIntervalHours": "任务超时阈值：当前时间-任务创建时间＞配置的阈值，则该任务算超时",
  "lang.ark.sys.config.values.accountLogin": "账号登录",
  "lang.ark.sys.config.values.async": "异步",
  "lang.ark.interface.config.field.needTimeDesc": "任务要求完成时间",
  "lang.ark.sys.config.stationConfig": "工作站类型",
  "lang.ark.task.nodeDevice.export.interfaceOrDevice": "接口地址/设备编码",
  "lang.ark.interface.config.taskCallback.field.robotPhaseDesc": "机器人任务阶段",
  "lang.ark.sys.config.denseStorageTemplateForPointToArea": "密集存储点到区域的动态点模板编码",
  "lang.ark.fed.frontend": "前端",
  "lang.ark.fed.updateUser": "更新人",
  "lang.ark.interface.config.taskCallback.field.workflowCodeDesc": "流程编码",
  "lang.ark.interface.config.field.requestTimeDesc": "请求发送时间，格式yyyy-MM-dd hh:mm:ss",
  "lang.ark.workflow.noAvailableOnlineDevices": "没有找到可用的在线设备。请检查设备是否处于离线状态",
  "lang.ark.sys.config.filterChannelButton": "回调通道配置：只回调发任务的通道，还是全部通道都回调",
  "lang.ark.sys.config.showExceptionTab": "是否展示异常页",
  "lang.ark.interface.config.movingMultiDesc": "从一个位置出发，连续的去往多个位置。可以按照目标位置的ID升序排列或按照发送的先后顺序去往目标位置。",
  "lang.ark.sys.config.modbusPort": "GMS通过modbus与外部设备（如物理按钮）进行交互时的端口",
  "lang.ark.fed.excel.data.binCodeAndOrderError": "第{0}行货位编码和货位顺序号须同时有值或者同时为空，请修改后重新导入",
  "lang.ark.sys.config.loginUrl": "权限系统登录地址",
  "lang.ark.fed.updateTime": "更新时间",
  "lang.ark.sys.config.shelfOnShare": "货架是否可以多流程共享",
  "lang.ark.task.nodeDevice.export.param3.name": "参数3：参数名称",
  "lang.ark.sys.config.values.version2": "2.x版本",
  "lang.ark.sys.config.values.version1": "1.x版本",
  "lang.ark.fed.containerColumn": "所在容器列数",
  "lang.ark.task.nodeDevice.export.param5.value": "参数5：参数值",
  "lang.ark.task.exception.endpoint.empty": "第{}行终点编码为空",
  "lang.ark.task.exception.templateCode.empty": "第{}行模板编码为空",
  "lang.ark.sys.config.warehousePointRelatedScheduled": "warehouse系统更新上料点配置数据的开关，打开则允许更新，关闭则不允许更新",
  "lang.ark.sys.config.values.withoutFloor": "不按楼层判断",
  "lang.ark.sys.config.httpTimeOut": "http连接的超时时间，默认2000ms",
  "lang.ark.sys.config.values.adaptive": "自适应终端",
  "lang.ark.sys.config.authUrl": "认证中心url",
  "lang.ark.interface.config.movingDesc": "两点之间搬运容器（货架），或两点之间机器人空载运行。\n\n场景：\n\n1、 主机系统管理货架且需要搬运指定货架时，可以传入货架号及目的点位编码，系统会将此货架搬运至目的点位。\n\n2、 主机系统不管理货架，但管理货架所在的位置，此时可以传入起始点位编码及目的点位编码，系统会将起始点位的货架搬运至目的点位。\n\n3、 主机系统管理点位或工位，并希望机器人空载从一个位置去往另一个位置，可以传入起始点位编码及目的点位编码。\n\n使用方法：\n\n1、在GMS系统中配置节点的机器人交互动作，GMS将根据主机系统传入的点位自动生成流程并执行任务。\n\n2、可提前在GMS中创建流程，GMS将根据主机系统传入的点位匹配已有流程并执行。",
  "lang.ark.sys.config.callbackMessageRetryAndConfirm": "Socket模式下，给上游回调时是否开启确认与重试机制。默认不开启，Socket模式下不重试；开启后，会确认上游是否收到回调，如未收到则重试；（注意，Socket模式下开启重试需上游系统开发，在收到GMS系统的回调时，告知GMS已收到）",
  "lang.ark.sys.config.values.doNotSplitTheRollerFetchCommand": "不拆分取货指令",
  "lang.ark.interface.config.field.languageDesc": "包含在消息头中，响应消息的语言设定。\n设置此字段后，RMS会根据这个字段选择返回响应消息的语言。\n目前支持简体中文（zh_cn）、英文(en_us)、日文（ja_jp）、繁体中文（zh_hk/zh_tw）等，\n不传默认为简体中文（zh_cn）",
  "lang.ark.fed.containerLayer": "所在容器层数",
  "lang.ark.interface.config.dynamicUnitMoving.field.robotIdDesc": "执行到达目标点的流程任务的机器人ID，需指定机器人执行时必传，若模板配置了机器人类型和id， 以传入为准。",
  "lang.ark.sys.config.values.mapStation": "Map版工作站",
  "lang.ark.sys.config.clientId": "mars Websocket调用客户端Id",
  "lang.ark.sys.config.asynCount": "异步请求的接口日志，每次插入数据库的数量",
  "lang.ark.sys.config.rmsUrl": "mars Websocket地址",
  "lang.ark.fed.excel.data.binCodeExist": "第{0}行货位点编码、货位顺序号已存在，不允许重复添加，请修改后重新导入",
  "lang.ark.flowNodeConfig.rollerFetchCommand.checkMsg": "“开始取货”和“完成取货”必须同时配置，不能单独使用",
  "lang.ark.interface.config.taskCallback.field.nodeCodeToDesc": "任务终点-流程中配置的节点编码",
  "lang.ark.sys.config.smpAlterIp": "smp接收预警消息的IP地址",
  "lang.ark.task.nodeDevice.export.param5.name": "参数5：参数名称",
  "lang.ark.interface.config.taskCallback.field.locationToDesc": "实际目标点编码",
  "lang.ark.task.plugin.deliver.manuallyChoose": "送容器-手动选择空满",
  "lang.ark.sys.config.values.notLeave": "不需要离场",
  "lang.ark.interface.config.movingMulti.field.msgTypeDesc": "消息类型：值必须是MultiMovingRequestMsg",
  "lang.ark.sys.config.ningdeHttpSoapUserName": "宁德soap请求地址用户名",
  "lang.ark.interface.config.dynamicUnitMoving.field.msgTypeDesc": "消息类型：值必须是DynamicUnitMovingRequestMsg",
  "lang.ark.fed.templateTask": "模板任务",
  "lang.ark.sys.config.isSynInterfaceRecord": "接口日志插入数据库的方式：同步/异步，默认同步",
  "lang.ark.binStopPoint.name": "名称",
  "lang.ark.task.exception.robotType.empty": "第{}行机器人型号为空",
  "lang.ark.fed.excel.data.deviceType.nonNumericFormat": "第{0}行设备类型必须为数字，请修改后重新导入",
  "lang.ark.interface.config.field.taskCodeDesc": "外部任务号/单据号等，响应或回调时会将此项内容回传给上游",
  "lang.ark.sys.config.values.leaveByTaskDest": "根据任务当前节点的交互决定是否离场",
  "lang.ark.interface.config.moving": "点到点任务",
  "lang.ark.interface.config.taskCallback.field.scanningInformationDesc": "扫码错误原因",
  "lang.ark.task.plugin.deliver.fullContainer": "送容器-送满容器",
  "lang.ark.interface.config.field.requestIdDesc": "包含在消息头中，用作对一次请求及其响应进行唯一标识。\n该字段的值由主机系统定义，并且响应消息应具有相同的值。\nGMS需要根据此ID来保证幂等性，因此需要保证该ID的全局唯一，推荐使用UUID。",
  "lang.ark.task.exception.priority.gt": "第{}行优先级只能为正整数",
  "lang.ark.sys.config.values.pc": "始终按PC端效果展示",
  "lang.ark.interface.interfaceDesc.button": "操作",
  "lang.ark.task.nodeDevice.export.param7.value": "参数7：参数值",
  "lang.ark.interface.config.moving.field.msgTypeDesc": "消息类型：值必须是MovingRequestMsg",
  "lang.ark.sys.config.robotMediaCatalogue": "影音文件本地存放目录",
  "lang.ark.sys.config.canBackTaskFlag": "工作站是否显示退回按钮",
  "lang.ark.sys.config.trafficControlInterfaceType": "交通管制接口访问方式：同步/异步",
  "lang.ark.sys.config.values.cancelInPlace": "原地取消",
  "lang.ark.binStopPoint.dropHeight": "放料高度",
  "lang.ark.sys.config.waitPointTaskShowNum": "等待点任务列表每页展示的任务个数",
  "lang.ark.task.nodeDevice.export.param3.value": "参数3：参数值",
  "lang.ark.interface.config.taskCallback.field.robotErrorDesc": "机器人任务故障",
  "lang.ark.sys.config.values.notStrictOrder": "不严格按顺序执行",
  "lang.ark.interface.config.dynamicUnitMoving.field.robotProductDesc": "指定机器人型号，若同时指定了机器人ID，以机器人ID为准",
  "lang.ark.apiCommonCode.name.overLengthError": "名称超出30个字符",
  "lang.ark.sys.config.robotDefaultPort": "机器人端口",
  "lang.ark.sys.config.rmsRetryCode": "给RMS下发任务失败时，需要针对哪些错误进行重试。配置在此处的错误码会重试，多个用逗号分隔。",
  "lang.ark.interface.config.field.channelIdDesc": "包含在消息头中，链路通道的标识",
  "lang.ark.sys.config.checkFreeRobotFlag": "下发任务时是否需要判断机器人空闲状态（判断：没有空闲机器人时无法下发任务；不判断：下发任务不受是否有空闲机器人影响）",
  "lang.ark.interface.config.dynamicUnitMoving.field.containerCategoryDesc": "容器类型编码\n使用场景：首个目标节点（流程首节点）为区域，且区域内存在两种类型以上的容器，在发起任务时需要传入一个容器类型。",
  "lang.ark.task.nodeDevice.export.param4.name": "参数4：参数名称",
  "lang.ark.sys.config.canCancelDoneTaskFlag": "工作站是否显示取消完成按钮",
  "lang.ark.sys.config.arkRoot": "ark根目录",
  "lang.ark.interface.config.field.clientCodeDesc": "包含在消息头中，用户编码，是上游客户的一个标识。\n此ID由GMS颁发给上游客户，在同一个项目服务中使用同一个clientCode。",
  "lang.ark.task.exception.startPoint.notMatch": "第{}行导入的起点编码与系统中维护的起点编码不匹配",
  "lang.ark.sys.config.callbackMsgTransFlag": "点到点下发，容器离场，容器入场，取消任务，任务继续，标准回调等从2.x版本转换为1.x版本控制开关（默认关闭使用2.x版本，设置为true打开使用1.x版本）",
  "lang.ark.interface.config.taskCallback.field.scanCodeDesc": "托盘码",
  "lang.ark.fed.excel.data.name.overLength": "第{0}行名称超出30个字符，请修改后重新导入",
  "lang.ark.sys.config.movingApiResponsePattern": "流程送/取消/自动匹配接口返回格式，1：标准格式，0：老格式（header和body）",
  "lang.ark.fed.flowNodeConfig.beforeTaskArrived.destDistance.label": "指令执行时机",
  "lang.ark.interface.config.dynamicUnitMovingDesc": "可以无限制的更新任务，传了isEnd后流程才结束。",
  "lang.ark.sys.config.ningdeHttpSoapPassword": "宁德soap请求地址密码",
  "lang.ark.binStopPoint.pickHeight": "取料高度",
  "lang.ark.task.rule.saveFloor": "流转策略 == 相同楼层",
  "lang.ark.fed.binStopPoint.file.excel.sheet1.name": "locationBaseInfo",
  "lang.ark.interface.config.dynamicUnitMoving.field.isEndDesc": "目标点为流程终点时， 流程创建完成即完整流程，执行完即自动结束。",
  "lang.ark.interface.config.taskCallback.field.locationFromDesc": "实际起始点编码",
  "lang.ark.sys.config.taskScheduleInterval": "任务调度",
  "lang.ark.task.rule.default": "完成情况 == 正常完成",
  "lang.ark.sys.config.stationDisplayMode": "工作站在各类终端上的展示效果",
  "lang.ark.sys.config.masterKey": "Redis中存储的当前运行服务器主机名的键",
  "lang.ark.sys.config.marsRoot": "mars根目录",
  "lang.ark.interface.config.field.taskTypeDesc": "匹配流程模板中的流程模板编码",
  "lang.ark.interface.config.dynamicUnitMoving": "动态单元模板搬运",
  "lang.ark.sys.config.stationNoticeTimes": "工作站通知播放次数",
  "lang.ark.sys.config.values.strictOrder": "严格按顺序执行",
  "lang.ark.sys.config.isSyncHandleApi": "处理接口请求的方式：同步/异步，默认同步",
  "lang.ark.task.nodeDevice.export.param10.name": "参数10：参数名称",
  "lang.ark.sys.config.modbusIp": "GMS通过modbus与外部设备（如物理按钮）进行交互时的IP",
  "lang.ark.interface.config.field.instanceIdDesc": "流程实例（任务编号）",
  "lang.ark.task.plugin.name.take.emptyContainer": "取空容器",
  "lang.ark.sys.config.values.fullStation": "Full版工作站",
  "lang.ark.sys.config.rmsHttpAddress": "RMS http请求地址",
  "lang.ark.interface.config.taskCallback.field.exceptionStateDesc": "流程异常处理状态",
  "lang.ark.sys.config.ningdeHttpSoap": "宁德soap请求地址",
  "lang.ark.task.nodeDevice.export.param4.value": "参数4：参数值",
  "lang.ark.workflow.action.commandExecutePhase.beforeTaskArrived": "任务到达前",
  "lang.ark.interface.config.dynamicUnitMoving.field.toBinTypeDesc": "目标点货位类型，0、1、2、3...，为空默认0。\n0外其他传值根据项目约定。",
  "lang.ark.task.nodeDevice.export.param9.value": "参数9：参数值",
  "lang.ark.task.plugin.deliver.emptyContainer": "送容器-送空容器",
  "lang.ark.task.nodeDevice.export.param9.name": "参数9：参数名称",
  "lang.ark.sys.config.loginType": "登录方式",
  "lang.ark.task.plugin.name.take.fullContainer": "取满容器",
  "lang.ark.task.nodeDevice.export.param2.name": "参数2：参数名称",
  "lang.ark.fed.menu.loginLog": "登录日志",
  "lang.ark.binStopPoint.dockingHeight": "对接高度",
  "lang.ark.interface.config.taskCallback.field.taskPhaseDesc": "任务阶段",
  "lang.ark.task.nodeDevice.export.param6.name": "参数6：参数名称",
  "lang.ark.sys.config.matchWorkflowStrategy": "流程匹配策略：模糊匹配/精准匹配",
  "lang.ark.fed.excel.nodeCodeNotExists": "{0}行节点编码不存在，请检查修改后再上传",
  "lang.ark.sys.config.authNoPerUrl": "403页面",
  "lang.ark.workflow.startPickUp": "开始取货",
  "lang.ark.fed.excel.data.stopPointCodeNotExist": "第{0}行点位编码在系统中不存在，请修改后重新导入",
  "lang.ark.sys.config.changepwUrl": "访问权限系统“修改密码页面”的地址",
  "lang.ark.interface.config.taskCallback": "任务回调",
  "lang.ark.interface.config.taskCallback.field.workflowExceptionCodeDesc": "流程异常编码",
  "lang.ark.interface.config.dynamicMovingDesc": "任务流程/步骤确定，每个步骤具体工位点不确定。依次单个步骤的传入起始位置ID和目标位置ID，完成正在执行流程或者发起新流程。\n使用方法：\n在GMS系统中配置机器人交互动作模板，GMS将根据主机系统传入的点位匹配正在执行流程实例延续当前流程或者自动生成流程并执行任务。",
  "lang.ark.sys.config.values.splitTheRollerFetchCommand": "拆分取货指令",
  "lang.ark.interface.config.movingMulti.field.flowStrategyDesc": "流转策略",
  "lang.ark.task.plugin.name.deliver.emptyContainer": "送空",
  "lang.ark.trigger.logClearDesc": "默认触发器（全部日志清理）",
  "lang.ark.sys.config.values.callbackToTaskChannel": "回调发任务的通道",
  "lang.ark.sys.config.authCode": "GMS系统访问权限系统时的Code，便于权限系统区分是哪个系统的访问",
  "lang.ark.interface.config.dynamicUnitMoving.field.robotSideDesc": "目标点机器人的面朝向\n交互配置中机器人朝向为接口赋值时生效",
  "lang.ark.fed.excel.data.nonNumericFormat": "第{0}行对接高度，放料高度，取料高度，货位顺序号必须为数字，请修改后重新导入",
  "lang.ark.sys.config.dmpSetContainerAngle": "dmp容器入场时容器角度设置开关，打开则需设置容器角度，关闭则无需设置",
  "lang.ark.sys.config.canDeleteTaskFlag": "工作站是否删除流程退回按钮",
  "lang.ark.interface.config.dynamicUnitMoving.field.toBinOrderDesc": "目标货位顺序号",
  "lang.ark.interface.config.field.scanCodeDesc": "托盘码（上位系统传输的码值）",
  "lang.ark.task.plugin.name.deliver.fullContainer": "送满",
  "lang.ark.sys.config.stationService": "工作站的Websocket地址",
  "lang.ark.sys.config.language": "语言",
  "lang.ark.sys.config.authId": "GMS系统访问权限系统时的Id，便于权限系统区分是哪个系统的访问",
  "lang.ark.sys.config.globalCanDeleteTaskFlag": "任务监控页面是否删除流程退回按钮",
  "lang.ark.sys.config.values.disable": "不开启",
  "lang.ark.sys.config.robotReceiveUrl": "机器人模拟接受文件目录",
  "lang.ark.interface.config.dynamicUnitMoving.field.robotRuleDesc": "若指定了机器人ID，需强制执行，则必填；\n否则不用传，默认为优先 ;",
  "lang.ark.sys.config.values.notShow": "不显示",
  "lang.ark.task.exception.taskType.empty": "第{}行任务类型为空",
  "lang.ark.fed.arkVersion": "ARK版本",
  "lang.ark.sys.config.recycleFunctionSwitch": "回收区功能开关，打开则可以使用回收区功能（但也需要在流程模板/流程编辑页面先配置），关闭则不能使用回收区功能（在流程模板和流程编辑配置页面无回收区功能供选择）",
  "lang.ark.trigger.dataArchivingDesc": "默认触发器（全部数据归档）",
  "lang.ark.sys.config.rollerFetchCommandSplitConfiguration": "辊筒取货是否拆分为开始取货、完成取货2个指令。注意：1、GMS拆分取货指令依赖上装开始滚动的回调，需本体和rms也升级到有该回调的版本；2、改成拆分后，辊筒交互取货指令需要重新配；",
  "lang.ark.sys.config.values.open": "打开",
  "lang.ark.trigger.logClear": "全部日志清理",
  "lang.ark.sys.config.rmsChannelId": "rms给gms回调时传的rpc通道id",
  "lang.ark.task.exception.priority.empty": "第{}行优先级为空",
  "lang.ark.sys.config.dataArchiveMaxNum": "数据归档时的频率，每产生多少条数据归档一次",
  "lang.ark.sys.config.shelfMovingCallbackMsgFlag": "容器变更是否需要回调的开关，打开则变更容器后有回调信息，关闭则无回调信息",
  "lang.ark.task.exception.templateCode.notEqual": "第{}行模板编号与系统中的模板编号不匹配",
  "lang.ark.apiCommonCode.stopPointCodeExistError": "点位编码已存在",
  "lang.ark.sys.config.ningdeHttpLes": "宁德LES请求地址",
  "lang.ark.sys.config.newShelfCodePre": "新增货架时自动生成的容器编码前缀",
  "lang.ark.fed.excel.data.stopPointCodeExist": "第{0}行货位顺序号、地图点位编码已存在，不允许重复添加，请修改后重新导入",
  "lang.ark.sys.config.isFloorRobot": "下发任务时是否根据楼层判断空闲机器人：根据楼层判断，则某楼层没有空闲机器人不影响其他楼层的任务下发；",
  "lang.ark.fed.flowNodeConfig.beforeTaskArrived.destDistance.checkMsg": "请配置任务到达前的指令执行时机：只能输入大于0的数字，支持两位小数",
  "lang.ark.sys.config.values.mobile": "始终按移动端效果展示",
  "lang.ark.sys.config.systemStatusConfig": "调RMS系统急停时的接口",
  "lang.ark.task.rule.diffFloor": "流转策略 == 不同楼层",
  "lang.ark.sys.config.values.withFloor": "按楼层判断",
  "lang.ark.sys.config.callbackMsgOvertime": "RPC回调请求的UUID保存的超时时间（单位毫秒）",
  "lang.ark.interface.config.taskCallback.field.taskIdDesc": "子任务编号",
  "lang.ark.task.plugin.take.manuallyChoose": "取容器-手动选择空满",
  "lang.ark.fed.flowNodeConfig.beforeTaskArrived.destDistance.action": "时执行",
  "lang.ark.sys.config.isFrklift": "是否叉车",
  "lang.ark.fed.flowNodeConfig.beforeTaskArrived.destDistance.condition": "机器人到达离当前节点",
  "lang.ark.task.exception.endpoint.notMatch": "第{}行导入的终点编码与系统中维护的终点编码不匹配",
  "lang.ark.interface.config.taskCallback.field.workflowExceptionDesc": "流程异常描述",
  "lang.ark.fed.backend": "后端",
  "lang.ark.task.nodeDevice.export.desc": "填写说明：\n1、点位编码：填地图点位的编码或者外部编码\n2、设备交互方式：填device_command（代表设备指令）或interface_command（代表接口指令）",
  "lang.ark.sys.config.values.enable": "开启",
  "lang.ark.interface.config.taskCallback.field.waitLocationDesc": "等待点编码",
  "lang.ark.apiCommonCode.binCodeAndOrderError": "货位编码和货位顺序号须同时有值或者同时为空",
  "lang.ark.interface.config.dynamicMoving.field.msgTypeDesc": "消息类型：值必须是DynamicMovingRequestMsg\n匹配流程模板中的流程模板编码",
  "lang.ark.sys.config.values.cardLogin": "刷卡登录",
  "lang.ark.task.nodeDevice.export.param8.value": "参数8：参数值",
  "lang.ark.interface.config.taskCallback.field.waitDirDesc": "等待点机器人朝向",
  "lang.ark.task.plugin.deliver.autoReturn": "空箱自动返还(末节点用)",
  "lang.ark.binStopPoint.stopPointCode": "点位编码",
  "lang.ark.sys.config.callbackMessageRetryAndConfirmFilter": "Socket模式下开启重试后，重发回调的过滤条件，只重发此处配置的robotPhase,taskPhase状态值（多组用|符号连接）,默认为空（不重发任何回调）,格式如下：ARRIVED,WAITING_NODE|SHELF_ARRIVED,COMPLETED|SHELF_ARRIVED,WAITING_NODE",
  "lang.ark.sys.config.values.forceDelete": "强制删除",
  "lang.ark.fed.excel.nodeTypeError": "{0}行节点类型错误，请检查修改后再上传",
  "lang.ark.sys.config.deleteTaskIfNeedRemoveShelf": "取消任务后，容器是否需要离场",
  "lang.ark.sys.config.imageUploadPath": "图片上传默认目录",
  "lang.ark.sys.config.canUndoTaskFlag": "工作站是否显示撤销按钮",
  "lang.ark.task.nodeDevice.export.param2.value": "参数2：参数值",
  "lang.ark.sys.config.iniChainDefinitionTimeout": "GMS系统查询权限系统内菜单的超时时间（单位：毫秒)",
  "lang.ark.interface.config.taskCallback.field.workflowPhaseDesc": "流程状态",
  "lang.ark.trigger.dataArchiving": "数据归档",
  "lang.ark.sys.config.vensVersion": "dmp版本",
  "lang.ark.fed.inventoryInfo": "库存信息",
  "lang.ark.task.nodeDevice.export.param10.value": "参数10：参数值",
  "lang.ark.fed.bundleDate": "打包日期",
  "lang.ark.interface.config.field.locationFromDesc": "起始点位编码\n如工作站（工位）只有一个点位，也可以传入工作站编号；\n匹配顺序：地图节点 > 工作站 > 区域",
  "lang.ark.interface.config.taskCallback.field.nodeCodeFromDesc": "任务起点-流程中配置的节点编码",
  "lang.ark.interface.config.dynamicMoving": "动态点搬运",
  "lang.ark.binStopPoint.binOrder": "货位顺序号",
  "lang.ark.task.nodeDevice.export.param1.name": "参数1：参数名称",
  "lang.ark.sys.config.wAreaStation": "是否为W区工作站",
  "lang.ark.sys.config.loginByCardNoUrl": "刷卡登录的链接",
  "lang.ark.interface.config.dynamicUnitMoving.field.instructionDesc": "执行指令",
  "lang.ark.record.robotCallback.action.completed": "收到机器人指令完成",
  "lang.ark.sys.config.forceCancelUseAthenaInstruction": "在GMS系统内删除任务时调用的RMS接口",
  "lang.ark.sys.config.authUrlServer": "GMS系统查询权限系统内用户信息的地址",
  "lang.ark.binStopPoint.binCode": "货位点编码",
  "lang.ark.sys.config.values.fuzzyMatch": "模糊匹配",
  "lang.ark.sys.config.values.callbackToAll": "全部通道都回调",
  "lang.ark.sys.config.values.liteStation": "Lite版工作站",
  "lang.ark.sys.config.stationUnique": "工作站唯一登录开关，打开则开启工作站功能，关闭则不能使用工作站（即使在工作站配置页面配了工作站功能也不生效）",
  "lang.ark.task.nodeDevice.export.param7.name": "参数7：参数名称",
  "lang.ark.task.exception.containerCode.notMatch": "第{}行容器编码不匹配",
  "lang.ark.sys.config.values.leaveByTaskLast": "根据任务终点的交互决定是否离场",
  "lang.ark.sys.config.values.standardFormat": "标准格式",
  "lang.ark.sys.config.values.websocketStop": "老版本websocket系统急停",
  "lang.ark.sys.config.publicApiVersion": "对外接口版本",
  "lang.ark.fed.menu.callerConfiguration":"呼叫器",
  'lang.ark.fed.excel.data.binOrder.ButtonBoxInformationWasNotObtainedAccordingToTheEncoding':"根据编码未获取到按钮盒子信息",
  "lang.ark.fed.excel.data.binOrder.theEncodingAlreadyExists":"编码重复",
  "lang.ark.fed.menu.callerConfiguration":"呼叫器",

  // 3.4.0
  "lang.ark.fed.menu.testCase": "任务调试",
  "lang.ark.fed.excel.data.binCode.overLength": "第{0}行货位点编码超出30个字符，请修改后重新导入",
  "lang.ark.fed.excel.data.binOrder.overLength": "第{0}行货位顺序号超出30个字符，请修改后重新导入",
  "lang.ark.apiCommonCode.binCode.overLengthError": "货位点编码超出30个字符",
  "lang.ark.apiCommonCode.binOrder.overLengthError": "货位顺序号超出30个字符",

  // ark嵌套rms地址变动，新增嵌套rms 系统参数配置 和 配置模板 两个页面
  "lang.ark.fed.menu.schedulingParameterConfiguration": "系统参数配置",
  "lang.ark.fed.menu.parameterConfigurationTemplate": "配置模板",
}
