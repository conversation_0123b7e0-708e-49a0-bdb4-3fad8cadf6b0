import { getContainerTypeList } from "gms-apis/container";
import { containerTypeShape } from "gms-constants";

/**
 * 动态加载svg文件
 * @param {*} filename
 * @returns
 */
export async function importSvg(filename) {
  const module = await import(`../../assets/images/container/${filename}.svg`);
  return module.default;
}

// 容器类型和图片文件名的对应关系
// 对于叉车托盘，键值是“容器形态+容器类型+托盘结构+托盘颜色”
// 对于其他容器，键值是“容器形态+容器类型”
const containerImageMap = {
  // 货架
  "PALLET_RACKING.PRECISION_MODELING": "solid-shelf",
  "PALLET_RACKING.FUZZY_MODELING": "special-shelf-thumb",

  // 料车、料柜
  "CONTAINER_BIN.PRECISION_MODELING": "solid-material-box",
  "CONTAINER_BIN.FUZZY_MODELING": "solid-material-box",

  // 笼车
  "TROLLEY.PRECISION_MODELING": "solid-roll-car",
  "TROLLEY.FUZZY_MODELING": "solid-roll-car",

  // 搬运托盘（M系列）
  "PALLET.PRECISION_MODELING": "solid-move-pallet",

  // 叉车托盘（F系列）
  "FORKLIFT_PALLET.PRECISION_MODELING.DOUBLE_DECK_PALLET.WOOD_COLOR": "double-wood-pallet-thumb",
  "FORKLIFT_PALLET.PRECISION_MODELING.DOUBLE_DECK_PALLET.BLUE": "double-blue-pallet-thumb",
  "FORKLIFT_PALLET.PRECISION_MODELING.DOUBLE_DECK_PALLET.OTHER": "double-black-pallet-thumb",
  "FORKLIFT_PALLET.PRECISION_MODELING.SINGLE_DECK_PALLET.WOOD_COLOR": "single-wood-pallet-thumb",
  "FORKLIFT_PALLET.PRECISION_MODELING.SINGLE_DECK_PALLET.BLUE": "single-blue-pallet-thumb",
  "FORKLIFT_PALLET.PRECISION_MODELING.SINGLE_DECK_PALLET.OTHER": "single-black-pallet-thumb",
  "FORKLIFT_PALLET.FUZZY_MODELING.DOUBLE_DECK_PALLET.WOOD_COLOR": "double-wood-pallet-thumb",
  "FORKLIFT_PALLET.FUZZY_MODELING.DOUBLE_DECK_PALLET.BLUE": "double-blue-pallet-thumb",
  "FORKLIFT_PALLET.FUZZY_MODELING.DOUBLE_DECK_PALLET.GRAY": "double-black-pallet-thumb",
  "FORKLIFT_PALLET.FUZZY_MODELING.DOUBLE_DECK_PALLET.OTHER": "double-black-pallet-thumb",
  "FORKLIFT_PALLET.FUZZY_MODELING.SINGLE_DECK_PALLET.WOOD_COLOR": "single-wood-pallet-thumb",
  "FORKLIFT_PALLET.FUZZY_MODELING.SINGLE_DECK_PALLET.BLUE": "single-blue-pallet-thumb",
  "FORKLIFT_PALLET.FUZZY_MODELING.SINGLE_DECK_PALLET.GRAY": "single-black-pallet-thumb",
  "FORKLIFT_PALLET.FUZZY_MODELING.SINGLE_DECK_PALLET.OTHER": "single-black-pallet-thumb",
};

/**
 * 获取容器的哈希键值，方便查找图片文件名
 * @param {object} model
 */
function getHashKey(model) {
  if (!model) {
    return "";
  }

  // 对于叉车托盘，键值是“容器形态+容器类型+托盘结构+托盘颜色”
  if (model.type === containerTypeShape.FORKLIFT_PALLET) {
    return ["type", "modelingMethod", "palletStructure", "surfaceColor"].map((key) => model[key]).join(".");
  }

  // 对于其他容器，键值是“容器形态+容器类型”
  return ["type", "modelingMethod"].map((key) => model[key]).join(".");
}

/**
 * 根据容器参数选择对应的图片
 * @param {object} model
 * @param {string} model.type 容器形态，比如货架、料车等
 * @param {string} model.modelingMethod 容器类型，比如标准容器、异形容器等
 * @param {string} model.palletStructure 托盘结构，仅叉车托盘有效，其余容器均为 null
 * @param {string} model.surfaceColor 托盘颜色，仅叉车托盘有效，其余容器均为 null
 * @returns {string} 图片地址
 */
export const getImgByShape = async (model) => {
  const hashKey = getHashKey(model);
  const filename = containerImageMap[hashKey];
  return await importSvg(filename);
};

/** 获取容器类型的列表，并添加图片 */
export const getPageContainerTypeList = async (params) => {
  let data, total;
  try {
    ({ data, total } = await getContainerTypeList({
      type: containerTypeShape.map((v) => v.value),
      offset: 1,
      limit: 1000,
      ...params,
    }));
    for (const v of data) {
      v.imgUrl = await getImgByShape(v);
    }

    return { data, total };
  } catch (error) {
    console.error(error);
    return { data, total };
  }
};
