<template>
  <div class="tw-flex tw-flex-col tw-relative tw--mt-4">
    <div class="tw-flex tw-justify-end tw-gap-2 tw-my-4">
      <ContainerFilter :options="{ containerTypeList: state.containerTypeList }" @change="handleFilterChange">
        <template #suffix>
          <OperateButtonGroup
            :row-selection="state.rowSelection"
            :record="state.record"
            @confirm="handleButtonConfirm"
          />
        </template>
      </ContainerFilter>
    </div>
    <TableWrapper
      class="tw-pb-12"
      :class="cn.table_wrapper"
      v-bind="tableState"
      @selection-change="handleSelectionChange"
    >
      <template #statusSlot="{ row }">
        <gp-tag :type="containerStatus.find({ value: row.status }, 'color')">{{
          containerStatus.getLabelByValue(row.status)
        }}</gp-tag>
      </template>
    </TableWrapper>

    <DialogAddContainer
      v-if="showContainerDialog"
      :visible="showContainerDialog"
      :options="{ containerTypeList: state.containerTypeList, materialTypeList: state.materialTypeList }"
      :record="state.record"
      :entry-list="state.entryList"
      :machine-state="machineState"
      :send="send"
      v-bind="$attrs"
      @save="handleAddSave"
    />
    <DialogOperateResult
      :visible="state.failDialogVisible"
      :title="state.failTitle"
      :list="state.failList"
      @update:visible="handleResultConfirm"
    />
  </div>
</template>
<script>
import { Message } from "geekplus-ui";
import { defineComponent, reactive, onMounted, computed } from "vue";
import TableWrapper, { useTableState } from "@srpe-fe/uic-el-table-wrapper-vue2";
import {
  batchUnBindPoint,
  deleteContainer,
  getContainerList,
  getMaterialTypeList,
  getPointsList,
  getPalletCodeList,
} from "gms-apis/container";

import { toLabelValueObj } from "gms-utils";
import { codeRule, containerStatus } from "gms-constants";
import { useI18n } from "gms-hooks";
import { ENTRY_BTN, LEAVE_BTN, DELETE_BTN, getTableColumns, ADD_BTN, confirmMsg, deletableStatus } from "./state";
import DialogAddContainer from "./dialog-add-container.vue";
import ContainerFilter from "./container-filter.vue";
import OperateButtonGroup from "./operate-button-group.vue";
import DialogOperateResult from "./dialog-operate-result.vue";
import { getPageContainerTypeList } from "../common.js";
import { useContainerMachine, matchesSome } from "./machine";

const getMaterialTypeOptions = async () => {
  const data = await getMaterialTypeList();
  return data?.map(toLabelValueObj("goodsName", "id"));
};
const getPointCodes = async () => {
  const data = await getPointsList();
  const list = data.map((v) => ({ value: v.cellCode, label: v.hostCellCode ?? v.cellCode }));
  return list;
};
const getPalletCodes = async () => {
  const data = await getPalletCodeList();
  const list = data.map((v) => ({ value: v.palletLatticeCode, label: v.palletLatticeHostCode ?? v.palletLatticeCode }));
  return list;
};

export default defineComponent({
  name: "ContainerList",
  components: {
    TableWrapper,
    DialogAddContainer,
    ContainerFilter,
    OperateButtonGroup,
    DialogOperateResult,
  },

  emits: ["onEdit"],

  setup() {
    const { state: machineState, send } = useContainerMachine();

    const t = useI18n();
    const state = reactive({
      record: {},
      queryParams: "",
      rowSelection: [],
      containerTypeList: [],
      materialTypeList: [],
      batchBtn: "",
      failDialogVisible: false,
      failList: [],
      entryList: [],
      failTitle: "",
      filterParams: {},
      palletCodeList: [],
      pointCodeList: [],
    });

    /** 是否显示容器弹框 */
    const showContainerDialog = computed(() => matchesSome(machineState.value, ["add", "edit", "entry"]));

    const updateState = (newState) => {
      Object.keys(newState).forEach((key) => {
        state[key] = newState[key];
      });
    };

    onMounted(() => {
      getPalletCodes().then((list) => (state.palletCodeList = list));
      getPointCodes().then((list) => (state.pointCodeList = list));
      Promise.all([getPageContainerTypeList(), getMaterialTypeOptions()]).then(
        ([{ data: containerTypeList }, materialTypeList]) => updateState({ containerTypeList, materialTypeList })
      );
    });

    const requestData = async ({ currentPage: offset, pageSize: limit }) => {
      const { data, total } = await getContainerList({
        limit,
        offset,
        ...state.filterParams,
      });

      return {
        data: data.map((v) => ({ ...v, lastModifiedTime: v.lastModifiedTime ? v.lastModifiedTime * 1000 : "" })),
        total,
      };
    };

    // 多选
    const handleSelectionChange = (val) => {
      state.rowSelection = val;
    };

    const handleFilterChange = (filterParams) => {
      state.filterParams = filterParams;
      tableState.query({ currentPage: 1 });
    };

    // toolbar 按钮操作
    const handleButtonConfirm = ({ btn, list }) => {
      if (btn === ADD_BTN) {
        handleAdd();
      } else if (btn === ENTRY_BTN) {
        handleEntry(tableState.data.filter((v) => list.includes(v.id)));
      } else if (btn === LEAVE_BTN) {
        handleLeaveConfirm(list);
      } else if (btn === DELETE_BTN) {
        handleDeleteConfirm(list);
      }
    };

    const handleAdd = async () => {
      await Promise.all([
        getPalletCodes().then((list) => (state.palletCodeList = list)),
        getPointCodes().then((list) => (state.pointCodeList = list)),
        getPageContainerTypeList().then(({ data: containerTypeList }) => (state.containerTypeList = containerTypeList)),
        getMaterialTypeOptions().then((materialTypeList) => (state.materialTypeList = materialTypeList)),
      ]);
      send("CREATE");
      state.record = { codeRule: codeRule.DEFAULT, modelInfo: {}, loadInfo: {} };
    };

    const handleAddSave = (data) => {
      if (data?.length) {
        state.failTitle = t("geekplus.gms.client.screen.container.tips.operationFailed");
        state.failList = data?.filter((v) => !v.isSuccess);
        state.failDialogVisible = state.failList.length > 0;
      }
      if (!state.failDialogVisible) {
        Message.success(t("geekplus.gms.client.commons.tips.operationSuccess"));

        refreshTable();
      }
    };

    // 编辑
    const handleEdit = (row) => {
      state.record = row;
      send("EDIT");
    };

    const refreshTable = () => {
      tableState.query();
    };

    /**
     * 批量离场
     * 当勾选的容器状态存在多种状态时，仅针对容器状态=空闲的数据变更状态
     */
    const handleLeaveConfirm = (list = []) => {
      let ids = JSON.stringify(list.map((v) => String(v)));
      batchUnBindPoint(ids).then((data) => {
        // TODO: dialog显示编码错误原因 编码：原因
        state.failList = data.filter((v) => !v.isSuccess);
        state.failDialogVisible = state.failList.length > 0;
        refreshTable();
      });
    };

    /**
     * 批量删除
     * 仅可删除，容器状态=离场的数据
     */
    const handleDeleteConfirm = (list = []) => {
      let ids = JSON.stringify(list);

      deleteContainer(ids).then(() => {
        Message.success(t("geekplus.gms.client.commons.tips.dataDeleteSuccess"));
        const currentPage = tableState.pagination?.currentPage;
        if (list.length === tableState.data.length && currentPage > 1) {
          // 如果删除整页数据，需要查询上一页数据，防止显示异常
          tableState.query({ currentPage: currentPage - 1 });
        } else {
          refreshTable();
        }
      });
    };

    const handleEntry = (list = []) => {
      send("ENTRY");
      state.entryList = list;
      state.record = list[0];
    };

    const handleResultConfirm = () => {
      refreshTable();
      state.failDialogVisible = false;
    };

    const tableState = useTableState({
      columns: getTableColumns(state, {
        handleEdit,
        handleEntry,
        handleDelete: handleDeleteConfirm,
        handleLeave: handleLeaveConfirm,
      }),
      fetchData: requestData,
    });

    onMounted(() => {
      tableState.query();
    });

    return {
      state,
      tableState,
      confirmMsg,
      containerStatus,
      DELETE_BTN,
      LEAVE_BTN,
      deletableStatus,
      machineState,
      showContainerDialog,
      send,
      handleAddSave,
      handleButtonConfirm,
      handleFilterChange,
      handleEntry,
      handleDeleteConfirm,
      handleLeaveConfirm,
      handleEdit,
      handleSelectionChange,
      handleResultConfirm,
    };
  },
});
</script>
<style lang="scss" module="cn">
.table_wrapper {
  overflow-y: scroll;
}
</style>
