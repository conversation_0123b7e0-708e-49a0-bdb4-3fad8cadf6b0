<template>
  <section class="tw-flex tw-gap-4 tw-flex-col tw-mt-6">
    <h3 class="tw-relative tw-font-bold tw-text-l">
      {{ $t("geekplus.gms.client.screen.container.form.modelInformation") }}
    </h3>
    <div class="tw-flex tw-justify-between tw-items-center tw-gap-2 tw--mt-4">
      <form-item v-bind="formGroup.modelingMethod" />
      <gp-button type="text" @click="exampleVisible = true">
        {{ $t("geekplus.gms.client.screen.container.form.checkExample") }}
      </gp-button>
    </div>
    <div class="tw-flex tw-justify-between tw-items-center tw-gap-2">
      <form-item v-bind="formGroup.shelves" />
      <gp-button type="primary" :disabled="$props.mode === 'view'" @click="handleAddLegs">{{
        $t("geekplus.gms.client.commons.btn.add")
      }}</gp-button>
    </div>
    <section>
      <template v-for="(model, index) in $props.formValues.modelInfo">
        <ShelfElements
          v-bind="$attrs"
          :key="model.key"
          :values="model"
          :index="index"
          :form-values="$props.formValues"
          :mode="$props.mode"
          @update:values="($event) => updateValues($event, index)"
          @update:formValues="$emit('update:formValues', $event)"
          @click.native="$emit('update:active', model.key)"
        />
      </template>
    </section>
    <ExampleModeExplain :visible.sync="exampleVisible" />
  </section>
</template>
<script>
import { uniqueId, cloneDeep } from "lodash";
import { defineComponent, reactive, ref } from "vue";
import { FormItem } from "@srpe-fe/uic-el-form-wrapper-vue2";
import { shelfElementType } from "gms-constants";
import ExampleModeExplain from "../../example-model-explain.vue";
import ShelfElements from "./shelf-elements.vue";

export default defineComponent({
  name: "NoStandardModel",
  components: { FormItem, ShelfElements, ExampleModeExplain },
  props: {
    formValues: { type: Object, default: () => ({}) },
    formGroup: { type: Object, default: () => ({}) },
    mode: { type: String, default: "add" },
  },
  emit: ["update:formGroupList", "update:formValues"],
  setup(props, { emit }) {
    const exampleVisible = ref(false);
    const state = reactive({
      initShelf: {
        type: shelfElementType.LEG,
        length: 40,
        width: 40,
        height: 300,
        x: 50,
        y: 50,
      },
    });

    const getInitShelf = () => {
      state.initShelf = {
        ...state.initShelf,
        x: state.initShelf.x + 10,
        y: state.initShelf.y + 10,
      };

      return { ...state.initShelf, key: uniqueId() };
    };

    const updateValues = ($event, index) => {
      const values = cloneDeep(props.formValues);
      values.modelInfo[index] = $event;
      emit("update:formValues", values);
    };

    const handleAddLegs = () => {
      const values = cloneDeep(props.formValues);
      values.modelInfo.push(getInitShelf());
      emit("update:formValues", values);
    };
    return { exampleVisible, shelfElementType, updateValues, handleAddLegs, getInitShelf };
  },
});
</script>
