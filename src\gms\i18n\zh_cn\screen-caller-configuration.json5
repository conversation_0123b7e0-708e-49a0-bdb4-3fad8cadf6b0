/**
 * 呼叫器页面国际化字符集
 * key 必须以 'geekplus.gms.client.screen.callerConfiguration.' 开头
 */
{
  "geekplus.gms.client.screen.callerConfiguration.title": "呼叫器",
  "geekplus.gms.client.screen.callerConfiguration.subtitle": "添加管理呼叫器并配置呼叫器的按钮功能",
  "geekplus.gms.client.screen.callerConfiguration.createCaller": "新建呼叫器",
  "geekplus.gms.client.screen.callerConfiguration.callerDetails": "呼叫器详情",
  "geekplus.gms.client.screen.callerConfiguration.callerButtonConfig": "呼叫器按钮配置",
  "geekplus.gms.client.screen.callerConfiguration.id": "编号",
  "geekplus.gms.client.screen.callerConfiguration.name": "名称",
  "geekplus.gms.client.screen.callerConfiguration.ip address": "IP地址",
  "geekplus.gms.client.screen.callerConfiguration.status": "状态",
  "geekplus.gms.client.screen.callerConfiguration.editor": "编辑人",
  "geekplus.gms.client.screen.callerConfiguration.edit time": "编辑时间",
  "geekplus.gms.client.screen.callerConfiguration.allocation": "配置",
  "geekplus.gms.client.screen.callerConfiguration.deletion": "删除",
  "geekplus.gms.client.screen.callerConfiguration.deletionSuccess": "删除成功",
  "geekplus.gms.client.screen.callerConfiguration.deletionErroe": "删除失败",
  "geekplus.gms.client.screen.callerConfiguration.saveSuccess": "保存成功",
  "geekplus.gms.client.screen.callerConfiguration.saveErroe": "保存失败",
  "geekplus.gms.client.screen.callerConfiguration.FourButtonCallerType":"呼叫器类型:4键呼叫器",
  "geekplus.gms.client.screen.callerConfiguration.callerid":"呼叫器编号",
  "geekplus.gms.client.screen.callerConfiguration.started":"已启用",
  "geekplus.gms.client.screen.callerConfiguration.not started":"未启用",
  "geekplus.gms.client.screen.callerConfiguration button number":"按钮编码",
  "geekplus.gms.client.screen.callerConfiguration trigger ":"触发",
  "geekplus.gms.client.screen.callerConfiguration.timerange":"时间范围",
  "geekplus.gms.client.screen.callerConfiguration.starttime":"开始时间",
  "geekplus.gms.client.screen.callerConfiguration.endtime":"结束时间",
  "geekplus.gms.client.screen.callerConfiguration.callercode":"呼叫器编码",
  "geekplus.gms.client.screen.callerConfiguration.caller id number":"呼叫器编号",
  "geekplus.gms.client.screen.callerConfiguration.button code":"按钮编号",
  "geekplus.gms.client.screen.callerConfiguration.execute content":"执行内容",
  "geekplus.gms.client.screen.callerConfiguration.message":"报文内容",
  "geekplus.gms.client.screen.callerConfiguration.executiTime":"执行时间",
  "geekplus.gms.client.screen.callerConfiguration.execution result":"执行结果",
  "geekplus.gms.client.screen.callerConfiguration.confirmDelete":"确定要删除此呼叫器配置吗？",
  "geekplus.gms.client.screen.callerConfiguration.confirmDelete.description":"请填入必填项",
  "geekplus.gms.client.screen.callerConfiguration.save.description":"保存",
  "geekplus.gms.client.screen.callerConfiguration.saveAndNext.description":"保存并下一步",
  "geekplus.gms.client.screen.callerConfiguration.cancel.description":"取消",
  "geekplus.gms.client.screen.callerConfiguration.edit.description":"编辑",
  "geekplus.gms.client.screen.callerConfiguration.callerName.description":"呼叫器名称",
  "geekplus.gms.client.screen.callerConfiguration.inputPrompt.description":"请输入",
  "geekplus.gms.client.screen.callerConfiguration.callerBoxUniqueCode":"呼叫器盒子的唯一编号",
  "geekplus.gms.client.screen.callerConfiguration.buttonOne":"按钮一",
  "geekplus.gms.client.screen.callerConfiguration.buttonTwo":"按钮二",
  "geekplus.gms.client.screen.callerConfiguration.buttonThree":"按钮三",
  "geekplus.gms.client.screen.callerConfiguration.buttonFour":"按钮四",
  "geekplus.gms.client.screen.callerConfiguration.buttonEnable":"按钮启用",
  "geekplus.gms.client.screen.callerConfiguration.buttonFunction":"按钮功能",
  "geekplus.gms.client.screen.callerConfiguration.fillRequiredFields":"填入必填项",
  "geekplus.gms.client.screen.callerConfiguration.pleaseSelect":"请选择",
  "geekplus.gms.client.screen.callerConfiguration.processFlow":"流程",
  "geekplus.gms.client.screen.callerConfiguration.triggerLocation":"触发位置",
  "geekplus.gms.client.screen.callerConfiguration.containerType":"容器类型",
  "geekplus.gms.client.screen.callerConfiguration.containerAngle":"容器角度",
  "geekplus.gms.client.screen.callerConfiguration.triggerLocation":"触发位置",
  "geekplus.gms.client.screen.callerConfiguration.entryLocation":"入场位置",
  "geekplus.gms.client.screen.callerConfiguration.exitLocation":"离场位置",
  "geekplus.gms.client.screen.callerConfiguration.triggerSuccess":"触发成功",
  "geekplus.gms.client.screen.callerConfiguration.configured":"已配置",
  "geekplus.gms.client.screen.callerConfiguration.notConfigured":"未配置",
  "geekplus.gms.client.screen.callerConfiguration.trigger":"触发",
}