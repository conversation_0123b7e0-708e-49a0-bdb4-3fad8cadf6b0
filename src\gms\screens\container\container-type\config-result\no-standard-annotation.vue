<template>
  <div
    class="tw-relative tw-h-full tw-w-full tw-flex tw-flex-col tw-justify-around tw-items-center tw-gap-4 tw-py-4 tw-pl-10"
  >
    <v-stage ref="stage" :config="stageConfig">
      <v-layer ref="layer">
        <v-rect :config="stageRectConfig" />
        <template v-for="model in $props.formValues.modelInfo.filter((v) => v.type === shelfElementType.LEG)">
          <v-rect
            :key="model.key"
            :config="{
              ...transformParam(model, active, ratio),
              draggable: false,
              strokeScaleEnabled: false,
            }"
            @tap="updateSelectedShape(model.key)"
            @click="updateSelectedShape(model.key)"
          />
          <v-rect :key="`${model.key}Point`" :config="getLegPointConfig(model, ratio)"></v-rect>
          <!-- <v-text :key="`${model.key}NameText`" :config="getLegNameConfig(model, index, ratio)" /> -->
          <v-text :key="`${model.key}LengthText`" :config="getLengthTextConfig(model, ratio)" />
          <v-text :key="`${model.key}WidthText`" :config="getWidthTextConfig(model, ratio)" />
          <v-line :key="`${model.key}LengthLine`" :config="getLegLengthLine(model, ratio)" />
          <v-line :key="`${model.key}widthLine`" :config="getLegWidthLine(model, ratio)" />
          <v-line :key="`${model.key}xToOriginLine`" :config="getXToOriginLine(model, ratio)" />
          <v-line :key="`${model.key}yToOriginLine`" :config="getYToOriginLine(model, ratio)" />
          <v-text :key="`${model.key}xToOriginText`" :config="getXToOriginText(model, ratio)" />
          <v-text :key="`${model.key}YToOriginText`" :config="getYToOriginText(model, ratio)" />
        </template>

        <v-arrow :config="xArrowConfig" />
        <v-arrow :config="yArrowConfig" />
        <v-rect :config="originPointConfig"></v-rect>
        <v-text :config="textConfig" />
        <v-text :config="xTextConfig" />
        <v-text :config="yTextConfig" />
      </v-layer>
    </v-stage>
    <div class="tw-text-center">
      <img :src="overlookRobotImg" class="tw-w-10" alt="" />
      <p class="tw-text-sm">{{ $t("geekplus.gms.client.screen.container.form.shelfOverLookDiagram") }}</p>
      <p class="tw-sub-title">{{ $t("geekplus.gms.client.screen.container.form.measureYAxisMethod") }}</p>
    </div>
    <div class="tw-absolute tw-top-4 tw-left-4 tw-text-center">
      <h3 class="tw-mb-2 tw-title">
        {{ $t("geekplus.gms.client.screen.container.form.specialShelfModelConfig") }}
      </h3>
      <img :src="specialShelfImg" class="2xl:tw-w-48 tw-w-24" alt="" />
      <p class="tw-text-sm tw-mt-4">
        {{ $t("geekplus.gms.client.screen.container.form.shelf3DDiagram") }}
      </p>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, watchEffect } from "vue";
import { shelfElementType } from "gms-constants";
import getContainerImage from "@/utils/containerImages";
const specialShelfImg = getContainerImage("special-shelf.svg");
import overlookRobotImg from "@/gms/assets/images/container/overlook-robot.svg";
import useKonva, { originPoint, originX, originY, containerWidth } from "@/gms/screens/container/useKonva";

export default defineComponent({
  name: "NoStandardAnnotation",
  components: {},
  props: {
    formValues: { type: Object, default: () => ({}) },
    active: { type: String, default: "" },
  },
  emits: ["update:active"],
  setup(props, { emit }) {
    const layer = ref(null);
    const stageRectConfig = ref({
      ...originPoint,
      width: containerWidth * 2.2,
      height: containerWidth * 2.2,
      offsetX: originX * 2.2,
      offsetY: originY * 2.2,
      stroke: "gray",
      strokeWidth: 10,
    });

    const ratio = ref(1);
    watchEffect(() => {
      const faceModel = props.formValues.modelInfo.find((v) => v.type === shelfElementType.FACE) ?? {};
      ratio.value = faceModel.length / stageRectConfig.value.height;
    });

    const updateSelectedShape = (key) => {
      emit("update:active", key);
    };

    return {
      stageRectConfig,
      layer,
      overlookRobotImg,
      specialShelfImg,
      shelfElementType,
      updateSelectedShape,
      ...useKonva("shelf", ratio),
      ratio,
    };
  },
});
</script>
