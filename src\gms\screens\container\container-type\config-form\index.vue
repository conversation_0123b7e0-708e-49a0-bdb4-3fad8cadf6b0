<template>
  <div class="tw-flex-grow tw-flex tw-flex-col tw-items-stretch tw-h-full">
    <h3 v-if="$props.mode !== 'view'" class="tw-title tw-mb-3">
      {{ $t("geekplus.gms.client.screen.container.form.containerType.step2") }}
    </h3>
    <div
      class="tw-flex-1 tw-flex tw-justify-between tw-items-stretch sm:tw-gap-4 md:tw-gap-8 tw-pr-0"
      :class="cn.form_wrapper"
    >
      <ConfigResult
        class="tw-flex-1 tw-border tw-border-gray-400 tw-border-solid"
        :form-values="$props.formValues"
        :active="activeItem"
        @update:active="updateActiveItem"
        @update:formValues="$emit('update:formValues', $event)"
      />
      <gp-form @click.native="handleFormClick" @blur.native="handleFormBlur" @mouseleave.native="handleFormBlur">
        <div class="tw-w-90 tw-pl-4 tw-h-full tw-max-h-full tw-overflow-y-scroll">
          <section class="tw-flex tw-flex-col" :class="viewClass">
            <h3 class="tw-relative tw-font-bold tw-text-l">
              {{ $t("geekplus.gms.client.screen.container.form.BasicInformation") }}
            </h3>
            <form-item v-bind="$props.formGroup.type" />
            <form-item v-bind="$props.formGroup.code" />
            <form-item v-bind="$props.formGroup.name" />
          </section>
          <ModelInfo
            v-bind="$props"
            :active.sync="activeItem"
            @update:formGroupList="$emit('update:formGroupList', $event)"
            @update:formValues="$emit('update:formValues', $event)"
            @update:active="updateActiveItem"
          />
        </div>
      </gp-form>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, computed } from "vue";
import { FormItem } from "@srpe-fe/uic-el-form-wrapper-vue2";
import { containerModel, containerTypeShape } from "gms-constants";
import ModelInfo from "../model-info";
import ConfigResult from "../config-result";

export default defineComponent({
  name: "ConfigForm",
  components: { FormItem, ModelInfo, ConfigResult },
  props: {
    formValues: { type: Object, default: () => ({}) },
    formGroup: { type: Object, default: () => ({}) },
    record: { type: Object, default: () => ({}) },
    mode: { type: String, default: "add" },
    step: { type: Number, default: 2 },
    isSubmiting: { type: Boolean, default: false },
    formGroupList: { type: Array, default: () => [] },
    handleSubmit: { type: Function, default: () => {} },
  },
  emit: ["update:formGroupList", "update:formValues"],
  setup(props, { emit }) {
    const activeItem = ref(null);
    const updateStep = () => {
      emit("update:step", 1);
    };

    const isStandard = computed(() => props.formValues.modelingMethod === containerModel.STANDARD);
    const viewClass = computed(() => [
      props.mode !== "view" ? "tw-gap-4" : "",
      props.formValues.type === containerTypeShape.FORKLIFT_PALLET ? "normal_width" : "",
    ]);

    const updateActiveItem = ($event) => {
      activeItem.value = $event;
    };

    const handleFormClick = (e) => {
      if (isStandard.value) {
        activeItem.value = e.target.name;
      }
    };

    const handleFormBlur = () => {
      if (isStandard.value) {
        activeItem.value = "";
      }
    };

    return { activeItem, containerTypeShape, viewClass, handleFormClick, handleFormBlur, updateStep, updateActiveItem };
  },
});
</script>
<style lang="scss" module="cn">
.form_wrapper {
  min-height: 620px;
  overflow-x: scroll;
  ::-webkit-scrollbar {
    width: 0;
  }
}
:global(.normal_width) {
  width: calc(100% - 20px);
}
</style>
