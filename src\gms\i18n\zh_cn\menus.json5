/**
 *   菜单国际化字符集
 *   key 必须以 'geekplus.gms.client.menus.' 开头
 */

{
  "geekplus.gms.client.menus.dashboard": "首页",
  "geekplus.gms.client.menus.modeling": "场地配置",
  "geekplus.gms.client.menus.operating": "运营",

  // ======== 地图 ======== //
  "geekplus.gms.client.menus.map": "地图",
  "geekplus.gms.client.menus.mapConfig": "地图配置",
  "geekplus.gms.client.menus.areaConfig": "区域配置",
  "geekplus.gms.client.menus.stockConfig": "托盘位配置",
  "geekplus.gms.client.menus.waitNodeConfig": "等待点交互配置",
  "geekplus.gms.client.menus.workstations": "工作站配置",

  // ======== 机器人 ======== //
  "geekplus.gms.client.menus.robot": "机器人",
  "geekplus.gms.client.menus.robotConfig": "机器人配置",
  "geekplus.gms.client.menus.compositeRobotConfig": "复合机器人配置",
  "geekplus.gms.client.menus.robotParams": "机器人参数",
  "geekplus.gms.client.menus.robotStrategy": "机器人策略",
  "geekplus.gms.client.menus.robotSoftwareVersion": "软件版本",
  "geekplus.gms.client.menus.robotUpgradeLog": "升级日志",

  "geekplus.gms.client.menus.container": "容器",
  "geekplus.gms.client.menus.scaffold": "支架",
  "geekplus.gms.client.menus.scaffoldModeling": "支架模型",
  "geekplus.gms.client.menus.material": "物料",

  "geekplus.gms.client.menus.equipment": "设备",
  "geekplus.gms.client.menus.equipmentModeling": "设备模型",
  "geekplus.gms.client.menus.equipmentInstance": "设备实例",
  "geekplus.gms.client.menus.elevatorBind": "提升机关联信息",
  "geekplus.gms.client.menus.templateInstance": "设备实例模板",
  "geekplus.gms.client.menus.taskManagement": "设备任务",
  "geekplus.gms.client.menus.appMaintenance": "应用维护",
  "geekplus.gms.client.menus.heartbeatManagement": "设备心跳",
  "geekplus.gms.client.menus.pdaConfig": "PDA配置",
  "geekplus.gms.client.menus.callmakerConfig": "呼叫器配置",
  "geekplus.gms.client.menus.buttonModel": "按钮模型配置",
  "geekplus.gms.client.menus.buttonFunction": "按钮功能配置",
  "geekplus.gms.client.menus.stopControllerManage": "急停控制器",

  "geekplus.gms.client.menus.process": "流程",
  "geekplus.gms.client.menus.processConfig": "流程配置(新)",
  "geekplus.gms.client.menus.processNodeConfig": "交互配置",
  "geekplus.gms.client.menus.processRuleConfig": "规则配置",
  "geekplus.gms.client.menus.processTemplate": "流程模板",
  "geekplus.gms.client.menus.processConfigOld": "流程",
  "geekplus.gms.client.menus.processCategory": "流程分类",

  "geekplus.gms.client.menus.schedule": "调度",
  "geekplus.gms.client.menus.scheduleParamConfig": "调度参数配置",
  "geekplus.gms.client.menus.regionalScheduleManage": "分区调度管理",

  "geekplus.gms.client.menus.interface": "系统接口",
  "geekplus.gms.client.menus.callbackConfig": "通知回调配置",
  "geekplus.gms.client.menus.apiDoc": "接口文档及调试",
  "geekplus.gms.client.menus.interfaceConfig": "接口参数配置",

  "geekplus.gms.client.menus.systemConfig": "系统配置",
  "geekplus.gms.client.menus.systemParamsConfig": "系统参数配置",
  "geekplus.gms.client.menus.dispatchParamsConfig": "调度参数配置",
  "geekplus.gms.client.menus.sensorTriggerTask": "传感器触发任务",
  "geekplus.gms.client.menus.licenseManagement": "证书管理",
  "geekplus.gms.client.menus.intervalTaskConfig": "定时任务配置",
  "geekplus.gms.client.menus.personalizedConfig": "系统个性化配置",

  "geekplus.gms.client.menus.monitoring": "监控",
  "geekplus.gms.client.menus.taskDashboard": "任务看板",
  "geekplus.gms.client.menus.siteMonitoring": "场地监控",
  "geekplus.gms.client.menus.taskMonitoring": "任务监控",
  "geekplus.gms.client.menus.limitingAreaMonitoring": "管制区域监控",
  "geekplus.gms.client.menus.robotMonitoring": "机器人监控",
  "geekplus.gms.client.menus.deviceTaskMonitoring": "设备任务监控",
  "geekplus.gms.client.menus.chargeStationMonitoring": "充电站监控",
  "geekplus.gms.client.menus.maintenanceSchedule": "维修调度",

  "geekplus.gms.client.menus.log": "日志",
  "geekplus.gms.client.menus.taskLog": "任务日志",
  "geekplus.gms.client.menus.operationLog": "操作日志",
  "geekplus.gms.client.menus.interfaceLog": "接口日志",
  "geekplus.gms.client.menus.containerLog": "容器日志",
  "geekplus.gms.client.menus.triggerLog": "触发器执行日志",
  "geekplus.gms.client.menus.callmakerLog": "呼叫器日志",

  "geekplus.gms.client.menus.permission": "权限",
  "geekplus.gms.client.menus.userManagement": "用户管理",
  "geekplus.gms.client.menus.roleManagement": "角色管理",

  // "geekplus.gms.client.menus.util": "工具",
  // "geekplus.gms.client.menus.robotExceptionHandling": "机器人异常处理",

  "geekplus.gms.client.menus.settings.account": "账号管理",
  "geekplus.gms.client.menus.settings.languages": "语言切换",
  "geekplus.gms.client.menus.settings.themes": "主题切换",
  "geekplus.gms.client.menus.settings.logout": "登出",

  "geekplus.gms.client.menus.language.zh-cn": "中文",
  "geekplus.gms.client.menus.language.en-us": "英文",

  "geekplus.gms.client.menus.theme.system": "跟随系统",
  "geekplus.gms.client.menus.theme.light": "浅色主题",
  "geekplus.gms.client.menus.theme.dark": "深色主题",

  "geekplus.gms.client.menus.downloads.tip": "请扫码下载",
  "geekplus.gms.client.menus.downloads.document": "帮助文档",
  "geekplus.gms.client.menus.downloads.operation": "GMS-操作手册",
  "geekplus.gms.client.menus.downloads.interface": "GMS-接口文档",
  "geekplus.gms.client.menus.downloads.PDADocument": "PDA手册",
  "geekplus.gms.client.menus.downloads.downloadApp": "PDA应用下载",
  "geekplus.gms.client.menus.downloads.PDA": "PDA",
  "geekplus.gms.client.menus.downloads.deviceKeyValue": "码表(设备指令)",
  "geekplus.gms.client.menus.downloads.device": "设备码表",
  "geekplus.gms.client.menus.downloads.aboutGMS": "关于GMS",
}
