{
  "geekplus.gms.client.commons.btn.moreAction": "Filters",
  "geekplus.gms.client.commons.btn.reset": "Reset",
  "geekplus.gms.client.commons.btn.submit": "Submit",
  "geekplus.gms.client.commons.btn.query": "Search",
  "geekplus.gms.client.commons.btn.save": "Save",
  "geekplus.gms.client.commons.btn.cancel": "Cancel",
  "geekplus.gms.client.commons.btn.cancelView": "Cancel viewing",
  "geekplus.gms.client.commons.btn.saveAndNext": "Save and next",
  "geekplus.gms.client.commons.btn.edit": "Edit",
  "geekplus.gms.client.commons.btn.add": "Add",
  "geekplus.gms.client.commons.btn.refresh": "Refresh",
  "geekplus.gms.client.commons.btn.cancelEditing": "Cancel editing",
  "geekplus.gms.client.commons.btn.details": "Details",
  "geekplus.gms.client.commons.btn.delete": "Delete",
  "geekplus.gms.client.commons.btn.batchDelete": "Batch deleting",
  "geekplus.gms.client.commons.btn.nextStep": "Next",
  "geekplus.gms.client.commons.btn.prevStep": "Previous",
  "geekplus.gms.client.commons.btn.confirm": "OK",
  "geekplus.gms.client.commons.btn.cancelEdit": "Cancel editing",
  "geekplus.gms.client.commons.btn.start": "Start",
  "geekplus.gms.client.commons.btn.pause": "Pause",
  "geekplus.gms.client.commons.btn.recover": "Recovery",
  "geekplus.gms.client.commons.btn.closeDialog": "Close the window",
  "geekplus.gms.client.commons.btn.close": "Close",
  "geekplus.gms.client.commons.btn.publish": "Released by",
  "geekplus.gms.client.commons.btn.update": "Update",
  "geekplus.gms.client.commons.btn.expand": "Unfold",
  "geekplus.gms.client.commons.btn.clollapse": "Collapse",
  "geekplus.gms.client.commons.btn.forceCancel": "Force to cancel",
  "geekplus.gms.client.commons.btn.forceComplete": "Force to complete",
  "geekplus.gms.client.commons.btn.givpUp": "Abandon",
  "geekplus.gms.client.commons.btn.done": "Complete",
  "geekplus.gms.client.commons.btn.confirmDone": "Confirm completion",
  "geekplus.gms.client.commons.btn.confirmResend": "Confirm resending",
  "geekplus.gms.client.commons.btn.completeAndRelease": "Complete and unlock",
  "geekplus.gms.client.commons.btn.resendDeviceTask": "Resend the device command",
  "geekplus.gms.client.commons.btn.manualCompleteDeviceTask": "Skip the device command",
  "geekplus.gms.client.commons.btn.manualComplete": "Complete",
  "geekplus.gms.client.commons.btn.gotIt": "I got it",
  "geekplus.gms.client.commons.btn.config": "Configuration",
  "geekplus.gms.client.commons.btn.copy": "Copy",
  "geekplus.gms.client.commons.btn.copyLink": "Copy Address",
  "geekplus.gms.client.commons.btn.visit": "Visit",
  "geekplus.gms.client.commons.btn.tryLater": "Try again later",
  "geekplus.gms.client.commons.btn.downloading": "Downloading",
  "geekplus.gms.client.commons.btn.completeTask": "Confirm to complete the task",
  "geekplus.gms.client.commons.btn.suspend": "Suspend",
  "geekplus.gms.client.commons.btn.import": "Import",
  "geekplus.gms.client.commons.btn.export": "Export",
  "geekplus.gms.client.commons.btn.view": "View",
  "geekplus.gms.client.commons.btn.logDownload": "Log download",
  "geekplus.gms.client.commons.text.fileImport": "File import",
  "geekplus.gms.client.commons.text.downloadDataTpl": "Template download",
  "geekplus.gms.client.commons.text.importSuccessfully": "Import successful",
  "geekplus.gms.client.commons.label.templateType": "Template type",
  "geekplus.gms.client.commons.confirmDelete": "Confirm deletion",
  "geekplus.gms.client.commons.assignStrategy": "Assignment strategy",
  "geekplus.gms.client.commons.dialogTitle.confirm": "Confirm",
  "geekplus.gms.client.commons.cols.id": "ID",
  "geekplus.gms.client.commons.cols.actions": "Operation",
  "geekplus.gms.client.commons.cols.updateTime": "Edit time",
  "geekplus.gms.client.commons.cols.updateUser": "Editor",
  "geekplus.gms.client.commons.cols.createBy": "Creator",
  "geekplus.gms.client.commons.cols.createTime": "Creation time",
  "geekplus.gms.client.commons.cols.lastModifiedTime": "Last edit time",
  "geekplus.gms.client.commons.cols.lastModifiedBy": "Last editor",
  "geekplus.gms.client.commons.cols.index": "SN",
  "geekplus.gms.client.commons.cols.size": "Size",
  "geekplus.gms.client.commons.tips.pleaseSelect": "Please choose",
  "geekplus.gms.client.commons.tips.confirmDelete": "Confirm deletion",
  "geekplus.gms.client.commons.tips.copied": "Copied",
  "geekplus.gms.client.commons.tips.copySucceed": "Copy succeeded",
  "geekplus.gms.client.commons.tips.confirmDeleteMsg": "The data cannot be recovered after deletion. Are you sure to delete them?",
  "geekplus.gms.client.commons.tips.dataDeleteSuccess": "Deleted successfully",
  "geekplus.gms.client.commons.tips.prompt": "Prompt",
  "geekplus.gms.client.commons.tips.operationSuccess": "Operation succeeded",
  "geekplus.gms.client.commons.tips.cancelSuccess": "Canceled successfully",
  "geekplus.gms.client.commons.tips.uploadA": "Only {sizeLimit} excel files ({fileTypes}) can be uploaded.",
  "geekplus.gms.client.commons.tips.uploadSucceedA": "A total of {count} data are imported.",
  "geekplus.gms.client.commons.tips.tempalteDownload": "Download template",
  "geekplus.gms.client.commons.tips.dataSaveInProgress": "Saving data...",
  "geekplus.gms.client.commons.unit.times": "Times",
  "geekplus.gms.client.commons.agvAction.load": "Feed",
  "geekplus.gms.client.commons.agvAction.unload": "Discharge",
  "geekplus.gms.client.commons.agvAction.equipLoad": "Feed by device",
  "geekplus.gms.client.commons.agvAction.equipLoadEmpty": "Emptily feed by device",
  "geekplus.gms.client.commons.agvAction.equipUnload": "Discharge by device",
  "geekplus.gms.client.commons.agvAction.equipUnloadEmpty": "Emptily discharge by device",
  "geekplus.gms.client.commons.agvAction.equipUnloadFull": "Fully discharge by device",
  "geekplus.gms.client.commons.agvAction.equipLoadFull": "Fully feed by device",
  "geekplus.gms.client.commons.agvAction.loadFullUnloadEmpty": "Fully feed and fully discharge",
  "geekplus.gms.client.commons.agvAction.loadEmptyUnloadFull": "Emptily feed and fully discharge",
  "geekplus.gms.client.commons.agvAction.pick": "Fetch",
  "geekplus.gms.client.commons.agvAction.pickEmpty": "Fetch empty tote",
  "geekplus.gms.client.commons.agvAction.pickMaterial": "Fetching",
  "geekplus.gms.client.commons.agvAction.pickMaterialFull": "Fetch full materials",
  "geekplus.gms.client.commons.agvAction.drop": "Place",
  "geekplus.gms.client.commons.agvAction.dropEmpty": "Place empty tote",
  "geekplus.gms.client.commons.agvAction.dropMaterial": "Placement",
  "geekplus.gms.client.commons.agvAction.dropMaterialFull": "Place full materials",
  "geekplus.gms.client.commons.status.empty": "Empty",
  "geekplus.gms.client.commons.status.full": "Full",
  "geekplus.gms.client.commons.status.loaded": "Load",
  "geekplus.gms.client.commons.status.noLoad": "No Load",
  "geekplus.gms.client.commons.equip.cacheRack": "Buffer shelf",
  "geekplus.gms.client.commons.equip.machine": "Machine",
  "geekplus.gms.client.commons.name.robotId": "Robot ID",
  "geekplus.gms.client.commons.name.robotModel": "Robot model",
  "geekplus.gms.client.commons.name.mapNodeCode": "Location code",
  "geekplus.gms.client.commons.name.cargoCode": "Shelf bin code",
  "geekplus.gms.client.commons.name.cargoOrderNo": "Shelf bin SN",
  "geekplus.gms.client.commons.name.shift": "Offset",
  "geekplus.gms.client.commons.name.dockHeight": "Docking height",
  "geekplus.gms.client.commons.name.dropHeight": "Placement height",
  "geekplus.gms.client.commons.name.pickHeight": "Fetching height",
  "geekplus.gms.client.commons.name.offsetX": "X-axis offset of upper structure",
  "geekplus.gms.client.commons.name.businessScene": "Business scenario",
  "geekplus.gms.client.commons.name.axisA": "A axis",
  "geekplus.gms.client.commons.name.axisB": "B axis",
  "geekplus.gms.client.commons.name.axisAB": "A/B axis",
  "geekplus.gms.client.commons.name.equipType": "Equipment type",
  "geekplus.gms.client.commons.name.nodeCode": "Node code",
  "geekplus.gms.client.commons.name.workflowTemplate": "Workflow Template",
  "geekplus.gms.client.commons.name.none": "Null",
  "geekplus.gms.client.commons.title.language": "Language",
  "geekplus.gms.client.commons.title.edit": "Edit",
  "geekplus.gms.client.commons.title.detail": "Details",
  "geekplus.gms.client.commons.validator.invalidCode": "A maximum of {num} characters can be entered, including letters and numbers.",
  "geekplus.gms.client.commons.validator.invalidName": "A maximum of {num} characters can be entered, including Chinese characters, letters, and numbers.",
  "geekplus.gms.client.commons.validator.required": "Please enter the required items.",
  "geekplus.gms.client.commons.validator.pleaseInputNumbersFrom1To50": "Please enter a number between 1 and 50.",
  "geekplus.gms.client.commons.validator.maximumChars": "Please enter a maximum of {num} characters.",
  "geekplus.gms.client.commons.validator.positiveInteger": "Please enter a positive integer.",
  "geekplus.gms.client.commons.validator.upTo4Bits": "A maximum of four letters can be entered.",
  "geekplus.gms.client.commons.form.viewExample": "View the example",
  "geekplus.gms.client.commons.emptyMsg": "No data",
  "geekplus.gms.client.commons.please": "Go",
  "geekplus.gms.client.commons.constants.workstationVisitAuth.all": "Accessible to all users",
  "geekplus.gms.client.commons.constants.workstationVisitAuth.some": "Accessible to specified users",
  "geekplus.gms.client.commons.constants.workstationSettingApplyMethod.all": "All workstations take effect",
  "geekplus.gms.client.commons.constants.workstationSettingApplyMethod.some": "Specified workstations take effect",
  "geekplus.gms.client.commons.constants.workstationDockingPointStatus.free": "Idle",
  "geekplus.gms.client.commons.constants.workstationDockingPointStatus.arrived": "Arrived",
  "geekplus.gms.client.commons.constants.containerStatus.idle": "Idle",
  "geekplus.gms.client.commons.constants.containerStatus.working": "At work",
  "geekplus.gms.client.commons.constants.containerStatus.leave": "Leave",
  "geekplus.gms.client.commons.constants.containerStatus.draft": "Draft",
  "geekplus.gms.client.commons.constants.containerStatus.locked": "Locked",
  "geekplus.gms.client.commons.constants.containerTypeShape.shelf": "Shelf",
  "geekplus.gms.client.commons.constants.containerTypeShape.materialVehicle": "Trolley and material cabinet",
  "geekplus.gms.client.commons.constants.containerTypeShape.materialBox": "Tote",
  "geekplus.gms.client.commons.constants.containerTypeShape.rollCintainer": "Cage car",
  "geekplus.gms.client.commons.constants.containerTypeShape.movePallet": "Handling pallet (M series)",
  "geekplus.gms.client.commons.constants.containerTypeShape.forkliftPallet": "Forklift pallet (F series)",
  "geekplus.gms.client.commons.constants.containerTypeShape.materialVehicleBox": "Trolley and material cabinet",
  "geekplus.gms.client.commons.constants.codeRule.default": "Default rule",
  "geekplus.gms.client.commons.constants.codeRule.customized": "Custom rule",
  "geekplus.gms.client.commons.constants.loadingStatus.loaded": "Full (loaded)",
  "geekplus.gms.client.commons.constants.loadingStatus.unloaded": "Empty",
  "geekplus.gms.client.commons.constants.containerModel.standard": "Standard container",
  "geekplus.gms.client.commons.constants.containerModel.not_standard": "Special-shaped container",
  "geekplus.gms.client.commons.constants.containerModel.forklift": "Forklift pallet model",
  "geekplus.gms.client.commons.constants.palletSpeciType.standard": "Standard pallet - regular (recommended)",
  "geekplus.gms.client.commons.constants.palletSpeciType.special": "Standard pallet - special",
  "geekplus.gms.client.commons.constants.palletStructureType.single": "Single hole tray",
  "geekplus.gms.client.commons.constants.palletStructureType.double": "Double hole tray",
  "geekplus.gms.client.commons.constants.shelfElementType.shelfFace": "Container dimensions",
  "geekplus.gms.client.commons.constants.shelfElementType.shelfLeg": "Container leg",
  "geekplus.gms.client.commons.constants.palletMaterialType.wood": "Wooden",
  "geekplus.gms.client.commons.constants.palletMaterialType.plastics": "Plastic",
  "geekplus.gms.client.commons.constants.palletMaterialType.matal": "Metal",
  "geekplus.gms.client.commons.constants.palletMaterialType.other": "Others",
  "geekplus.gms.client.commons.constants.palletColorType.woody": "Wood color",
  "geekplus.gms.client.commons.constants.palletColorType.blue": "Blue",
  "geekplus.gms.client.commons.constants.palletColorType.black": "Black/Gray",
  "geekplus.gms.client.commons.constants.palletColorType.other": "Others",
  "geekplus.gms.client.commons.constants.bracketShapeType.ground": "Ground bracket",
  "geekplus.gms.client.commons.constants.bracketModelType.standard": "Standard bracket",
  "geekplus.gms.client.commons.constants.bracketModelType.special": "Special-shaped bracket",
  "geekplus.gms.client.commons.constants.bracketElementType.face": "Bracket dimensions",
  "geekplus.gms.client.commons.constants.bracketElementType.leg": "Bracket leg",
  "geekplus.gms.client.commons.constants.processStatus.inDesign": "In design",
  "geekplus.gms.client.commons.constants.processStatus.published": "Published",
  "geekplus.gms.client.commons.constants.stopPointTypes.common": "Map location",
  "geekplus.gms.client.commons.constants.stopPointTypes.pallet": "Pallet position",
  "geekplus.gms.client.commons.constants.agvClass.forklift": "Forklift",
  "geekplus.gms.client.commons.constants.agvClass.roller": "Roller",
  "geekplus.gms.client.commons.constants.agvClass.carrier": "Latent",
  "geekplus.gms.client.commons.constants.agvClass.topModule": "Composite",
  "geekplus.gms.client.commons.constants.testStatus.inProgress": "Executing",
  "geekplus.gms.client.commons.constants.testStatus.paused": "In pause",
  "geekplus.gms.client.commons.constants.testStatus.done": "Completed",
  "geekplus.gms.client.commons.constants.testStatus.suspend": "Suspended",
  "geekplus.gms.client.commons.constants.testStatus.waiting": "To be executed",
  "geekplus.gms.client.commons.constants.FBLROrientations.front": "F (front)",
  "geekplus.gms.client.commons.constants.FBLROrientations.back": "B (back)",
  "geekplus.gms.client.commons.constants.FBLROrientations.left": "L (left)",
  "geekplus.gms.client.commons.constants.FBLROrientations.right": "R (right)",
  "geekplus.gms.client.commons.constants.taskLaunchRule": "Task initiation strategy",
  "geekplus.gms.client.commons.constants.taskLaunchRule.none": "Make no judgment whether there are idle robots",
  "geekplus.gms.client.commons.constants.taskLaunchRule.queue": "Tasks queue up due to no idle robots available.",
  "geekplus.gms.client.commons.constants.taskLaunchRule.fail": "Task failed due to no idle robot available.",
  "geekplus.gms.client.screen.taskMonitoring.locationTypeEnum.start": "Starting point",
  "geekplus.gms.client.screen.taskMonitoring.locationTypeEnum.current": "Current position",
  "geekplus.gms.client.screen.taskMonitoring.locationTypeEnum.location": "Specified position",
  "geekplus.gms.client.screen.taskMonitoring.rollerLocationTypeEnum.current": "Current position (manual handling)",
  "geekplus.gms.client.screen.taskMonitoring.rollerLocationTypeEnum.location": "Specified position (manual handling)",
  "geekplus.gms.client.screen.taskMonitoring.rollerLocationTypeEnum.transportLine": "Specified conveyor line (automatic placement)",
  "geekplus.gms.client.screen.taskMonitoring.cellTypeEnum.station": "Workstation",
  "geekplus.gms.client.screen.taskMonitoring.cellTypeEnum.map": "Map location",
  "geekplus.gms.client.screen.taskMonitoring.cellTypeEnum.rollerStation": "Roller station",
  "geekplus.gms.client.screen.taskMonitoring.cellTypeEnum.pallet": "Pallet position",
  "geekplus.gms.client.screen.taskMonitoring.cellTypeEnum.area": "Area",
  "geekplus.gms.client.commons.placeholder.search": "Search",
  "geekplus.gms.client.commons.placeholder.startTime": "Start time",
  "geekplus.gms.client.commons.placeholder.endTime": "Completed time",
  "geekplus.gms.client.commons.toast.developing": "Developing, coming soon.",
  "geekplus.gms.client.commons.clickDownload": "Click to download",
  "geekplus.gms.client.commons.colon": ":",
  "geekplus.gms.client.commons.semicolon": ";",
  "geekplus.gms.client.commons.constants.dmpActionTypeEnum.read": "Read",
  "geekplus.gms.client.commons.constants.dmpActionTypeEnum.write": "Write",
  "geekplus.gms.client.commons.constants.dmpTaskStatusEnum.create": "Create",
  "geekplus.gms.client.commons.constants.dmpTaskStatusEnum.executing": "Executing",
  "geekplus.gms.client.commons.constants.dmpTaskStatusEnum.complete": "Complete",
  "geekplus.gms.client.commons.constants.dmpTaskStatusEnum.cancel": "Cancel",
  "geekplus.gms.client.commons.constants.dmpTaskStatusEnum.error": "Exception",
  "geekplus.gms.client.commons.constants.dmpExceptionStatusEnum.normal": "Normal",
  "geekplus.gms.client.commons.constants.dmpExceptionStatusEnum.offline": "Device is offline",
  "geekplus.gms.client.commons.constants.dmpExceptionStatusEnum.timeout": "Timeout",
  "geekplus.gms.client.commons.constants.dmpExceptionStatusEnum.triggerBitFlag": "Trigger by flag bit",
  "geekplus.gms.client.commons.constants.dmpExceptionStatusEnum.systemError": "System error",
  "geekplus.gms.client.commons.constants.flowTemplateType.pToP": "Point to point",
  "geekplus.gms.client.commons.constants.flowTemplateType.pToMP": "Point-to-multipoint",
  "geekplus.gms.client.commons.constants.flowTemplateType.DP": "Dynamic point",
  "geekplus.gms.client.commons.constants.flowTemplateType.DU": "Dynamic unit",
  "geekplus.gms.client.commons.constants.taskTestType.route": "Route location debugging",
  "geekplus.gms.client.commons.constants.taskTestType.workflow": "Workflow debugging",
  "geekplus.gms.client.commons.taskCloseModeEnum.normal": "Completed normally",
  "geekplus.gms.client.commons.taskCloseModeEnum.gmsManual": "Task monitoring completed manually",
  "geekplus.gms.client.commons.taskCloseModeEnum.robotManual": "Completed manually at robot end",
  "geekplus.gms.client.commons.taskCloseModeEnum.gmsCancel": "Task monitoring canceled",
  "geekplus.gms.client.commons.taskCloseModeEnum.apiCancel": "Canceled by the upstream API",
  "geekplus.gms.client.commons.taskCloseModeEnum.removeRobot": "Robot removal canceled",
  "geekplus.gms.client.commons.taskCloseModeEnum.robotCancel": "Canceled at robot end",
  "geekplus.gms.client.commons.taskCloseModeEnum.apiManual": "Completed by upstream API",
  "geekplus.gms.client.commons.taskCloseModeEnum.pdaManual": "Completed manually at PDA end",
  "geekplus.gms.client.commons.taskCloseModeEnum.pdaCancel": "Canceled at PDA end",
  "geekplus.gms.client.commons.taskCloseModeEnum.stationCancel": "Canceled by workstation",
  "geekplus.gms.client.commons.taskCloseModeEnum.stationManual": "Completed manually at workstation",
  "geekplus.gms.client.commons.taskCloseModeEnum.scanError": "Canceled for code scanning error",
  "geekplus.gms.client.commons.locationPhaseEnum.begin": "Start",
  "geekplus.gms.client.commons.locationPhaseEnum.startLocation": "Starting point",
  "geekplus.gms.client.commons.locationPhaseEnum.midLocation": "Middle point",
  "geekplus.gms.client.commons.locationPhaseEnum.endLocation": "Destination",
  "geekplus.gms.client.commons.locationPhaseEnum.finish": " End",
  "geekplus.gms.client.commons.interactionCommands.goReceive": "Pickup",
  "geekplus.gms.client.commons.interactionCommands.goDrop": "Placement",
  "geekplus.gms.client.commons.interactionCommands.goShelf": "Offset",
  "geekplus.gms.client.commons.interactionCommands.goTurn": "Rotate",
  "geekplus.gms.client.commons.exceptionState.cancelExecuting": "Canceling",
  "geekplus.gms.client.commons.exceptionState.cancelFinished": "Canceled",
  "geekplus.gms.client.commons.exceptionState.cancelFailed": "Cancellation failed",
  "geekplus.gms.client.commons.exceptionState.cancelNewDestExecuting": "Execute the task to a new destination after cancellation",
  "geekplus.gms.client.commons.exceptionState.cancelManualWaiting": "Wait for manual handling",
  "geekplus.gms.client.commons.exceptionState.cancelCompleted": "Cancellation completed",
  "geekplus.gms.client.commons.exceptionState.cancelForceCompleted": "Force to cancel",
  "geekplus.gms.client.commons.exceptionState.manualCompeteExecuting": "Being completed manually",
  "geekplus.gms.client.commons.exceptionState.manualCompeteFailed": "Manual completion failed",
  "geekplus.gms.client.commons.exceptionState.manualManualWaiting": "Wait for manual handling after manual completion",
  "geekplus.gms.client.commons.exceptionState.manualCompleted": "Completed manually",
  "geekplus.gms.client.commons.exceptionState.manualForcedCompleted": "Force to complete",
  "geekplus.gms.client.commons.internalTask.triggerTask": "System trigger",
  "geekplus.gms.client.commons.internalTask.workflowInitiation": "Main workflow and sub-workflow",
  "geekplus.gms.client.commons.internalTask.nodeRelationTask": "Point reference task",
  "geekplus.gms.client.commons.internalTask.autoMaticReturnEmptyContainerTask": "Task of automatic return of empty pallet",
  "geekplus.gms.client.commons.internalTask.waveTask": "Wave task",
  "geekplus.gms.client.commons.constants.rollerFlag.yes": "Scroll",
  "geekplus.gms.client.commons.constants.rollerFlag.no": "No scroll",
  "geekplus.gms.client.commons.constants.topModuleType.singleLift": "Single lifting",
  "geekplus.gms.client.commons.constants.topModuleType.doubleLift": "Double lifting",
  "geekplus.gms.client.commons.constants.topModuleType.singleSpiralArm": "Single cantilever",
  "geekplus.gms.client.commons.btn.collapse": "Fold up",
  "geekplus.gms.client.commons.btn.giveUp": "Abandon",
}
