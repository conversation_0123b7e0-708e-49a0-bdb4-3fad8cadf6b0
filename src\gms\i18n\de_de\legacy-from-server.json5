/**
 *   v3.4.0后端接口提供的国际化内容
 */
{
    "lang.gles.baseData.warehouse": "",
    "lang.ark.fed.common.deleteTipMsg": "Nach dem Löschen kann es nicht wiederhergestellt werden. Sind Sie sicher, dass Sie es löschen möchten?",
    "lang.ark.fed.detour": "Bypass",
    "lang.ark.fed.cutting": "Trimmen",
    "lang.ark.fed.flowAndFlowTemplate": "Prozess / Prozessvorlage",
    "lang.ark.fed.deviceAccessType.fixation": "",
    "lang.ark.fed.addNew": "Neu",
    "lang.ark.fed.shelfAttribute.MOVE": "",
    "lang.ark.fed.minutesLater": "Nach Minuten",
    "lang.ark.workflow.paramValueCode.floor": "",
    "lang.ark.fed.trafficAndStopNotwManage": "Die Funktion des Verkehrssteuerungsbereichs oder des Nothaltebereichs ist bereits vorhanden. Das Hinzufügen von Management-Bereichen wird nicht unterstützt.",
    "lang.ark.fed.verySerious": "Extrem",
    "lang.ark.fed.queuePointLevel": "Warteschlangenpunktpriorität",
    "lang.ark.trafficControl.robotRange": "Verkehrskontrollbereich",
    "lang.ark.fed.chargingTime": "Ladezeit",
    "lang.ark.fed.theMapHasBeenSavedAndYouCanEditItNow": "Die Karte wurde gespeichert. Sie können sie bearbeiten.",
    "lang.ark.fed.locationOfRobotCharging": "Ladeposition des Roboters",
    "lang.ark.fed.side": "Seite",
    "lang.ark.fed.component.workflow.label.specifyNodeType": "Knotentyp angeben",
    "lang.ark.shelfTypeRefShelf": "Fehler beim Löschen. Wird vom Regal benutzt! Regal-Nr.: {0}",
    "lang.ark.apiContainerCode.containerNotExistsByLocation": "LocationCode: {0} Container existiert nicht",
    "lang.ark.apiCallbackReg.controlSendFrequency": "Steuerung der Lieferfrequenz",
    "lang.ark.workflow.containerLevel2Classifica": "Klassifizierung von Containern der Sekundärklasse",
    "lang.ark.fed.pleaseAddCacheArea": "Knotenpunkt-Zwischenspeicher nicht konfiguriert.",
    "lang.ark.fed.nodeConfirmedLeave.type": "Bestätigen Sie, dass der Roboter abfährt",
    "lang.ark.fed.screen.equipmentAssociatedInfo.dialogDetail.labelPosition": "Punktposition",
    "lang.ark.workflow.completeBizAutoTriggerSimple": "Geschäftsauswahl",
    "lang.ark.waveType.alikeProduct": "Gleiche Produktionslinie",
    "lang.ark.fed.pickingUpRack": "Regale holen",
    "lang.ark.fed.leftBracket": "+Linke Klammer",
    "lang.mwms.fed.simulation": "Analoge Steuerung",
    "lang.ark.workflowConfig.cellFunctions.turn": "Wende",
    "lang.ark.fed.rmsRange": "Effektivwert-Synchronisationsbereichssteuerung",
    "lang.ark.fed.sendMaterialType": "Zuordnungs-Methode für Materialeinspeiseziele",
    "lang.ark.fed.pleaseChangeBins": "Bitte ändern Sie den Vorratsbehälter.",
    "lang.ark.action.interface.conditionExtraParam9": "",
    "lang.ark.workflow.B": "H",
    "lang.ark.action.interface.conditionExtraParam8": "",
    "lang.ark.action.interface.conditionExtraParam7": "",
    "lang.ark.action.interface.conditionExtraParam6": "",
    "lang.ark.fed.makeSure": "Ja",
    "lang.ark.action.interface.conditionExtraParam5": "",
    "lang.ark.workflow.F": "V",
    "lang.ark.action.interface.conditionExtraParam4": "",
    "lang.ark.action.interface.conditionExtraParam3": "",
    "lang.ark.externalDevice.instructionRule4": "",
    "lang.ark.action.interface.conditionExtraParam2": "",
    "lang.ark.externalDevice.instructionRule3": "",
    "lang.ark.action.interface.conditionExtraParam1": "",
    "lang.gles.strategy.robotGoodsPosition": "",
    "lang.ark.fed.screen.hybridRobot.installEquipmentTip": "Daten kommen aus DMP-Oberbaugerät",
    "lang.ark.fed.liveNoSaveGoods": "Es sind nicht gespeicherte Materialdetails vorhanden.",
    "lang.ark.workflow.L": "L",
    "lang.ark.workflow.R": "R",
    "lang.ark.workflow.template.validate.templateOrderNodeMustGreaterThan1": "Die Anzahl der dynamischen Vorlagen-Buisnessknotenpunkte muss größer als 1 sein.",
    "lang.ark.fed.uninstallSuccess": "Erfolgreich deinstalliert",
    "lang.ark.fed.redistribution": "Umverteilen",
    "lang.authManage.web.others.expand": "Entfalten",
    "lang.ark.singleCellStation": "Einzelpunktstation",
    "lang.ark.fed.containerTypeExternalNo": "Containertyp externe Nr.",
    "lang.ark.fed.selectPoints": "Wählen Sie einen Punkt",
    "lang.ark.fed.templateCode": "Vorlagencode",
    "lang.gles.receipt.adjustOrder.adjustOrder": "",
    "lang.mwms.monitorRobotMsg12009": "Timeout bei der Aufnahme des Regals",
    "lang.ark.fed.goodsLocation": "Vorratsbehälter",
    "lang.mwms.monitorRobotMsg12006": "Pfadabschnitte sind ggf. blockiert",
    "lang.ark.operatelog.operatetype.fetch": "Holen",
    "lang.mwms.monitorRobotMsg12007": "Keine Route für die Planung",
    "lang.mwms.monitorRobotMsg12004": "Der Start- oder Endpunkt der geplanten Route ist ein Hindernis",
    "lang.ark.fed.emergencyStopError": "System-Notaus fehlgeschlagen!",
    "lang.mwms.monitorRobotMsg12005": "Roboter oder Regal steht im Weg",
    "lang.mwms.monitorRobotMsg12013": "Roboter erreicht ein niedriges Stromverhältnis",
    "lang.ark.workflow.buttonAlreadyConfigured": 'Löschen fehlgeschlagen! Die physische Tastenkonfiguration ist vorhanden. Bitte löschen Sie es zuerst auf der Seite "Tastenkonfiguration"',
    "lang.ark.fed.uploadViewSuccess": "Upload erfolgreich",
    "lang.mwms.monitorRobotMsg12014": "Die Ladeaufgabe kann nicht ohne Anpassung an die Ladestation ausgeführt werden",
    "lang.ark.task.log.export.title.end.time": "Endzeit",
    "lang.mwms.monitorRobotMsg12011": "Der Wartepunkt wird lange Zeit nicht erreicht",
    "lang.mwms.monitorRobotMsg12012": "Roboterleistung erhöht sich nicht für eine lange Zeit",
    "lang.ark.fed.chargingCapacity": "Ladeleistung",
    "lang.mwms.monitorRobotMsg12010": '"Das Regal wurde aufgenommen, ohne die Regalposition zu erreichen"',
    "lang.ark.warehouse.containerConfigEmpty": "Die Containerkonfigurationsinformationen sind Null.",
    "lang.ark.record.rms.sendRobotTask": "",
    "lang.ark.fed.screen.flowNodeConfig.ifExecuteTask": "",
    "lang.ark.workflowConfig.cellFunctions.restart": "Neustart",
    "lang.ark.fed.screen.workflowInfo.requestParamDetail": "",
    "lang.ark.workflow.exceptionHandler.idlePriority": "Der Leerlauf hat Priorität",
    "lang.ark.workflow.task.status.create": "Erstellen",
    "lang.ark.fed.inWarehouse": "Manueller Eingang",
    "lang.ark.fed.lineName": "Name der Produktionslinie",
    "lang.mwms.monitorRobotMsg12019": "Laden über Etagen fehlgeschlagen",
    "lang.mwms.monitorRobotMsg12017": "Die Ladestationszelle wird von anderen Robotern besetzt",
    "lang.mwms.monitorRobotMsg12018": "Etagen-übergreifendes Laden fehlgeschlagen",
    "lang.ark.warehouse.noMatchZagvdbm": "Einspeisepunkt nicht übereinstimmend",
    "lang.mwms.monitorRobotMsg12015": "Ohne Leerlauf-Ladestation kann die Ladeaufgabe nicht ausgeführt werden",
    "lang.mwms.monitorRobotMsg12016": '"Es gibt keine Ladestation, die Ladeaufgabe kann nicht ausgeführt werden"',
    "lang.mwms.monitorRobotMsg12024": "Kein Docking-Platz gefunden",
    "lang.ark.workflow.arrive.action.goTurnOfAngle.pos180": "180°",
    "lang.mwms.monitorRobotMsg12022": "Ladezeit ist zu lang",
    "lang.mwms.monitorRobotMsg12023": "Roboterbatterietemperatur ist zu hoch",
    "lang.mwms.monitorRobotMsg12020": "Die aktuelle Aufgabe ist blockiert und die Ladeaufgabe kann nicht ausgeführt werden",
    "lang.mwms.monitorRobotMsg12021": '"Die aktuelle Aufgabe ist nicht abgeschlossen, und die Ladungsaufgabe kann nicht ausgeführt werden"',
    "lang.ark.fed.whetherToWait": "Warten oder nicht",
    "lang.ark.fed.goodsExComplete": "Abnormal abgeschlossen",
    "lang.ark.fed.triggerCompletion": "Auslösen vervollständigen",
    "lang.ark.fed.goToNextFlowNode": "Gehen Sie zum nächsten Knoten",
    "lang.ark.fed.south": "Süden",
    "lang.ark.dynamicTemplate.cellCodeNotMatch": "Der automatische Zuweisungsknoten stimmt nicht überein.",
    "lang.ark.fed.shelfLock": "Regalschloss",
    "lang.ark.fed.pickingTask": "Aufgabe kommissionieren",
    "lang.ark.fed.screen.hybridRobot.binInfo": "Angaben zum Vorratsbehälter",
    "lang.ark.workflow.area.queueRange": "Warteschlangenbereich",
    "lang.ark.fed.nodeType": "Knotentyp",
    "lang.mwms.fed.warehouseInit": "Lagerbau",
    "lang.ark.fed.common.placeholder.select": "Wählen Sie bitte",
    "lang.ark.workflow.robotWaitFlag": "Ob vor Ort gewartet werden soll",
    "lang.ark.fed.waveStrategyName": "Strategiename",
    "lang.mwms.fed.shelfIn": "Lagereingang der Regale",
    "lang.ark.fed.tripTo": "Ausreise",
    "lang.ark.interface.notExist": "Protokoll existiert nicht",
    "lang.mwms.fed.stocktakeException": "Bestandsstörung",
    "lang.ark.fed.end": "Ende",
    "lang.ark.fed.selectTriggerEvent": "Auslöserereignis auswählen",
    "lang.ark.fed.belongsToArea": "Gerichtsstand",
    "lang.ark.fed.shelfAttribute.RECALL": "",
    "lang.ark.interface.interfaceDesc.edit": "Schnittstellendaten bearbeiten",
    "lang.mwms.monitorRobotMsg12002": "Pfadplanung fehlgeschlagen",
    "lang.mwms.monitorRobotMsg12003": "Roboter nicht in der Karte",
    "lang.ark.fed.blankingTimeout": "Ablegen über Zeitplan",
    "lang.mwms.monitorRobotMsg12000": "Roboter kann nicht angeschlossen werden",
    "lang.mwms.monitorRobotMsg12001": "Zeitüberschreitung beim Senden einer Unteraufgabe",
    "lang.ark.fed.areDeletionsConfirmed": "Sind Sie sicher, zu löschen?",
    "lang.ark.fed.taskFrom.putTask": "",
    "lang.ark.fed.beforeExecuteSaveDayLog": "Bewahren Sie das Protokoll vor jeder Ausführung für die letzten {0} Tage auf",
    "lang.ark.fed.customStartAndendNode": "Bestimmter Startpunkt und Endpunkt werden unterstützt",
    "lang.ark.fed.dataUpdate": "Daten aktualisieren",
    "lang.ark.workflow.template.validate.templateNotExist": "Vorlage existiert nicht",
    "lang.ark.fed.actionsErrorNeedRemoveRobot": "Der Endknoten hat einen Interaktionskonfigurationsfehler. Der Roboter soll freigegeben werden!",
    "lang.authManage.web.common.pleaseSelect": "Bitte auswählen",
    "lang.ark.fed.demandQuantity": "Anzahl der Anforderungen",
    "lang.mwms.fed.arrangePlan": "Zeitplanmanagement der Lagerzählung",
    "lang.ark.robot.go.fetch.pallet": "Palette abrufen",
    "lang.ark.fed.takeTheRack": "Regal",
    "lang.ark.fed.robotApplications": "Roboterkategorie",
    "lang.ark.fed.screen.LoginLog.roleName": "",
    "lang.ark.fed.floorCode": "Ebenencode",
    "lang.ark.fed.screen.flowNodeConfig.offsetValue": "Versatzwert",
    "lang.ark.fed.scrollIsExistsInBoundRecord": "Eingang fehlgeschlagen, da für diese Spulen-Nr. bereits ein Bestandseintrag vorhanden ist.",
    "lang.ark.bussinessModel.wave": "Wellenauswahl",
    "lang.ark.fed.lackRobotQueue": "Aufgabenwarteschlange",
    "lang.ark.fed.require": '"Material, das auf Anfrage abgerufen wird"',
    "lang.ark.fed.robotStatus": "Ausführungsergebnis",
    "lang.ark.fed.screen.equipmentAssociatedInfo.dialogDetail.labelEntryType": "Zuführungsart",
    "lang.ark.rollerCellStation.canNotDelete": "Roller-Station kann nicht gelöscht werden",
    "lang.ark.fed.thereIsARequiredItemNotFilled": ",die erforderlichen Felder sind nicht ausgefüllt!",
    "lang.ark.fed.menu.systemSetting": "",
    "lang.ark.fed.numberOfControllers": "Anzahl der Controller",
    "lang.ark.action.interface.extraParam18": "",
    "lang.ark.fed.abnormalCompleteSuccessfully": "Abnormal abgeschlossen",
    "lang.ark.action.interface.extraParam17": "",
    "lang.ark.fed.sendMaterialPayAttention": "Automatische Materialverteilung: Bestätigt gemäß den Materialinformationen an gebundenen Produktionslinienstationen die Station für die Materialverteilung.       Manuelle Auswahl des Ziels: Wählen Sie die Verteilungsziele manuell aus den Einspeisepunkten aus. Verteilen Sie das Material nach ausgewähltem Ziel.       Verteilung nach Materialanforderung, die von diesem Konfigurationselement nicht betroffen ist",
    "lang.ark.fed.configurationValue": "Konfigurationswert",
    "lang.ark.action.interface.extraParam19": "",
    "lang.ark.action.interface.extraParam14": "",
    "lang.ark.action.interface.extraParam13": "",
    "lang.ark.interface.messageNameDesc": "Name der Schnittstelle",
    "lang.ark.action.interface.extraParam16": "",
    "lang.ark.action.interface.extraParam15": "",
    "lang.ark.action.interface.extraParam10": "",
    "lang.ark.fed.waitStatus": "Wartestatus ",
    "lang.ark.action.interface.extraParam12": "",
    "lang.ark.action.interface.extraParam11": "",
    "lang.ark.workflow.wareHouseStationConfig": "Wie an der Arbeitsstation",
    "lang.ark.fed.exceptionHandler": "Störungbehandlung",
    "lang.ark.apiRuleCode.defaultRuleNotExists": "Standardgestell existiert nicht",
    "lang.ark.operatelog.operatetype.send": "Liefern",
    "lang.authManage.web.common.toLoginPage": "Zurück zur Anmeldeseite",
    "lang.ark.fed.templateType": "Vorlagentyp",
    "lang.ark.common.failed": "Aufruf fehlgeschlagen",
    "lang.ark.fed.interruptInstruct": "Wartebefehl unterbrechen",
    "lang.ark.fed.hybridRobot.hybridRobotType.singleSpiralArm": "Einzelner Kragarm",
    "lang.ark.workflowPeriod.one": "Nur einmal",
    "lang.authManage.web.common.creator": "Ersteller",
    "lang.ark.waveStatus.waveTaskCreateFail": "Die Aufgabenerstellung ist fehlgeschlagen",
    "lang.ark.fed.loadingMount": "Zufuhrmenge",
    "lang.ark.fed.mapsAreBeingUpdated": "Karte wird aktualisiert",
    "lang.ark.fed.menu.buttonFunctionConfiguration": "Konfiguration der Tastenfunktion",
    "lang.ark.fed.sendMaterialDestination": "Wählen Sie das verfügbare Ziel für die Verteilung aus",
    "lang.ark.fed.selfCharging": "Selbstaufladung",
    "lang.ark.fed.contents.flowConfig.recycleType.autoRecoveryRule.alert": "",
    "lang.ark.warehouse.materialPreparePointCellCode": "Einspeisepunkt",
    "lang.ark.workflow.wareHouseManuallyCreate": "Manuelle Erstellung",
    "lang.ark.warehouse.containerBin": "Container der Vorratsbehälter",
    "lang.ark.fed.serverAddress": "Serveradresse",
    "lang.ark.fed.screen.flowNodeConfig.turnSideTo": "Drehen nach",
    "lang.ark.fed.originAngle": "Ursprünglicher Winkel",
    "lang.ark.fed.editNode": "Knoten bearbeiten",
    "lang.ark.workflow.completeTrigger": "Automatische Auslösung nachfolgender Aufgaben abschließen ",
    "lang.ark.fed.shelfAttribute": "",
    "lang.ark.record.dmp.receiveCallBack": "",
    "lang.ark.interface.sendSucceed": "Das Protokoll wurde erfolgreich gesendet und kann nicht erneut gesendet werden.",
    "lang.ark.fed.secondsAndTime": "Sekunde/Zeit",
    "lang.ark.fed.sound": "Klang",
    "lang.ark.fed.deleteActionFailedRef": "Die interaktive Konfiguration kann nicht gelöscht werden und der Referenzprozess existiert weiterhin",
    "lang.ark.fed.queuePoint": "Warteschlangenpunkt",
    "lang.ark.warehouse.materialPreparePointName": "Name des Einspeisepunkts",
    "lang.ark.fed.receiveMaterial": "Materialanforderung und Einspeisung",
    "entry.shelf.failed": "Regaleingang fehlgeschlagen. Bitte versuchen Sie es später noch einmal!",
    "lang.ark.interface.interfaceName": "Schnittstellenname",
    "lang.ark.externalDevice.device_own_type.robotDeviceComponent": "Roboterkomponente und Ausrüstung",
    "lang.mwms.fed.pickException": "Entnahme-Störung",
    "lang.ark.fed.firstDay": "Der erste Tag",
    "lang.ark.robot.go.drop": "Entladen",
    "lang.ark.fed.roadWidth": "Straßenbreite:",
    "lang.ark.waveTaskStatus.disCanceled": "Stornieren",
    "lang.ark.fed.firstDrawWorkStop": "Bitte zeichnen Sie den ersten Knoten der Prozedur, der Typ des ersten Knotens ist Arbeitsstation,Dockingpunkt",
    "lang.ark.trafficControl.trafficFunction": "Verkehrskontrollbereichsfunktion",
    "lang.ark.fed.source": "Quelle",
    "lang.ark.fed.backButton": "Schaltfläche „Rollback“",
    "lang.ark.fed.manualClean": "Manuelles Räumen",
    "lang.ark.fed.confirmGoods": "Material bestätigen",
    "lang.ark.fed.menu.taskManagement": "Aufgabenverwaltung",
    "lang.ark.workflow.init": "",
    "lang.ark.button.type.selfRecovery": "Selbstrückstellung",
    "lang.ark.action.interface.fixedValue": "Fester Wert",
    "lang.ark.fed.passbyPointType": "Durchpunktetyp:",
    "lang.ark.fed.screen.flowNodeConfig.ifNextPoint": "",
    "lang.ark.fed.selectionWorkflow": "Einen Prozess auswählen",
    "lang.ark.fed.currentLocation": "Aktuelle Position",
    "lang.ark.fed.uploadFileLimit500": "Nur Excel-Dateien (.xls / .xlsx) können ohne mehr als 500 KB hochgeladen werden.",
    "lang.ark.fed.containPoints": "Enthaltene Punkte",
    "lang.ark.warehouse.poleCabinet": "Materialschrank mit Stangen",
    "lang.ark.fed.chargingStrategy": "Ladestrategie",
    "lang.ark.loadCarrier.loadCarrierModelCodeGenerateErr": "",
    "lang.authManage.web.others.subsystem": "Berechtigungssubsystem",
    "lang.ark.fed.stopCharging": "Ladevorgang beenden",
    "lang.ark.fed.dateRange": "Datumsbereich",
    "lang.ark.fed.pleaseCreateARule": "Bitte erstellen Sie eine Regel",
    "lang.ark.fed.imageTypeJudge": "Unterstützte Formate: jpeg, jpg, png.",
    "lang.ark.fed.north": "Norden",
    "lang.ark.interface.apiStart": "Workflow eingeleitet",
    "lang.ark.fed.rollerRobot": "Rollenroboter",
    "lang.ark.fed.robotWaitFlag": "Vor Ort warten",
    "lang.ark.fed.specifyRobot": "Einen Roboter bestimmen",
    "lang.ark.warehouse.materialPreparePointOrder": "Materialzufuhrsequenz",
    "lang.ark.fed.fullStation": "VOLLE Version der Arbeitsstation",
    "lang.ark.workflowConfig.status.uninstalled": "Deinstalliert",
    "lang.ark.workflow.canDeleteshelfFlag": "Regale entfernen",
    "lang.ark.fed.screen.flowNodeConfig.tip.onlyWaitPoint": "",
    "lang.ark.fed.image": "Bild",
    "lang.ark.workTask.export.title.fileName": "task_query",
    "lang.ark.fed.pleaseEnterANumber": "Bitte geben Sie die Nummer ein",
    "lang.ark.fed.menu.workstationEditController": "Arbeitsplatz",
    "lang.authManage.web.others.activePermission": "Berechtigungen aktivieren",
    "lang.ark.fed.isClearTrigger": "Sind Sie sicher, den aktuellen Auslöser abzubrechen?",
    "lang.ark.fed.selectArea": "Bereich auswählen",
    "lang.ark.fed.byRackType": "Nach Regaltypen",
    "lang.ark.action.interface.paramValue": "Parameterwert",
    "lang.ark.fed.shangliao": "Zuführung über Zeitplan",
    "lang.ark.fed.taskTriggerCycle": "Auslösezeitraum",
    "lang.ark.apiCommonCode.locationFromNotMatchStart": '"locationFrom: {0} konnte nicht mit der entsprechenden Position im GMS in übereinstimmung gebracht werden, nachdem ""Punkt"", ""Arbeitsstation"" und ""Bereich"" ausprobiert wurden."',
    "lang.ark.fed.surlpusGoods": "{0} übrig",
    "lang.ark.common.exportTaskDetail": "Aufgabenansicht importieren",
    "lang.ark.fed.currentTask": "Aktuelle Aufgabe",
    "lang.ark.fed.taskOverTime": "Aufgaben-Fertigstellungszeit",
    "lang.ark.fed.DingTalk": "DingTalk",
    "lang.ark.workflow.queue.noAvailableStopPoint": "Kein Punkt verfügbar",
    "lang.ark.fed.allowInterruptionOfCharging": "Unterbrochene Ladevorgänge zulassen",
    "lang.ark.robot.Task.send.failed": "Fehler beim Senden von Roboteraufgaben",
    "lang.ark.fed.order": "Dokumentinformationen",
    "lang.ark.workflowTrigger.logType.taskRecordLog": "",
    "lang.ark.fed.pleaseSelectCellCode": "Zuerst die Ladestelle auswählen.",
    "lang.ark.fed.disCharingCell": "Ablage an Arbeitsstation",
    "lang.ark.fed.shelfModel": "Regalmodell",
    "lang.ark.fed.container.leaveFailure": "Die folgenden Behälter sind nicht ausgetreten. Versuchen Sie es erneut!",
    "lang.ark.warehouse.demandProductionLine": "Produktionslinie anfordern",
    "lang.mwms.rf.rfVersion": "PDA-Versionskontrolle",
    "lang.ark.fed.virtualNode": "Virtueller Knoten",
    "lang.ark.workflow.nodeStatus.empty": "Leer (Logistikcontainer konnte nicht abgeholt werden)",
    "lang.ark.fed.goBack": "Rückkehr",
    "lang.ark.fed.afterGoing": "Weggehen nach",
    "lang.ark.fed.container": "Container",
    "lang.ark.fed.resume": "Weiterlaufen",
    "lang.ark.fed.screen.workflowInfo.dmpTaskId": "",
    "lang.ark.workflow.deviceTaskNotExistOrCompleted": "",
    "lang.ark.fed.externalInterfaceInteraction": "Externe Schnittstelleninteraktion",
    "lang.ark.workflow.authRoleHasUsed": "Die folgenden Rollen haben die Berechtigung der Arbeitsstation festgelegt.",
    "lang.ark.fed.optionalDockPoints": "Optionale Dockingpunkte",
    "lang.gles.receipt.tallyList": "",
    "lang.ark.fed.shelfAttribute.ENO": "",
    "lang.ark.fed.screen.workflowInfo.responseParam": "",
    "lang.mwms.fed.shelfStrategy": "Einlagerungsstrategie",
    "lang.ark.warehouse.estimateUseTimeUnit": "Einheit",
    "lang.ark.fed.pauseEnter": "Pausieren",
    "lang.ark.fed.standbyPoint": "Standby-Punkt",
    "lang.ark.fed.arriveOperation": "In Aktion angekommen",
    "lang.ark.fed.orderReceive": "Bereits erforderlich",
    "lang.ark.apiNodeActionCode.successHandlerNotEmpty": "Die Erfolgsverarbeitungslogik der Knoteninteraktionskonfiguration ist null",
    "lang.ark.task.log.export.title.fileName": "Aufgabenprotokoll-verarbeiten",
    "lang.ark.apiRobotTaskCode.waitPointExecutingTaskNotExists": "Die Aufgabe in Ausführung am Wartepunkt existiert nicht.",
    "lang.ark.fed.ruleOperator": "Betreiber",
    "lang.ark.trafficControl.enterType.singleFactorySingleEnter": "Einzeleingang für Einzelhersteller",
    "lang.ark.workflow.externalInteraction": "Externe Interaktion nach der Ankunft",
    "lang.ark.fed.uploadImageCutText": "Um eine genaue Erkennung zu gewährleisten, schneiden Sie das Bild nach dem Hochladen nur auf den Barcode zu.",
    "lang.ark.robot.classfy.noaction": "Bewegungslos",
    "lang.ark.fed.screen.flowNodeConfig.ifExecuteRule": "",
    "lang.ark.fed.noLoad": "Ohne Laden",
    "lang.ark.workflow.containerNeedOrientation": "Container-Nachfrage-Ausrichtung",
    "lang.ark.fed.materialCode": "Materialnummer",
    "lang.ark.apiContainerCode.isEmptyIsBlank": "isEmpty ist leer",
    "lang.ark.fed.workstationConfig": "Arbeitsstations-Konfiguration",
    "lang.ark.manual.pick.up": "Manuelle Entfernung",
    "lang.ark.interface.enableSuccess": "Erfolgreich aktiviert",
    "lang.ark.waveStatus.create": "Wellenpflücken erstellen",
    "lang.ark.binStopPointRelation.binCellCode": "Punktcode des Vorratsbehälters",
    "lang.ark.trafficControl.triggerLock": "Externe Auslösung gesperrt",
    "lang.ark.fed.siteManagement": "Standort-Management",
    "lang.ark.workflow.workflowTaskSendPause": "Der angehaltene Prozess kann nicht ausgeführt werden",
    "lang.authManage.web.permission.roleDesc": "Rollenbeschreibung",
    "lang.ark.fed.conButtonLogPieceData": "Stück-Daten",
    "lang.ark.fed.inventoryType": "Speichertyp",
    "lang.ark.workflow.workflowStartNode": "Startpunkt des Prozesses",
    "lang.ark.existWorkflowConfigUseTheArea": "Eine Prozesskonfiguation nutzt dieses Bereichs!",
    "lang.ark.fed.others": "Andere",
    "lang.ark.fed.defaultSite": "Standarder Standort",
    "lang.ark.workflowConfig.cellFunctions.avoid": "Vermeidung",
    "lang.ark.fed.obstacleAvoidance": "Vermeidung von Hindernissen vorne und hinten",
    "lang.ark.warehouse.containerStockEdit": "Die Container dieses Typs sind auf Lager. Eine Bearbeitung ist nicht erlaubt.",
    "lang.ark.common.ok": "Aufruf erfolgreich",
    "lang.ark.fed.pointPosition": "Punkt",
    "lang.ark.fed.waveTaskCreateFail": "Die Aufgabenerstellung ist fehlgeschlagen",
    "lang.ark.apiCommonCode.instructionNotBlank": "Anweisung kann nicht null sein.",
    "lang.ark.fed.containerLevelTwo": "Klassifizierung von Containern der Sekundärklasse",
    "lang.ark.fed.pleaseEnterAPositiveInteger": "Bitte geben Sie eine positive Ganzzahl ein",
    "lang.ark.interface.apiDelete": "Workflow-Instanz gelöscht",
    "lang.ark.fed.rackState": "Container-Status",
    "lang.ark.fed.cancelWaveSure": "Nach dem Abbrechen kann das Wellenauswahl-Dokument nicht mehr verteilt werden, und es ist eine neue Wellengruppierung erforderlich. Sind Sie sicher, abzubrechen?",
    "lang.ark.fed.sendingNode": "Einen Knoten senden",
    "lang.ark.warehouse.buttonOperationEndSystemUni": "Gleiche Tastenkonfiguration. Der Operationsbefehl stimmt nicht mit dem aktuell eingegebenen Operationsbefehl überein.",
    "lang.ark.fed.mobileLocation": "Position verschieben",
    "lang.ark.fed.manageAreaNoTrayPointMsg0": "Koexistenz von Palettenposition und Punktposition ist derzeit unmöglich!",
    "lang.ark.api.workflowConfigNoMatched": "Kein passender Workflow",
    "lang.ark.fed.menu.vens.dmpDeviceModel": "",
    "lang.ark.fed.businessRuleFlow": "Geschäftsregel-Bewegung",
    "lang.ark.warehouse.Baiting": "Materialentladung",
    "lang.ark.fed.move": "Bewegung",
    "lang.ark.workflow.denseStorageTaskUpperLimit": "Die Anfangsstelle ist der kompakte Lagerbereich, der die Höchstzahl der angenommenen Aufgaben erreicht hat",
    "lang.ark.fed.conButtonLogIP": "IP",
    "lang.ark.fed.escDrawsBranchLines": "ESC Zweiglinien zeichnen ",
    "lang.ark.fed.waitLeave": "Auf das Verlassen warten.",
    "lang.ark.fed.mergeNode": "Vorhandene Knoten verbinden",
    "lang.ark.fed.responseParameterValue": "Verarbeitung des Rückgabeparameters",
    "lang.ark.fed.linkDisconnect": "Getrennt",
    "lang.ark.workflow.areaLocked": "Der Verkehrskontrollbereich ist gesperrt und kann nicht gelöscht werden.",
    "lang.ark.robot.classfy.cage": "Traktion",
    "lang.ark.fed.pleaseCheckTheRequiredItems": "Bitte überprüfen Sie die erforderlichen Felder",
    "lang.mwms.fed.strategyAllocate": "Hit-Strategie-Management",
    "lang.ark.fed.logManagement": "Protokollverwaltung",
    "lang.ark.action.interface.extraParam20": "",
    "lang.ark.fed.nextStep": "Nächster Schritt",
    "lang.ark.fed.editingWorkflow": "Workflow-Bearbeitung",
    "lang.ark.fed.conButtonLogID": "ID",
    "lang.ark.fed.circulationStrategy": "Transferstrategie",
    "lang.authManage.web.others.disabledPermisssion": "Berechtigungen deaktivieren",
    "lang.ark.fed.startTime": "Startzeit",
    "lang.ark.fed.containerManage": "Container-Management",
    "lang.ark.apiCallbackReg.timeInterval": "Intervall",
    "lang.ark.fed.noGoodsChangeLocation": "Keine Einspeisungsaufzeichnung dieses Materials, bitte Vorratsbehälter wechseln.",
    "lang.ark.fed.waitingEnter": "Soll eintreten",
    "lang.ark.fed.sourceProductionLine": "Quellproduktionslinie",
    "lang.ark.fed.taskTypeName": "Aufgabentyp",
    "lang.ark.fed.containerBinUsed": "Aufgabenbelegung",
    "lang.ark.element.has.bound": "Dieses Element wurde an den Workflow gebunden",
    "lang.ark.fed.menu.workstationaddress": "Adresse aufrufen",
    "lang.ark.fed.editComponentInterface": "Komponentenbefehl bearbeiten",
    "lang.ark.workflowTriggerMonitorStatus.create": "Erstellen",
    "lang.ark.operation.workflow.recoveryWorkflow": "",
    "lang.ark.fed.noGoodsInfo": "Kein Material",
    "lang.ark.fed.nodeEditing": "Knotenbearbeitung",
    "lang.ark.fed.menu.robotTypeManagement": "Robotermodell",
    "lang.ark.fed.menu.templateInstance": "Vorlagenbeispiel",
    "lang.ark.robot.robotBindDeviceExist": "Das entsprechende Gerät wurde mit dem Roboter verbunden.",
    "lang.ark.fed.length": "Länge ",
    "lang.ark.systemErrCannot_operate": "Systemstörung und keine Bedienung ist erlaubt",
    "lang.ark.fed.Wechat": "Enterprise WeChat",
    "lang.ark.fed.estimate": "Erwartet",
    "lang.mwms.fed.SNPoolCharts": "SN (Seriennummer)",
    "lang.ark.fed.productType": "Modellnummer",
    "lang.mwms.fed.monitoring": "Lagerüberwachung",
    "lang.authManage.web.common.isDelRol": "Bestätigen Sie, dass die Rolle gelöscht wird?",
    "lang.ark.apiCommonCode.needTimeFormatError": "Das needTime-Format ist ungültig.",
    "lang.ark.fed.optionalRackPoints": "Optionale Regalpunkte",
    "lang.ark.fed.autoSetNodeValue": "Automatischer Zuweisungsknoten",
    "lang.mwms.monitorRobotMsg.norobot": "Keine Roboter verfügbar",
    "lang.ark.robot.go.return.pallet": "Palette zurückführen",
    "lang.ark.fed.screen.flowNodeConfig.pleaseSelectCondition": "",
    "lang.ark.fed.higherSpeed": "Hohe Geschwindigkeit",
    "lang.ark.loadCarrier.loadCarrierModelSyncErr": "",
    "lang.ark.workflow.condition.unEqual": "Nicht gleich mit",
    "lang.ark.record.robotCallback.fetched": "",
    "lang.ark.fed.remap": "Die Karte neu zeichnen",
    "lang.ark.fed.expression": "Ausdruck",
    "lang.ark.fed.interfaceSetting": "API-Einstellungen",
    "lang.ark.fed.orderCreate": "Erstellen",
    "lang.ark.fed.pleaseSelectTheTaskRecord": "Bitte einen Aufgabendatensatz auswählen",
    "lang.ark.fed.menu.authManage": "Berechtigungs-Management",
    "lang.ark.workflow.startNode": "Startpunkt",
    "lang.ark.trafficControl.shelfRange": "Verkehrskontrollbereich",
    "lang.mwms.rf.receiveWithoutTask": "Kein Lagereingang",
    "lang.ark.fed.screen.flowNodeConfig.isDoubleLiftRobot": "Roboter mit doppelter Hebebühne?",
    "lang.authManage.web.common.editor": "Bearbeiter",
    "lang.ark.interface.apiLocationList": "Punktabfrage",
    "lang.ark.fed.GRAVE": "Hoch",
    "lang.ark.fed.createTriggerTask": "Auslöseraufgabe erstellen",
    "lang.ark.workflow.task.status.node.backing": "Rückkehren",
    "lang.ark.fed.name": "Name:",
    "lang.ark.fed.contents.flowConfig.recycleType": "",
    "lang.mwms.fed.pickWorkManualCreate": "Entnahmeaufgabe wird manuell generiert",
    "lang.ark.task.log.export.title.robot.number": "Roboternummer",
    "lang.ark.fed.component.workflow.nodeType.equipment": "Geräteknoten",
    "lang.ark.binStopPointRelation.binOrder": "Seriennr. des Vorratsbehälters",
    "lang.authManage.web.others.missPage": "Die gesuchte Seite kann leider nicht gefunden werden!",
    "lang.ark.fed.menu.robotControlStrategy": "Roboterstrategie",
    "lang.ark.workflow.recoveryAreaType.workflowStart": "Startpunkt des Prozesses",
    "lang.ark.waveGeneratePattern.documentAuto": "Automatische Wellengruppierung gemäß den Dokumenten",
    "lang.ark.fed.menu.monitoringAndManagement": "Überwachungsmanagement",
    "lang.ark.fed.waitSendGoods": "Ausstehende Lieferung",
    "lang.ark.robot.robotModelExist": "Der Mitrobotertyp, der dem Robotertyp entspricht, liegt bereits vor. Ergänzen Sie ihn nicht mehrmals.",
    "lang.ark.fed.productDate": "Produktionsdatum",
    "lang.ark.fed.waveTaskCode": "Aufgabencode für die Wellenauswahl",
    "lang.ark.workflow.workflowRuleNameExist": "Regelname duplizieren",
    "lang.ark.fed.completionTime": "Fertigstellungszeit",
    "lang.ark.fed.expiryDate": "Ablaufdatum",
    "lang.ark.warehouse.canNotUseNodeToNode": "Für mehrere Zielorte kann die Punkt-zu-Punkt-Vorlage nicht verwendet werden.",
    "lang.ark.action.interface.integer": "",
    "lang.ark.fed.orderAbnormalTip": '"Operation {successNum}, Fehler {faildNum}; Es kann nur das abnormale Dokument im Schwebezustand ausgeführt werden."',
    "lang.ark.fed.containerForm": "Container-Form",
    "lang.ark.api.template.startNodeIsBlank": "Der Startpunkt der Aufgabe ist null.",
    "lang.ark.fed.processGroupNumber": "Anzahl der Prozessgruppen ",
    "lang.ark.fed.viewTheWholeProcess": "Vollständigen Prozess anzeigen",
    "lang.authManage.web.common.oldPassword": "Altes Passwort",
    "lang.ark.interface.apiContainerCategoryList": "Abfrage des Container-Typs",
    "lang.ark.fed.contents.flowConfig.recycleType.manual": "",
    "lang.ark.workflow.area.autoRealease": "Automatische Freigabe des Roboters",
    "lang.ark.fed.conditionNumber": "Bedingungswert",
    "lang.ark.fed.batteryVoltage": "Batteriespannung",
    "lang.ark.fed.show": "Anzeige",
    "lang.ark.fed.redistributionSuccessfully": "Die Umverteilung war erfolgreich",
    "lang.ark.fed.personalizationOptionsTitle": "Nach dem Aktivieren unterstützt die Arbeitsstation für Materialaufrufe die Eingabe der erwarteten Zeit des Materialbedarfs durch den Benutzer.",
    "lang.ark.fed.sourcePoint": "Quellstation",
    "lang.ark.fed.pickListsPending": '"Materialanforderungsbogen, der bearbeitet werden soll"',
    "lang.mwms.fed.user.add": "Hinzufügen von Benutzern",
    "lang.ark.robot.go.deliver.pallet": "Palette liefern",
    "lang.ark.fed.activeDistribution": "Aktive Verteilung",
    "lang.ark.warehouse.getTaskHashCancle": "Materialanforderungsbogen storniert",
    "lang.gles.workflow.receiptMonitor": "",
    "lang.ark.fed.screen.hybridRobot.pleaseInputNumber": "Geben Sie etwas ein. Nur Ziffern können eingegeben werden.",
    "lang.authManage.web.others.pwsavesuccess": "Das Kennwort wurde erfolgreich zurückgesetzt und das neue Kennwort ist {0}",
    "lang.ark.fed.logControllerConfigId": "Controller-Konfigurations-ID",
    "lang.authManage.web.common.edit": "Bearbeiten",
    "lang.ark.fed.openAutoFlow": "Automatischen Prozess starten",
    "lang.ark.fed.controllerNumber": "Controller-Nr.",
    "lang.ark.fed.enableEdit": "Modifizierbar",
    "lang.ark.fed.seriNum": "Knotencode",
    "lang.ark.fed.conditionalCoding": "Bedingungscode",
    "lang.ark.fed.width": "Breite ",
    "lang.ark.fed.editExtendDevice": "Externe Geräte bearbeiten",
    "lang.ark.apiStationCode.stationStopPointNotOnlyOne": "Der Andockort in der Arbeit ist nicht eindeutig und muss angegeben werden",
    "lang.ark.action.interface.string": "",
    "lang.ark.fed.scopestartEnd": "Bereich {start}~{end}",
    "lang.ark.fed.isFastStartFlow": "Möchten Sie den aktuellen Workflow schnell einleiten?",
    "lang.ark.fed.cellCode": "Punktpositionscode",
    "lang.ark.fed.pleaseInputGoodsCode": "Bitte geben Sie den Materialcode ein oder scannen Sie ihn ein.",
    "lang.ark.fed.receivingPoint": "Materialanforderungsstation",
    "lang.ark.fed.waveStrategyTrigger": "Auslösebedingungen",
    "lang.ark.workflow.shelfCodeAlreadyExists": "Die externe Nummer des Container-Typs ist bereits vorhanden",
    "lang.ark.workstationIsExist": "Arbeitsstation ist bereits vorhanden",
    "lang.ark.fed.taskFrom.fetchTask": "",
    "lang.ark.fed.workstationPage": "Arbeitsstation-Seite",
    "lang.ark.areaCellCodeUsed": "Der Punkt kann nicht zu beiden Bereichen gehören, in denen sich eine Warteschlangenstrategie befindet",
    "lang.ark.workflow.arrive.action.component.commandPhaseIllegal": "Unzulässige Befehlsausführungsphase",
    "lang.ark.fed.materialDetails": "Materialliste",
    "lang.ark.fed.contents.flowConfig.recycleType.autoRecoveryRule.columns.position": "",
    "lang.ark.workflow.containerSelectedByTask": "Der Container wurde von einer Aufgabe belegt",
    "lang.ark.workflow.noLongerWaiting": 'Die aktuelle Aufgabe befindet sich nicht mehr unter "Soll eintreten" und kann nicht angehalten werden. Bitte aktualisieren.',
    "lang.ark.fed.citeFlow": "Referenzprozess",
    "lang.ark.fed.screen.flowNodeConfig.executeByNormal": "",
    "lang.ark.fed.trafficAreaControlManagement": "",
    "lang.ark.fed.outGoodsNumMoreThanRemain": "Regalfach {0} unterstützt einen max. Ausgang von {1}.",
    "lang.ark.fed.contents.flowConfig.autoRecyclePosition": "",
    "lang.authManage.web.others.pinlessIp": "Hardwareinformationen binden",
    "lang.ark.workflow.workflowNotCancel": "Der Abbruch des aktuellen Prozesses entspricht nicht den Abbruchregeln.",
    "lang.ark.fed.containBinIsOccupy": "Das Regalfach ist bereits belegt.",
    "lang.ark.fed.orderSend": "Verteilen",
    "lang.ark.fed.modificationTime2": "Zeit aktualisieren",
    "lang.ark.fed.getMaterialDescribe": '"Anwendbar auf das Szenario der aktiven Verteilung durch Einspeisepunkte, d.h. Die Einspeisepunkte verteilen Material aktiv."',
    "lang.ark.fed.taskManagement": "Aufgabenüberwachung",
    "lang.authManage.web.others.filterKey": "Bitte geben Sie Schlüsselwörter ein, um zu filtern",
    "lang.ark.archiveType.containerChangeLog": "?nderungsprotokoll des Containers",
    "lang.mwms.fed.inventoryAge": "Bericht über das Lageralter",
    "lang.ark.base.license.instanceIdIsNull": "Artikelinstanz ist null!",
    "lang.ark.fed.billDashboard": "Dokumenten-Anzeigentafel",
    "lang.ark.fed.restart": "Neustart",
    "lang.ark.fed.newWorkflow": "Einen neuen Prozess erstellen",
    "lang.ark.fed.operationDurationGreaterThan": "Vorgangsdauer >=",
    "lang.ark.fed.nodeConfirmedLeave": "Bestätigen Sie, dass der Roboter den Knoten verlässt",
    "lang.ark.fed.fullGoods": "Auf Lager",
    "lang.ark.interface.dmpHandleDoor": "Tür entriegeln und verriegeln",
    "lang.ark.fed.paramValueCode": "Parameterwertcodierung",
    "lang.ark.base.license.customerIdIsNull": "Kunden-ID ist null!",
    "lang.ark.fed.placeItHere": "Stellen Sie ihn hier ab",
    "lang.ark.fed.interactiveList": "Interaktionsliste",
    "lang.ark.fed.processNumber": "Arbeitssequenznummer",
    "lang.ark.fed.unit": "Einheit",
    "lang.ark.workflow.robotAllocationStrategy": "Regeln des Roboters",
    "lang.mwms.fed.user.edit": "Benutzer bearbeiten",
    "lang.ark.fed.totalStock": "Lager- / Materialmenge",
    "lang.ark.fed.deliveryTime": "Verteilungszeit",
    "rms.system.container.entry.failed": "",
    "lang.mwms.monitorRobotMsg21085": "Antriebsbefehlsfehler",
    "lang.mwms.monitorRobotMsg21084": "Fehler im Aufgabenschritt",
    "lang.mwms.monitorRobotMsg21083": "Am Ende der Aufgabe wird ein Fehlerbefehl ausgegeben",
    "lang.mwms.monitorRobotMsg21082": "Task-Status Maschinenwechselfehler",
    "lang.ark.fed.executionCondition": "Ausführungsbedingung",
    "lang.mwms.monitorRobotMsg21089": "Antrieb-Unterspannung",
    "lang.ark.warehouseTask.loadTaskOverTime": "Zeitwarnung für Zufuhr zur Arbeitsstation ({0} m über Planung).",
    "lang.mwms.monitorRobotMsg21088": "Antrieb-Feedback-Fehler",
    "lang.mwms.monitorRobotMsg21087": "Antriebs-Verfolgungsfehler",
    "lang.ark.fed.functionType": "Funktionsart",
    "lang.mwms.monitorRobotMsg21086": "Antriebmotorphasenfehler",
    "lang.ark.fed.takeNode": "Einen Knoten nehmen",
    "lang.gles.baseData.productionLine": "",
    "lang.ark.fed.manualSelectGoodsAndDest": "Manuelle Auswahl des Ziels",
    "lang.mwms.monitorRobotMsg21081": "Kommunikation zwischen Master-Control und Industriesteuerung ist unterbrochen",
    "lang.mwms.monitorRobotMsg21080": "Die Daten des Gewichtssensors fehlen.",
    "lang.ark.fed.obstacleAvoidanceArea": "Hindernisvermeidungsbereich",
    "lang.ark.action.interface.conditionType": "Typ des bedingten Wertes",
    "lang.mwms.fed.warehouse": "Lager",
    "lang.ark.fed.nodeControl": "Knotensteuerung",
    "lang.ark.warehouse.demandLineStation": "Nachfragepunkt",
    "lang.ark.workflow.hitStrategy": "Treffer-Strategie",
    "lang.ark.fed.screen.container.belongsToGroup": "Gruppierung",
    "lang.ark.fed.commandDetail": "Anweisungsbeschreibung",
    "lang.ark.action.interface.conditionContainerCode": "",
    "lang.ark.workflow.customCellNotMatchCell": "Benutzerdefinierter Wiederherstellungsbereich passt nicht zum Leerlaufpunkt. Abbruch fehlgeschlagen!",
    "lang.mwms.monitorRobotMsg21079": "Batterie des Absolute Encoder ist niedrig",
    "lang.mwms.monitorRobotMsg21096": "Antriebstemperatur ist zu niedrig",
    "lang.mwms.monitorRobotMsg21095": "Übermäßige Antriebstemperatur",
    "lang.mwms.monitorRobotMsg21094": "Antriebsmotortemperatur ist zu niedrig",
    "lang.mwms.monitorRobotMsg21093": "Antriebsmotortemperatur ist zu hoch",
    "lang.mwms.monitorRobotMsg21099": "Komponente Zustellungsausnahme",
    "lang.mwms.monitorRobotMsg21098": "Antriebsadressfehler",
    "lang.mwms.monitorRobotMsg21097": "Antriebs-übergeschwindigkeitsalarm",
    "lang.ark.fed.component.workflow.label.specifyEquip": "Gerät angeben",
    "lang.mwms.monitorRobotMsg21092": "Antriebstrom Kurzschluss",
    "lang.mwms.monitorRobotMsg21091": "Antrieb-überstrom",
    "lang.mwms.monitorRobotMsg21090": "Antrieb-überspannung",
    "lang.ark.warehouse.hasSameCellCode": "Der gleiche Stationspunkt existiert bereits.",
    "lang.ark.fed.systemexceptionPleaseContactTheAdministrator": "Systemanomalie, bitte wenden Sie sich an den Administrator",
    "lang.ark.fed.orderNo": "Bestellnummer",
    "lang.ark.fed.deliveryCompletionTime": "Liefer-Fertigstellungszeit",
    "lang.ark.fed.robotDeviceComponent": "Roboterbauteilger?t",
    "lang.ark.fed.selected.containerCode": "Punktposition oder Behältercode wählen",
    "lang.ark.fed.screen.equipmentAssociatedInfo.dialogDetail.titleView": "Ansicht der Gerätezuordnungsdaten",
    "lang.ark.fed.logicOr": "+ oder-Operator (||)",
    "lang.mwms.monitorRobotMsg21063": "kein Ladestrom",
    "lang.mwms.monitorRobotMsg21062": "Die von der Kamera hochgeladenen Bilddaten konnten die Header- und Tail-Prüfsumme nicht analysieren",
    "lang.mwms.monitorRobotMsg21061": "Die von der Kamera hochgeladenen Bilddaten konnten die Header- und Tail-Prüfsumme nicht analysieren",
    "lang.ark.fed.thereAreNoProcessNodesPleaseClickAdd": "Kein Prozessknoten, bitte klicken Sie auf Hinzufügen~",
    "lang.ark.warehouse.transfer": "Bewegung",
    "lang.mwms.monitorRobotMsg21060": "Hindernisvermeidung des Roboters",
    "lang.ark.fed.screen.hybridRobot.offsetValueL": "Versatzwert der Ebene L",
    "lang.ark.fed.errorSide": "Containerflächenfehler, bitte bestätigen Sie den Barcode.",
    "lang.mwms.monitorRobotMsg21067": "Antriebsradmotor-überstrom",
    "lang.ark.thisWorkstationStopPointNotOnlyOne": "Die Arbeitsstation hat keine Punkte oder ist nicht eindeutig",
    "lang.mwms.monitorRobotMsg21066": "Das Antriebsrad ist überladen",
    "lang.ark.fed.containerCode": "Code des Behälters",
    "lang.mwms.monitorRobotMsg21065": "Die ursprünglichen Daten zur Hindernisvermeidung wurden nicht innerhalb von zwei Sekunden aktualisiert",
    "lang.mwms.monitorRobotMsg21064": "Ladegerät-Sensor Ausfall",
    "lang.ark.warehouse.workflowTemplateDescription": "Die Workstation bindet die Prozessvorlage, d.h. Aufgaben wie Materialzufuhr und Bewegung (Verwaltungsmodus) können gemäß dem interaktiven Modus der Vorlage ausgeführt werden.",
    "lang.ark.fed.screen.hybridRobot.offsetValueR": "Versatzwert der Ebene R",
    "lang.ark.fed.deliveryOrder": "Verteilungssequenz",
    "lang.ark.fed.isDefaultAction": "Standardinteraktion",
    "lang.mwms.monitorRobotMsg21059": "Ausgelöst durch den hinteren Antikollisionsstreifen",
    "lang.mwms.monitorRobotMsg21058": '"Die Decodierung des Shelf-QR-Codes ist falsch. Zum Beispiel: Der schwarze Rahmen wurde gelesen, aber der Codewert ist falsch"',
    "lang.mwms.monitorRobotMsg21057": "Frontstoßstange Auslöser",
    "lang.ark.fed.interfaceLocation": "Schnittstellenadresse",
    "lang.ark.fed.goodsCode": "Material-Code",
    "lang.ark.warehouse.estimateUseTimeUnit.hour": "Stunde",
    "lang.ark.action.interface.failure": "Fehlgeschlagen",
    "lang.mwms.monitorRobotMsg21074": "Laserradardaten fehlen oder Gerätefehler",
    "lang.mwms.monitorRobotMsg21073": "Aufwärtsdekodierung des Regal-QR-Codes ist abgelaufen.",
    "lang.ark.fed.tuesday": "Dienstag",
    "lang.mwms.monitorRobotMsg21072": "Aufwärtsdekodierung des Regal-QR-Codes ist abgelaufen.",
    "lang.mwms.monitorRobotMsg21071": "Regal-QR-Code-Decodierung fehlgeschlagen",
    "lang.ark.fed.addStation": "Neu hinzugefügte Station",
    "lang.mwms.monitorRobotMsg21078": "Nicht behebbarer Fehler des Motormoduls (Ersatz)",
    "lang.mwms.monitorRobotMsg21077": "Behebbarer Fehler des Motormoduls",
    "lang.mwms.monitorRobotMsg21076": "übertemperaturschutz der Batterie",
    "lang.mwms.monitorRobotMsg21075": "Batteriedaten fehlen.",
    "lang.ark.fed.menu.apiSchema": "Schnittstellenkonfiguration",
    "lang.ark.fed.syncInformSuccess": "Informationen erfolgreich synchronisiert.",
    "lang.mwms.monitorRobotMsg21070": "DSP-Datenfeedbackfehler",
    "lang.ark.workflow.area.queueStrategy": "Warteschlangenstrategie",
    "lang.ark.trafficControl.enterType": "Zugangsmodus",
    "lang.ark.fed.uncheckedDataToBeDeleted": "Es werden keine zu löschenden Daten ausgewählt!",
    "lang.mwms.monitorRobotMsg21069": "DSP verlor Herzschlag",
    "lang.mwms.monitorRobotMsg21068": "überstrom des Hubmotors",
    "lang.ark.areaCode.not.exist.stop.range": "Kein Not-Aus in der Umgebung",
    "lang.mwms.monitorRobotMsg21041": "Der Antrieb ist verloren",
    "lang.ark.roller.docking.feeding": "Rollenendezufuhr",
    "lang.ark.fed.menu.robotUpgradeLog": "Aufrüstprotokoll des Roboters",
    "lang.mwms.monitorRobotMsg21040": "CAN2-Kommunikationsfehler",
    "lang.ark.fed.selectMaterialAndQuantity": "Material und Menge auswählen",
    "lang.mwms.monitorRobotMsg21045": "Die Grundwerte von zwei Kalibrierungen vor und nach dem Kreisel haben sich zu stark verändert",
    "lang.ark.workflow.workflowTaskFetchDuplicate": "Holbetrieb-Wiederholung",
    "lang.mwms.monitorRobotMsg21044": "Kreiseltemperatur ändert sich zu stark.",
    "lang.mwms.fed.user.disable": "Benutzer aktivieren/deaktivieren",
    "lang.mwms.monitorRobotMsg21043": "DSP verloren",
    "lang.mwms.monitorRobotMsg21042": "Encoder trennen",
    "lang.ark.fed.pleaseSelectTheControlButtonToDelete": "Bitte wählen Sie die zu löschende Steuertaste",
    "lang.ark.workflow.arrive.action.goTurnOfAngle.pos270": "270°",
    "lang.mwms.monitorRobotMsg21038": "Die Powerbox hat falsche Batteriedaten erhalten",
    "lang.mwms.monitorRobotMsg21037": "Abnormale Verbindung zwischen Strombox und Hauptsteuerungskommunikation",
    "lang.mwms.monitorRobotMsg21036": '"Fehler-Hindernisvermeidungssensordaten, die von der Hindernisvermeidungsbox oder dem Obercomputer empfangen werden"',
    "lang.mwms.monitorRobotMsg21035": "Abnormale Kommunikationsverbindung zwischen Hindernisvermeidungsbox und Master-Control",
    "lang.ark.fed.fiveLight": "Fünffarbleuchte",
    "lang.ark.fed.successHandler": "Erfolgreich verarbeitet",
    "lang.ark.fed.normal": "Normal",
    "lang.mwms.monitorRobotMsg21039": "CAN1-Kommunikationsfehler",
    "lang.mwms.monitorRobotMsg21052": "Fehler beim Speichern des Bildes",
    "lang.mwms.monitorRobotMsg21051": "Das rechte Rad kugelt beim Bewegen in einer geraden Linie",
    "lang.mwms.monitorRobotMsg21050": '"Das linke Rad schleudert, wenn es sich in einer geraden Linie bewegt"',
    "lang.ark.fed.component.workflow.label.equipType": "Gerätetyp",
    "lang.ark.fed.serialNumber": "S/N",
    "lang.mwms.monitorRobotMsg21056": "Antriebsrad-Encoder-Impuls-überlauf",
    "lang.mwms.monitorRobotMsg21055": "Antriebsrad-Encoder-Impulsanzahl nicht aktualisiert",
    "lang.mwms.monitorRobotMsg21054": "Der Hubmotor kann nicht heben",
    "lang.mwms.monitorRobotMsg21053": "Nicht-QR-Codebereich Bild-Feedback",
    "lang.ark.api.flowStrategyIsNull": "Die Workflow-Matching-Strategie ist leer",
    "lang.ark.fed.operationalProcessConfiguration": "Konfiguration des Betriebsprozesses",
    "lang.ark.apiCommonCode.locationToNotExists": "LocationCode: {0} existiert nicht",
    "lang.ark.fed.noAvailableRobotsWereFound": "Keine verfügbaren Roboter gefunden",
    "lang.ark.fed.menu.interfaceLog": "Schnittstellenprotokoll",
    "lang.ark.fed.orderTaskHang": "Setzen Sie das Dokument aus",
    "lang.ark.fed.trafficControl": "Verkehrskontrolle",
    "lang.ark.fed.stationExistPassWorkflowInstance": "Eine Prozessinstanz, die diese Arbeitsstation durchläuft, existiert. Bitte versuchen Sie es später noch einmal",
    "lang.ark.waveStatus.distributed": "Verteilung beendet",
    "lang.mwms.monitorRobotMsg21049": "Antriebsrad-Verriegelungsfehler",
    "lang.mwms.monitorRobotMsg21048": "Kein Batterie-Update in 200 Sekunden",
    "lang.mwms.monitorRobotMsg21047": "Keine Hindernisvermeidungsdatenaktualisierung innerhalb von 2 Sekunden",
    "lang.mwms.monitorRobotMsg21046": "Das Antriebsrad wurden beim Drehen geschleudert",
    "lang.mwms.monitorRobotMsg.targetnotfree": "Zielpunkt (Zielbereich) ist nicht in Leerlauf.",
    "lang.ark.illegal_containerType_code": "Falsches Container-Codierungsformat",
    "lang.ark.fed.menu.editWorkstationConfig": "Display-Steuerung",
    "lang.mwms.monitorRobotMsg21023": "Timeout bei Ceil-Rect der DSP-Datei",
    "lang.mwms.monitorRobotMsg21022": "Mehr als 2 QR-Codes am Boden fehlen.",
    "lang.mwms.monitorRobotMsg21021": '"Wenn in dem Regal platziert wird, nicht ablegen, wenn der QR Code in einer schlechten Verfassung ist, um eine Kollision mit naheliegenden Regalen zu vermeiden."',
    "lang.mwms.monitorRobotMsg21020": '"Es ist nicht empfohlen, das Regal beim Drehen an die Regalposition anzupassen"',
    "lang.ark.container.entry.failed": "",
    "lang.ark.apiRobotTaskCode.robotIdNotEmpty": "Die Roboter-ID darf nicht null sein",
    "lang.ark.apiContainerCode.containerCategoryOnlyOneNotMatch": "Fehlschlag einer eindeutigen containerCategory Zuordnung",
    "lang.ark.hitStrategy.shelfDenseStorage": "Kompakte Lagerung im Stützenkorridor ",
    "lang.ark.action.interface.containerCode": "",
    "lang.ark.fed.completed": "Abgeschlossen",
    "lang.mwms.monitorRobotMsg21016": "Der Roboter geht bei Ladung rückwärts in die falsche Richtung",
    "lang.ark.apiRobotTaskCode.robotTaskNotCreate": "Die Roboteraufgabe wird nicht generiert",
    "lang.mwms.monitorRobotMsg21015": "die Position der Ladestation xy weicht um 20mm von der Stundenkoordinate ab",
    "lang.mwms.monitorRobotMsg21014": "der Roboter bewegt sich mit einer Abweichung von 20mm xy-Koordinaten zur Ladestation",
    "lang.ark.fed.excel.deviceCodeNotExist": "",
    "lang.ark.fed.warehouse.goods": "Materialmanagement",
    "lang.mwms.monitorRobotMsg21013": "Erkennung von Winkelwechsel des Encoders im Roboter-Stopp-Modus",
    "lang.ark.fed.ruleExpression": "Ausdruck",
    "lang.mwms.monitorRobotMsg21019": "Der aktuelle Punkt und die Ausgangsposition des Roboters überschreiten die Grenze",
    "lang.mwms.monitorRobotMsg21018": "Der Höhenparameter ist beim Abstieg nicht korrekt",
    "lang.mwms.monitorRobotMsg21017": "Der Parameter der Hubhöhe ist falsch",
    "lang.mwms.monitorRobotMsg21030": "Das Rad rutschte während der Bewegung.",
    "lang.ark.workflow.task.status.InterruptWaiting": "Warten unterbrechen",
    "lang.mwms.monitorRobotMsg21034": "Hindernisvermeidungsbox/Obercomputer erhielt keine Hindernisvermeidungssensordaten",
    "lang.mwms.monitorRobotMsg21033": "In den manuellen Modus gewechselt.",
    "lang.mwms.monitorRobotMsg21032": "Notfall-Stopp-Knopf ausgelöst",
    "lang.mwms.monitorRobotMsg21031": "Beim Erkennen des Regalmodells ist eine Ausnahme aufgetreten.",
    "lang.ark.fed.addRack": "Ein Regal hinzufügen ",
    "lang.ark.fed.areYouSureToDeleteTheListInformation": "Sind Sie sicher, die Listeninformation zu löschen?",
    "lang.ark.fed.interfaceTaskCode": "Externe Aufgabennummer",
    "lang.ark.fed.severityLevel": "Schwere",
    "lang.mwms.monitorRobotMsg21027": "Laden fehlgeschlagen",
    "lang.mwms.monitorRobotMsg21026": "Der manuelle Modus hat einen Fehler beim Ausführen einer Aufgabe erhalten",
    "lang.mwms.monitorRobotMsg21025": "Falscher Winkel bei der Planung eines kleinen Pfades",
    "lang.ark.fed.nodeActionName": "Name der Knoteninteraktionskonfiguration",
    "lang.mwms.monitorRobotMsg21024": "Timeout bei Abruf der Ceil-Dekodierung der DSP-Datei",
    "lang.mwms.monitorRobotMsg21029": "Nach dem Wechsel zur QR-Code-Navigation kann der erste QR-Code auf dem Boden nicht erreicht werden.",
    "lang.ark.waveTaskStatus.distributing": "Verteilen",
    "lang.mwms.monitorRobotMsg21028": "Während der Bewegung sind die QR-Codes oben in den Regalen außer Sichtweite.",
    "lang.ark.fed.hybridRobot.hybridRobotType.singleLift": "Einzelne Hebebühne",
    "lang.ark.fed.receiveCallTask": "Materialaufrufaufgabe erhalten",
    "lang.mwms.monitorRobotMsg21001": "Die Abweichung des geraden Gehwinkels beträgt mehr als 3°",
    "lang.ark.fed.waveOrders": "Anzahl der Dokumente",
    "lang.mwms.monitorRobotMsg21000": "Die seitliche Abweichung der QR-Code-Position am Ende ist >20mm und der Winkel ist größer als 2°.",
    "lang.ark.fed.deliverOrder": "Verteilungssequenz",
    "lang.ark.fed.leftRotation": "Gegen den Uhrzeigersinn",
    "lang.ark.fed.waveTriggerCondition": "Auslösebedingungen",
    "lang.ark.fed.queueUp": "Warteschlange",
    "lang.ark.hitStrategy.cyclicSave": "Zyklische Speicherung",
    "lang.ark.fed.menu.vens.equipmentAssociatedInfo": "Aufzugskonfiguration",
    "lang.ark.fed.optionalDockPointPosition": "optionalen Punktes",
    "lang.ark.fed.second": "Zweite",
    "lang.ark.fed.interfaceInstructMsg1": "Fügen Sie mindestens einen Zuweisungswert zum gesendeten Parameter hinzu!",
    "lang.ark.workflow.area.noorder": "Keine Warteschlange",
    "lang.ark.fed.interfaceInstructMsg2": "Fügen Sie mindestens einen Prozessablauf zum zurückgegebenen Parameter hinzu!",
    "lang.ark.fed.menu.apiPlatform": "Schnittstellenplattform",
    "lang.ark.fed.interfaceInstructMsg0": "Bei der Zuweisung von Werten zu gesendeten Parametern oder bei der Verarbeitung von zurückgegebenen Parametern liegen ungesicherte Daten vor!",
    "lang.mwms.monitorRobotMsg21012": "Bildfusion Winkeländerungserkennung im Roboter-Stopp-Modus",
    "lang.mwms.monitorRobotMsg21011": "Gyro-Integral-Erkennung im Roboter-Stopp-Modus",
    "lang.ark.fed.rackName": "Regalname",
    "lang.mwms.monitorRobotMsg21010": "Das Regal wurde schief gehoben",
    "lang.ark.fed.day": "Tag",
    "lang.ark.workflow.task.status.assign": "Aufgabe erteilt",
    "lang.authManage.web.common.modifySuccess": "Erfolgreich modifiziert",
    "lang.ark.interface.interfaceDesc": "Schnittstellenbeschreibung",
    "lang.ark.apiContainerCode.mapRemoveContainerFail": "Fehler beim Entfernen des Containers von der Karte",
    "lang.ark.fed.confirmCallRobot": "Bestätigen Sie den Ruf des Roboters",
    "lang.authManage.web.common.dataPermission": "Datenberechtigung",
    "lang.ark.fed.copySuccess": "Kopiert!",
    "lang.ark.fed.shelfEditor": "Regalbearbeitung",
    "lang.ark.fed.chromeForbidScan": "2. Wenn der Browser eine aktivierte Kamera hat, die blockiert wird, klicken Sie auf das Warnsymbol in der Suchleiste und folgen Sie den Anweisungen, um die Blockierung der Kamera aufzuheben.",
    "lang.ark.workflow.goShift": "Offset",
    "lang.mwms.monitorRobotMsg21005": "Die Richtung der Steueranweisung ist nicht konsistent mit der Richtung der tatsächlichen Winkeldrehung",
    "lang.mwms.monitorRobotMsg21004": "Das Antriebsrad wurden beim Drehen geschleudert",
    "lang.mwms.monitorRobotMsg21003": "Offset der Robotermittel überschreitet die Grenze",
    "lang.mwms.monitorRobotMsg21002": "Die Differenz zwischen dem Drehwinkel Integral und dem Winkel des Encoders überschreitet die Grenze",
    "lang.mwms.monitorRobotMsg21009": "Der relative Fehler des zweidimensionalen Codes des Regals und des zweidimensionalen Codes des Bodens ist über die Grenze",
    "lang.ark.workflow.existsAttachTaskNotComplete": 'Der untergeordnete Knoten des aktuellen Prozesses wurde nicht beendet. Sie können den Vorgang "vollständig" nicht ausführen.',
    "lang.ark.fed.unloading": "Keine Materialzufuhr",
    "lang.mwms.monitorRobotMsg21008": "Drehen Sie den Winkel des Gestells um mehr als 180°",
    "lang.mwms.monitorRobotMsg21007": "Der Fehler der Zieleinstellung und Stop-Einstellung ist über der Grenze",
    "lang.mwms.monitorRobotMsg21006": "Beim Anpassen des Lichtbogens entsprechend dem Punkt überschreitet der akkumulierte Fehler der Bahnglätte die Grenze",
    "lang.ark.robot.robotExist": "Roboter ist vorhanden. Ergänzen Sie ihn nicht mehrmals.",
    "lang.ark.fed.personalizationOptions": "Personalisierte Optionen",
    "lang.ark.fed.executionStatus": "Ausführungsstatus",
    "lang.ark.fed.cancelWave": "Wellenauswahl abbrechen",
    "lang.ark.fed.noWorkflowPleaseCreateANewWorkflow": "Keine Prozesse, bitte erstellen Sie einen neuen Prozess.",
    "lang.ark.fed.rackEntry": "Regal-Eingang",
    "lang.ark.fed.workstation.noMoreWorkstations": "",
    "lang.ark.fed.slight": "Niedrig",
    "lang.ark.fed.childWorkflowUnpublished": "Unterprozess {0} ist nicht veröffentlicht. Bitte veröffentlichen Sie zunächst den Unterprozess.",
    "lang.ark.fed.removeFromTheSystem": "Aus dem System entfernen",
    "lang.authManage.web.others.importLicense": "Zertifikat importieren",
    "lang.ark.fed.theConnectionBetweenTheDockingPointAndWorkstationCannotBe": "Angeschlossene Punkte/Arbeitsstaionen können nicht verbunden werden",
    "lang.gles.strategy": "",
    "lang.ark.fed.curvePathOfRobotWalkable": "Kurvenpfade, auf denen der Roboter laufen kann",
    "lang.ark.apiContainerCode.containerCategoryMustSelect": "Der Containertyp muss angegeben werden",
    "lang.ark.fed.menu.physicalButtonLog": "Physikalisches Tastenprotokoll",
    "lang.mwms.fed.taskResolve": "Aufgabenteilung",
    "lang.ark.workflow.taskNodeWaiting": "Aufgaben-Knoten wartet",
    "lang.ark.fed.roller": "Rollbahn",
    "lang.mwms.pickingException": "Bestandsstörung",
    "lang.ark.fed.screen.equipmentAssociatedInfo.rowDeleteConfirm": "Stornierung kann nicht rückgängig gemacht werden. Möchten Sie wirklich fortfahren?",
    "lang.ark.fed.pleaseSelectTheRobotBeforeDrawingTheMap": "Bitte wählen Sie einen Roboter aus, bevor Sie eine Karte zeichnen",
    "lang.ark.record.robotCallback.fbShift": "",
    "lang.ark.apiCommonCode.robotTypeNotEmpty": "Der Parameter robotType darf nicht leer sein.",
    "lang.ark.waveStatus.disCanceled": "Wellenauswahlabgebrochen",
    "lang.ark.fed.theAudioCacheing": "Audio-Puffer zuhören... Später erneut versuchen.",
    "lang.ark.fed.baseWorkStation": "Standard-Arbeitsstation",
    "lang.ark.fed.thereIsNoWorkflowAtThisDockPoint": "Keine Prozesse für diesen Dockingpunkt ",
    "lang.ark.countNum": "Gesamt:",
    "lang.ark.fed.allowPutDown": "{0} nur ein Ablegen-Befehl erlaubt.",
    "lang.ark.workflow.recycleTypeNoConfig": "",
    "lang.ark.fed.containerType": "Container-Typ",
    "lang.ark.fed.upload": "Hochladen",
    "lang.ark.fed.priority": "Priorität ",
    "lang.mwms.monitorRobotMsg.sendtaskfail": "Ausnahme beim Senden von RMS durch Aufgabe. Bitte überprüfen Sie den RMS Laufstatus.",
    "lang.authManage.web.common.creatTime": "Erstellungszeit",
    "lang.ark.fed.unloadWholeOrder": "Voller Auftrag ablegen",
    "lang.ark.warehouse.hasSameProductionLineName": "Der gleiche Produktionslinienname existiert bereits.",
    "lang.ark.fed.cycleTimes": "Zyklusnummer",
    "lang.ark.fed.cancel": "Stornieren",
    "lang.ark.fed.screen.hybridRobot.robotBindDevice": "Binden Sie den Roboter an den Oberbau",
    "lang.ark.fed.startPointName": "Name des Startpunkts",
    "lang.ark.fed.materialNo": "Dokumentzeilennummer",
    "lang.ark.fed.workstationOnlyOneUser": "",
    "lang.ark.fed.cellCodeType": "Codetyp",
    "lang.ark.workflow.template.type.nodeToNode": "Punkt-zu-Punkt-Bewegung",
    "lang.ark.fed.goodsLocationLimtMax": "Obergrenze der Anzahl der Materialien auf dem Vorratsbehälter",
    "lang.ark.fed.screen.hybridRobot.robotBodyTip": "Daten kommen aus RMS-Roboterdaten",
    "lang.ark.interface.interfaceDesc.type": "Feldtyp",
    "lang.ark.externalDevice.cautionContent": "Der ausgewählte Gerätetyp stimmt nicht mit den interaktiven Bewegungen des Geräts überein. Eine vollständige Übereinstimmung ist erforderlich, bitte sie überprüfen und anpassen!",
    "lang.ark.apiStationCode.stationStopPointNotMatch": "Der Andockort in der Arbeitsstation stimmt nicht überein",
    "lang.ark.workflow.arrive.action.goTurnOfAngle.neg360": "-360°",
    "lang.ark.fed.pleaseUploadImage": "Sie müssen ein gültiges Barcode-Bild hochladen.",
    "lang.ark.fed.fullBinsChange": "Der Vorratsbehälter ist voll, bitte ändern Sie den Vorratsbehälter.",
    "lang.ark.fed.destGroup": "Knotengruppe",
    "lang.ark.fed.lower": "Ab",
    "lang.ark.fed.operationMode": "Betriebsart",
    "lang.ark.fed.dynamicNotAllowBranch": "Dieser Zweig ist als dynamischer Zielpunkt nicht erlaubt",
    "lang.ark.fed.beforeExecute": "Bewahren Sie vor jeder Ausführung das neueste Protokoll auf",
    "lang.ark.fed.automaticExecutionExpected": "Automatische Ausführung, geschätzt",
    "lang.ark.fed.areUSureStop": "Not-Aus bestätigt?",
    "lang.ark.fed.optionsSetting": "Optionseinstellungen",
    "lang.ark.dynamicTemplate.nextPoint": "Nächster Knoten",
    "lang.ark.fed.timeRange": "Zeitbereich",
    "lang.ark.fed.runMode.load": "",
    "lang.ark.fed.confirmShelfLeave": "Sind Sie sicher, zu entfernen?",
    "lang.ark.fed.creator": "Ersteller",
    "lang.ark.fed.containsIllegalCharacters": "Unzulässige Zeichen enthalten",
    "lang.ark.fed.menu.vens.dmpHeartbeat": "",
    "lang.ark.workflow.notAllowCancelIfTaskPhase": "",
    "lang.ark.fed.notEnabled": "Nicht auf",
    "lang.ark.fed.singleFactoryMultipleEntrances": "Mehrere Einträge für einen Hersteller",
    "lang.authManage.web.permission.roleName": "Rollenname",
    "lang.ark.fed.productLineAutoSelect": "Automatische Verteilung nach Material",
    "lang.ark.fed.stationCode": "Arbeitsstations-Nr.",
    "lang.ark.fed.actionsNotAllowAddContainer": "Der Endknoten hat einen Interaktionskonfigurationsfehler. Der Container kann nicht hinzugefügt werden!",
    "lang.ark.warehouse.lineStationNotEmpty": "Produktionslinienstation darf nicht Null sein",
    "lang.ark.button.operation.command.send": "Liefern",
    "lang.ark.fed.abnormalInformation": "Störungsinformation",
    "lang.mwms.fed.warehouseVolumeCharts": "Lagerkapazitätsbericht",
    "lang.ark.workflow.platformPriority": "Logistikcontainer (Regal) hat Priorität",
    "lang.ark.fed.shelfAttribute.RETURN": "",
    "lang.ark.goodsTask.export.title.fileName": "Pick_list",
    "lang.ark.fed.systemVersion": "Systemversion Nr.",
    "lang.ark.interface.apiNext": "Führen Sie den Workflow weiter aus",
    "lang.ark.fed.illegalProcess": "Unzulässiger Prozess",
    "lang.ark.workflow.buttonAlreadyExists": "Diese Controller-Taste ist bereits vorhanden und kann nicht wiederholt hinzugefügt werden",
    "lang.ark.fed.theConnectionIsClosedAndReconnectedToTheServer": "Verbindung verloren, Verbindung zum Server wird wiederhergestellt..",
    "lang.ark.robotDeviceComponent.robotType": "Robotermodell",
    "lang.ark.fed.amount": "Menge abholen/liefern",
    "lang.mwms.fed.roleManage": "Rollenverwaltung",
    "lang.ark.workflow.arrive.action.goTurnOfSide.side": "Drehen zu",
    "lang.ark.warehouse.zagvdbmNotexits": "Materialeinspeisepunkt {0} existiert nicht",
    "lang.ark.workflow.area.releaseStrategy": "Freigabe-Strategie",
    "lang.ark.fed.remark": "Hinweis",
    "lang.ark.fed.confirmMoving": "Sind Sie sicher, zu verschieben?",
    "lang.ark.workflow.task.status.canceled": "Stornierung abgeschlossen",
    "lang.ark.fed.workFlowNodeNotEdit": "Der aktuelle Knoten hat keine Bearbeitungselemente",
    "lang.ark.workflowConfig.beginOrEndNodeCanNotEmpty": "Startknoten und Endknoten müssen für den Workflow konfiguriert werden",
    "lang.ark.fed.goodsLocationInfo": "Vorratsbehälterinformationen",
    "lang.ark.fed.robotStrategy": "Roboterstrategie",
    "lang.ark.workflow.condition.in": "Enthalten",
    "lang.ark.fed.allType": "Alle Typen",
    "lang.ark.warehouse.materialPointOrderNotNull": "Die Zufuhrsequenz darf nicht leer sein.",
    "lang.ark.api.goturn.turnangleError": "Der Drehungswinkelwert des Containerrotationsparameters ist kein Vielfaches von 90.",
    "lang.ark.fed.pickUpTaskNumber": "Nummer des Materialanforderungsblatts",
    "lang.ark.equipment.notSelectExistEquipment": "Ein schon bestehendes Gerät kann nicht anfügt werden. Name des bestehenden Geräts: {0}",
    "lang.ark.warehouse.hasSameSort": "Die gleiche Verteilungssequenz existiert bereits.",
    "lang.ark.interface.interfaceDesc.format": "Feldlänge",
    "lang.ark.workflow.wareHouseAllType": "Alles",
    "lang.ark.fed.ruleName": "Regelname",
    "lang.ark.fed.screen.equipmentAssociatedInfo.columnEquipType": "Gerätetyp",
    "lang.ark.fed.retryDuration": "Wiederholungszeit (in Minuten)",
    "lang.mwms.fed.excelImport": "Excel-Importabfrage",
    "lang.ark.fed.describe": "Beschreibung",
    "lang.ark.fed.applicationEntryTime": "Bewerbungs- / Eintrittszeit",
    "lang.ark.fed.number": "Nummer",
    "lang.ark.workflow.putDownStuff": "Lieferung von Fracht",
    "lang.ark.fed.mapManagement": "Kartenverwaltung",
    "lang.ark.fed.screen.hybridRobot.binOrderTip": "Das ist die Seriennr. des Vorratsbehälters des Geräts.",
    "lang.ark.action.interface.conditionValue": "Bedingter Wert",
    "lang.ark.fed.interactionOfInternalInterface": "Interne Schnittstelleninteraktion",
    "lang.ark.operatelog.operatetype.auto.group": "Automatischer Prozess (Prozessgruppe)",
    "lang.ark.fed.demandForMaterialsTitle": "Material, das auf Anfrage aufgerufen wird: Alle Prozesse, die die Arbeitsstation durchlaufen, können an der Arbeitsstation initiiert werden.",
    "lang.ark.fed.screen.equipmentAssociatedInfo.dialogDetail.btnAddEquip": "Gerät anfügen",
    "lang.ark.fed.uploadImageMessage2": "Die hochgeladene Bilddatei sollte idealerweise vor einem weißen Hintergrund sichtbar sein.",
    "lang.mwms.fed.putaway": "Einlagerungsmanagement",
    "lang.ark.fed.cancelMiddlePoint": "Durchfahren-Punkt gelöscht",
    "lang.ark.fed.updateMap": "Karte aktualisieren",
    "lang.ark.fed.screen.hybridRobot.robotPointOffset": "Versatz des Roboterpunkts",
    "lang.ark.robot.go.receive": "Automatischer Empfang",
    "lang.gles.logisticsConfig.stationDefaultConfig": "",
    "lang.authManage.web.others.packup": "Einpacken",
    "lang.ark.fed.uploadImageMessage1": "Die hochgeladene Bilddatei sollte idealerweise vor einem dunkelgrauen Hintergrund zu sehen sein.",
    "lang.ark.fed.uploadImageMessage0": "Die hochgeladene Bilddatei sollte idealerweise vor einem hellblauen Hintergrund zu sehen sein.",
    "lang.ark.fed.twoPointMode": "Doppelpunktmodus",
    "lang.ark.fed.goodsPlaceType.put": "Platzierungstor",
    "lang.ark.fed.lowVolumeCharging": "Niedrige Akkuladung",
    "lang.ark.fed.emptyContainerArea": "Rückgabebereich für leere Boxen",
    "lang.ark.robot.go.return.box": "Box zurückführen ",
    "lang.ark.workflow.enterMapDestNotExistReturnFail": "Kein Endpunkt der Einlagerung in das Regal vorhanden. Rückgabe des leeren Boxen fehlgeschlagen!",
    "lang.ark.robotDeviceComponent.recordAlreadyExist": "Der Datensatz ist bereits vorhanden und kann nicht wiederholt hinzugefügt werden!",
    "lang.ark.workflow.sendOperationFailed": "Liefervorgang fehlgeschlagen",
    "lang.ark.fed.screen.hybridRobot.robotTypeNotAddedTip": "Das Modell ist noch nicht in dem Mitrobotertyp eingegeben. Fügen Sie es hinzu.",
    "lang.ark.workflow.function.type.recycleArea": "Wiederherstellungsbereich",
    "lang.ark.workflowConfig.cellFunctions.skip": "Überspringen",
    "lang.ark.sys.config.group.other": "Andere Konfiguration",
    "lang.ark.fed.homePageBackgroundImageUploadViewSuccess": "",
    "lang.authManage.web.common.userName": "Benutzername",
    "lang.ark.fed.byRackCode": "Regalcodierung",
    "lang.ark.fed.areUSureClearLock": "Freischaltung bestätigt?",
    "lang.ark.workflowRule.referenced.cannot.be.delete": "Die Regelkonfiguration wird von der Workflow-Konfiguration referenziert und kann nicht gelöscht werden",
    "lang.ark.fed.materialStation": "Station zur Materialverteilung verfügbar",
    "lang.ark.workflow.cycleType.infiniteLoop": "Unendliche Schleife",
    "lang.ark.fed.rackEntrance": "Container-Eingang",
    "lang.ark.fed.closeAutoFlow": "Automatischen Prozess schließen",
    "lang.ark.workflow.areaInUsed": '"Bereich Nr. ({}) existiert, bitte überprüfen!"',
    "lang.ark.fed.robot": "Roboter",
    "lang.ark.fed.sourceOrDestination": "Quelle/Ziel",
    "lang.ark.fed.externalDeviceType": "Gerätetyp",
    "lang.ark.fed.enabledShelfAttribute": "",
    "lang.ark.fed.dataType.value": "Parametertyp",
    "lang.ark.apiContainerCode.containerCodeNotBlank": "Die Containernummer muss angegeben werden",
    "lang.ark.fed.messagePush": "Push-Nachricht",
    "lang.ark.fed.fullWorkStation": "VOLLE Version der Arbeitsstation",
    "lang.ark.fed.excel.deviceCodeRepeat": "",
    "lang.ark.fed.issuedQuantity": "Tatsächliche Verteilungsmenge",
    "lang.ark.fed.flowTemplate": "Prozessvorlage",
    "lang.gles.stock": "",
    "lang.ark.fed.taskStartTime": "Startzeit der Aufgabe",
    "lang.ark.workflow.shelfEntryNotSupported": "Container-Eingang wird nicht unterstützt",
    "lang.ark.fed.unsupportCamera": "Ihr Browser unterstützt das Scannen von Barcodes nicht. Laden Sie ein Bild hoch, um den Barcode zu parsen.",
    "lang.ark.fed.serious": "Hoch",
    "lang.ark.fed.piece": "Stück",
    "lang.ark.fed.dockPoint": "Dockingpunkt",
    "lang.mwms.fed.putawayRuleDesignatedShelf": "Spezifiziertes Regal mit Einlagerungsregeln",
    "lang.mwms.fed.freeze": "Einfrieren von Lagerbeständen",
    "lang.ark.action.interface.locationTo": "",
    "lang.ark.fed.allValuesInTheCollectionAndRelationships": "Alle Werte in der Sammlung haben eine UND-Beziehung",
    "lang.ark.fed.thereIsNoOptionalRobot": "Keine Roboter zur Auswahl!",
    "web.c.RoleAPI.item0090": "Der Rollenname ist bereits vorhanden. Bitte verwenden Sie einen anderen Namen!",
    "lang.ark.fed.previousStation": "Vorherige Station",
    "lang.ark.fed.pleaseEnterANonzeroPositiveInteger": "Bitte geben Sie eine positive Ganzzahl ungleich Null ein",
    "web.c.RoleAPI.item0092": "Das Rollenschlüsselwort kann nicht null sein!",
    "web.c.RoleAPI.item0091": "Fehler beim Hinzufügen der Rolle!",
    "web.c.RoleAPI.item0094": "Fehler beim Ändern!",
    "lang.ark.fed.currentPoint": "Aktueller Punkt",
    "lang.ark.fed.authWorkStation": "Berechtigung der Arbeitsstation",
    "web.c.RoleAPI.item0093": "Der Rollenname darf nicht null sein!",
    "web.c.RoleAPI.item0096": "Bitte wählen Sie eine Rolle aus!",
    "lang.ark.fed.taskDashboard": "Aufgaben-Anzeigentafel",
    "web.c.RoleAPI.item0095": "Fehler beim Löschen!",
    "lang.ark.fed.pleaseSelectWorkStation": "Bitte wählen Sie mindestens eine Arbeitsstation aus!",
    "web.c.RoleAPI.item0089": "Keine Rollendaten",
    "lang.ark.fed.import": "Importieren ",
    "lang.ark.fed.workOrientation": "Arbeitsorientierung:",
    "lang.ark.fed.floorNo": "Die {0} Ebene",
    "lang.ark.fed.destPoint": "Zielposition",
    "lang.ark.workflow.actionNameExists": "Der Interaktionsname existiert bereits!",
    "lang.ark.targetNotFree": "Der Zielort befindet sich nicht im Leerlauf!",
    "lang.ark.robot.robotModelBindTopModulesNotAllowDelete": "Der Verbundrobotertyp, der an das Oberdeck gebunden wurde, kann nicht gelöscht werden",
    "lang.ark.fed.autoRefreshEvery20Seconds": "Die Überwachungsdatensätze werden automatisch alle 20 Sekunden aufgefrischt.",
    "lang.ark.fed.rackStatusRecovery": "Wiederherstellung des Regalzustands",
    "lang.ark.fed.email": "E-Mail",
    "lang.ark.fed.SMPAlter": "Alarmbenachrichtigung",
    "lang.ark.fed.triggerContent": "Inhalt auslösen",
    "lang.ark.fed.chargingStation": "Ladestation",
    "lang.ark.workflow.noContainersWereFoundThatCouldBeMoved": "Beweglicher Container nicht gefunden!",
    "lang.ark.fed.selectMaterial": "Wählen Sie Materialien aus",
    "lang.ark.fed.boxMoving": "Container-Behandlung",
    "lang.ark.fed.monitor.taskDashboard": "Aufgaben-Anzeigentafel",
    "lang.ark.singleCellStation.canNotEdit": "Einzelpunktstation kann nicht bearbeitet werden",
    "lang.ark.fed.required": "Erforderlich",
    "lang.ark.fed.wareHouseSetting": "Eingangs-/Ausgangs-Konfiguration",
    "lang.ark.warehouse.configEnableDescription": 'Die Konfiguration kann nur aktualisiert werden, wenn "aktivieren" geschlossen ist. Bitte stellen Sie sicher, dass die Aktualisierung während der arbeitsfreien Zeit erfolgt.',
    "lang.ark.fed.goodsNonExistent": "Das aktuelle Material ist nicht vorhanden. Versuchen Sie es mit einem anderen.",
    "lang.ark.fed.exceptionDescription": "Störungsbeschreibung",
    "lang.ark.fed.deliveryCompleteTime": "Liefer-Fertigstellungszeit",
    "lang.ark.workflow.archiveType": "Archiv-Arten",
    "lang.ark.interface.apiWorkflowList": "Workflow-Abfrage",
    "lang.ark.fed.failedRetryTime": "Abrufzeit",
    "lang.ark.fed.shelfAttribute.P1": "",
    "lang.ark.fed.shelfAttribute.P3": "",
    "lang.ark.fed.soon": "Kommt bald",
    "lang.ark.fed.pleaseEnterContent": "Bitte geben Sie den Inhalt ein",
    "lang.ark.button.operation.command.systemControl": "Systemkontrolle",
    "lang.ark.fed.menu.editingWorkflow": "Arbeitsablauf",
    "lang.ark.fed.paramConfig": "Parameterkonfiguration",
    "lang.gles.baseData.warehouseArea": "",
    "lang.ark.fed.robotMonitoring": "Roboterüberwachung",
    "lang.ark.fed.warnType": "Alarmmodus",
    "lang.ark.interface.apiCancel": "Aufgabe widerrufen",
    "lang.mwms.fed.index": "Homepage",
    "lang.ark.shelf.status.noValiable": "Kein Regal vorhanden ",
    "lang.ark.fed.interface": "Schnittstellenbefehl",
    "lang.ark.workflow.task.status.cancelToNewAssignation": "Zustellen an den vorgesehenen Platz",
    "lang.ark.fed.waitingToSend": "Warten, um zu senden",
    "lang.ark.fed.moveTo": "Mobile",
    "lang.ark.fed.forceDeleteConfirmMsg1": "Prozessabbruch fehlgeschlagen. Erzwungenes Löschen verarbeiten oder nicht.",
    "lang.ark.workflow.shelfExchange": "Regaltausch",
    "lang.ark.fed.id": "Verschlüsselung:",
    "lang.mwms.fed.receive": "Inspektion der eingehenden Fracht",
    "lang.ark.fed.onePicking": "Materialanforderung für einen Einzel-Roboter",
    "lang.ark.fed.palletPoint": "Position der Palette",
    "lang.ark.fed.batchCancellation": "Batch-Stornierung",
    "lang.ark.interface.businessStatus": "Aufgabenstatus",
    "lang.ark.fed.value": "Wert",
    "lang.ark.robot.invalid": "Ungültiger Modus",
    "lang.ark.fed.reqNum": "Ursprüngliche Bestellnummer",
    "lang.ark.fed.startScanningTheMap": "Scannen der Karte starten",
    "lang.ark.fed.file": "Datei",
    "lang.ark.workflowOuterCode.exists": "Der externe Workflow-Code kann nicht wiederholt werden",
    "lang.ark.fed.dayLogo": "Tag(e)-Protokoll",
    "lang.ark.fed.menu.operationLog": "Betriebsprotokoll",
    "lang.ark.fed.task": "Aufgabe",
    "lang.ark.loadCarrier.inUsing": "",
    "lang.ark.fed.normalStation": "Gemeinsame Arbeitsstation",
    "lang.ark.fed.operationInformation": "Betriebsinformationen",
    "lang.ark.interface.response": "Reaktion",
    "lang.ark.record.interface.sendTask": "",
    "lang.ark.fed.allOptions": "Alles",
    "lang.ark.fed.hostCellCode": "Externe Nr.",
    "lang.ark.fed.lowerSpeed": "Geringe Geschwindigkeit",
    "lang.ark.record.robotCallback.arrivePassWaitPoint": "",
    "lang.mwms.monitorRobotMsg10069": "Antriebsbefehlsfehler",
    "lang.ark.fed.menu.mapController": "Kartierung",
    "lang.mwms.monitorRobotMsg10068": "Fehler im Aufgabenschritt",
    "lang.ark.equipment.notFoundEquipment": "Das entsprechende Gerät liegt nicht vor",
    "lang.mwms.monitorRobotMsg10076": "Antriebstrom Kurzschluss",
    "lang.mwms.monitorRobotMsg10075": "Antrieb-überstrom",
    "lang.ark.workflow.workflowTaskStatusError": "Falscher Status der Prozessaufgabe",
    "lang.mwms.monitorRobotMsg10078": "Antriebsmotortemperatur ist zu niedrig",
    "lang.ark.base.license.sysParamForInstanceIdIsNull": "Die Instanz-ID, die vom Zertifikat überprüft wird, ist null",
    "lang.mwms.monitorRobotMsg10077": "Antriebsmotortemperatur ist zu hoch",
    "lang.mwms.monitorRobotMsg10072": "Antrieb-Feedback-Fehler",
    "lang.mwms.monitorRobotMsg10071": "Antriebs-Verfolgungsfehler",
    "lang.mwms.monitorRobotMsg10074": "Antrieb-überspannung",
    "lang.mwms.monitorRobotMsg10073": "Antrieb-Unterspannung",
    "lang.mwms.monitorRobotMsg10070": "Antriebmotorphasenfehler",
    "lang.ark.fed.segmentName": "Name der Verbindungslinie",
    "lang.ark.fed.menu.config": "",
    "lang.ark.workflow.condition.equal": "Gleich mit",
    "lang.ark.fed.funcComponent": "Funktionskomponente",
    "lang.ark.fed.addInterface": "Schnittstellenbefehl hinzufügen",
    "lang.ark.fed.component.workflow.tooltip.specifiedEquip": "Wenn kein angegebenes Gerät gewählt ist, wird standardmäßig nach allen Geräten der ausgewählten Zuführungsart gesucht.",
    "lang.mwms.monitorRobotMsg10079": "Übermäßige Antriebstemperatur",
    "lang.ark.record.upstream.callback": "",
    "lang.ark.workflow.area.range": "Bereichsumfang",
    "lang.ark.fed.delete": "Löschen",
    "lang.ark.api.workflow.break.failure": "Abbruch fehlgeschlagen, Abbruch der Roboteraufgabe fehlgeschlagen",
    "lang.mwms.monitorRobotMsg10082": "Antriebsadressfehler",
    "lang.ark.fed.containerPoint": "Container-Punkt",
    "lang.mwms.monitorRobotMsg10081": "Antriebs-übergeschwindigkeitsalarm",
    "lang.mwms.monitorRobotMsg10080": "Antriebstemperatur ist zu niedrig",
    "lang.authManage.web.common.status": "Status",
    "lang.authManage.web.others.customer": "Kunden-ID",
    "lang.ark.fed.common.btn.edit": "Bearbeiten",
    "lang.ark.interface.shelfMovingCallbackMsg": "Behälter in Bewegung(Rückruf)",
    "lang.ark.fed.pleaseSelectTheConsole": "Bitte das Operationsende auswählen",
    "lang.ark.fed.siteMainInterface": "Feld-Hauptschnittstelle",
    "lang.mwms.monitorRobotMsg10047": "Der Fehler der Zieleinstellung und Stop-Einstellung ist über der Grenze",
    "lang.mwms.monitorRobotMsg10046": "Beim Anpassen des Lichtbogens entsprechend dem Punkt überschreitet der akkumulierte Fehler der Bahnglätte die Grenze",
    "lang.ark.fed.pinkingFinish": "Einspeisung abgeschlossen",
    "lang.mwms.monitorRobotMsg10049": "Der relative Fehler des zweidimensionalen Codes des Regals und des zweidimensionalen Codes des Bodens ist über die Grenze",
    "lang.ark.workflowConfig.configInUse": "Es gibt laufende Workflow-Instanzen. Bitte versuchen Sie es später noch einmal",
    "lang.ark.fed.hour": "Stunde",
    "lang.mwms.monitorRobotMsg10048": "Drehen Sie den Winkel des Gestells um mehr als 180°",
    "lang.mwms.monitorRobotMsg10054": "Abweichung von xy Koordinate beträgt 20mm, beim Eintreffen an der Ladestation",
    "lang.ark.fed.no": "Nein",
    "lang.ark.fed.goodsPlaceType": "Zuführungsart",
    "lang.mwms.monitorRobotMsg10053": "Die Erfassung der Winkelveränderung findet in im Stopp-Modus statt. Zum Beispiel: Ein Fehler wird gemeldet, wenn es durch eine externe Kraft im Ruhemodus bewegt wird.",
    "lang.mwms.monitorRobotMsg10056": "Der Roboter geht bei Ladung rückwärts in die falsche Richtung",
    "lang.mwms.monitorRobotMsg10055": "die Position der Ladestation xy weicht um 20mm von der Stundenkoordinate ab",
    "lang.ark.fed.timeoutInfoTip": "Das Auftrags-Kanban im Aufgaben-Kanban. Zeitüberschreitungsinformationen anzeigen.",
    "lang.mwms.monitorRobotMsg10050": "Das Regal wurde schief gehoben",
    "lang.ark.workflow.mandatoryAllocation": "Verpflichtend",
    "lang.ark.workflow.nodeClassification": "Knotenklassifizierung",
    "lang.mwms.monitorRobotMsg10052": "Bildfusion Winkeländerungserkennung im Roboter-Stopp-Modus",
    "lang.ark.fed.billCreateFail": "Dokumenterstellung fehlgeschlagen",
    "lang.mwms.fed.strategyOrderTime": "Berechnungsstrategie für die Schlusszeit",
    "lang.mwms.monitorRobotMsg10051": '"Gyro-Integral-Erkennung im Roboter-Stopp-Modus. Zum Beispiel: Ein Fehler wird gemeldet, wenn es durch eine externe Kraft im Ruhemodus bewegt wird."',
    "lang.mwms.fed.sysconfig": "Systemkonfiguration",
    "lang.ark.warehouse.materialPointNameNotNull": "Der Name darf nicht Null sein!",
    "lang.ark.agv.instructionRule.executeByNormal": "",
    "lang.ark.fed.goodsPlaceType.fetch": "Abholtor",
    "lang.ark.fed.closeMoreGoodsInfo": "Materialinformationen einklappen",
    "lang.mwms.fed.dictSet": "Datenwörterbuch",
    "lang.ark.fed.allDay": "Ganztägig",
    "lang.ark.fed.newAddedSystem": "Neu hinzugefügtes System",
    "lang.ark.fed.servicePoints": "Servicepunkt",
    "lang.mwms.monitorRobotMsg10058": "Der Höhenparameter ist beim Abstieg nicht korrekt",
    "lang.ark.fed.externalDevice": "Gerätename",
    "lang.mwms.monitorRobotMsg10057": "Der Parameter der Hubhöhe ist falsch",
    "lang.ark.fed.screen.area.groupName": "Gruppierungsname",
    "lang.mwms.monitorRobotMsg10059": "Der aktuelle Punkt und der Wegstartpunkt überschreiten die Grenze.",
    "lang.mwms.monitorRobotMsg10065": "Falscher Winkel bei der Planung eines kleinen Pfades",
    "lang.mwms.monitorRobotMsg10064": "Timeout bei Abruf der Ceil-Dekodierung der DSP Datei",
    "lang.ark.action.interface.robotType": "",
    "lang.mwms.monitorRobotMsg10067": "Am Ende der Aufgabe wird ein Fehlerbefehl ausgegeben",
    "lang.mwms.monitorRobotMsg10066": "Task-Status Maschinenwechselfehler",
    "lang.ark.fed.menu.destPoint": "Ausgangspunkt der Verfahraufgabe ",
    "lang.mwms.monitorRobotMsg10061": '"Wenn in dem Regal platziert wird, nicht ablegen, wenn der QR Code in einer schlechten Verfassung ist, um eine Kollision mit naheliegenden Regalen zu vermeiden."',
    "lang.mwms.monitorRobotMsg10060": '"Es ist nicht empfohlen, das Regal beim Drehen an die Regalposition anzupassen"',
    "lang.mwms.monitorRobotMsg10063": "Timeout bei Abruf des Ceil-Rect der DSP-Datei.",
    "lang.mwms.monitorRobotMsg10062": "Mehr als 2 QR-Codes am Boden fehlen.",
    "lang.ark.fed.modeTitle": "Verwaltungsmodus: Aktivieren, d.h. die Workstation aktiviert den Verwaltungsmodus für Materialaufruf, Zuführung und Bewegung, sodass die oben genannten Prozesse und Bewegungsaufgaben an einem bestimmten Punkt ausgeführt werden können.",
    "lang.ark.fed.oneWayEntrance": "Einzel-Eingang",
    "lang.ark.workflow.cycleType.timeLoop": "Schleifenzählung",
    "lang.mwms.monitorRobotMsg22010": "Fehlerstatus des Lademoduls",
    "lang.mwms.monitorRobotMsg22011": '"Die Ladestation meldet, dass diese Station zurzeit nicht verfügbar ist"',
    "lang.ark.fed.alarmLevel": "Alarmstufe",
    "lang.ark.fed.excel.data.isBlank": "",
    "lang.ark.loadCarrier.loadCarrierIsInUse": "",
    "lang.ark.fed.drawTheMap": "Eine Karte zeichnen",
    "lang.ark.fed.shelfHeat": "Regalwärme",
    "lang.mwms.monitorRobotMsg10029": "Antrieb-übertemperatur",
    "lang.mwms.monitorRobotMsg10028": "Regal-QR-Code-Decodierung Timeout",
    "lang.mwms.monitorRobotMsg10025": "DSP verlor Herzschlag",
    "lang.mwms.monitorRobotMsg10024": "überstrom des Hubmotors",
    "lang.ark.fed.classCode": "Kategoriecode",
    "lang.mwms.monitorRobotMsg10027": "Regal-QR-Code-Decodierung fehlgeschlagen",
    "lang.mwms.monitorRobotMsg10026": "DSP-Datenfeedbackfehler",
    "lang.mwms.monitorRobotMsg10032": '"Fehler-Hindernisvermeidungssensordaten, die von der Hindernisvermeidungsbox oder dem Obercomputer empfangen werden"',
    "lang.ark.fed.deleteSuccessfully": "Erfolgreich gelöscht",
    "lang.ark.fed.pleaseEnterAWorkflowName": "Bitte geben Sie einen Prozessnamen ein",
    "lang.mwms.monitorRobotMsg10031": "Abnormale Kommunikationsverbindung zwischen Hindernisvermeidungsbox und Master-Control",
    "lang.mwms.monitorRobotMsg10034": "Die Powerbox hat falsche Batteriedaten erhalten",
    "lang.mwms.monitorRobotMsg10033": "Abnormale Verbindung zwischen Strombox und Hauptsteuerungskommunikation",
    "lang.ark.workflow.lastNodeMustConnectionEndNode": '"Der letzte Knoten, der nicht vom Typ Subflow ist, ist nicht mit dem Endknoten verbunden!"',
    "lang.mwms.monitorRobotMsg10030": "Hindernisvermeidungsbox/Obercomputer erhielt keine Hindernisvermeidungssensordaten",
    "lang.ark.interface.id": "Ordnungsnummer",
    "lang.ark.workflow.template.type": "Prozessvorlagentyp",
    "lang.mwms.monitorRobotMsg22007": "Lademodul übertemperatur",
    "lang.mwms.monitorRobotMsg22008": "Ladestrom im Automatikmodus ist 0.",
    "lang.mwms.monitorRobotMsg22009": "Warnstatus des Lademoduls",
    "lang.mwms.monitorRobotMsg22003": "RMS-Daten-Ausnahme",
    "lang.ark.manual.place": "Manuelle Platzierung",
    "biz.UserServiceImpl.updateUserAndRoleRelation.msg1": "Der Administratorbenutzer kann nicht bearbeitet werden",
    "lang.mwms.monitorRobotMsg22004": "RMS-Befehlsausnahme",
    "lang.mwms.monitorRobotMsg22005": "Notfall-Stopp-Knopf wurde an der Gabelstabler-Ladestation betätigt.",
    "lang.ark.fed.enterCode": "Geben Sie den Stationscode oder den Produktionsliniencode ein.",
    "lang.mwms.monitorRobotMsg22006": "Kein Sensor wurde an der Gabelstabler-Ladestation festgestellt",
    "lang.ark.fed.singlePointMode": "Einzelpunktmodus",
    "lang.ark.waveTaskStatus.distributed": "Verteilung beendet",
    "lang.mwms.monitorRobotMsg10039": "DSP verloren",
    "lang.ark.fed.workflowStatus": "Prozessstatus",
    "lang.mwms.monitorRobotMsg10036": "CAN2-Kommunikationsfehler",
    "lang.mwms.monitorRobotMsg10035": "CAN1-Kommunikationsfehler",
    "lang.gles.receipt.warehousingOrder": "",
    "lang.ark.workflow.action.command.paramSourceType": "Quelle des Werts",
    "lang.mwms.monitorRobotMsg10038": "Encoder trennen",
    "lang.mwms.monitorRobotMsg10037": "Der Antrieb ist verloren",
    "lang.mwms.monitorRobotMsg10043": "Die Abweichung der Mittenposition überschreitet den Grenzwert.",
    "lang.mwms.monitorRobotMsg10042": "Die Differenz zwischen dem Drehwinkel Integral und dem Winkel des Encoders überschreitet die Grenze",
    "lang.ark.fed.shelfAttribute.IA": "",
    "lang.mwms.monitorRobotMsg10045": "Die Richtung der Steueranweisung ist nicht konsistent mit der Richtung der tatsächlichen Winkeldrehung",
    "lang.ark.workflow.robotLineUp": "Roboterwarteschlange",
    "lang.mwms.monitorRobotMsg10044": "Das Antriebsrad wurden beim Drehen geschleudert",
    "lang.ark.fed.noOperationAvailable": "Keine Operation verfügbar!",
    "lang.mwms.monitorRobotMsg10041": "Die Abweichung des geraden Gehwinkels beträgt mehr als 3°",
    "lang.mwms.monitorRobotMsg10040": "Laterale Abweichung überschreitet 20mm während geraden Fortbewegens",
    "lang.ark.fed.menu.logManagement": "Protokollverwaltung",
    "lang.ark.apiCommonCode.robotStopFailed": "Roboter-Not-Aus fehlgeschlagen",
    "lang.ark.workflow.workflowHaveFinished": "Stornierung fehlgeschlagen. Prozess abgeschlossen",
    "lang.ark.fed.flowClass": "Prozesskategorie",
    "lang.ark.fed.goodsExistent": "Das aktuelle Material ist bereits vorhanden. Versuchen Sie es mit einem anderen.",
    "lang.ark.fed.doYouConfirmTheGeneration": "Sind Sie sicher, zu generieren?",
    "lang.ark.workflow.wareHouseUnifiedConfig": "Einheitliche Konfiguration",
    "lang.mwms.monitorRobotMsg10": "Aufgabenstörung",
    "lang.mwms.monitorRobotMsg10007": "Das rechte Rad kugelt beim Bewegen in einer geraden Linie",
    "lang.mwms.monitorRobotMsg10006": '"Das linke Rad schleudert, wenn es sich in einer geraden Linie bewegt"',
    "lang.mwms.monitorRobotMsg10009": "Nicht-QR-Codebereich Bild-Feedback",
    "lang.mwms.monitorRobotMsg10008": "Ein Fehler trat während des Bildspeichervorgangs auf. Zum Beispiel: Boden-QR-Code Kallibrationsfehler > 10° und Seitenfehler > 4cm (nicht ausgeführt).",
    "lang.mwms.monitorRobotMsg10003": "Keine Hindernisvermeidungsdatenaktualisierung innerhalb von 2 Sekunden",
    "lang.mwms.monitorRobotMsg10002": "Das Antriebsrad wurden beim Drehen geschleudert",
    "lang.gles.baseData.baseFactoryPosition": "",
    "lang.mwms.monitorRobotMsg10005": "Antriebsrad-Verriegelungsfehler",
    "lang.mwms.monitorRobotMsg10004": "Kein Batterie-Update in 200 Sekunden",
    "lang.mwms.monitorRobotMsg10010": "Der Hubmotor kann nicht heben",
    "lang.ark.fed.notification": "Hinweisobjekt",
    "lang.mwms.monitorRobotMsg10012": "Antriebsrad-Encoder-Impuls-überlauf",
    "lang.mwms.monitorRobotMsg10011": "Antriebsrad-Encoder-Impulsanzahl nicht aktualisiert",
    "lang.gles.receipt.outWarehouseExternalOrder": "",
    "lang.ark.fed.designatedRobotNew": "Einen Roboter bestimmen",
    "lang.ark.fed.systemEmergencyStop": "System-Notaus",
    "lang.mwms.fed.base": "Grundeinstellungen",
    "lang.mwms.monitorRobotMsg22000": "RMS-Kommunikation unterbrochen",
    "lang.mwms.monitorRobotMsg22001": "CAN Kommunikation wurde unterbrochen (Lademodul)",
    "lang.mwms.monitorRobotMsg22002": "Bildschirmkommunikation wird unterbrochen",
    "lang.ark.warehouse.materialPointOrderNoLessZero": "Die Zufuhrsequenz muss größer als 0 sein.",
    "lang.ark.robot.robotNotExist": "Roboter ist nicht vorhanden.",
    "lang.ark.fed.menu.taskLog": "Protokoll",
    "lang.mwms.monitorRobotMsg10018": "Die von der Kamera hochgeladenen Bilddaten konnten die Header- und Tail-Prüfsumme nicht analysieren",
    "lang.mwms.monitorRobotMsg10017": "Angehobener Motor kann sich nicht absenken. Zum Beispiel: Das Regal kann nicht innerhalb von 40s abgestellt werden.",
    "lang.ark.fed.childWorkflowInUse": "Prozess {0} wird ausgeführt. Entladen fehlgeschlagen!",
    "lang.ark.fed.noMatchCellCode": "Ziel-Arbeitsstation",
    "lang.mwms.monitorRobotMsg10019": "kein Ladestrom",
    "lang.ark.fed.saturday": "Samstag",
    "lang.mwms.monitorRobotMsg10014": "Regal-QR-Code-Decodierung-Fehler",
    "lang.mwms.monitorRobotMsg10013": "Frontstoßstange Auslöser",
    "lang.ark.fed.endTime": "Endzeit",
    "lang.mwms.monitorRobotMsg10016": "Hindernisvermeidung wird ausgelöst",
    "lang.mwms.monitorRobotMsg10015": "Ausgelöst durch den hinteren Antikollisionsstreifen",
    "lang.mwms.monitorRobotMsg10021": "Die ursprünglichen Daten zur Hindernisvermeidung wurden nicht innerhalb von zwei Sekunden aktualisiert",
    "lang.mwms.monitorRobotMsg10020": "Ladegerät-Sensor Ausfall",
    "lang.mwms.monitorRobotMsg10023": "Antriebsradmotor-überstrom",
    "lang.ark.workflow.action.command.robot.MediaStop": "Stimmwiedergabe stoppen",
    "lang.mwms.monitorRobotMsg10022": '"Antriebsrad-überstrom, kein Fehler"',
    "lang.authManage.fed.screen.creditCardLogin.pleaseBrushCard": "",
    "lang.ark.fed.areaGroupCode": "Gruppierungscode",
    "lang.ark.fed.take": "Holen",
    "lang.ark.fed.pleaseSelectATarget": "Bitte wählen Sie ein Ziel",
    "lang.ark.interface.responseStatus": "Kommunikationsstatus",
    "lang.ark.interface.startPoint": "Startpunktcode",
    "lang.ark.fed.materialPreparationPoint": "Einspeisepunkt",
    "lang.ark.warehouse.stationOtherCellCodeNotFree": '"Die anderen Punkte an der Arbeitsstation haben eine Aufgabe, und die Materialaufrufaufgabe kann nicht initiiert werden."',
    "lang.ark.fed.agvIsMovingWaitLock": "AGV-Bewegung findet statt. Warten auf Sperren.",
    "lang.ark.interface.responseDate": "Reaktionszeit",
    "lang.ark.fed.offShelves": "Zum Regal gehen",
    "lang.ark.logType.rmsCallbackInfo": "rms Rückruf-Information",
    "lang.ark.fed.to": "Bis",
    "lang.ark.fed.dmpTaskUnComplete": "Es gibt noch nicht abgeschlossene Geräteinteraktionsaufgaben",
    "lang.ark.mechanical.arm.pick.up": "Durch mechanische Arme entfernt",
    "lang.authManage.web.common.realName": "Name",
    "lang.ark.fed.enabledScanValidate": "Zum Verifizieren des Behälters scannen",
    "lang.ark.robotDeviceComponent.robotId": "Roboter-ID",
    "robot.task.already.send": "Bereits an den Roboter ausgegeben und kann nicht manuell durchgeführt werden!",
    "lang.ark.fed.specifications": "Spezifikation",
    "lang.ark.fed.throughEscSwitchLineDrawingModeDoublePointSwitchSinglePoint": "Schalten Sie den Linedraw-Modus über ESC um. Wenn Sie von Doppelpunkten zu Einzelpunkten wechseln möchten, müssen Sie zweimal 1 klicken. Einzelpunktmodus (sequenzieller A-B-C-Klick) 2. Doppelpunktmodus (A-B B-C-Modus)",
    "lang.ark.fed.triggerWorkflow": "Auslösen-Prozess",
    "lang.ark.fed.screen.equipmentAssociatedInfo.columnEquipName": "Gerätename",
    "lang.ark.fed.robotWillArriveText": '"Roboter: {0} kommt bald, die kürzeste Entfernung"',
    "lang.ark.deliverOrder.defaultOrder": "Produktionslinienverteilungssequenz",
    "lang.ark.workflowgroup.triggerpoint.begin": "Start",
    "lang.mwms.homePage": "Homepage",
    "lang.ark.fed.trafficLockMsg": "Sobald der Bereich manuell verriegelt wurde, ist eine erneute manuelle Freigabe erforderlich, damit der Roboter den Bereich wieder betreten kann. Sind Sie sicher, den Bereich zu verriegeln?",
    "lang.ark.fed.taskType": "Aufgabentyp",
    "lang.mwms.monitorRobotMsg10001": "Die Grundwerte von zwei Kalibrierungen vor und nach dem Kreisel haben sich zu stark verändert",
    "lang.mwms.monitorRobotMsg10000": "Kreiseltemperatur ändert sich zu stark.",
    "lang.ark.warehouseTask.cuttingTaskOverTime": "Zeitwarnung für Ablage an Arbeitsstation ({0} m über Planung).",
    "lang.ark.interface.interfaceDesc.numbers": "Ziffer",
    "lang.ark.apiCommonCode.locationToNotEmpty": "Der Zielort darf nicht Null sein.",
    "lang.ark.fed.recoverRefresh": "Aktualisieren fortfahren",
    "lang.ark.waitForAssign": "Warten auf Zuteilung",
    "lang.ark.fed.revoke": "Zurücknehmen",
    "lang.ark.fed.arrivedTime": "Dauer der Ankunft an Ziel-Arbeitsstation",
    "lang.ark.warehouse.manualOperateTypeIn": "Manueller Eingang",
    "lang.ark.workflow.chooseStrategy.exception": "Abnormal abgeschlossen",
    "lang.ark.fed.scrollNumber": "Spulen-Nr.",
    "lang.ark.fed.sureClickConfirm": "Sind Sie sicher, dass Sie speichern wollen?",
    "lang.ark.fed.uploadImage": "Bild hochladen",
    "lang.ark.fed.defaultName": "Standardname",
    "lang.ark.fed.liveNotSaveParamConfig": "Nicht gespeicherte Parameterkonfiguration vorhanden, bitte zuerst speichern!",
    "lang.mwms.fed.wareMutex": "Sich gegenseitig ausschließende Konfiguration von Materialien",
    "lang.ark.fed.prepareToExecuteTheWorkflowAutomatically": "Den automatischen Ausführungsprozess vorbereiten",
    "lang.ark.fed.operationSuccessfully": "Erfolgreich",
    "lang.ark.fed.noSelectGoods": "Zurzeit ist kein Material ausgewählt",
    "lang.ark.fed.screen.flowNodeConfig.pleaseInputCellCode": "",
    "lang.ark.warehouse.goodsManagement": "Vorratsbehälter-Management",
    "lang.ark.fed.contents.flowConfig.recycleType.autoRecoveryRule.columns.priority": "",
    "lang.mwms.fed.outWarehouseCollect": "Lagerausgang-Zusammenfassung",
    "lang.ark.fed.theSpaceIsTaken": "Das Regalfach ist bereits belegt.",
    "lang.ark.fed.stationNumber": "Anzahl der Stationen",
    "lang.ark.fed.notMergeNode": "Keine Knoten zum Verbinden!",
    "lang.authManage.web.common.delete": "Löschen",
    "lang.ark.fed.reasonsForFailure": "Grund für das Scheitern",
    "lang.ark.fed.status": "Status",
    "lang.ark.fed.arriveRobotActions": "Der Roboter bewegt sich nach der Ankunft",
    "lang.ark.workflow.arrive.action.goTurnOfAngle.pos360": "360°",
    "lang.ark.fed.leavingSoon": "Wird in Kürze verlassen",
    "lang.ark.fed.tray": "Palette ",
    "lang.ark.workflowConfig.cellFunctions.notAllowTurn": "Keine Wende",
    "lang.ark.apiContainerCode.containerAlreadyRemoved": "Container {} bereits entfernt",
    "lang.ark.fed.config": "Konfiguration",
    "lang.ark.warehouse.goodsTaskHasHang": "Brechen Sie einen abnormalen Prozess ab und setzen Sie das Dokument für die weitere Verarbeitung aus.",
    "lang.gles.workflow.abuttingJoint": "",
    "lang.auth.UserAPI.item0306": "Aktivieren",
    "lang.ark.fed.workflowGroupConfiguration": "Bearbeitung von Workflow-Gruppen",
    "lang.auth.UserAPI.item0305": "Deaktivieren",
    "lang.ark.workflow.initiateNextTaskSimple": "Nächster Schritt",
    "lang.ark.button.node.type.startPoint": "Betätigungspunkt",
    "lang.ark.workflow.wareHouseAutoCreate": "Automatische Erstellung",
    "lang.ark.workflow.workflowTaskHasCompleted": "Die Workflow-Aufgabe wurde abgeschlossen",
    "lang.ark.apiContainerCode.angleValueIllegal": "Der Bereich des Behälterwinkels beträgt -180,0 bis 180,0",
    "lang.ark.trafficControl.artificialControlFunction": "Manueller Steuerungsbereich Funktion",
    "lang.ark.action.interface.conditionLocationTo": "",
    "lang.ark.fed.robotInstruct": "Roboteranweisung",
    "lang.ark.warehouse.setPointNumber": "Knotennummer",
    "lang.ark.workflow.wareHouseConfigMethod": "Konfigurationsmethode",
    "lang.ark.fed.addParam": "",
    "lang.ark.fed.yes.generateCode": "Ja, das System hat Container-Nummern generiert",
    "lang.ark.fed.component.workflow.label.hoisterFlow": "",
    "lang.ark.apiStationCode.stationCodeNotBlank": "StationCode darf nicht Null sein",
    "lang.ark.fed.targetProductionLine": "Produktionslinie anfordern",
    "lang.ark.action.interface.extraParam": "",
    "lang.ark.fed.endDate": "Enddatum",
    "lang.ark.fed.autoDeliver": "Aktive Verteilung",
    "lang.authManage.web.others.project": "Artikel-ID",
    "lang.ark.workflowConfig.status.error": "Abnormal",
    "lang.ark.fed.deleteAll": "Alles löschen",
    "lang.ark.record.dmp.sendTask": "",
    "lang.ark.workflow.arrive.action.goTurnOfAngle.angle": "Drehwinkel",
    "lang.ark.robot.robotModelNotExist": "Der entsprechende Mitrobotertyp besteht nicht.",
    "lang.ark.fed.menu.robotParamConfig": "",
    "lang.ark.fed.pleaseSelectCycle": "Bitte wählen Sie die Frequenz",
    "lang.ark.fed.pleaseAtLeastOneCellInfo": "Bitte fügen Sie mindestens eine Punktinformation hinzu",
    "lang.ark.fed.areaCode": "Code des Bereichs",
    "lang.ark.workflow.arrive.action.goTurnOfAngle.neg270": "-270°",
    "lang.ark.fed.menu.applyMaintenance": "Anwendungswartung",
    "lang.ark.fed.senior": "Erweitert",
    "lang.ark.trafficControl.enterPattern.serialPass": "Kontinuierliches Passieren",
    "lang.mwms.fed.asn": "Einlagerungseingang",
    "lang.ark.warehouse.theMatraialHaveAnyWave": '"Es gibt mehrere Wellenauswahl, die am Materialort verteilt werden sollen."',
    "lang.ark.fed.selectRobotsThatNeedToBeDisplayedAndManipulated": "Wählen Sie den Roboter aus, den Sie anzeigen und bedienen möchten",
    "lang.auth.UserAPI.item0302": "",
    "lang.auth.UserAPI.item0301": "",
    "lang.ark.fed.createNewExcute": "Bedingungen hinzufügen",
    "lang.ark.fed.workstation": "Arbeitsstation ",
    "lang.ark.workflow.arrive.action.goTurnOfSide": "Komponenten um Flächen gedreht",
    "lang.ark.fed.screen.area.grouping": "Gruppierung",
    "lang.ark.fed.beforeExecuteSaveListLog": "Bewahren Sie vor jeder Ausführung die letzten {0} Protokolle auf",
    "lang.ark.fed.dealSuccessCode": "Die Logik nach dem Erfolg verarbeiten",
    "lang.ark.fed.systemControl": "Systemkontrolle",
    "lang.ark.fed.pleaseSelectTheLastNode": "Bitte den letzten Knoten auswählen",
    "lang.authManage.web.common.noPermission": "Es tut uns leid, Sie haben keine Berechtigung von {0} System!",
    "lang.ark.fed.screen.LoginLog.userName": "",
    "lang.ark.fed.padFlowStartAgain": "PAD-Prozessneustart",
    "lang.ark.api.goturn.isForbidden": "Die Drehung des Containers kann für diese Aufgabe im aktuellen Status nicht ausgeführt werden.",
    "lang.ark.fed.childFlowOnlySaveType": "Unterprozesse sind nur in Verbindung mit Unterprozessen erlaubt!",
    "lang.ark.fed.screen.hybridRobot.bindReason": "Grund der Bindung: Der Vorratsbehälter muss an die Andockpunkte gebunden werden, denn der Vorschaltbereich kann nur die Vorratsbehälterdaten zurückgeben anstatt der uns bekannten Punktdaten. Wenn der Vorschaltbereich die Angaben zum Vorratsbehälter zurückgibt, kann GMS den betreffenden Punkt verfolgen, damit das Andocken an die Maschine gelingt.",
    "lang.ark.fed.currentNode": "Aktueller Knoten",
    "lang.ark.fed.monitoringObjects": "Überwachungsobjekt",
    "lang.ark.fed.theTotalNumberOfTrays": "Gesamte Regalfächer",
    "lang.ark.fed.isOpenAllQueue": "Möchten Sie die Warteschlange für alle aktivieren?",
    "lang.ark.base.license.licenseExpiredWarningMsg": "Zertifikat abgelaufen. Sie können das System weiterhin verwenden.",
    "lang.ark.fed.pullLoadMore": "Hochziehen, um mehr zu laden",
    "lang.ark.fed.locked": "Gesperrt",
    "lang.ark.fed.menu.workflowTrigger": "Trigger-Konfiguration",
    "lang.ark.button.node.type.middlePoint": "Zwischenpunkt",
    "lang.ark.fed.switchingStandPoint": "Zu einem anderen Punkt wechseln ...",
    "lang.ark.paramNameExist": "Namen wiederholen sich nicht",
    "lang.ark.trafficControl.areaLockStatus": "Bereichsstatus",
    "lang.ark.fed.screen.flowNodeConfig.offsetParam": "Parameterwert",
    "lang.ark.fed.nodePoint": "Knotenposition",
    "lang.ark.fed.scrollCode": "Spulen-Nr.",
    "lang.ark.apiRobotTaskCode.waitPointTaskContinueFailed": '"Die Aufgabe, die am Wartepunkt ausgeführte wird, kann nicht fortgesetzt werden."',
    "lang.authManage.fed.instanceId": "Artikelinstanz",
    "lang.authManage.fed.remainingDays": "Verbleibende Tage",
    "lang.ark.agv.instructionRule1": "",
    "lang.authManage.web.others.license": "Zertifikat",
    "lang.ark.fed.batchNumber": "Chargennummer",
    "lang.ark.fed.CuttingFinish": "Materialentladung abgeschlossen",
    "lang.ark.workflow.arriveOperation": "Der Roboter bewegt sich nach der Ankunft",
    "lang.ark.fed.resetAll": "Alles zurücksetzen",
    "lang.ark.workflowConfig.status.designing": "Im Design",
    "lang.ark.fed.screen.equipmentAssociatedInfo.columnUpdater": "Bearbeitet von",
    "lang.ark.fed.pleaseEnterAPositiveNumber": "Bitte geben Sie eine nicht negative Zahl ein",
    "lang.ark.fed.menu.flowTemplate": "Arbeitsablaufvorlage",
    "lang.gles.receipt.upAndDownMaterialExternalOrder": "",
    "lang.ark.fed.orderWaveSucess": "Wellengruppierung war erfolgreich",
    "lang.ark.fed.contents.flowConfig.recycleType.auto": "",
    "lang.ark.fed.material": "Einspeisepunkt",
    "lang.gles.material": "",
    "lang.ark.fed.waitSend": "Um verteilt zu werden",
    "lang.ark.fed.batteryTemperature": "Batterietemperatur",
    "lang.ark.fed.orderCollection": "Verteilung der Materialanforderungen",
    "lang.ark.plugin.pluginType.fetchContainer.way.full": "Nimm volle Container",
    "lang.ark.workflowConfig.status.released": "Veröffentlicht",
    "lang.ark.fed.screen.hybridRobot.pleaseInputIntOffset": "Geben Sie bitte einen Versatzwert ein.",
    "lang.ark.workflowTriggerType.workflow": "Auslösen-Prozess",
    "lang.ark.fed.screen.equipmentAssociatedInfo.labelEquipName": "Gerätename",
    "lang.ark.fed.everyOnceHappen": "alle {0} Tage",
    "lang.ark.fed.sendGoodsTitle": '"Materialzufuhr: Aktivieren, d.h. die Workstation unterstützt die manuelle Auswahl der Materialien, um eine Materialaufruf durchzuführen, sie kann auch die Aufgabe des Materialaufrufs zur Verteilung übernehmen."',
    "UserAPI.item0100": "Der Benutzer ist deaktiviert. Wenden Sie sich an den Administrator, um den Benutzer erneut zu aktivieren oder melden Sie sich mit einem anderen Benutzer an.",
    "lang.ark.action.interface.instanceId": "",
    "lang.ark.workflow.action.commandExecutePhase.nextStart": "Die nächste Aufgabe wird gestartet",
    "lang.ark.fed.secondClassification": "Tier 2 Kategorie",
    "lang.ark.element.shelf.point.belong.to.area": "Dieser Regalpunkt gehört zu einem Bereich",
    "lang.ark.fed.enable": "Aktivieren",
    "lang.ark.workflowTriggerStatus.create": "Erstellen",
    "lang.ark.notManualTrigger": "Der Zielort wird nicht manuell ausgelöst und dieser Vorgang wird nicht unterstützt!",
    "lang.ark.fed.theSameLevelAlterMessageCanNotBeRepeat": "Alarmbenachrichtigungen der gleichen Stufe und des gleichen Typs können nicht dupliziert werden.",
    "lang.ark.fed.cellIdleTrigger": "Knoten im Ruhezustand Auslösung",
    "lang.ark.fed.addGoods": "Material hinzugefügt",
    "lang.ark.fed.lineDrawingMode": "Strichzeichnungsmodus",
    "lang.ark.warehouse.goodsEditError": "Materialbearbeitungsfehler",
    "lang.ark.fed.goodsComplete": "Verteilung beendet",
    "lang.ark.interface.requestDate": "Anfordern-Zeit",
    "lang.ark.fed.robotConfigurationEditingPage": "Bearbeitungsseite für die Roboterkonfiguration",
    "lang.ark.workflow.waitRelease": "Warten auf Freigabe",
    "lang.ark.workflowTrigger.logType.all": "Alles",
    "lang.ark.fed.taskList": "Aufgabeliste",
    "lang.gles.logisticsConfig.workPosition": "",
    "lang.ark.fed.uploadFailed": "Upload fehlgeschlagen",
    "lang.ark.workflowConfig.cellFunctions.blockedCell": "BLOCKED_CELL",
    "lang.ark.fed.screen.workflowInfo.requestParam": "",
    "lang.ark.fed.uninstall": "Deinstallieren",
    "lang.ark.fed.signOut": "Ausgang",
    "lang.ark.fed.common.validator.required": "Pflichtfeld",
    "lang.ark.interface.containerAmountNumberTip": "Zählt bis zu 5000 jedes Mal, nur Zahlen sind zugelassen.",
    "lang.ark.fed.isForceDelete": "Riskant! Möchten Sie das Löschen erzwingen? Bitte seien Sie vorsichtig!",
    "lang.mwms.fed.reportManagement": "Statement-Management",
    "lang.ark.fed.sendGoods": "Materialzufuhr",
    "lang.ark.fed.warehouse": "Lagerverwaltung",
    "lang.ark.fed.calledGoods": "Bestellt",
    "lang.ark.workflow.positionIsOccupied": "Der Standort ist besetzt",
    "lang.ark.workflow.rollOver": "Umschlagen",
    "lang.ark.fed.theRobotIsNotHere": "Der Roboter ist nicht hier",
    "lang.ark.workflowConfig.cellFunctions.omniDirCell": "OMNI_DIR_CELL",
    "lang.ark.fed.liveNotSaveExternalInteraction": "Es ist eine nicht gespeicherte externe Interaktionskonfiguration vorhanden, bitte zuerst speichern!",
    "lang.ark.record.nextTask": "",
    "lang.ark.fed.addNewRobotInstruct": "Roboteranweisung hinzufügen",
    "lang.ark.fed.extraParam1": "extraParam1",
    "lang.ark.workflowConfig.cellFunctions.elevatorCell": "ELEVATOR_CELL",
    "lang.ark.fed.extraParam9": "extraParam9",
    "lang.ark.fed.extraParam8": "extraParam8",
    "lang.ark.fed.extraParam7": "extraParam7",
    "lang.ark.workflow.workflowTaskSendRecover": "Der Workflow ist in Betrieb und kann nicht wiederhergestellt werden",
    "lang.ark.fed.extraParam6": "extraParam6",
    "lang.ark.fed.extraParam5": "extraParam5",
    "lang.ark.fed.extraParam4": "extraParam4",
    "lang.ark.fed.extraParam3": "extraParam3",
    "lang.ark.fed.extraParam2": "extraParam2",
    "lang.authManage.web.common.logout": "Abmelden",
    "lang.ark.fed.productLineDetail": "Details zur Produktionslinie",
    "lang.ark.fed.containerInfo": "Behälterinformationen",
    "lang.ark.fed.screen.equipmentAssociatedInfo.dialogDetail.titleNew": "Gerätezuordnungsdaten hinzufügen",
    "lang.ark.warehouse.noMatchCellCode": "Materialzielstation nicht übereinstimmend",
    "lang.ark.workflow.template.type.dynamiNodeUnit": "Dynamische Zellaufgabe",
    "lang.gles.workflow.MonitorManagement": "",
    "lang.ark.base.license.sysParamForCustomerIdIsNull": "Die Kunden-ID, die vom Zertifikat überprüft wird, ist null",
    "lang.ark.fed.executeSuccessfully": "Lieferung erfolgreich durchgeführt",
    "lang.slam.api.menu.item0001": "PC",
    "lang.ark.fed.containerTypeName": "Name des Container-Typs",
    "lang.ark.fed.activeDistributionTitle": "Aktive Verteilung: Die Arbeitsstation kann nur die von ihr aus startenden Prozesse initiieren.",
    "lang.ark.fed.notParams": "Parameter noch nicht konfiguriert!",
    "lang.mwms.fed.qrCodeAnalysis": "QR-Code-Analysestrategie",
    "lang.ark.fed.time": "Zeit",
    "lang.ark.base.license.licensePreAlertMsg": "Das Zertifikat läuft nach {0} Tagen ab!",
    "lang.ark.fed.setStartPoint": "Als Startpunkt festlegen",
    "lang.ark.fed.wave": "Wellenauswahl",
    "lang.ark.workflow.task.status.deviceExecuting": "Externes Gerät im Transport",
    "lang.mwms.fed.category": "Frachtkategorie",
    "lang.ark.workflow.containerEnterMapDest": "Endpunkt der Containereinlagerung",
    "lang.ark.fed.processOperation": "Prozessbetrieb",
    "lang.ark.fed.screen.flowNodeConfig.judgingByPath": "",
    "lang.ark.fed.orderDetail": "Dokumentdetails",
    "lang.ark.fed.login": "Anmeldung",
    "lang.ark.fed.screen.equipmentAssociatedInfo.dialogDetail.titleEdit": "Gerätezuordnungsdaten bearbeiten",
    "lang.ark.fed.timeOfReceipt": "Empfangsdauer",
    "geekplus.moving.uic.elTableWrapperVue2.column.action": "Betrieb",
    "lang.ark.fed.device.ruleMatch": "",
    "lang.ark.fed.instructList": "Befehlsliste",
    "lang.ark.waveGenerateScope.materialDocument": "Gemäß Materialvorbereitungspunkten",
    "lang.ark.workflowConfig.cellFunctions.stayBlocking": "Blockierend bleiben",
    "lang.ark.interface.apiStationQueueStop": "Deaktivieren Sie die Warteschlange zum Abholen",
    "lang.ark.fed.startOrEndNodeNotExist": "Vorgang fehlgeschlagen. Kein Start- oder Endknotenpunkt!",
    "lang.ark.fed.containerEntryWay": "Eingangsmethoden",
    "lang.ark.auth.otherUserHaveLoginThisStation": "Ein Benutzer {0} hat sich an der Arbeitsstation Nr. {0} angemeldet!",
    "lang.ark.fed.cancelEndPoint": "Endpunkt gelöscht",
    "lang.gles.workflow.workReceipt": "",
    "lang.ark.fed.screen.flowNodeConfig.assignOffset": "Senden Sie einen Versatzwert des Roboterpunkts",
    "lang.gles.strategy.shelf": "",
    "lang.ark.fed.release": "Freigeben",
    "lang.ark.fed.menu.nodeMapRelation": "",
    "lang.ark.fed.whatAreYouGoingToDoWithTheCurrentAreaOrShelf": "Welche Verarbeitung werden Sie für den aktuellen Bereich oder das aktuelle Regal durchführen?",
    "lang.ark.fed.lengthLang": "Länge",
    "lang.ark.fed.putDownOrTurnSide": "{0} Die Komponente wurde herabgehoben. Flächendrehen darf nicht konfiguriert werden!",
    "lang.ark.workflow.condition.notIn": "Nicht enthalten",
    "lang.ark.fed.twoDimensionalCodeMode": "QR-Code-Modus",
    "lang.ark.fed.sourceProductionLineOrWorkshop": "Quellproduktionslinie / Werkstatt",
    "lang.ark.fed.displayOrder": "Gruppenposition Anzeigesequenz",
    "lang.ark.waveTriggerCondition.wireless": "Physikalische Taste",
    "lang.ark.fed.notSameStartAndEnd": "Der Start- und der Zielpunkt können nicht identisch sein.",
    "lang.ark.workflow.enterMapDest": "Endpunkt des Eingangs",
    "lang.ark.fed.buttonCommand": "Tastenbefehl",
    "lang.ark.fed.dataTimeRange": "Datums- und Zeitbereich",
    "lang.ark.fed.areYouSureYouWantToDeleteThisWorkflow": "Sind Sie sicher, diesen Prozess zu löschen?",
    "lang.ark.workflow.workflowTaskNotExists": "Die Workflow-Aufgabe existiert nicht",
    "lang.ark.workflow.noPause":"Die letzte Unteraufgabe kann nicht pausiert werden",
    "lang.mwms.rf.outboundWithoutTask": "Kein Lagerausgang",
    "lang.ark.workflow.paramValueCode.taskId": "",
    "lang.ark.workflow.fetchStuff": "Abholung von Fracht",
    "lang.ark.fed.hostSeriNum": "Externer Code",
    "lang.ark.fed.screen.hybridRobot.hybridRobotType": "Mitrobotertyp",
    "lang.ark.fed.StationNum": "Stationsblattnummer",
    "lang.ark.action.interface.exceptionResponse": "Abstand (in Sekunden) zwischen zwei aufeinander folgenden Wiederholungsversuchen",
    "lang.ark.fed.goodsCoding": "Regalfach-Code",
    "lang.mwms.fed.workStationEfficiencyCharts": "Bericht zur Stationseffizienz",
    "lang.ark.fed.light": "Lampenlicht",
    "lang.ark.fed.operatingTime": "Betriebszeit",
    "lang.mwms.fed.shelfRuleManagement": "Einlagerungs-Regelverwaltung",
    "lang.ark.dynamicTemplate.dynamicControlLogic": "Steuerlogik",
    "lang.ark.fed.taskDashboardColumn": "Aufgaben-Kanban Anzeigesäule",
    "lang.ark.robotUsage.sorting": "Sortierung",
    "lang.ark.fed.disCharingMount": "Ablagemenge",
    "lang.ark.workflow.autoOperationFailed": "Automatische Ausführung fehlgeschlagen",
    "lang.ark.fed.areasThatCanBeSpeciallyControlledForRobotMovements": "Ein Bereich, der eine spezielle Steuerung der Bewegungen eines Roboters ermöglicht",
    "lang.ark.fed.floors": "Anzahl der Schichten",
    "lang.ark.task.log.export.title.workflow.name": "Prozessname",
    "lang.ark.workflowAction.noDefault": "Nicht standardmäßig",
    "lang.ark.fed.redraw": "Neu streichen",
    "lang.ark.fed.recoverSuccess": "Erfolgreich fortgesetzt",
    "lang.ark.fed.triggerName": "Name des Auslösers",
    "lang.ark.fed.dragPictureFileHereOr": "Ziehen Sie die Bilddatei per Drag & Drop hierher, oder",
    "lang.ark.fed.menu.nodeConfig": "Interaktionswartezeit",
    "lang.ark.apiContainerCode.containerCodeGenerateFail": "Fehler beim Generieren des Containercodes",
    "lang.ark.workflow.lackRobotQueue": "Es gibt eine Aufgabenwarteschlange, wenn der Roboter nicht ausreicht",
    "lang.ark.fed.selectedGoods": "Material ausgewählt",
    "lang.ark.apiStationCode.stationQueueAlreadyEnable": "Die Warteschlangensteuerung der Arbeitsstation ist bereits aktiviert und kann daher nicht erneut aktiviert werden",
    "lang.ark.workflow.workflowTask": "Workflow-Aufgabe",
    "lang.ark.fed.copyError": "Kopieren fehlgeschlagen!",
    "lang.ark.fed.logicalNodeInfo": "Logischer Knoten-Information",
    "lang.ark.fed.grabShelvesFromWorkstations": "Das Regal von der Arbeitsstation nehmen",
    "lang.ark.workflow.workflowConfigNotExists": "Die Workflow-Konfiguration ist nicht vorhanden",
    "lang.ark.warehouse.hasSameStationNumber": "Die gleiche Arbeitssequenznummer existiert bereits.",
    "lang.ark.fed.backPre": "Rückkehr",
    "lang.ark.fed.deleteNode": "Knoten löschen",
    "lang.ark.fed.receivingWorkstation": "Arbeitsstation für Materialanforderungen",
    "lang.ark.fed.creationTime": "Erstellungszeit",
    "lang.ark.fed.goodsIsNotExists": "Die Materialinformation ist nicht vorhanden.",
    "lang.ark.fed.pleaseSelectAtLeastOneProcess": "Bitte wählen Sie mindestens einen Prozess aus",
    "lang.ark.fed.pleaseSelectGoods": "Bitte Material auswählen.",
    "lang.ark.common.invalidParameter": "Unzulässiger Parameter",
    "lang.mwms.fed.pickWorkCreate": "Entnahmeaufgabe wird generiert",
    "lang.ark.robot.go.rest": "Zu fester Position gehen",
    "lang.mwms.fed.codeRule": "Barcoderegeln",
    "lang.ark.fed.username": "Benutzername",
    "lang.ark.workflowConfig.cellFunctions.recycleid": "Recyclingstelle",
    "lang.ark.fed.menu.siteMonitoring": "Standortüberwachung",
    "lang.ark.fed.areaStop": "Regionales Not-Aus",
    "lang.ark.workflowgroup.triggerpoint.end": "Ende",
    "lang.ark.warehouse.noMatchToloc": "Produktionslinie ist nicht übereinstimmend",
    "lang.ark.interface.apiCallback": "Aufgabenrückruf",
    "lang.ark.fed.cancelStartPoint": "Startpunkt gelöscht",
    "lang.ark.fed.flowBelongClass": "Prozesskategorie",
    "lang.ark.hitStrategy.queue": "First-in First-out",
    "lang.ark.existsDefaultNodeAction": "Die Standardkonfiguration existiert bereits!",
    "lang.ark.fed.wall": "Mauer",
    "lang.ark.fed.systemCustomization": "systemAnpassung",
    "lang.ark.fed.originalLocation": "Ursprungsort",
    "lang.ark.workflow.workflowTaskSendDuplicate": "Lieferoperation-Wiederholt",
    "lang.mwms.fed.internalManager": "Lagermanagement",
    "lang.ark.fed.areaTypeExistence": "Der aktuelle Bereichstyp ist bereits vorhanden. Der gleiche Typ kann nicht wiederholt werden.",
    "lang.ark.fed.priorityGoDown": "Prioritätsrückgang",
    "lang.ark.workflow.noAvailableEndNode": "Der verfügbare Zielknoten kann nicht ausgewählt werden",
    "lang.ark.fed.pleaseSelectAConditionValueCollectionFile": "Bitte wählen Sie die Sammlungsdatei für Bedingungswerte aus",
    "lang.ark.task.log.export.title.startNode.name": "Name des Startpunkts",
    "lang.ark.fed.demandFactory": "Fabrik nachfragen",
    "lang.ark.lift.up": "Aufbocken",
    "lang.ark.fed.sendMaterialForStations": "Für welche Stationen kann diesem Einspeisepunkt Materialien vorbereiten?",
    "lang.ark.trafficControl.enterStrategy.byOccupancy": '"Erste Einnahme, erster Durchgang"',
    "lang.mwms.fed.mergeInventory": "Bestand konsolidieren",
    "lang.ark.fed.middlePoint": "Zwischenpunkt",
    "lang.ark.fed.workstationManage": "Arbeitsstations-Verwaltung ",
    "lang.ark.fed.demandStation": "Arbeitsstation anfordern",
    "lang.ark.fed.collectionAnd": "Sammlung (UND)",
    "lang.ark.fed.execution": "Betrieb erreichen",
    "lang.ark.loadCarrier.loadCarrierReqParamsErr": "",
    "lang.gles.workPositionMaterialConfig": "",
    "lang.ark.fed.destCode": "Knoten-Code",
    "lang.ark.fed.lastDay": "Der letzte Tag",
    "lang.mwms.fed.inManage": "Lagereingang-Management",
    "lang.ark.fed.menu.parameterConfigOuter": "Planen der Parameterkonfiguration",
    "lang.ark.fed.isSureDelFlowList": "Sind Sie sicher, den Prozess zu löschen?",
    "lang.ark.fed.batchSave": "Stapelweise speichern",
    "lang.ark.containerType.exists": "Der Name der Container-Klassifizierung ist bereits vorhanden",
    "lang.ark.fed.deleteTaskProcessConfirmText": "Sind Sie sicher, dass Sie alle Aufgaben dieses Prozesses löschen möchten?",
    "lang.ark.apiNodeActionCode.commonNodeActionNotExists": "Die Konfiguration der universellen Knoteninteraktion ist nicht vorhanden",
    "lang.ark.effectiveTimeCannotLessThanEffectiveTime": "Das Ablaufdatum darf nicht vor dem Wirksamkeitsdatum liegen",
    "lang.ark.fed.moveBins": "Gestell entfernt und Materialzufuhr abgebrochen. Bitte ziehen Sie das Material erneut zurück.",
    "lang.ark.interface.moving": "Punkt-zu-Punkt-Bewegung",
    "lang.ark.fed.belongNodeAlreadyExistInOtherAreaConfig": "Knotenpunkt existiert in einer anderen Gebietskonfiguration und ist in der ausgewählten Knotenpunktliste.",
    "lang.mwms.fed.robotCharts": "Roboterbericht",
    "lang.ark.workflow.authUserHasUsed": "Die folgenden Benutzer haben die Berechtigung für die Arbeitsstation festgelegt.",
    "lang.ark.api.template.startNodeNotMatch": "Der angegebene Startpunkt [{}] und der Vorlagenstartpunkt [{}] stimmen nicht überein",
    "lang.ark.workflow.queue.noAvailableRobot": "Keine Roboter verfügbar",
    "lang.ark.interface.resentFail": "Aufruf fehlgeschlagen",
    "lang.ark.fed.singleGeneration": "Einzelgenerierung",
    "lang.ark.fed.screen.equipmentAssociatedInfo.dialogDetail.labelMaxTaskCont.tooltip": "",
    "lang.ark.fed.emptyShelves": "Leeres Regal",
    "lang.ark.fed.isOpenCamera": "Prüfen Sie, ob Ihre Kamera aktiviert ist.",
    "lang.ark.fed.containerPosition": "Ort der Container",
    "lang.ark.workflow.noLongerPause": 'Die aktuelle Aufgabe befindet sich nicht mehr unter "Pausieren" und kann nicht wiederhergestellt werden. Bitte aktualisieren.',
    "lang.ark.container,syncContainerErr": "Container-Information konnte nicht synchronisiert werden",
    "lang.ark.logType.trafficControlTask": "Verkehrskontrollaufgabe",
    "lang.ark.fed.triggerMode": "Auslösemodus",
    "lang.ark.fed.interactiveActionName": "Interaktionsaktion",
    "lang.ark.fed.leftAndRightObstacleAvoidance": "Vermeidung von Hindernissen links und rechts",
    "lang.ark.fed.pleaseEnterARuleName": "Bitte geben Sie den Regelnamen ein",
    "lang.ark.fed.workflowTrigger": "Aufgabe-Auslöser",
    "lang.ark.fed.obstacleAvoidanceRange": "Umfang der Hindernisvermeidung",
    "lang.ark.fed.area": "Bereich",
    "lang.ark.fed.oneByOne": "Eins nach dem anderen",
    "lang.ark.warehouse.getTaskFailed": "Diese Materialaufrufaufgabe wurde bereits von einem anderen Materialvorbereitungspunkt übernommen.",
    "lang.ark.fed.cancelCall": "Materialaufruf abbrechen",
    "lang.ark.fed.workFlowType": "Prozesskategorie",
    "lang.ark.workflow.area.releaseTime": "Zeit der automatischen Freigabe",
    "lang.ark.fed.menu.areaManage": "Bereich",
    "lang.ark.warehouse.Feeding": "Materialzufuhr",
    "lang.ark.fed.trafficArea": "Kontrollbereich",
    "lang.ark.fed.interactiveMode": "",
    "lang.ark.fed.executing": "Wird ausgeführt",
    "lang.ark.fed.received": "Bereits erforderlich",
    "lang.ark.fed.siteName": "Standort-Name",
    "lang.ark.fed.menu.workflowConfiguration": "Workflow-Management",
    "lang.ark.fed.pleaseAddAreaFun": "Bitte fügen Sie mindestens einen Funktionstyp hinzu.",
    "lang.ark.workflow.containerNotExists": "Container existiert nicht",
    "lang.ark.fed.passbyPoint": "Durchpunkt",
    "lang.ark.fed.theFirstNodeCanNotBeDeleted": "Der erste Knoten kann nicht gelöscht werden!",
    "lang.ark.apiNodeActionCode.nodeActionNotExists": "Die Konfiguration der Knoteninteraktion ist nicht vorhanden",
    "lang.ark.workflow.arrive.action.goTurnOfAngle.pos90": "90°",
    "lang.mwms.fed.kpi": "Personaleffizienzmanagement",
    "lang.ark.fed.nodeConfig": "Allgemeine Konfiguration der Knoten",
    "lang.mwms.fed.outWarehouse": "Lagerausgang-Tagesbericht",
    "lang.ark.action.interface.conditionExtraParam": "",
    "lang.ark.fed.endType": "Zieltyp",
    "lang.ark.fed.shelfSwap": "Regaltausch",
    "lang.ark.fed.selectProductLineOrCellcode": "Produktionslinie oder Station auswählen",
    "lang.ark.fed.waveStatus": "Wellenauswahl-Status",
    "lang.ark.fed.menu.callbackAddressConfig": "Konfigurationen der Rückrufadresse",
    "lang.ark.fed.drawANewMapToCoverTheCurrentMap": "Zeichnen Sie eine neue Karte, die die aktuelle Karte abdeckt",
    "lang.ark.workflow.recycleAreaNoConfigAction": "Stornierung fehlgeschlagen. Die Wiederherstellungsbereichsinteraktion ist nicht für den Prozess konfiguriert!",
    "lang.ark.workflow.exceptionHandler.cache": "Der temporäre Lagerbereich steht in der Warteschlange",
    "lang.ark.fed.batchNo": "Los-Nr.",
    "lang.ark.warehouse.binNoShelf": '"Der Vorgang kann nicht ausgeführt werden, da das Materialgestell nicht vorhanden ist."',
    "lang.ark.api.workflowTask.notExistOrCompleted": "Die Aufgabe existiert nicht oder wurde beendet und der Vorgang kann nicht fortgesetzt werden",
    "lang.ark.container.containerCodeTooLong": "Die Länge des Typcodes darf 64 nicht überschreiten",
    "lang.ark.fed.pleaseSelectALanguage": "Bitte eine Sprache auswählen ",
    "lang.ark.fed.cageTrolley": "Käfigwagen",
    "lang.ark.fed.common.btn.cancel": "Löschen",
    "lang.ark.api.workflow.locationToIsNull": "Der Zielpunktcode kann nicht leer sein",
    "lang.auth.UserAPI.item2001": "",
    "lang.ark.common.exportExcelFile": "Vorlage exportieren",
    "lang.auth.UserAPI.item2000": "",
    "lang.ark.workflowTriggerType.clear.log": "Protokoll löschen",
    "lang.ark.fed.unknowError": "Unbekannter Fehler",
    "lang.ark.fed.workflowName": "Prozessname",
    "lang.ark.fed.wednesday": "Mittwoch",
    "lang.ark.fed.restartAngle": "Winkel neu starten",
    "lang.ark.fed.mediumSpeed": "Zwischengeschwindigkeit",
    "lang.ark.workflow.arrive.action.goTurnOfAngle.neg180": "-180°",
    "lang.mwms.fed.inWarehouseCollect": "Lagereingang-Zusammenfassung",
    "lang.ark.fed.modifier": "Geändert durch:",
    "lang.ark.fed.layer": "Schicht",
    "lang.ark.apiCommonCode.locationToNotMatchDest": '"LocationTo: {0} konnte nicht mit der entsprechenden Position im GMS in übereinstimmung gebracht werden, nachdem ""Punkt"", ""Arbeitsstation"" und ""Bereich"" ausprobiert wurden."',
    "lang.ark.fed.mainProcessInstance": "Hauptprozessinstanz",
    "lang.mwms.fed.containerInfo": "Container-Management",
    "lang.ark.interface.interfaceDesc.required": "Erfordert/nicht erfordert",
    "lang.ark.fed.taskCycle": "Aufgabenzyklus",
    "lang.ark.fed.noWorkFlowData": "Keine Prozessinformation.",
    "lang.ark.fed.help": "Hilfe",
    "lang.ark.fed.screen.workflowInfo.workflowTaskId": "",
    "lang.mwms.fed.kpiCharts": "Bericht über die Effizienz des Personals",
    "lang.ark.fed.taskDetail": "Aufgabendetails",
    "lang.ark.shelfTypeNotExist": "Container-Typ existiert nicht",
    "lang.ark.apiStationCode.stationNotSupportLogin": "Die Arbeitsstation unterstützt keine Anmeldung.",
    "lang.ark.fed.manualTrigger": "Manueller Auslöser",
    "lang.ark.trafficControl.trafficLightRange": "Verkehrskontrollbereich",
    "lang.ark.fed.dataChangeRefreshPage": "Datenänderungen. Bitte aktualisieren Sie die Seite~",
    "lang.ark.fed.logining": "Einloggen ...",
    "lang.ark.fed.common.btn.confirm": "OK",
    "lang.auth.UserAPI.item0203": "Störung in {0} Benutzer",
    "lang.ark.exceptionHandle": "Keine Bedienung erlaubt. Der Zielort verfügt über DMP-Konfigurationen.",
    "lang.auth.UserAPI.item0202": "Kein Benutzer ausgewählt",
    "lang.ark.fed.materialName": "Materialname",
    "lang.ark.fed.menu.configManage": "Konfigurationsverwaltung",
    "lang.auth.UserAPI.item0201": "Der Benutzerstatus wird nicht geändert",
    "lang.ark.fed.screen.flowNodeConfig.checkMsg.robot": "",
    "lang.auth.UserAPI.item0200": "Störung beim Ändern des Benutzers",
    "lang.ark.fed.actionsErrorCommandDetailError": "Fehler beim Speichern der Interaktionskonfiguration und der Befehlsdetails",
    "lang.authManage.web.others.noAllow": "Sie haben leider keinen Zugriff auf diese Seite",
    "lang.ark.fed.groupDisplayOrder": "Gruppensequenz",
    "lang.ark.workflow.putDown": "Niederlegen",
    "lang.ark.workflowConfig.segmentWithoutRule": "Für die Verbindungsleitung ist keine Regel konfiguriert. Bitte prüfen",
    "lang.ark.fed.taskIsSavedSuccessfully": "Aufgabe erfolgreich gespeichert",
    "lang.ark.fed.repeatWeek": "Woche",
    "lang.ark.fed.shelfType": "Regaltyp",
    "lang.ark.fed.pickUpARack": "Regal",
    "lang.ark.apiContainerCode.codeAndNumberAreNull": "containerCodes und numberOfContainer dürfen nicht zur gleichen Zeit leer sein",
    "lang.ark.fed.menu.robotSoftwareManagement": "Softwareversion",
    "lang.mwms.fed.systemLack": "Umgang mit Systemknappheitsberichtsstörung",
    "lang.ark.warehouse.uploadFileFormatError": "Das hochgeladene Dateiformat ist ungültig.",
    "lang.ark.fed.copy": "Kopieren",
    "lang.ark.apiContainerCode.containerCategoryMustUnique": "Die Containerkategorie im GMS ist nicht eindeutig und muss angegeben werden",
    "lang.ark.button.operation.command.end": "Ende",
    "lang.ark.fed.binFree": "Verfügbar",
    "lang.ark.fed.operationFailed": "Fehlgeschlagen",
    "lang.ark.fed.closeotherTabs": "Andere schließen",
    "lang.ark.fed.batchEditing": "Batch-Bearbeitung",
    "lang.ark.fed.shelfAttribute.BALANCE": "",
    "lang.ark.fed.wfTaskInfo": "Materialanforderungsbogen",
    "lang.ark.fed.modifier2": "Aktualisiert von",
    "lang.ark.loadCarrier.loadCarrierModelCodeIsEmpty": "",
    "lang.ark.operation.workflow.adjustPriority": "",
    "lang.ark.fed.forceDeleteSuccess": "Erzwungene Löschung erfolgreich!",
    "lang.ark.fed.listLogo": "Protokoll(e)",
    "lang.ark.fed.send": "Liefern",
    "lang.ark.fed.menu.containerManagement": "",
    "lang.ark.fed.screen.equipmentAssociatedInfo.btnAddInfo": "Gerätezuordnungsdaten hinzufügen",
    "lang.ark.fed.disable": "Deaktiviert",
    "lang.ark.fed.container.confirmLeave": "Austritt des Behälters bestätigen",
    "lang.ark.fed.download": "Herunterladen",
    "lang.ark.fed.idleCharging": "Leerlaufladung",
    "lang.ark.fed.startType": "Startpunkttyp",
    "lang.ark.fed.date": "Datum",
    "lang.ark.fed.autoUpdatePage": "Auto-Aktualisierung Seite umblättern",
    "lang.ark.groupStrategy.closestDistance": "Kürzeste Entfernung",
    "lang.ark.groupStrategy.sequentialSelection": "Folgeauswahl",
    "lang.ark.workflow.removeShelfFailed": "Container konnte nicht gelöscht werden",
    "lang.ark.fed.waveTasks": "Anzahl der Wellenauswahl-Aufgaben",
    "lang.ark.fed.queuingAtTheWorkstation": "Warten auf die Arbeitsstation",
    "lang.ark.workflow.nodeStatus": "Aktueller Punktzustand",
    "lang.ark.pda.function.container.entry": "Container-Eingang",
    "lang.ark.workflow.template.validate.templateError": "Fehler bei der Konfiguration der Dynamikvorlagen",
    "lang.ark.fed.menu.containerType": "Container-Typ",
    "lang.ark.fed.screen.flowNodeConfig.judgingByRobotStatus": "",
    "lang.ark.fed.drivingRestrictions": "Fahrbeschränkungen:",
    "lang.ark.fed.getGoodsTitle": "Materialaufruf: Aktivieren, d.h. die Arbeitsstation wählt die Materialien, um einen Materialaufruf durchzuführen.",
    "lang.authManage.web.auth.roler": "Rolle",
    "lang.ark.fed.multiNodeCustom": "Kundenspezifische Punkt-zu-Mehrpunkt-Aufgabe",
    "lang.ark.fed.suggestedTreatment": "Empfohlene Behandlungsmethode",
    "lang.ark.record.robotCallback.move": "",
    "lang.ark.fed.normalWork": "Normal arbeiten",
    "lang.ark.shelfNameExist": "Der Name des Container-Typs ist bereits vorhanden",
    "lang.ark.fed.robotTaskFlow": "Roboter-Aufgabe-Bewegung",
    "lang.ark.fed.editor": "Bearbeiter",
    "lang.ark.workflow.paramValueCode.locationFromFloor": "",
    "lang.mwms.fed.sysMonitor": "Systemüberwachung",
    "lang.ark.fed.component": "Komponente",
    "lang.mwms.monitorRobotMsg.scanabnormal.notMatch": "",
    "lang.ark.workflow.autoSkip": "Automatisches überspringen nach Abschluss",
    "lang.ark.workflow.exceptionHandler.taskQueue": "Aufgabenwarteschlange",
    "lang.ark.fed.rackManagement": "Container-Management",
    "lang.ark.workflow.cycleType": "Schleifentyp",
    "lang.ark.loadCarrier.loadCarrierModelTypeIsEmpty": "",
    "lang.ark.fed.screen.workflowInfo.commandTaskId": "",
    "lang.ark.warehouse.deliveryMaterial": "Materialverteilung",
    "lang.ark.element.stop.point.belong.to.workstation": "Der Punkt gehört zur Arbeitsstation",
    "lang.auth.PwdMgrAPI.item0009": "",
    "lang.auth.PwdMgrAPI.item0008": "",
    "lang.ark.apiStationCode.stationQueueNotExists": "Die Warteschlangensteuerung in der Arbeitsstation ist nicht vorhanden",
    "lang.auth.PwdMgrAPI.item0005": "",
    "lang.auth.PwdMgrAPI.item0004": "",
    "lang.auth.PwdMgrAPI.item0007": "",
    "lang.ark.fed.flowTemplateSelTypeOrRobot": "Eines der Robotermodelle oder der spezifizierte Roboter müssen ausgefüllt werden",
    "lang.auth.PwdMgrAPI.item0006": "",
    "lang.auth.PwdMgrAPI.item0001": "",
    "lang.auth.PwdMgrAPI.item0003": "",
    "lang.auth.PwdMgrAPI.item0002": "",
    "lang.ark.fed.waitExecute": "Nicht ausgeführt",
    "lang.ark.workflow.TimeTriggerSimple": "Automatisierung",
    "lang.ark.fed.shelfAttribute.name": "",
    "lang.ark.fed.orderComplete": "Verteilung beendet",
    "geekplus.moving.uic.elTableWrapperVue2.column.index": "Nr.",
    "lang.ark.interface.apiWorkflowInstanceList": "Abfrage der Workflow-Instanz",
    "lang.ark.workflow.recoveryAreaType": "Typ des Rückholbereichs",
    "lang.ark.warehouse.manualOperateTypeOut": "Manueller Ausgang",
    "lang.ark.fed.menu.workstationManage": "Arbeitsstations-Verwaltung",
    "lang.auth.PwdMgrAPI.item0012": "",
    "lang.ark.action.interface.compareValue": "Vergleich mit bedingtem Wert",
    "lang.auth.PwdMgrAPI.item0011": "",
    "lang.ark.warehouse.goodsNumberExists": "Materialcodes sind bereits vorhanden",
    "lang.auth.PwdMgrAPI.item0013": "",
    "lang.ark.fed.screen.equipmentAssociatedInfo.dialogDetail.labelEquipName": "Gerätename",
    "lang.authManage.web.others.login": "Anmeldung",
    "lang.auth.PwdMgrAPI.item0010": "",
    "lang.ark.fed.containerEntryAndLeave": "Container eingehen und entfernen",
    "lang.ark.fed.machineUse": "Verwendungszweig der Maschine",
    "lang.ark.fed.description": "Beschreibung",
    "lang.mwms.fed.sku": "Produktdatei",
    "lang.ark.fed.waveTask": "Wellenauswahl-Aufgabe",
    "lang.ark.fed.startDate": "Startdatum",
    "lang.ark.workflow.exceptionHandler.robotQueue": "Roboterwarteschlange",
    "lang.ark.archiveType.workStationOperateLog": "Betriebsprotokoll der Arbeitsstation",
    "lang.mwms.fed.customer": "Kundendatei",
    "lang.ark.workflow.area.factory": "Hersteller",
    "lang.ark.warehouse.theMatrialPointIsNotUni": "Einspeisepunkt nicht eindeutig. Wellengruppierung fehlgeschlagen",
    "lang.gles.baseData": "",
    "lang.ark.warehouse.thePointExistsAnyOrderPleaseCancel": "Fehlgeschlagen, da es mehrere Einzelwellen-Kommissionieraufträge für die Produktionslinie gibt. Stornieren Sie alle überflüssigen Aufträge.",
    "lang.ark.fed.workflowNumber": "Prozessnummer",
    "lang.ark.trafficControl.blockRange": "Verkehrskontrollbereich",
    "lang.ark.fed.demandProductionLine": "Produktionslinie anfordern",
    "lang.ark.trafficControl.enterStrategy.byOccupancyPriority": "Erste Einnahme, erster Durchgang (Priorität zuerst)",
    "lang.ark.fed.goodsInfo": "Materialinformationen",
    "lang.ark.fed.deliveryTimeV2": "Lieferdauer",
    "lang.ark.fed.switchingWorkstation": "Arbeitsstationen wechseln",
    "lang.ark.fed.defaultSet": "Standard-Layout",
    "lang.ark.workflow.condition.lessThanOrEqual": "Kleiner als oder gleich",
    "lang.ark.workflow.sendTask": "Anweisungen senden",
    "lang.ark.fed.partition": "Trennwand",
    "lang.common.invalidParameters": "Falsche Eingabeparameter",
    "lang.ark.fed.notNow": "Vorerst nicht",
    "lang.ark.fed.component.workflow.msg.inconsistentEquipIds": "Einige Komponenten/Geräte gehören nicht zu demselben Gerät. Prüfen Sie die betreffenden Gerätekonfigurationen.",
    "lang.ark.interface.disabledSuccess": "Erfolgreich deaktiviert",
    "lang.ark.fed.reeditTheCurrentMapBackground": "Den aktuellen Kartenhintergrund erneut bearbeiten ",
    "lang.ark.fed.robotModel": "Robotermodell",
    "lang.ark.fed.adjustment": "Anpassungen",
    "lang.ark.fed.editingMapBackground": "Kartenhintergrund bearbeiten",
    "lang.ark.interface.interfaceDesc.phase": "Zeiteinstellung für Rückruf",
    "lang.ark.removeContainerFail": "Container konnte nicht gelöscht werden!",
    "lang.ark.fed.strategyManagement": "Strategisches Management",
    "lang.ark.fed.modificationTime": "Änderungszeit:",
    "lang.ark.fed.collectionOr": "Sammlung (OR)",
    "lang.ark.trafficControl.trafficFunctionType": "Funktionsart",
    "lang.ark.areaCodeExist": "Regionalcode kann nicht wiederholt werden",
    "lang.ark.fed.screen.flowNodeConfig.executeDeviceInstruct": "",
    "lang.ark.externalDevice.device_own_type.externalDevice": "Externe Ausrüstung",
    "lang.ark.workflow.area.increaseStrategy": "Progressive Strategie",
    "lang.ark.action.interface.applicationType": "Anwendung des Rückgabeparameters",
    "lang.ark.operatelog.operatetype.auto": "Automatischer Prozess",
    "lang.ark.warehouse.quadrupleContainerSide": "Vierseitig",
    "lang.ark.fed.RightBracket": "+Rechter Klammer",
    "lang.ark.workflow.task.status.wait.queue.robot": "Warten auf Roboter in der Warteschlange",
    "lang.ark.fed.goodsManagement": "Regalfach-Management",
    "lang.ark.robotDeviceComponent.deviceName": "Gerätename",
    "lang.ark.fed.expressionError": "Falsches Ausdrucksformat",
    "lang.ark.fed.waitingSeconds": "Warte-Sekunden",
    "lang.mwms.fed.arrangePlanDetails": "Zeitplan-Detailanfrage der Lagerzählung",
    "lang.ark.equipment.equipmentTypeCannotNull": "Gerätetyp darf nicht leer sein",
    "lang.ark.fed.configurationParameter": "Konfigurationsparameter",
    "lang.ark.fed.editBackgroundMap": "Hintergrundbilder bearbeiten",
    "lang.ark.workflow.paramValueCode.constant": "constant",
    "lang.ark.fed.containerOrientation": "Container-Ausrichtung",
    "lang.ark.fed.screen.hybridRobot.binBindStopPoint": "Vorratsbehälter an Kartenpunkt binden",
    "lang.ark.fed.pauseSuccess": "Erfolgreich ausgesetzt",
    "lang.ark.interface.endpoint": "Endpunktcode",
    "lang.ark.workflow.notAllowFinishIfUnArrivedEnd": "",
    "lang.ark.fed.batchGeneration": "Stapelgenerierung",
    "lang.ark.fed.inAutomaticExecution": "In automatischer Ausführung...",
    "lang.ark.fed.chinese": "Chinesisch",
    "lang.ark.workflowTriggerMonitorStatus.cancel": "Stornieren",
    "lang.ark.workStatus.execute": "Wird ausgeführt",
    "lang.ark.waveTaskStatus.executing": "Wird ausgeführt",
    "lang.ark.fed.taskQueue": "Aufgabenwarteschlange",
    "lang.ark.fed.triggerTimer": "Auslösedauer",
    "lang.ark.workflow.area.artificialControl": "Manueller Steuerungsbereich",
    "lang.ark.workflow.task.status.node.pause": "Aufgabe ausgesetzt",
    "lang.ark.fed.unknownArea": "Unbekannter Bereich",
    "lang.ark.fed.workflowConfiguration": "Workflow-Management",
    "lang.ark.robot.classfy.lift": "Heben",
    "lang.ark.workflow.task.status.exception.completed": "Abnormal abgeschlossen",
    "lang.ark.fed.nonumberWorkstation": "Nr. {number} Arbeitsstaion",
    "lang.ark.fed.inventoryAdjustment": "Anpassung des Einladevorgangs ",
    "lang.ark.fed.robotWaitFlagnew": "Roboter wartet vor Ort",
    "lang.ark.interface.apiRemoveContainer": "Container löschen",
    "lang.ark.fed.noRmsTask": "Aufgabe zum Verschieben wird nicht generiert",
    "lang.auth.DataAPI.item0001": "Störung beim Abrufen von Benutzerdatenberechtigungen. Störung-Informationen: {0}",
    "lang.ark.fed.screen.flowNodeConfig.conditionTips": "",
    "lang.ark.fed.managementMode": "Verwaltungsmodus",
    "lang.ark.fed.distributionMode": "Verteilungsmodus",
    "lang.ark.workflow.arrive.action.goTurnOfAngle": "Komponenten um Winkel gedreht",
    "lang.ark.fed.screen.flowNodeConfig.ifRobotStatus": "",
    "lang.ark.fed.taskQuery": "Aufgabenabfrage",
    "lang.ark.fed.lift": "Heben",
    "lang.ark.fed.buttonFunctionConfiguration": "Konfiguration der Tastenfunktion",
    "lang.ark.fed.pleaseSelectTheProcessNodeYouWantToEdit": "Bitte wählen Sie den Prozessknoten aus, den Sie bearbeiten möchten!",
    "lang.ark.fed.uploadCutImage": "Laden Sie den Barcode hoch und schneiden Sie ihn zu.",
    "lang.ark.fed.floorStage.differentFloor": "Verschiedene Etagen",
    "lang.ark.fed.editing": "Bearbeiten",
    "lang.ark.fed.getGoodsMESLocation": "Adresse der Material-MES-Anforderung abrufen",
    "lang.ark.fed.systemConfiguration": "Systemkonfiguration",
    "lang.ark.element.workstation.atLeast.one": "Die Arbeitsstation muss mindestens einen Punkt haben",
    "lang.ark.bussinessModel.workflow": "Prozess",
    "lang.mwms.fed.arrangeTaskSplit": "Aufgabenteilungsstrategie der Lagerzählung",
    "lang.ark.workflow.canNotContinue": "Workflow-Statusstörung!",
    "lang.ark.container.containerAmountNumberCurrentScope": "Der angegebene Container-Code {} kann bis zu {} hinzugefügt werden, sind Sie sicher, es zu hinzufügen?",
    "lang.ark.fed.exportSuccessFailNum": '"Daten importiert, Import fehlgeschlagen {0}"',
    "lang.ark.apiCommonCode.flowStrategyNotSupported": "flowStrategy: {0} wird nicht unterstützt",
    "lang.ark.fed.productionLineNoGoodsInfo": "Produktionslinienstation ist nicht an Materialinformationen gebunden",
    "workflow.task.cancel.robot.task.failure": "Fehler beim Löschen! Grund: rms-Nachricht",
    "lang.ark.fed.callMaterialTask": "Materialaufrufaufgabe wird gestartet",
    "lang.ark.auth.userLoginNotSessionOtherStation": "",
    "lang.mwms.rf.multiSkuBoard": "Zug-Anzeigetafel",
    "lang.ark.warehouse.materialPointCellCodeRepeat": "{0} verwendet",
    "lang.ark.fed.pleaseGoToTheLoginPageAndSelectTheSerialPassword": "Bitte gehen Sie zur Anmeldeseite, um die Seriennummer auszuwählen.",
    "lang.ark.fed.goToCharge": "Aufladen",
    "lang.ark.apiCommonCode.binCodeAndOrderExist": "Punkt und Seriennr. desselbes Vorratsbehälters bestehen bereits. Ergänzen Sie sie nicht mehrmals.",
    "lang.ark.fed.startPoint": "Startpunkt",
    "lang.ark.fed.everyOnce": "Einmal",
    "lang.gles.interface.interfaceConfig": "",
    "lang.ark.robotDeviceComponent.deviceType": "Gerätetyp",
    "lang.ark.fed.startFlowSuccess": "Workflow erfolgreich initiiert",
    "lang.ark.button.operation.command.cleanWaitPoint": "Wartepunkt räumen",
    "lang.ark.fed.pleaseChooseAreaCode": "Wählen Sie die entsprechenden Knoten des Bereichs aus.",
    "lang.ark.workflowTriggerType.clear.workflow": "Bereinige den spezifizierten Prozess",
    "lang.ark.fed.taskCancelTime": "Aufgabenabbruchzeit",
    "lang.ark.fed.rackCode": "Code des Behälters",
    "lang.auth.fed.password.ruleDesc": "",
    "lang.ark.warehouse.noMatchMatnr": "Material ist nicht übereinstimmend",
    "lang.ark.fed.noWorkflowNodePleaseClickToSelect": "Derzeit gibt es keine Prozessknoten, bitte zur Auswahl klicken!",
    "lang.ark.action.interface.saveValue": "Bedingten Wert speichern",
    "lang.ark.fed.screen.area.addGroup": "Gruppieren anfügen",
    "lang.auth.role.edit.sysName.gms": "GMS",
    "lang.common.success": "Erfolgreich",
    "lang.ark.deliverOrder.invertedSequence": "Produktionslinienreihenfolge umkehren",
    "lang.ark.fed.wirelessSignal": "Drahtloses Signal",
    "lang.ark.logType.warehouseInterfaceLog": "Protokoll der Lagerschnittstellen",
    "lang.ark.fed.handleRefresh": "Manuelle Aktualisierung",
    "lang.ark.warehouse.cellCodeHasTask": "Die Aufgabe wurde an der Station als nicht erledigt erkannt. Bitte arbeiten Sie nach Abschluss.",
    "lang.ark.fed.playVoice": "Stimme senden",
    "lang.ark.fed.entryStartPoint": "Startpunkt des Eingangs",
    "lang.ark.fed.xialiao": "Ablegen über Zeitplan",
    "lang.ark.fed.cycleNum": "Zyklusnummer",
    "lang.ark.apiCallbackReg.sendInterval": "Element für Element/Alle",
    "lang.ark.fed.existBinCode": "Der Vorratsbehälter S / N ist bereits vorhanden",
    "lang.ark.loadCarrier.loadCarrierModelUsed": "",
    "lang.ark.workflow.area.releaseOrder": "Freigabesequenz",
    "lang.ark.fed.menu.workstationAndqueueController": "Warteschlangensteuerung",
    "lang.ark.fed.noWorkflowConfiguration": "Workflow-Konfiguration nicht verfügbar",
    "lang.ark.fed.startFlow": "Start",
    "lang.ark.fed.shelfAttribute.msgType": "Meldungst",
    "lang.common.cancel": "Stornieren",
    "lang.ark.fed.containerChangeLogType": "Aufgabenart des Containerbetriebsprotokolls",
    "lang.mwms.fed.innerException": "Bestands-Störung",
    "lang.ark.fed.add": "Hinzufügen",
    "lang.mwms.exceptionHandling": "Störungbehandlung",
    "lang.ark.fed.firstDrawWorkStop1": "Bitte zeichnen Sie zuerst den ersten Knoten des Workflows und den Typ des ersten",
    "lang.ark.workflow.brotherNodeNotWorkflowNode": "Die Robotervererbungsknoten und Bruderknoten müssen vom Typ Subflow sein!",
    "lang.ark.fed.bindWorkstation": "Gebundene Arbeitsstation",
    "lang.ark.fed.nodeNumber": "Knotennummer",
    "lang.ark.fed.japanese": "Japanisch",
    "lang.ark.fed.screen.LoginLog.loginTime": "",
    "lang.ark.fed.screen.systemConfig.businessGroup": "Geschäftsfunktion",
    "lang.ark.fed.savedSuccessfully": "Erfolgreich gespeichert",
    "lang.ark.fed.trafficRangeRun": "Läuft im Kontrollbereich",
    "lang.ark.hitStrategy.shortestDistance": "Kürzeste Entfernung",
    "lang.ark.fed.sureCutting": "Das folgende Material wird nicht vollständig entladen. Sind Sie sicher, dass Sie die Entladung abschließen?",
    "lang.ark.workflow.task.status.fetched": "Container abgeholt",
    "lang.ark.warehouse.policyHaveSameTriggerCondition": "Dieselbe Auslösebedingungsstrategie existiert bereits",
    "lang.mwms.fed.batchAdjustmentManager": "Ladungsanpassungsblattverwaltung",
    "lang.ark.fed.angle": "Winkel",
    "lang.ark.fed.sureFeeding": "Das folgende Material wird nicht vollständig eingespeist. Sind Sie sicher, dass die Einspeisung abgeschlossen ist?",
    "lang.ark.fed.workstationType": "Typ",
    "lang.ark.fed.waveGeneratePattern": "Wellenauswahl-Generierungsmodus",
    "lang.ark.fed.isExcetuTrigger": "Möchten Sie den aktuellen Auslöser ausführen?",
    "lang.ark.fed.node": "Knoten",
    "lang.ark.fed.successfulLogin": "Login erfolgreich",
    "lang.ark.base.license.exceptionForCannotFindServerUUID": "Hardwareinformationen stimmen nicht überein!",
    "lang.ark.operation.workflow.deleteExecution": "",
    "lang.ark.fed.inventoryStatus": "Speicherstatus",
    "lang.ark.loadCarrier.batchAddAmountTransfinite": "",
    "lang.ark.fed.options": "Optionen",
    "lang.ark.fed.homepage": "Homepage",
    "lang.ark.fed.taskSource.station": "Arbeitsstation ",
    "lang.ark.fed.targetPointCode": "Code des Zielpunkts",
    "lang.ark.fed.cycle": "Umlauf",
    "lang.ark.fed.workflowGroupName": "Prozessgruppenname",
    "lang.ark.fed.edit": "Bearbeiten",
    "lang.ark.fed.circle": "Kreis",
    "lang.ark.ruleStage.sameFloor": "",
    "lang.ark.fed.working": "Aktiv ",
    "lang.ark.auth.otherUserHaveLoginOtherStation": "",
    "lang.ark.fed.waveStrategy": "Wellenauswahl-Strategie ",
    "lang.ark.fed.appointmentTip": "Die Startzeit des Termins muss später als die aktuelle Uhrzeit sein!",
    "lang.ark.fed.bindTemplate": "Bindungsvorlage",
    "lang.ark.bin.binNotExist": "Punkt des Vorratsbehälters besteht nicht.",
    "lang.ark.fed.menu.vens.dmpTaskManage": "",
    "lang.authManage.fed.expiryDate": "Ablaufdatum",
    "lang.ark.fed.morePickingTitle": "Materialanforderung für mehrere Roboter: Wenn die Materialaufrufaufgabe mehrere Einspeisepunkte für die Materialvorbereitung benötigt, werden nach Bestätigung der kombinierten Einspeisepunkte mehrere Materialanforderungsblätter basierend auf verschiedenen Einspeisepunkten generiert. Auf jeden Einspeisepunkt verteilen. Kehrt nach Materialanforderung zur Bedarfsstation zurück.",
    "lang.ark.workflow.area.factoryFollowControl": '"Hersteller, für die Aufgrund von anderen Herstellern kein Zugang gestattet ist"',
    "lang.ark.workStatus.exception": "Abnormal abgeschlossen",
    "lang.ark.fed.fullBins": "Der Vorratsbehälter ist voll und es können höchstens 7 gelagert werden.",
    "lang.ark.fed.upper": "Auf",
    "lang.ark.fed.screen.hybridRobot.binCellCodeTip": "Das ist der Vorratsbehältercode des Geräts.",
    "lang.ark.fed.syncProductGoodsInfo": "Materialinformationen zu synchronisierten Produktionslinienstationen",
    "lang.ark.workflow.template.validate.templateMidNodeMustUnique": "Vorlagenmitte muss einzigartig sein.",
    "lang.ark.workflow.action.commandExecutePhase.undoBackArrived": "Rückzug / Rückkehr-Ankunft",
    "lang.ark.fed.menu.strategyCenter": "Strategiezentrum",
    "lang.ark.fed.startDrawing": "Mit dem Zeichnen beginnen",
    "lang.ark.warehouse.triggerPoint": "Arbeitsstation starten",
    "lang.ark.fed.offRefresh": "Aktualisierung deaktivieren",
    "lang.ark.interface.resent": "Erneut senden",
    "lang.ark.fed.areaAreaOrAreaShelfOrShelfShelfIllegalProcess": "Bereich-Bereich oder Bereich-Regal oder Regal-Regal, unzulässiger Prozess",
    "lang.ark.operation.workflow.pauseExecution": "",
    "lang.ark.workflow.condition.greaterThan": "Größer als",
    "lang.ark.robot.firmware.update": "Firmware aktualisieren",
    "lang.ark.fed.goodsName": "Materialname",
    "lang.ark.loadCarrier.alreadyRemoved": "",
    "lang.ark.workflow.existMultipleNodeExtendRobot": "Mehrere Roboter für Vererbung der Knoten verfügbar!",
    "lang.mwms.rf.receive": "Lagereingang",
    "lang.ark.fed.eitherOrRobotAndTypeNew": "Wählen Sie entweder ein Robotermodell oder einen Roboter",
    "lang.ark.taskCannotOperate": "Der Vorgang kann nicht ausgeführt werden!",
    "lang.ark.workflow.rollOverBack": "Umschlagen - Zurücksetzen",
    "lang.ark.equipment.equipmentCellCodeExists": "Punktposition {0} von einem Gerät besetzt",
    "lang.ark.fed.waveType": "Wellengruppierungsbedingungen",
    "lang.ark.fed.alarmType": "Alarmtyp",
    "lang.ark.fed.pointsList": "Punkteliste",
    "lang.ark.workflow.area.vertexInfo": "Spitzeninformationen",
    "lang.ark.fed.addContainerAmount": "Das Batch kann eine Containermenge hinzufügen (standardmäßig 1, bis zu 5.000)",
    "lang.ark.fed.types": "Typ",
    "lang.ark.apiCommonCode.instructionNotSupported": "Art des Ausführungsbefehls {0} wird nicht unterstützt",
    "lang.mwms.fed.businessRule": "Geschäftsregeln",
    "lang.ark.fed.abnormal": "Abnormal",
    "lang.ark.fed.orderAbnormalFailed": "Gescheitert. Es kann nur das Dokument mit abnormaler Aufgabensperre ausgeführt werden.",
    "lang.ark.fed.autoCancleTask": "Auto-Abbruch der Aufgabe",
    "lang.ark.fed.menu.palletPositionManage": "",
    "lang.ark.workflow.controllerOutOfBounds": "Die Controllermenge muss zwischen 1 und 65.536 liegen.",
    "lang.ark.fed.emptyIt": "Bereinigen",
    "lang.ark.fed.theMaterialsOfTime": "Material Gebrauchszeit",
    "lang.ark.fed.numberLang": "MENGE",
    "lang.ark.fed.robotID": "Roboter-ID",
    "lang.ark.fed.nodeConfirmedLeave.tip": "Zusammen mit Pfadpunkten der Karte genutzt",
    "lang.ark.fed.distributionMaterials": "Materialverteilung",
    "lang.auth.UserAPI.item0195": "Die Rolle ist nicht vorhanden",
    "lang.auth.UserAPI.item0194": "Fehler beim Ändern der Rolle",
    "lang.auth.UserAPI.item0197": "Der echte Name darf nicht null sein.",
    "lang.ark.fed.taskStartingPoint": "Aufgabenstartpunkt",
    "lang.auth.UserAPI.item0196": "Fehler beim Ändern der grundlegenden Informationen des Benutzers",
    "lang.authManage.web.existLoginUser": "An anderer Stelle eingeloggt, melden Sie sich bitte nach dem normalen Abmelden an!",
    "lang.auth.UserAPI.item0191": "Fehler beim Hinzufügen des Benutzers",
    "lang.auth.UserAPI.item0190": "Alter Kennwortfehler",
    "lang.auth.UserAPI.item0193": "Der Benutzername existiert bereits.",
    "lang.ark.apiStationCode.stationQueueUnDefinite": "Der Status der Warteschlangensteuerung für Arbeitsstationen ist nicht definiert",
    "lang.auth.UserAPI.item0192": "Störung beim Hinzufügen des Benutzers",
    "lang.ark.fed.waveConfig": "Wellenauswahl-Konfiguration",
    "lang.ark.addContainerFail": "Container konnte nicht hinzugefügt werden!",
    "lang.ark.fed.distribution": "Zuweisen",
    "lang.ark.fed.showByFlowClass": "Anzeigen entsprechend der Prozesskategorie",
    "lang.auth.UserAPI.item0199": "Kein zu ändernder Benutzer",
    "lang.mwms.monitorRobotMsg.notfree": "Das Regal ist nicht in Leerlauf.",
    "lang.auth.UserAPI.item0198": "Benutzername darf nicht leer sein",
    "lang.ark.fed.containerTypeInfo": "Informationen zum Containertyp",
    "lang.ark.fed.scanExceptionProcess": "Scan-Ausnahmebehandlung",
    "lang.ark.fed.shelfAttribute.REPLEN": "",
    "lang.ark.fed.onTheWay": "Auf dem Weg",
    "lang.ark.api.moving.startIsEmpty": "Der Startpunkt der Aufgabe ist null.",
    "lang.ark.fed.ContainerIsWorkingCanNotEmptyInventory": '"Container funktioniert, Lagerung nicht bereinigt."',
    "lang.ark.workflow.area.append": "Füllen Sie die freie Stelle von hinten",
    "lang.ark.fed.amounts": "Anzahl der Anforderungen",
    "lang.mwms.fed.taskMonitor": "Aufgabenüberwachung",
    "lang.ark.fed.SIMPPushTip": "SIMP-Konfigurationsregeln auswählen, unterstützt E-Mail, Enterprise WeChat, DingTalk, etc.",
    "lang.ark.externalDevice.caution": "Anmerkungen:",
    "lang.ark.fed.flowCreateTitle": "Prozesserstellung: Eine Prozessseite und einen Prozessanzeigestil erstellen.",
    "lang.ark.fed.pleaseAtLeastOneGoodsInfo": "Bitte fügen Sie mindestens eine Materialinformation hinzu",
    "lang.ark.fed.query": "Anfrage",
    "lang.ark.api.none": "Unbekannte Bedeutung",
    "lang.ark.fed.pleaseEnterANonnegativeNumber": "Bitte geben Sie eine nicht negative Zahl ein!",
    "lang.ark.fed.intelligentMovingSystem": "eines intelligenten Handhabungssystems!",
    "lang.ark.fed.chooseGoods": "Material zur Verteilung verfügbar",
    "lang.auth.UserAPI.item0175": "Fehler beim Abrufen des aktuellen Anmeldebenutzers",
    "lang.auth.UserAPI.item0174": "Keine Berechtigung",
    "lang.ark.fed.deviceNotExists": "",
    "lang.ark.fed.sourcesOfTheFactory": "Quellfabrik",
    "lang.mwms.fed.stocktake": "Bestandsprüfung",
    "lang.auth.UserAPI.item0177": "Der Benutzer ist deaktiviert. Wenden Sie sich an den Administrator, um den Benutzer erneut zu aktivieren oder melden Sie sich mit einem anderen Benutzer an.",
    "lang.auth.UserAPI.item0176": "Datenfehler",
    "lang.auth.UserAPI.item0179": "Kennwortfehler",
    "lang.auth.UserAPI.item0178": "Das Konto ist nicht vorhanden. Bitte noch einmal eingeben.",
    "lang.ark.record.interface.createTask": "",
    "lang.ark.fed.executeNotAllowed": "Während der Ausführung sind keine Vorgänge erlaubt.",
    "lang.common.failed": "Fehlgeschlagen",
    "lang.ark.fed.flowRule": "Verbreitungsregeln",
    "lang.ark.api.cellNotExists": "Zelle existiert nicht",
    "lang.ark.workflow.includesSubflowCancellationNotAllowed": "",
    "lang.ark.workflow.pathDecision": "Pfadurteil",
    "lang.authManage.web.others.relUser": "Zugehöriger Benutzer",
    "lang.ark.fed.materialConsumptionTime": "Material Gebrauchszeit",
    "lang.auth.UserAPI.item0184": "Keine Benutzerdaten",
    "lang.auth.UserAPI.item0183": "Abmeldungs-Störung",
    "lang.auth.UserAPI.item0186": "Das eingegebene Kennwort stimmt nicht mit dem vorherigen Kennwort überein.",
    "lang.ark.fed.notAllowOperation": "Ein Ziel-Code ist bereits vorhanden.",
    "lang.auth.UserAPI.item0185": "Benutzerabfrage-Störung",
    "lang.auth.UserAPI.item0180": "Datenfehler",
    "lang.auth.UserAPI.item0182": "Bitte geben Sie den Benutzernamen ein",
    "lang.ark.workflow.canNotPublish": "Workflow-Störung und kann nicht veröffentlicht werden",
    "lang.auth.UserAPI.item0181": "Bitte geben Sie das Passwort ein",
    "lang.ark.trafficControl.enterStrategy": "Freigabesequenz",
    "lang.auth.UserAPI.item0188": "Fehler beim Ändern des Kennworts",
    "lang.auth.UserAPI.item0187": "Alte Kennwortüberprüfungs-Störung des Benutzers",
    "lang.ark.systemParamCannotEdit": "Systemstandard, nicht bearbeitbar!",
    "lang.ark.fed.queue": "Warteschlangenlogik",
    "lang.auth.UserAPI.item0189": "Störung beim Ändern des Kennworts",
    "lang.ark.fed.screen.equipmentAssociatedInfo.columnUpdateTime": "Bearbeitet am",
    "lang.ark.backTaskNotAllowedUndo": "Die vorige Aufgabe kann nicht widerrufen werden!",
    "lang.ark.fed.processDescription": "Prozessbeschreibung",
    "lang.ark.fed.goSomewhere": "Irgendwohin gehen",
    "lang.ark.fed.trafficControlManage": "Verkehrsleitsystem",
    "lang.ark.fed.citeNode": "Referenzknoten",
    "lang.ark.fed.waveGenerateScope": "Wellengruppierungsbereich",
    "lang.authManage.web.others.number": "Ordnungsnummer",
    "lang.ark.fed.targetStation": "Arbeitsstation anfordern",
    "lang.ark.waveStatus.distributing": "Verteilen",
    "lang.gles.strategy.tallyStrategy": "",
    "lang.ark.fed.createAMap": "Eine Karte erstellen",
    "lang.ark.fed.delNodeConfig": "Sind Sie sicher, die aktuelle Interaktionskonfiguration des Knotens zu löschen?",
    "lang.ark.fed.multipleChoice": "Mehrere Optionen",
    "lang.ark.apiContainerCode.locationCodeExistsUsingContainer": "locationCode:{0}Der Behälter ist in Benutzung.",
    "lang.ark.apiCallbackReg.single": "Element für Element",
    "lang.ark.getDMPErr": "Fehlerhafte Geräteinformation erfasst",
    "lang.ark.fed.workstationUrl": "Arbeitsstations-URL",
    "lang.ark.fed.taskFrom": "",
    "lang.ark.fed.menu.chargeInfo": "",
    "lang.gles.interface.interfaceLog": "",
    "lang.ark.workflow.template.type.dynamiNode": "Dynamische Punk-Bewegung",
    "lang.ark.fed.chooseFreeMaterial": "Wählen Sie verfügbares Material für die Verteilung aus",
    "lang.ark.fed.GENERAL": "Normal",
    "lang.ark.workflowTriggerStatus.unEnable": "Deaktivieren",
    "lang.ark.workflow.Full": "Voll/besetzt",
    "lang.ark.fed.playVoiceTime": "Sendezeit",
    "lang.ark.fed.shelfLeaveSuccess": "Container erfolgreich entfernt",
    "lang.ark.fed.common.btn.delete": "Löschen",
    "lang.ark.fed.deliverType": "Verteilungsmethode",
    "lang.ark.workflow.canDeleteContainerFlag": "Den Container entfernen",
    "lang.ark.fed.screen.workflowInfo.workflowExeTaskId": "",
    "lang.ark.noOperation": "Keine Aktion",
    "lang.ark.workflowTrigger.logType.interface": "Schnittstellenprotokoll",
    "lang.ark.base.license.errorLicenseCustomerIdOrInstanceId": "Die Zertifikatskunden-ID oder Instanz-ID ist nicht mit dem Zertifikat konsistent",
    "lang.ark.fed.firstNode": "Erster Knoten",
    "lang.ark.fed.cleanAll": "Alles löschen",
    "lang.ark.fed.feedingNode": "Einspeisungsknoten",
    "lang.ark.fed.ordinary": "Normal",
    "lang.ark.api.goturn.neededsidesError": "Der benötigte Seitenwert des Containerrotationsparameters ist nicht F, B, L oder R.",
    "lang.mwms.monitorRobotMsg.scanabnormal": "Scan-Ausnahme.",
    "lang.ark.fed.forklift": "Gabelstapler",
    "lang.ark.fed.theCargoSpaceIsLocked": "Die Position wurde gesperrt.",
    "lang.auth.UserAPI.item0115": "Der Benutzername existiert bereits.",
    "lang.auth.UserAPI.item0114": "Störung beim Hinzufügen des Benutzers",
    "lang.ark.workflow.template.validate.templateFinishNodeMustUnique": "Vorlagenendpunkt muss einzigartig sein.",
    "lang.auth.UserAPI.item0117": "Die Rolle ist nicht vorhanden",
    "lang.auth.UserAPI.item0116": "Fehler beim Ändern der Rolle",
    "lang.auth.UserAPI.item0111": "Alter Kennwortfehler",
    "lang.auth.UserAPI.item0110": "Fehler beim Ändern des Kennworts",
    "lang.auth.UserAPI.item0113": "Fehler beim Hinzufügen des Benutzers",
    "lang.auth.UserAPI.item0112": "Störung beim Ändern des Kennworts",
    "lang.ark.workflow.arrive.action.command.executeFailed": "Befehl konnte nicht ausgeführt werden.",
    "lang.auth.UserAPI.item0108": "Das eingegebene Kennwort stimmt nicht mit dem vorherigen Kennwort überein.",
    "lang.auth.UserAPI.item0107": "Benutzerabfrage-Störung",
    "lang.ark.interface.oneToSixTeenLettersAndNumbers": "1-16 Stellen von Buchstaben oder Zahlen unterstützt!",
    "lang.ark.workflow.cellCode": "Punkt-Standortnummer",
    "lang.auth.UserAPI.item0109": "Alte Kennwortüberprüfungs-Störung des Benutzers",
    "lang.ark.workflow.extendRobotFalse": "Nein",
    "lang.ark.interface.checkout": "Anzeigen",
    "lang.auth.UserAPI.item0120": "Benutzername darf nicht leer sein",
    "lang.gles.systemManage.baseDict": "",
    "lang.ark.fed.interfaceSetNodeValue": "API-Zuweisungsknoten",
    "lang.ark.fed.outWarehouse": "Manueller Ausgang",
    "lang.ark.fed.conButtonLogResult": "Ausführungsergebnis",
    "lang.auth.UserAPI.item0126": "Keine Benutzerdaten",
    "lang.ark.fed.linkName": "Linkname",
    "lang.auth.UserAPI.item0125": "Störung in {0} Benutzer",
    "lang.auth.UserAPI.item0122": "Störung beim Ändern des Benutzers",
    "lang.ark.fed.rotateMap": "Drehen",
    "lang.ark.fed.theFirstNodeMustBeWorkstationOrDockPoint": "Der erste Knoten muss eine Arbeitsstaion oder ein Punkt sein",
    "lang.ark.fed.screen.hybridRobot.stopPointCode": "Punktcode der Karte",
    "lang.auth.UserAPI.item0121": "Kein zu ändernder Benutzer",
    "lang.auth.UserAPI.item0124": "Kein Benutzer ausgewählt",
    "lang.auth.UserAPI.item0123": "Der Benutzerstatus wird nicht geändert",
    "lang.ark.fed.suspend": "Pause",
    "lang.ark.fed.processInstance": "Prozessinstanz",
    "lang.ark.fed.screen.area.ynGroup": "Gruppierung ja/nein",
    "lang.auth.UserAPI.item0119": "Der echte Name darf nicht null sein.",
    "lang.ark.fed.entryPoint": "Eingangspunkt",
    "lang.wms.biz.UserServiceImpl.deleteAdminAlert": "",
    "lang.auth.UserAPI.item0118": "Fehler beim Ändern der grundlegenden Informationen des Benutzers",
    "lang.ark.controlNodeType.station": "Arbeitsstation ",
    "lang.ark.workflow.completeBizAutoTrigger": "Automatische Filialauswahl nach Geschäftsabschluss",
    "lang.ark.apiNodeActionCode.componentCommandIsEmpty": "Der Komponentenbefehl für die Konfiguration der Knoteninteraktion ist Null.",
    "lang.ark.warehouse.goodsTaskCantNotExecute": "Diese Aufgabe wurde von der anderen Materialeinspeisepunkte übernommen. Nicht an dieser Stelle ausführen.",
    "lang.ark.fed.common.checkNumberFormatMsg0": "Geben Sie eine Ziffer ein, deren Länge innerhalb von {0} liegt.",
    "lang.ark.workflow.area.stragingRange": "Lagerbereich",
    "lang.ark.fed.common.checkNumberFormatMsg1": "Geben Sie eine Ziffer ein, deren Länge zwischen {0} und {1} liegt.",
    "lang.ark.fed.orderAbnormalSure": "Fahren Sie mit dem Dokument manuell in einem abnormalen Zustand fort. Sind Sie sicher, dass Sie fortfahren wollen?",
    "lang.ark.fed.menu.flowNodeConfig": "Interaktionskonfiguration",
    "lang.ark.fed.factory": "Fabrik",
    "lang.authManage.web.auth.roleList": "Rollenprofil",
    "lang.ark.recycleAreaTaskNotAllowedOperate": "Die Aufgaben in der Recycling-Zone nicht bearbeitet werden kann",
    "lang.ark.fed.location": "Vorratsbehälter",
    "lang.ark.fed.defaultType": "Standardtyp",
    "lang.mwms.fed.inventoryNum": "Buchbestand",
    "lang.ark.shelfCodeErr": "Das Format der Container-Typnummer ist falsch, bitte geben Sie eine 6-stellige Zahl ein",
    "lang.ark.fed.sure": "OK",
    "lang.ark.fed.reflectCell": "Entsprechende Knoten des Bereichs",
    "lang.ark.workflow.paramValueCode.count": "count",
    "lang.ark.fed.friday": "Freitag",
    "lang.ark.task.log.export.title.workflow.instance": "Prozessinstanz",
    "lang.ark.fed.descriptionMessage": "Beschreibungsinformation",
    "lang.auth.UserAPI.item0104": "Bitte geben Sie den Benutzernamen ein",
    "lang.ark.fed.station": "Arbeitsstation",
    "lang.auth.UserAPI.item0103": "Bitte geben Sie das Passwort ein",
    "lang.auth.UserAPI.item0106": "Keine Benutzerdaten",
    "lang.auth.UserAPI.item0105": "Abmeldungs-Störung",
    "lang.auth.UserAPI.item0100": "Der Benutzer ist deaktiviert. Wenden Sie sich an den Administrator, um den Benutzer erneut zu aktivieren oder melden Sie sich mit einem anderen Benutzer an.",
    "lang.ark.fed.thisWorkflowHasBeenSelected": "{str} ausgewählt, dieser Workflow",
    "lang.auth.UserAPI.item0102": "Kennwortfehler",
    "lang.auth.UserAPI.item0101": "Das Konto ist nicht vorhanden. Bitte noch einmal eingeben.",
    "lang.ark.fed.rackType": "Regaltyp",
    "lang.authManage.web.permission.permissiontype": "Berechtigungstyp",
    "lang.ark.workflow.recoveryAreaType.customCell": "Benutzerdefinierter Rückholbereich",
    "lang.ark.fed.arrivalOrientation": "Ausrichtung erreichen",
    "lang.ark.loadCarrier.loadCarrierModelFormErr": "",
    "lang.ark.fed.whereRobotsCanWalk": "Wo der Roboter ankommen kann",
    "lang.ark.fed.thursday": "Donnerstag",
    "lang.mwms.fed.charts": "Statistischer Bericht",
    "lang.ark.workflow.template.validate.templateTypeNotBlank": "Vorlagentyp darf nicht 0 sein.",
    "lang.ark.fed.oneWayExit": "Einzel-Ausgang",
    "lang.authManage.web.common.modifyPw": "Passwort ändern",
    "lang.ark.action.interface.responseParamType": "Typ des zurückgegebenen Parameters",
    "lang.ark.robot.classfy.mix": "Rekombination",
    "lang.ark.fed.cancelledSuccessfully": "Erfolgreich abgebrochen",
    "lang.authManage.web.others.toPrePage": "Zurück zur vorherigen Seite",
    "lang.ark.firstSendNodeUnsupportedOperation": "Der erste Knoten unterstützt diesen Vorgang nicht!",
    "lang.ark.fed.expiringDate": "Ablaufdatum",
    "lang.ark.fed.pleaseSelectARobot": "Bitte einen Roboter auswählen",
    "lang.ark.fed.goToWork": "Zur Arbeit gehen",
    "lang.ark.workflow.function.type.functionArea": "Funktionsbereich",
    "lang.ark.fed.closeRightTabs": "Die rechte Seite schließen",
    "lang.ark.fed.component.workflow.label.nonSpecified": "Nicht angeben",
    "lang.ark.fed.firstClassification": "Tier 1 Kategorie",
    "lang.ark.workstationNotExists": "Arbeitsstation existiert nicht!",
    "lang.ark.fed.screen.equipmentAssociatedInfo.dialogDetail.labelFloor": "Etage",
    "lang.ark.fed.basicData": "Grundlegende Daten",
    "lang.ark.fed.pleaseSelectAtLeastOneAction": "Bitte wählen Sie mindestens eine Operation aus",
    "lang.ark.fed.waiting": "Wartet",
    "lang.ark.fed.orientation": "Ausrichtung",
    "lang.ark.fed.emptyMoving": "Roboter ohne Last",
    "lang.ark.container.containerEntryWay.manual": "Manueller Eingang",
    "lang.auth.PermissionAPI.item0009": "Die gegenseitige ausschließliche Überprüfung zwischen den Subsystemen der Speicherseitenberechtigung und der Seitenberechtigung ist fehlgeschlagen.",
    "lang.ark.workflowConfig.configErr": "Workflow-Konfigurationsfehler. Bitte prüfen",
    "lang.auth.PermissionAPI.item0008": "Die Subsystem-ID der Speicherseitenberechtigung stimmt nicht mit der Seitenberechtigung überein.",
    "lang.gles.receipt.receiptUpAndDownMaterialOrder": "",
    "lang.auth.PermissionAPI.item0005": "Störung beim Erfragen aller Berechtigungen: {0}",
    "lang.auth.PermissionAPI.item0004": "Störung beim Speichern von Seitenberechtigung: {0}",
    "lang.auth.PermissionAPI.item0007": "Fehler beim Überprüfen der Anzahl der Speicherseitenberechtigungs-Subsysteme",
    "lang.auth.PermissionAPI.item0006": "Wiederholung des Rollennamens",
    "lang.authManage.web.common.newPassword": "Neues Passwort",
    "lang.ark.fed.flowStartAgain": "Prozessneustart",
    "lang.ark.fed.arrived": "Eingegangen",
    "lang.ark.fed.cellNodeDeviceExists": "",
    "lang.gles.containerManage": "",
    "lang.ark.fed.screen.equipmentAssociatedInfo.dialogDetail.labelEquipType": "Gerätetyp",
    "lang.ark.fed.containerDeal": "Container-Behandlung",
    "lang.ark.fed.monday": "Montag",
    "lang.ark.hitStrategy.denseStorage": "Kompakte Palettenlagerung",
    "lang.ark.fed.conditionalValue": "Bedingungswert",
    "lang.ark.workflow.palletLatticeNotExist": "Am Zielort ist kein Fach vorhanden.",
    "lang.ark.fed.shelfLocation": "Regalposition",
    "lang.ark.fed.greaterThan": ">",
    "lang.ark.fed.please": "Bitte",
    "lang.ark.workflow.rollBack": "Rückgängig machen",
    "lang.mwms.monitorRobotMsg81000": "Geringer Batteriestatus aller Roboter vor Ort",
    "lang.ark.fed.rackPoint": "Regalpunkt",
    "lang.ark.fed.delRowData": "Sind Sie sicher, die Daten in dieser Zeile zu löschen?",
    "lang.ark.workflowTrigger.logType.task": "Protokoll",
    "lang.ark.robot.manual": "Fernsteuerungsmodus",
    "lang.ark.workflowConfig.cellFunctions.wait": "Warten",
    "lang.mwms.fed.kanban": "Elektronische Anzeigetafel",
    "lang.ark.fed.modificationRecord": "Eintrag aktualisieren",
    "lang.ark.fed.recommendedSize": "Die empfohlenen Größen sind",
    "lang.mwms.monitorRobotMsg16000": "Roboterausnahme",
    "lang.ark.fed.sacnFailAndCancelTaskV2": "Behälter-Code-Erkennung fehlgeschlagen, Ausnahme-Fehlercode {0}. Warten auf vorgelagerte Systemverarbeitung.",
    "lang.ark.workflow.executingCancelOperation": "",
    "lang.ark.workflow.controllerCodeOutOfBounds": "Die Controllernummer muss zwischen 0 und 65535 liegen ",
    "lang.ark.fed.triggerHandler": "Auslösezeit",
    "lang.ark.interface.interfaceDesc.name": "Feldname",
    "lang.ark.equipment.equipmentAlreadyRelevanceWorkflow": "Das Gerät wurde mit einem Arbeitsablauf verknüpft. Deinstallieren Sie den Arbeitsablauf zuerst.",
    "lang.ark.workflow.paramValueCode.locationToFloor": "",
    "lang.ark.fed.receiptTimeout": "Erhalt über Zeitplan",
    "lang.ark.fed.licenseEditController": "Zertifikatsverwaltung",
    "lang.ark.element.area.atLeast.one": "Der Bereich muss mindestens einen Regalpunkt haben",
    "lang.ark.cellCodeConfig.exists": "Speichern fehlgeschlagen, die Knotenkonfiguration ist bereits vorhanden!",
    "lang.ark.unsupportedRunMode": "Nicht unterstützte Betriebsart!",
    "lang.ark.fed.appointMentStatus": "Terminstatus",
    "lang.gles.baseData.baseContainerType": "",
    "lang.ark.fed.stopPointIdNonExistent": "Die aktuelle Position ist nicht vorhanden. Versuchen Sie es mit einem anderen.",
    "lang.ark.fed.serialNumPlaceholder": "Verlassen Sie die Materialauswahl, der Fokus wählt automatisch die Scan-Box aus und scannt den Stab-S / N.",
    "lang.ark.fed.distributionRobot": "Roboterverteilung",
    "lang.ark.fed.setEndPoint": "Als Endpunkt festlegen",
    "lang.ark.fed.standardStation": "Standard-Arbeitsstation",
    "lang.ark.fed.triggerTime": "Auslösezeit",
    "lang.ark.fed.noAvailableRobots": "Noch kein Roboter verfügbar",
    "lang.ark.fed.ruleCode": "Bedingungscode",
    "lang.ark.fed.cellName": "Positionsname",
    "lang.ark.fed.orderExComplete": "Abnormal abgeschlossen",
    "lang.ark.fed.rack": "Regal",
    "lang.ark.fed.enlarge": "Herauszoomen",
    "lang.ark.ruleStage.differentFloor": "",
    "lang.ark.fed.fixedRollerTrackOnTheGroundToConnectWithRobot": "Ein auf dem Boden befestigter Rollentisch zum Andocken an den Roboter",
    "lang.ark.workflow.cycleType.noLoop": "Keine Schleife",
    "lang.ark.workflow.denyFollowFactoryNullError": '"Der Hersteller, für den kein Zugang erlaubt ist, ist Null. Bitte geben Sie den Hersteller ein."',
    "lang.ark.fed.waveStrategyCode": "Strategiecode",
    "lang.ark.workflow.paramValueCode.skuCode": "skuCode",
    "lang.ark.fed.workstation.msg.logout": "",
    "lang.ark.fed.autoRefresh": "Automatische Aktualisierung",
    "lang.auth.PermissionAPI.item0001": "Störung beim Nachfragen von Seitenberechtigung: {0}",
    "lang.auth.PermissionAPI.item0003": "Fehler beim Bearbeiten der Seitenberechtigung. Andere Berechtigung kann nicht als Seitenberechtigung gespeichert werden.",
    "lang.ark.fed.contents.flowConfig.recycleType.api": "",
    "lang.auth.PermissionAPI.item0002": "Fehler beim Bearbeiten der Seitenberechtigung. Die {0}-Rolle ist nicht vorhanden.",
    "lang.ark.syncRobotInfoError": "Erfassung von Roboterinformation fehlgeschlagen",
    "lang.ark.fed.speedLimitZone": "Geschwindigkeitsbegrenzungsbereich",
    "lang.ark.interface.clientCode": "Kundencode",
    "lang.ark.workflow.canAddshelfFlag": "Ein Regal hinzufügen",
    "lang.ark.fed.AutoIn": "Automatisch...",
    "lang.ark.robot.map.init": "Karteninitialisierung",
    "lang.ark.fed.remainingDistance": "Tipp: Entfernung zum nahe gelegenen FTF:",
    "lang.ark.fed.targetFactory": "Fabrik nachfragen",
    "lang.ark.fed.collectionTime": "Empfangszeit",
    "lang.ark.fed.buttonType": "Tastenart",
    "lang.ark.workflow.arrive.action.component.robotComponentExecuteFailed": "Der Roboter kann den Komponentenbefehl der Knoteninteraktionskonfiguration nicht ausführen. ({0})",
    "lang.ark.fed.waveRanage": "Wellengruppierungsbereich",
    "lang.ark.fed.screen.flowTemplate.specialNodeRepetitionTip": "Der gleiche Knotentyp und die gleichen Knotencodedaten sind bereits vorhanden, bitte ändern Sie sie vor dem Absenden!",
    "lang.ark.fed.orderCreateFail": "Erstellung fehlgeschlagen",
    "lang.ark.workflowTriggerMonitorStatus.success": "Erfolg",
    "lang.ark.fed.logType": "Protokolltyp",
    "lang.ark.fed.robotLog": "Roboterprotokoll",
    "lang.ark.fed.pickUpTaskDetail": "Details des Materialanforderungsblattes",
    "lang.ark.fed.getGoods": "Materialaufruf",
    "lang.authManage.web.common.requiredInput": "Bitte füllen Sie die erforderlichen Felder aus",
    "lang.ark.workflowTriggerMonitorStatus.failed": "Gescheitert",
    "lang.ark.workflow.denseStorageTemplateAreaEmpty": "Die Anfangsstelle liegt in einem kompakten Lagerbereich ohne freie Behälter.",
    "lang.ark.workflow.wareHouseStationBusinessConfig": "Arbeitsstationsbetrieb",
    "lang.ark.loadCarrier.loadCarrierModelRequired": "",
    "lang.ark.fed.screen.flowTemplate.LogicNodeRepetitionTip": "Logischer Punkt: derselbe Knotentyp und dieselben Knotencode-Daten sind bereits vorhanden, bitte ändern Sie sie vor dem Absenden!",
    "lang.ark.workflow.invalidStopPointStatus": "Punktstatusstörung",
    "lang.mwms.fed.stockRotation": "Bestandsumschlagsregelung",
    "lang.ark.fed.deleteTaskConfirmText": "Wenn Aufgabe gelöscht wird, wird sie bis zur letzten Unterbrechung und aussetzen des Prozesses wiederhergestellt und im Falle einer Wiederherstellung wieder eingesetzt. Sind Sie sicher, zu löschen?",
    "lang.ark.workflowConfig.cellCodeDoNotExists": "Knotencode nicht konfiguriert",
    "lang.ark.fed.containerSide": "Containerfläche",
    "lang.ark.fed.screen.hybridRobot.stopPointTip": "Dies ist der mechanische Punktcode, bevor der Roboter das Gerät erreicht.",
    "lang.ark.fed.cuttingGoods": "Ablegen",
    "lang.ark.fed.component.workflow.label.nodeComponent": "Teil des Knotens",
    "lang.ark.fed.rotateLeft": "Nach links drehen",
    "lang.ark.fed.nonEmptyShelf": "Nicht leeres Regal",
    "lang.ark.workflow.end": "Ende",
    "lang.ark.workflow.template.validate.templateCodeNotBlank": "Vorlagencode darf nicht 0 sein.",
    "lang.ark.fed.component.workflow.label.specified": "Angeben",
    "lang.ark.fed.unlockSure": "Entsperrungsposition bestätigen",
    "lang.ark.fed.liveAllNodeUnallowAdd": '"Alle Knoten wurden neu hinzugefügt, wiederholen Sie das Hinzufügen nicht!"',
    "lang.gles.receipt.stockAdjustOrder": "",
    "lang.ark.fed.onEnter": "Eintreten",
    "lang.ark.fed.floorStage.sameFloor": "Gleiche Etage",
    "lang.auth.UserAPI.item0098": "Fehler beim Abrufen des aktuellen Anmeldebenutzers",
    "lang.auth.UserAPI.item0097": "Keine Berechtigung",
    "lang.ark.apiContainerCode.codeRightNotNumber": "Die ganz rechte(n) Stelle(n) von startContainerCode müssen Ziffern sein",
    "lang.ark.fed.screen.area.maxTaskSize": "Trefferquote bei gleichzeitigen Aufgaben",
    "lang.ark.taskStatusCannotCancel": "Die aktuelle Aufgabe befindet sich im Status {0} und kann nicht gelöscht werden!",
    "lang.ark.fed.createNew": "Erstellen ",
    "lang.ark.workflow.task.status.node.wait": "Knoten wartet",
    "lang.auth.UserAPI.item0099": "Datenfehler",
    "lang.mwms.fed.classifyMutex": "Sich gegenseitig ausschließende Konfiguration der Kategorie",
    "lang.ark.fed.triggerNameRepeat": "Doppelter Auslösername",
    "lang.authManage.web.common.item0027": "Bearbeiten",
    "lang.mwms.monitorRobotMsg13003": "Ende des Befehlssendens des Ladestationsbefehls",
    "lang.authManage.web.common.item0028": "Neu",
    "lang.mwms.monitorRobotMsg13001": "Ladestation ist offline (langfristiger Verbindungsverlust)",
    "lang.mwms.monitorRobotMsg13002": "Starten Sie das Senden des Ladestation Befehls-Timeout",
    "lang.ark.fed.reset": "Zurücksetzen",
    "lang.mwms.fed.move": "Bestandsbewegung",
    "lang.ark.area.lockJobInsertError": "Regionale Terminsperrzeit überschneidet sich. Bitte prüfen!",
    "lang.mwms.monitorRobotMsg13000": "Ladestation unverbunden (temporärer Netzwerkfehler)",
    "lang.ark.button.command.reset": "Hüpfen",
    "lang.ark.waveTaskStatus.create": "Erstellen",
    "lang.ark.workflow.workflowRuleExpressionErr": "Fehler beim Speichern. Bitte konfigurieren Sie den vollständigen Ausdruck!",
    "lang.ark.fed.create": "Erstellen",
    "lang.ark.sendRmsTaskError": "RMS-Wiederherstellungsstörung. Bitte versuchen Sie es später noch einmal",
    "lang.authManage.web.common.item0023": "Anfrage",
    "lang.ark.api.globalConfigCancelOff": "Löschen der Konfiguration nicht aktiviert",
    "lang.ark.workflow.endSimple": "Ende",
    "lang.ark.pda.function.container.leave": "Container entfernen",
    "lang.ark.fed.robotLoadStatus": "",
    "lang.ark.warehouse.materialPointNameRepeat": "Der Name existiert bereits!",
    "lang.mwms.fed.ownerMutex": "Sich gegenseitig ausschließende Konfiguration des Frachtbesitzers",
    "lang.ark.fed.pleaseSelectADestinationOnTheMapElementsOnly": "Bitte wählen Sie ein Ziel auf der Karte (nur Kartenelemente) aus",
    "lang.ark.fed.welcomePage": "Begrüßungsseite",
    "lang.ark.fed.screen.hybridRobot.setPointOffset": "Senden Sie einen Versatzwert des Punkts",
    "lang.ark.recycleFunctionSwitchIsClosed": "Nicht aktivierte Funktion, bitte wenden Sie sich an das Systempersonal",
    "lang.ark.fed.menu.dockModel": "Regalmodell",
    "lang.common.ok": "OK",
    "lang.ark.fed.alarmTypeDetail": "Erhalt über Zeitplan, Zuführung über Zeitplan, Ablegen über Zeitplan.",
    "lang.ark.fed.dispatchQueue": "Verteilungssequenz",
    "lang.ark.fed.cancelButton": "Schaltfläche „Abbrechen“",
    "lang.ark.fed.shelfAttribute.RTV": "",
    "lang.mwms.fed.batchProperty": "Ladungs-Attribut",
    "lang.ark.interface.apiStationQueueStart": "Aktivieren Sie die Warteschlange zum Abholen",
    "lang.ark.fed.selectTaskType": "Bitte wählen Sie den Aufgabentyp",
    "lang.ark.loadCarrier.loadCarrierModelNameIsEmpty": "",
    "lang.authManage.web.others.deviceInfo": "Hardwareinformationen",
    "lang.ark.button.type.selfLocking": "Selbsthemmend",
    "lang.ark.fed.strategyCenter": "Strategiezentrum",
    "lang.authManage.api.menu.userList": "Benutzerprofil",
    "lang.ark.fed.theTotalNumberOfContainers": "Gesamte Behälter",
    "lang.ark.fed.buttonNumber": "Tasten-Nr.",
    "lang.ark.fed.unMatchGoodsLocation": '"Gestell stimmt nicht überein, Position des Einspeisungsgestells {0}, bitte bei Antwort auf das Gestell bedienen."',
    "lang.ark.record.dmp.createTask": "",
    "lang.ark.fed.continuityPassage": "Kontinuierliches Passieren",
    "lang.ark.fed.stationLineUpControl": "Arbeitsstations-Warteschlangensteuerung",
    "lang.ark.fed.rackTypeManagement": "Verwaltung des Regaltyps",
    "lang.ark.fed.containBinIsNotEmpty": "Das Regalfach ist nicht leer.",
    "lang.ark.fed.baseInfoV2": "Grundlegende Informationen",
    "lang.ark.api.template.unclearTargetBusinessType": "Undefinierter Geschäftstyp des Zielorts.",
    "lang.ark.fed.rmsTaskPhase": "Ausführungsphase",
    "lang.mwms.fed.adjustments": "Bestandsregulierung",
    "lang.ark.task.log.export.title.task.status": "Aufgabenstatus",
    "lang.ark.workflowConfig.cellFunctions.beep": "Horn",
    "lang.ark.fed.notAllowedEditContainer": "Container wurde bereits eingegeben und kann nicht bearbeitet werden",
    "lang.ark.trafficControl.manageArea": "Management-Bereich",
    "lang.ark.fed.bezier": "Kurve",
    "lang.ark.fed.onePickingTitle": "Materialanforderung für einen Roboter: Wenn die Materialaufrufaufgabe mehrere Einspeisepunkte für die Materialvorbereitung benötigt, übernimmt ein Roboter die Aufgabe am ersten Einspeisepunkt. Dann geht der Roboter zum zweiten Einspeisepunkt. Wenn er alle Punkte durchlaufen hat, sendet er das Material zur Entladung an die Bedarfsstation zurück.",
    "lang.ark.fed.excel.data.repeat": "",
    "lang.ark.apiNodeActionCode.componentCommandNotSupported": "Der Komponentenbefehl für die Konfiguration der Knoteninteraktion wird nicht unterstützt.",
    "lang.ark.operation.workflow.recoveryExecution": "",
    "lang.authManage.web.common.editTime": "Bearbeitungszeit",
    "lang.ark.fed.password": "Passwort",
    "lang.ark.fed.component.workflow.msg.duplicatedMaterialEntryType": "Die Zuführungsart ist dieselbe. Prüfen Sie die betreffenden Gerätekonfigurationen.",
    "lang.authManage.web.common.cancel": "Stornieren",
    "lang.mwms.fed.seedManager": "Säen-Mauer-Management",
    "lang.ark.interface.messageName": "Schnittstelle ausführen",
    "lang.ark.workflow.template.validate.templateStartNodeMustUnique": "Vorlagenstartpunkt muss einzigartig sein.",
    "lang.ark.fed.changePassword": "Passwort ändern",
    "lang.ark.fed.releaseSuccess": "Erfolgreich veröffentlicht",
    "lang.ark.workflow.ruleConfig": "Regelkonfiguration",
    "lang.ark.fed.taskName": "Aufgabename",
    "lang.ark.base.license.errorLicenseInfoStr": "Fehler bei der Zertifikatsüberprüfung!",
    "lang.ark.fed.theBranchMustStartWithTheMainLineNode": "Zweige müssen mit dem Hauptknoten beginnen",
    "lang.ark.fed.select": "Auswählen",
    "lang.wms.station.web.UserAPI.item0305": "Deaktivieren",
    "lang.ark.workflow.lastTaskArrived": "Letzte Aufgabe angekommen",
    "lang.ark.warehouse.productLineInfoException": "Produktionslinieninformationen existieren nicht, bitte bestätigen!",
    "lang.ark.warehouse.stationBusinessConfigDescription": "Nur gültig für die Arbeitsstation in der Vollversion, die als einen einzelnen Eingang dient. Bitte kontaktieren Sie uns, wenn Sie die Besuchsadresse benötigen.",
    "lang.ark.fed.areanodeidDidNotSelectShelfPoint": "Der Bereich {nodeId} hat keinen Regalpunkt ausgewählt.",
    "lang.ark.fed.orderStatus": "Dokumentstatus",
    "lang.ark.fed.menu.moduleInformationConfiguration": "Tastenfunktionskonfiguration",
    "lang.mwms.fed.logistics": "",
    "lang.ark.interface.messageBody": "Nachricht",
    "lang.authManage.fed.import": "Importieren",
    "lang.ark.fed.operateSwitch": "Betriebsschalter",
    "lang.ark.workflow.priorityAllocation": "Priorität",
    "lang.ark.workflow.arrive.action.goTurnOfAngle.neg90": "-90°",
    "lang.ark.fed.orderHang": "Aussetzen",
    "lang.ark.shelfFirCodeErr": "Das Format der Container-Typnummer ist falsch, der Anfangsbuchstabe der Palette ist P und der Anfangsbuchstabe des Regals ist S",
    "lang.ark.record.rms.sendCommandTask": "",
    "lang.ark.fed.period": "Frequenz",
    "lang.ark.workflow.action.commandExecutePhase.previousArrived": "Vorherige Aufgabe kommt an",
    "lang.common.internalException": "Innere Störung des Servers",
    "lang.ark.record.breakTask": "",
    "lang.ark.fed.containBinIsNotExists": "Das Regalfach ist nicht vorhanden.",
    "lang.ark.fed.workTriggerManage": "Auslöser-Management",
    "lang.ark.workflow.exceptionHandler.queueUp": "Anstellen",
    "lang.ark.workflow.workflowTaskHasArrivedTargetLocation": "",
    "lang.ark.fed.flowName": "Prozessname",
    "lang.ark.workflowConfig.cellFunctions.backup": "Rückseite",
    "lang.ark.workflow.task.status.manual.completed": "Manuell erledigt",
    "lang.ark.fed.saveFailed": "Speichern fehlgeschlagen",
    "lang.ark.workflowgroup.selector.ok": "Ja",
    "lang.ark.interface.interfaceDesc.english": "Deutsch",
    "lang.ark.fed.nowYouCanDrawAMap": "Jetzt können Sie eine Karte zeichnen",
    "lang.ark.fed.listInformation": "Listeninformation:",
    "lang.ark.fed.editingTime": "Bearbeitungszeit",
    "lang.gles.stockInStore.fixedGoodsPositionStock": "",
    "lang.ark.fed.returnShelf": "Regale zurückgeben",
    "lang.ark.workflow.notAllowCancel": "Stornierung ist während der Roboterkombination und -platzierung nicht zulässig",
    "lang.gles.baseData.baseDevice": "",
    "lang.mwms.monitorRobotMsg3": "Der Start- oder Endpunkt der geplanten Route ist ein Hindernis",
    "lang.mwms.monitorRobotMsg4": "Roboter oder Regal steht im Weg",
    "lang.ark.warehouse.singleContainerSide": "Einseitig",
    "lang.mwms.monitorRobotMsg5": "Die Route wird von anderen Robotern besetzt",
    "lang.ark.workflow.task.status.executing": "Wird ausgeführt",
    "lang.ark.fed.locationOfShelvesWithXyCoordinateAttributes": "Eine Position, an der Regale mit xy-Koordinateneigenschaften platziert werden können",
    "lang.ark.fed.configEnableTip": 'Die Konfiguration kann nur aktualisiert werden, wenn "aktivieren" geschlossen ist. Bitte stellen Sie sicher, dass die Aktualisierung während der arbeitsfreien Zeit erfolgt.',
    "lang.mwms.monitorRobotMsg6": "Keine Route für die Planung",
    "lang.mwms.monitorRobotMsg7": "Roboter kann nicht angeschlossen werden",
    "lang.mwms.monitorRobotMsg8": "Zeitüberschreitung beim Senden einer Unteraufgabe",
    "lang.ark.record.clearWaitPoint": "",
    "lang.mwms.monitorRobotMsg9": "Zeitüberschreitung beim Senden eines Befehls",
    "lang.ark.fed.confirm": "Eingeben",
    "lang.ark.robot.Task.pausing": "Der Workflow wurde angehalten. Bitte versuchen Sie es nach der Wiederherstellung erneut!",
    "lang.ark.fed.cancelFinish": "Stornierung abgeschlossen",
    "geekplus.moving.uic.elTableWrapperVue2.column.datetime": "Datum und Uhrzeit",
    "lang.ark.workflowgroup.selector.no": "Nein",
    "lang.ark.workflow.areaLockedEdit": "Verkehrskontrollbereich gesperrt und kann nicht bearbeitet werden.",
    "lang.ark.warehouse.importFileFormatError": "Dokumentenformatfehler! Bitte laden Sie eine Vorlage herunter und importieren Sie sie.",
    "lang.ark.fed.front": "Vorderseite",
    "lang.ark.fed.rackPositionRecovery": "Wiederherstellung der Regalposition",
    "lang.ark.workflow.area.trafficRange": "Verkehrskontrollbereich",
    "lang.ark.fed.updateAngle": "Winkel aktualisieren",
    "lang.ark.fed.addTaskTrigger": "Task-Auslöser hinzufügen",
    "lang.ark.fed.manuallyControlArea": "Manueller Kontrollbereich",
    "lang.ark.fed.screen.equipmentAssociatedInfo.dialogDetail.labelMaxTaskCont": "Höchstzahl der operativen Aufgaben",
    "lang.ark.api.moving.destIsEmpty": "Der Zielort der Aufgabe ist null.",
    "lang.ark.workflowPeriod.oneDay": "Einmal jeden Tag",
    "lang.ark.fed.cannotDeleteFetchNodesAlone": "Die Aufnahme eines Knotens kann nicht separat gelöscht werden",
    "lang.ark.interface.apiStationList": "Arbeitsstations-Abfrage",
    "lang.ark.fed.details": "Details",
    "lang.ark.fed.trafficDeleteMsg": "Nach Entfernen wird der Betrieb des Roboters im Kontrollbereich derzeit nicht mehr weiter verfolgt und andere Roboter können den Bereich betreten. Sind Sie sicher, dass Sie ihn entfernen wollen?",
    "lang.ark.fed.welcomeToUse": "Willkommen bei der Verwendung",
    "lang.ark.fed.robotType": "Robotertyp",
    "lang.ark.interface.apiLastStep": "Aufgabe zurückgegeben",
    "lang.ark.fed.workFlowConfigIsNotModel": "Der Workflow ist kein Vorlagenprozess.",
    "lang.ark.fed.conButtonLogFailed": "Fehlschlag ",
    "lang.mwms.monitorRobotMsg1": "Die Route konnte nicht geplant werden",
    "lang.mwms.monitorRobotMsg2": "Roboter nicht in der Karte",
    "lang.ark.fed.taskInterVal": "Aufgabenintervall",
    "lang.mwms.fed.priorityManager": "Prioritätsmanagement",
    "lang.ark.workflow.autoSkipSimple": "Automatisch überspringen",
    "lang.ark.fed.isForbidTrigger": "Möchten Sie diesen Auslöser deaktivieren?",
    "lang.ark.fed.rice": "m",
    "lang.ark.fed.workFlowRule": "Verbreitungsregeln",
    "lang.ark.warehouse.hasSameStationGoods": "Innerhalb derselben Produktionslinie-Station ist kein sich wiederholendes Material zulässig.",
    "lang.ark.workflow.TimeTrigger": "Verzögerte automatische Auslösung nachfolgender Aufgaben",
    "lang.ark.fed.automaticTrigger": "Zeitauslöser",
    "lang.ark.workflowTrigger.logType.controllerButtonLog": "Physikalisches Tastenprotokoll",
    "lang.ark.fed.productionLine": "Produktionslinienmanagement",
    "lang.auth.RoleAPI.item0007": "Störung beim Speichern der Datenberechtigung: {0}",
    "lang.ark.fed.abnormalCancelTime": "Ausnahmeabbruchzeit",
    "lang.ark.fed.production": "Produktionslinie",
    "lang.ark.fed.Entrance": "Zugang",
    "lang.auth.RoleAPI.item0008": "roleId-Parameter kann nicht null sein",
    "lang.auth.RoleAPI.item0005": "Fehler beim Bearbeiten der Datenberechtigung. Die Rolle ist nicht vorhanden.",
    "lang.ark.workflowConfig.cellFunctions.palletPackCell": "PALLET_PACK_CELL",
    "lang.auth.RoleAPI.item0006": "Fehler beim Bearbeiten der Datenberechtigung. Andere Berechtigung kann nicht als Datenberechtigung gespeichert werden.",
    "lang.ark.fed.interactiveActionNameConfig": "Konfiguration der Interaktionsaktion",
    "lang.ark.api.workflowWorkIdIsNull": "workflowWorkId darf nicht leer sein",
    "lang.auth.RoleAPI.item0009": "Fehler beim Löschen der Rolle. Die Rolle ist nicht vorhanden.",
    "lang.auth.RoleAPI.item0003": "Störung beim Aktivieren oder Deaktivieren der Rollenberechtigung: {0}",
    "lang.auth.RoleAPI.item0004": "Störung beim Aktivieren der Rollenberechtigung: {0}",
    "lang.auth.RoleAPI.item0001": "Fehler beim Aktivieren oder Deaktivieren der Rollenberechtigung! Der Parameter ist null",
    "lang.ark.fed.common.btn.save": "Speichern",
    "lang.auth.RoleAPI.item0002": "Störung beim Aktivieren oder Deaktivieren der Rollenberechtigung! Der Parameter entspricht nicht dem Standard: Status {0}",
    "lang.ark.fed.workstationAndqueueController": "Warteschlangensteuerung",
    "lang.ark.fed.workTaskDetails": "Prozessaufgabendetails",
    "lang.mwms.fed.strategyWave": "Wellenstrategie",
    "lang.ark.record.robotCallback.arrived": "",
    "lang.authManage.web.others.operation": "Betrieb",
    "lang.auth.RoleAPI.item0016": "Die verwendete Rolle kann nicht gelöscht werden",
    "lang.auth.RoleAPI.item0017": "Die Rolle (roleId = 1) kann nicht bearbeitet werden",
    "lang.auth.RoleAPI.item0010": "Störung beim Löschen der Rolle: {0}",
    "lang.ark.fed.nodeInteractiveMode": "",
    "lang.auth.RoleAPI.item0011": "{0} Rollenberechtigungs-Störung. Parameter ist null: roleId {1} roleName {2}",
    "lang.auth.RoleAPI.item0014": "{0} Rollenberechtigungs-Störung: Fehler beim Aktualisieren der roleId{1}",
    "lang.auth.RoleAPI.item0015": "Störung beim Nachfragen von Rollenberechtigung: {0}",
    "lang.auth.RoleAPI.item0012": "{0} Rollenberechtigungs-Störung. Es werden mehrere Datensätze gefunden: roleId {1} roleName {2}",
    "lang.auth.RoleAPI.item0013": "{0} Rollenberechtigungs-Störung. Parameter-id und name stimmen nicht mit db-Datensätzen überein",
    "lang.gles.workflow.abnormalTask": "",
    "lang.mwms.fed.strategicCenter": "Strategiezentrum",
    "lang.ark.fed.productionDate": "Produktionsdatum",
    "lang.ark.fed.ensure": "Eingeben",
    "lang.ark.fed.rackAngle": "Behälterwinkel",
    "lang.gles.strategy.hit": "",
    "lang.ark.apiContainerCode.containerCategoryNoMatch": "Nicht übereinstimmend mit containerCategory: {0}",
    "lang.ark.fed.templateName": "Name der Vorlage",
    "lang.wms.station.web.UserAPI.item0306": "Aktivieren",
    "lang.ark.container.containerEntryWay.moving": "Eingang des Roboters",
    "lang.ark.warehouse.noShelf": "Derzeit ist kein Gestell verfügbar",
    "lang.ark.workstationDoesNotExistNode": "{1} Dockingpunkt ist in der Arbeitsstation Nr. {0} nicht vorhanden!",
    "lang.ark.fed.alreadyIssued": "Ausgestellt",
    "lang.ark.action.interface.referenceValue": "Wert zitieren",
    "lang.ark.fed.taskAbnormal": "Aufgabenausnahme.",
    "lang.ark.fed.publish": "Veröffentlichen",
    "lang.ark.action.interface.internal": "Antwortausnahmen",
    "lang.ark.warehouse.supportBusinessDescription": "Materialaufruf: Aktivieren, d.h. die Arbeitsstation wählt die Materialien, um einen Materialaufruf durchzuführen. Materialzufuhr: Aktivieren, d.h. die Workstation unterstützt die manuelle Auswahl der Materialien, um eine Materialaufruf durchzuführen, sie kann auch die Aufgabe des Materialaufrufs zur Verteilung übernehmen. Bewegung: Aktivieren, d.h. die Workstation wählt einen Prozess aus, um die Bewegungsaufgabe zu initiieren. Verwaltungsmodus: Aktivieren, d.h. die Workstation aktiviert den Verwaltungsmodus für Materialaufruf, Zuführung und Bewegung, sodass die oben genannten Prozesse und Bewegungsaufgaben an einem bestimmten Punkt ausgeführt werden können.",
    "lang.ark.workflow.manulSimple": "Manueller Auslöser",
    "lang.ark.fed.conButtonLogDayData": "Tagesdaten",
    "lang.ark.fed.manualProcess": "Manuelle Bearbeitung",
    "lang.ark.fed.successfulConnectionToTheServer": "Mit dem Server erfolgreich verbindet ",
    "lang.ark.fed.notAllowMergeGet": "Der Abrufknoten unterstützt es nicht, mit anderen Knoten verbunden zu werden!",
    "lang.ark.fed.menu.scaffold": "",
    "lang.ark.fed.model": "Typ",
    "lang.ark.fed.zoneLockReleaseTime": "Bereichssperr- / Freigabezeit",
    "lang.ark.fed.createFlow": "Prozesse erstellen",
    "lang.ark.fed.selectPalletPosition": "Palettenposition wählen",
    "lang.ark.fed.waitTime": "Wartezeit",
    "lang.ark.fed.firstAddSendPoint": "Bitte fügen Sie zuerst einen Lieferknoten hinzu!",
    "lang.ark.workflow.staging": "An einen temporären Lagerbereich liefern",
    "lang.ark.fed.enterCodeOrName": "Geben Sie den Materialcode oder den Materialnamen ein.",
    "lang.ark.button.operation.command.systemEmergencyStop": "System-Notaus",
    "lang.ark.fed.loadingCell": "Zufuhr zur Arbeitsstation",
    "lang.ark.fed.actionTriggerHandlerError": "Bitte geben Sie die Auslösezeit an",
    "lang.ark.fed.pointContainNoStation": "Auslöseposition/Behälter-ID/Arbeitsstation",
    "lang.ark.fed.demandProductionLineOrWorkshop": "Produktionslinie / Werkstatt anfragen",
    "lang.ark.waveStatus.preDistribute": "Um verteilt zu werden",
    "lang.ark.interface.errorMessage": "Ursache der Störung",
    "lang.ark.fed.screen.systemConfig.sysGroupName": "Kategorienmarkierung",
    "lang.ark.fed.materialQuantity": "Anzahl der Typen",
    "lang.ark.workflowTriggerType.workflowGroup": "Auslösen-Prozess-Gruppe",
    "lang.ark.fed.defaultCacheArea": "Standardpufferbereich",
    "lang.ark.fed.enabled": "Aktiviert",
    "lang.ark.workflow.recycleAreaIsFull": "Abbrechen fehlgeschlagen. Der Wiederherstellungsbereich ist voll, bitte versuchen Sie es später!",
    "lang.ark.fed.leaving": "Verlassen...",
    "lang.ark.fed.screen.workflowInfo.executeAction": "",
    "lang.ark.fed.endPoint": "Endpunkt",
    "lang.ark.apiContainerCode.locationCodeExistsContainer": "LocationCode: {0} Container existiert bereits",
    "lang.ark.fed.operationDuration": "Vorgangsdauer",
    "lang.ark.fed.deliveryInfo": "Informationen zur Verteilung",
    "lang.ark.fed.closeLoop": "Geschlossener-Kreislauf-Prozess",
    "lang.ark.fed.cancelConfirmMsg3": "Prozess nach Prozessabbruch beendet. Der Container wird in den vorgesehenen Rückholbereich gesendet. Abbruch bestätigt?",
    "lang.ark.fed.cancelConfirmMsg2": "Alle Aufgaben in dieser Prozessinstanz abbrechen. Abbruch bestätigt?",
    "lang.ark.fed.cancelConfirmMsg1": '"Bitte geben Sie den Bereich an, in den der Container nach dem Abbruch des Vorgangs gesendet werden soll."',
    "lang.ark.fed.total": "Gesamt",
    "lang.ark.fed.goodsWaitSend": "Um verteilt zu werden",
    "lang.ark.workflow.acceptTask": "Anweisungen erhalten",
    "lang.mwms.monitorTaskPhaseMsg21": "Am Ziel angekommen",
    "lang.ark.theRunningTaskIsUsingThisArea": "Edition ist nicht erlaubt. Die ausgeführte Aufgabe nutzt dieses Bereichs!",
    "lang.mwms.monitorTaskPhaseMsg22": "Die Aufgabe wurde abgeschlossen",
    "lang.ark.fed.menu.vensManagement": "",
    "lang.mwms.monitorTaskPhaseMsg20": "Verließ den Aufzug",
    "lang.ark.workflowTrigger.logType": "Protokolltyp",
    "lang.ark.api.requestExists": "Doppelte requestId",
    "lang.ark.workflow.shelfAlreadyExists": "Container existiert bereits",
    "lang.ark.fed.timingTrigger": "Geplante Auslösung",
    "lang.ark.fed.areUSureLocked": "Sperren bestätigt?",
    "lang.ark.base.license.licenseInfoStrIsNull": "Der Geheimschlüssel des Zertifikats ist null!",
    "lang.ark.task.exception.cellCode.not.null": "Zielposition {} hat bereits einen Behälter ({}).",
    "lang.ark.fed.conButtonLogALLData": "Alle Daten",
    "lang.ark.fed.stationPosition": "Stationspunkt",
    "lang.ark.fed.containerLevelOne": "Klassifizierung von Containern erster Klasse",
    "lang.mwms.monitorTaskPhaseMsg10": "Der Rollenroboter kam am Abholpunkt an und machte sich zum Abholen bereit",
    "lang.mwms.monitorTaskPhaseMsg11": "Der Rollenroboter kam am Abwurfpunkt an und machte sich bereit zum Ablegen",
    "lang.ark.fed.initiationTime": "Anfangszeit",
    "lang.ark.fed.containerTypeNo": "Container-Typnummer",
    "lang.ark.workStatus.complete": "Fertig",
    "lang.mwms.monitorTaskPhaseMsg18": "Am Wartepunkt (außerhalb des Aufzugs) angekommen und auf das Betreten des Aufzugs gewartet",
    "lang.mwms.monitorTaskPhaseMsg19": "Betrat den Aufzug",
    "lang.mwms.monitorTaskPhaseMsg16": "Abholen der Kiste abgeschlossen",
    "lang.mwms.monitorTaskPhaseMsg17": "Die Kiste kam an",
    "lang.ark.fed.executeWorkflowFailed": "Kein Container verfügbar. Prozesseinleitung fehlgeschlagen.",
    "lang.mwms.monitorTaskPhaseMsg14": "Der Rollenroboter hat das Abholen abgeschlossen",
    "lang.ark.api.goturn.noTurnParameter": "Geben Sie entweder die benötigten Seitenwerte oder Drehungswinkel der Containerrotationsparameter an.",
    "lang.mwms.monitorTaskPhaseMsg15": "Der Rollenroboter hat das Ablegen abgeschlossen",
    "lang.ark.fed.excel.deviceMode": "",
    "lang.ark.fed.inputParameterAssignment": "Wertzuweisung des gesendeten Parameters",
    "lang.mwms.monitorTaskPhaseMsg12": "Am Wartepunkt angekommen",
    "lang.mwms.monitorTaskPhaseMsg13": "Den Wartepunkt verlassen",
    "lang.ark.workflow.occupiedByWorkflowGroup": "Dieser Prozess wurde der Prozessgruppe zugeordnet. Sie können diesen Prozess nicht löschen oder den ersten Knoten auf manuell setzen",
    "lang.ark.workflow.extendRobot": "Gleichen Roboter beauftragen",
    "lang.ark.fed.waveTaskStatus": "Status der Wellenauswahl-Aufgabe",
    "lang.auth.UserAPI.item0001": "Für Benutzer {0}, die sich angemeldet haben, melden Sie sich bitte nach dem Abmelden an",
    "lang.ark.fed.processDemonstration": "Prozessanzeige",
    "lang.ark.workflow.workflowEditFailed": "Der verwendete Prozess oder die verwendete Prozessgruppe kann nicht bearbeitet werden",
    "lang.ark.fed.pointPositionName": "Punkt",
    "lang.ark.fed.component.workflow.label.materialEntryType": "Zuführungsart",
    "lang.ark.fed.floorStage": "Durchlaufstrategie der Etage",
    "lang.ark.fed.scrollIsNotMatchWithGoods": "Die Spulen-Nr. und die Materialinformationen stimmen nicht überein.",
    "lang.authManage.web.common.pagePermission": "Seitenberechtigungen",
    "lang.ark.no.operation": "Keine Operation",
    "lang.ark.fed.autoClean": "Automatisches Räumen",
    "lang.ark.warehouse.columnCountLimit26": '"Maximal 26 Spalten für jede Ebene, bitte geben Sie die Anzahl der Spalten erneut ein."',
    "lang.ark.fed.component.workflow.edgeName.equipmentTask": "Durchlauf des Geräts",
    "lang.ark.fed.stop": "Pause",
    "lang.ark.workflow.area.releaseCountDown": "Countdown für die automatische Freigabe",
    "lang.authManage.fed.screen.login.toModify": "",
    "lang.ark.trafficControl.taskControlRange": "Verkehrskontrollbereich",
    "lang.ark.workflow.task.status.commandExecuting": "Befehl in Ausführung",
    "lang.ark.fed.yes": "Ja",
    "lang.ark.fed.language": "Sprache",
    "lang.ark.fed.screen.hybridRobot.robotOffsetConfig": "Konfiguration des Versatzes des Roboterpunkts",
    "lang.ark.workflowConfig.cellFunctions.transCell": "TRANS_CELL",
    "lang.ark.warehouse.cellCodeNotexits": "Material mit Punkt {0} existiert nicht",
    "lang.ark.fed.eachRow": "Jede Zeile",
    "lang.ark.trafficControl.containerFunction": "Speicherbereichsfunktion",
    "lang.ark.fed.lagTime": "Verzögerungszeit",
    "lang.ark.alreadyExpired": "Die aktuelle Zeit hat den eingestellten Gültigkeitszeitraum überschritten",
    "lang.ark.workflow.userNoAuthToLogin": '"Der aktuelle Benutzer ist nicht berechtigt, sich bei der Arbeitsstation anzumelden."',
    "lang.ark.fed.createFlowSuccess": "Workflow erfolgreich initiiert",
    "lang.ark.workflow.paramValueCode.extraParam15": "extraParam15",
    "lang.ark.action.interface.conditionExtraParam18": "",
    "lang.ark.fed.uploadOnly": "Nur hochladen",
    "lang.ark.trigger.missedEffectiveTime": "Verpasste das Datum des Inkrafttretens",
    "lang.ark.workflow.paramValueCode.extraParam14": "extraParam14",
    "lang.ark.fed.noPermissionPage": "Diese Seite hat keine Berechtigung",
    "lang.ark.action.interface.conditionExtraParam19": "",
    "lang.ark.workflow.paramValueCode.extraParam13": "extraParam13",
    "lang.ark.action.interface.conditionExtraParam16": "",
    "lang.ark.workflow.paramValueCode.extraParam12": "extraParam12",
    "lang.ark.action.interface.conditionExtraParam17": "",
    "lang.ark.workflow.paramValueCode.extraParam19": "extraParam19",
    "lang.ark.action.interface.conditionExtraParam14": "",
    "lang.ark.fed.waitingPoint": "Wartepunkt",
    "lang.ark.workflow.paramValueCode.extraParam18": "extraParam18",
    "lang.ark.action.interface.conditionExtraParam15": "",
    "lang.ark.workflow.paramValueCode.extraParam17": "extraParam17",
    "lang.ark.action.interface.conditionExtraParam12": "",
    "lang.ark.workflow.paramValueCode.extraParam16": "extraParam16",
    "lang.ark.action.interface.conditionExtraParam13": "",
    "lang.ark.workflowConfig.cellFunctions.chargerCell": "CHARGER_CELL",
    "lang.ark.fed.containerLeave": "Entfernen",
    "lang.ark.fed.enableSettings": "Einstellung aktivieren",
    "lang.ark.interface.interfaceDesc.content": "Feldrückgabewert",
    "lang.ark.interface.apiRecover": "Workflow wiederhergestellt",
    "lang.ark.fed.menu.operator": "",
    "lang.ark.fed.sunday": "Sonntag",
    "lang.ark.workflow.taskType": "Aufgabentyp",
    "lang.ark.action.interface.conditionExtraParam20": "",
    "lang.ark.workflow.paramValueCode.extraParam20": "extraParam20",
    "lang.ark.fed.triggerType": "Auslösertyp",
    "lang.common.retry": "Wiederholen",
    "lang.gles.materialArchives": "",
    "lang.ark.warehouse.binShelfNotFree": '"Der Vorgang kann nicht ausgeführt werden, da das Materialgestell nicht leer ist."',
    "lang.ark.fed.sendComplete": "Verteilung beendet",
    "lang.ark.fed.siteMonitoring": "Standortüberwachung",
    "lang.ark.interface.movingMulti": "Punkt-zu-Mehrpunkt-Bewegung",
    "lang.ark.fed.pleaseSelectAvailableRobot": "Einen Roboter auswählen:",
    "lang.ark.fed.deviceInfo": "",
    "lang.ark.warehouseTask.pickTaskOverTime": "Der Kommissionierauftrag wird nicht beansprucht ({0} m über Planung).",
    "lang.ark.fed.robotNumber": "Roboternummer",
    "lang.ark.fed.getGoodsTimeout": "Zuführung über Zeitplan",
    "lang.ark.action.interface.conditionExtraParam10": "",
    "lang.ark.fed.moveTitle": '"Bewegung: Aktivieren, d.h. die Workstation unterstützt die Auswahl eines Prozesses zum Initiieren der Bewegungsaufgabe."',
    "lang.ark.action.interface.conditionExtraParam11": "",
    "lang.ark.workflow.notAllowFinishIfEmptyLoad": "",
    "lang.authManage.web.common.search": "Anfrage",
    "lang.ark.button.operation.command.addContainer": "Eintritt des Behälters",
    "lang.mwms.fed.putawayRuleDesignatedBin": "Angegebener Abholpunkt von Einlagerungsregeln",
    "lang.ark.fed.processProcessGroup": "Prozess/Prozessgruppe",
    "lang.ark.fed.insert": "Einfügen",
    "lang.ark.workflow.acceptUpstreamParam": "Upstream-Parameter empfangen",
    "lang.ark.fed.turningSurface": "Oberfläche drehen",
    "lang.fed.ark.sure": "Eingeben",
    "lang.ark.fed.taskOver": "Zurücknehmen",
    "lang.mwms.fed.queryTask": "Aufgabenabfrage",
    "lang.ark.fed.pleaseSelectShelf": "Bitte ein Regal auswählen!",
    "lang.ark.fed.shelfAttribute.GMSInterfaceField": "GMS-Schnittstellenfeld",
    "lang.ark.fed.editInterface": "Schnittstellenbefehl bearbeiten",
    "lang.ark.fed.completionOfDrawing": "Die Zeichnung vervollständigen",
    "lang.ark.apiCommonCode.systemRecoverFailed": "Systemwiederherstellung fehlgeschlagen",
    "lang.mwms.monitorTaskPhaseMsg9": "Aufladen",
    "lang.mwms.monitorTaskPhaseMsg8": "Bewegen",
    "lang.mwms.monitorTaskPhaseMsg7": "Regal drehen",
    "lang.authManage.web.common.abled": "Aktivieren",
    "lang.mwms.monitorTaskPhaseMsg2": "Regal wird erhalten",
    "lang.mwms.monitorTaskPhaseMsg1": "Regale holen",
    "lang.mwms.fed.putawayRuleDesignatedArea": "Angegebener Bereich mit Einlagerungsregeln",
    "lang.mwms.monitorTaskPhaseMsg6": "Regal zurückgeben",
    "lang.mwms.monitorRobotMsg.noshelf": "Kein Gestell vorhanden ",
    "lang.mwms.monitorTaskPhaseMsg5": "Regal am Ziel angekommen",
    "lang.mwms.monitorTaskPhaseMsg4": "Anstellen",
    "lang.mwms.monitorTaskPhaseMsg3": "Regal bewegen",
    "lang.ark.interface.interfaceDesc.desc": "Feldbeschreibung",
    "lang.ark.workflow.template.type.nodeToMultiNode": "Punkt-zu-Mehrpunkt-Bewegung",
    "lang.ark.sys.config.group.common": "Grundlegende Einstellung",
    "lang.gles.StockInTransit": "",
    "lang.ark.fed.publishAndSave": "Veröffentlichen und speichern",
    "lang.ark.fed.refreshCycle": "Aktualisierungsfrequenz",
    "lang.ark.fed.operationSymbol": "Betreiber",
    "lang.mwms.fed.stationManager": "Arbeitsstations-Verwaltung",
    "lang.ark.fed.targetPointType": "Art des Zielpunkts",
    "lang.ark.workflow.canAddContainerFlag": "Fügen Sie einen Container hinzu",
    "lang.ark.fed.departure": "Entfernen",
    "lang.ark.pda.function.area.lock": "Bereichsverriegelung",
    "lang.ark.apiStationCode.stationQueueAlreadyDisable": "Die Warteschlangensteuerung der Arbeitsstation ist bereits deaktiviert und kann daher nicht erneut deaktiviert werden",
    "lang.ark.fed.paramCode": "Parametercodierung",
    "lang.ark.interface.apiContainerList": "Container-Abfrage",
    "lang.ark.fed.moduleInformationConfiguration": "Konfiguration der Modulinformation",
    "lang.ark.fed.singleFactorySingleEntrances": "Einzeleingang für Einzelhersteller",
    "lang.ark.fed.shelfCondition": "Regalzustand",
    "lang.ark.fed.basicInformation": "Grundinformation",
    "lang.ark.workflow.ruleDecision": "Regelbewertung",
    "lang.ark.fed.unlock": "Entsperren",
    "lang.ark.fed.cloaseAll": "Alle deaktivieren",
    "lang.ark.fed.containerCodeAutoIncrement": "Automatischer Anstieg von mehreren Container-Codes",
    "lang.ark.containerNotAtTheTriggerPoint": "Die aktuelle Position des Containers {0} ist {1}, nicht der Betätigungspunkt {2}!",
    "lang.ark.fed.palletBitNode": "Positionsknoten der Palette",
    "lang.ark.fed.priorityGoUp": "Prioritätsanstieg",
    "lang.ark.fed.flowNodeConfig": "Interaktionskonfiguration",
    "lang.ark.systemParamCannotDelete": "Systemstandard und kann nicht gelöscht werden",
    "lang.ark.fed.rotateRight": "Nach rechts drehen",
    "lang.ark.container.shelfTypeNotExit": "Eine sekundäre Klassifizierung des Containers existiert nicht",
    "lang.gles.logisticsConfig": "",
    "lang.ark.fed.dischargeLoadMore": "Nach der Freigabe laden",
    "lang.gles.baseData.area": "",
    "lang.common.abort": "Aussetzen",
    "lang.ark.fed.workstationManagement": "Arbeitsstations-Verwaltung ",
    "lang.ark.fed.entryEndPoint": "Endpunkt des Eingangs",
    "lang.ark.interface.messageInstruction": "Befehl ausführen",
    "lang.ark.fed.specialAreaSavedSuccessfully": "Sonderbereich",
    "lang.ark.fed.orderCancel": "Stornieren",
    "lang.ark.fed.menu.replacementMaterial": "",
    "lang.ark.fed.userHelpDocumentDownload": "Benutzer helfen beim Herunterladen von Dokumenten",
    "lang.ark.record.robotCallback.leaveStart": "",
    "lang.ark.fed.conButtonLogSuccess": "Erfolg",
    "lang.ark.fed.currentContainer": "Aktueller Container",
    "lang.mwms.fed.inventoryCharts": "Lagerbericht",
    "lang.ark.fed.GoodsTask": "Aufgabe kommissionieren",
    "lang.ark.fed.actionsNotAllowToUp": "Der Endknoten hat einen Interaktionskonfigurationsfehler. Die Aktion kann nicht aufbocken!",
    "lang.ark.fed.changeStopPoint": "Wechseln Sie zu einem anderen Dockingpunkt",
    "lang.ark.fed.theDeliveryTime": "Verteilungszeit",
    "lang.ark.fed.orderOccupy": '"Erste Einnahme, erster Durchgang"',
    "lang.ark.containerAlreadyExists": "Container existiert bereits!",
    "lang.ark.fed.workflowList": "Prozessliste",
    "lang.ark.workflow.paramValueCode.extraParam11": "extraParam11",
    "lang.ark.workflow.paramValueCode.extraParam10": "extraParam10",
    "lang.ark.record.robotCallback.arriveWaitPoint": "",
    "lang.auth.Audit.item0010": "",
    "lang.auth.Audit.item0012": "",
    "lang.auth.Audit.item0011": "",
    "lang.ark.fed.shelfAttribute.upstreamInterfaceField": "Vorschaltschnittstellenfeld",
    "lang.ark.fed.taskId": "Aufgabenummer",
    "lang.auth.Audit.item0013": "",
    "lang.ark.workflowTrigger.logType.triggerExeLog": "Überwachung des Aufgabe-Auslösers",
    "lang.ark.fed.pleaseChooseShelfTypeOrShelfCode": "Bitte wählen Sie Regaltyp oder Regalcode",
    "lang.ark.workflow.shelfNotExists": "Container existiert nicht",
    "lang.ark.interface.request": "Anfordern",
    "lang.ark.fed.robotsCanBeScheduledByTheSystemDuringChargingElectricityMust": "Der Roboter, der gerade geladen wird (muss größer als 5 % geladen werden), kann vom System geplant werden",
    "lang.ark.fed.breakAndWait": "Unterbrechen und warten",
    "lang.ark.task.exception.work.not.null": "Zielposition {} ist bereits von einer Aufgabeninstanz belegt ({}).",
    "lang.ark.fed.areaMustContainNodes": "Fehler beim Speichern! Der Bereich muss mindestens einen Punkt enthalten!",
    "lang.ark.workflow.workflowUndoing": "Stornierung fehlgeschlagen. Prozess wird abgebrochen",
    "lang.ark.fed.raceway": "Rollentisch",
    "lang.ark.fed.selectionRobot": "Einen Roboter auswählen",
    "lang.ark.fed.specialArea": "Sonderbereich",
    "lang.mwms.monitorRobotMsg14004": "Antrieb-Unterspannung",
    "lang.ark.workflow.noAvailableStopPointBeginNode": "Es wurden keine verfügbaren Ausgangspunkte gefunden",
    "lang.mwms.monitorRobotMsg14000": "Regalposition muss bestätigt werden",
    "lang.ark.workflow.condition.greaterThanOrEqual": "Größer als oder gleich",
    "lang.gles.receipt.receiptWarehousingOrder": "",
    "lang.ark.fed.target": "Ziel",
    "lang.ark.fed.minute": "Minute",
    "lang.ark.fed.menu.containerManage": "Behälterverwaltung",
    "lang.ark.fed.addComponentInterface": "Komponentenbefehl hinzufügen",
    "lang.ark.api.template.startNodeNotMatchForWave": "Der angegebene Startpunkt {0} stimmt nicht mit dem Vorlagen-Startpunkt {1} überein.",
    "lang.ark.element.no.element.selected": "Keine ausgewählten Elemente",
    "lang.gles.receipt.outWarehouseOrder": "",
    "lang.ark.trafficControl.enterStrategy.byTime": "Wer zuerst kommt, erhält zuerst",
    "lang.ark.workflowTrigger.logType.containerChangeLog": "Container-Protokoll",
    "lang.ark.fed.canDeleteshelfFlag": "Regale entfernen",
    "lang.ark.fed.saveCurCustomConfig": "Speichern der aktuellen benutzerdefinierten Konfiguration",
    "lang.ark.loadCarrier.loadCarrierModelNotExist": "",
    "lang.ark.fed.tow": "Traktion",
    "lang.authManage.web.common.input": "Bitte eingeben",
    "lang.ark.api.nodesNeedConfigureAction": "Interaktive Konfiguration, die dem Knoten entspricht, nicht gefunden",
    "lang.ark.fed.wholeTriggeWorkflow": "Allgemeiner Auslösen-Prozess",
    "lang.ark.plugin.pluginType.returnContainer.way.empty": "Leere Container senden",
    "lang.ark.fed.addExtendDevice": "Externe Geräte hinzufügen",
    "lang.ark.fed.viewMap": "Karte anzeigen",
    "lang.ark.fed.shelfArriveOrientation": "Die von Regale verlangte Ausrichtung",
    "lang.ark.sys.config.group.switch": "Systemschalter",
    "lang.ark.button.operation.command.systemRecover": "Systemwiederherstellung",
    "lang.ark.fed.implement": "Ausführen",
    "lang.ark.workStatus.create": "Erstellen",
    "lang.ark.fed.screen.hybridRobot.installEquipment": "Oberbaugerät",
    "lang.ark.fed.hybridRobot.hybridRobotType.doubleLift": "Doppelte Hebebühne",
    "lang.ark.fed.carryOutTheTask": "Aufgaben ausführen",
    "lang.ark.auth.userHaveLoginOtherPlace": "Der aktuelle Benutzer hat sich an anderer Stelle angemeldet!",
    "lang.ark.fed.waitLockSureCancel": "Warten auf Sperren. Abbruch bestätigt.",
    "lang.ark.fed.right": "Rechts",
    "lang.ark.fed.processControl": "Prozesssteuerung",
    "lang.ark.fed.driving": "Aktive Verteilung",
    "lang.ark.fed.goodsNum": "Materialnummer",
    "lang.ark.fed.twoDimensionalCodeFlowManagement": "QR-Code-Workflow-Management",
    "lang.ark.fed.waitCodeType": "Auf Anweisungen warten",
    "lang.ark.workflow.currentOperateIsHappening": "Nicht wiederholt bedienen!",
    "lang.ark.warehouse.workstationPointIsMuchForWave": "Die Workstation im Wellenauswahl-Modus kann nicht mehrere Punkte haben.",
    "lang.ark.fed.renderingFlowChart": "Flussdiagramm rendern",
    "lang.ark.fed.collect": "Sammlung",
    "lang.ark.trafficControl.enterPattern": "Eins nach dem anderen / kontinuierliches übergeben",
    "lang.ark.fed.currentStopPointStatus": "Aktueller Knotenstatus",
    "lang.ark.fed.screen.workflowInfo.robotTaskId": "",
    "lang.ark.fed.moreOperations": "Weitere Operationen",
    "lang.ark.fed.sureWantExecute": "Sind Sie sicher, dass Sie diesen Vorgang ausführen wollen?",
    "lang.ark.fed.wfTaskNum": "Nummer des Materialanforderungsblatts",
    "lang.ark.fed.taskManagementMsg0": "Aufgabenüberwachung: Ob auf der Seite der Aufgabenüberwachung die folgenden Schaltflächen angezeigt werden. Initialisierung der Schaltfläche „Löschen“ ist im Zustand „Ausgewählt“. Initialisierung der Schaltflächen „Abbrechen“ und „Zurück“ ist im Zustand „Nicht gewählt“.",
    "lang.ark.area.areaAlreadyLock": "Region gesperrt!",
    "lang.ark.interface.interfaceDesc.targetName": "Feldname des Vorschaltbereichs",
    "lang.ark.action.interface.locationFrom": "",
    "lang.ark.fed.arriveOrientation": "Regalorientierung",
    "lang.ark.fed.fieldInform": "Ortsbeschreibung",
    "lang.ark.fed.eraseNoise": "Rauschen löschen",
    "lang.ark.fed.recoveryToTargetSucceedStatus": "Zum Zielerfolgsstatus zurückkehren",
    "lang.ark.action.interface.extraParam1": "",
    "lang.ark.fed.orderPass": "Wer zuerst kommt, erhält zuerst",
    "lang.ark.action.interface.extraParam2": "",
    "lang.ark.action.interface.extraParam3": "",
    "lang.ark.action.interface.extraParam4": "",
    "lang.ark.action.interface.extraParam5": "",
    "lang.ark.fed.robotWillArrive": "Kürzeste Entfernung:{0}",
    "lang.ark.fed.demandForMaterials": '"Material, das auf Anfrage abgerufen wird"',
    "lang.ark.workflowTriggerStatus.enable": "Aktivieren",
    "lang.ark.auth.userHaveLoginCurrentPlace": "Der aktuelle Benutzer hat sich hier angemeldet!",
    "lang.ark.fed.workflowEncoding": "Prozesscodierung",
    "lang.ark.apiRobotTaskCode.waitPointExecutingTaskNotOnlyOne": "Die ausgeführte Aufgabe am Wartepunkt ist nicht eindeutig.",
    "lang.ark.workflow.criteria": "Bedingungen",
    "lang.authManage.web.common.makeSure": "OK",
    "lang.ark.trafficControl.enterPattern.singlePass": "Eins nach dem anderen",
    "lang.ark.fed.menu.taskExeRecord": "",
    "lang.ark.workflow.allType": "Alles",
    "lang.ark.fed.orderWaitSend": "Um verteilt zu werden",
    "lang.ark.fed.timingCharging": "Zeitgesteuertes Laden",
    "lang.ark.fed.uploadFileLimit3M": "Unterstützte Formate: mp3, wma, wav, amr. Keine einzelne Datei darf die Größenbegrenzung überschreiten.",
    "lang.ark.workflow.chooseStrategy.cancel": "Vorgang abbrechen ",
    "lang.ark.fed.technicalSupport": "Technischer Support",
    "lang.authManage.web.common.differentPassword": "Die zweimal eingegebenen Kennwörter sind inkonsistent!",
    "lang.ark.trafficControl.enterType.singleFactoryMultiEnter": "Mehrere Einträge für einen Hersteller",
    "lang.ark.fed.loopSetup": "Zykluseinstellung",
    "lang.auth.Audit.item0001": "",
    "lang.auth.Audit.item0003": "",
    "lang.ark.fed.isSureStartFlow": "Sind Sie sicher, den Workflow zu starten?",
    "lang.auth.Audit.item0002": "",
    "lang.auth.Audit.item0005": "",
    "lang.ark.action.interface.extraParam6": "",
    "lang.auth.Audit.item0004": "",
    "lang.ark.action.interface.extraParam7": "",
    "lang.ark.fed.pickingMethod": "Materialanforderungsmodus",
    "lang.auth.Audit.item0007": "",
    "lang.ark.action.interface.extraParam8": "",
    "lang.auth.Audit.item0006": "",
    "lang.ark.action.interface.extraParam9": "",
    "lang.auth.Audit.item0009": "",
    "lang.auth.Audit.item0008": "",
    "lang.ark.fed.createType": "Generierungsmodus",
    "lang.ark.workflowConfig.cellFunctions.stationCell": "STATION_CELL",
    "lang.ark.fed.componentInterface": "Komponentenbefehl",
    "lang.ark.noContainerAvailableAtCurrentPoint": "Derzeit kein Container verfügbar!",
    "lang.ark.fed.pleaseSelect": "Bitte auswählen",
    "lang.ark.fed.outerData": "Externe Daten",
    "lang.ark.fed.afterArrivingHere": "Hier angekommen",
    "lang.ark.fed.shelfAttribute.SSR": "",
    "lang.mwms.fed.innerFreezes": "Einfrieren im Lager",
    "lang.ark.warehouse.containerWorkingEdit": "Die Aufgabe oder der Speicher sind vorhanden oder verfügbar. Der Vorratsbehälter kann nicht bearbeitet werden.",
    "lang.ark.apiCommonCode.notMasterServer!": '"Der Server, der gerade angefordert wird, ist nicht der Hauptserver!"',
    "lang.ark.fed.currentRackInformation": "Aktuelle Regalinformationen",
    "lang.ark.fed.changePointPosition": "Punkt wechseln",
    "lang.ark.fed.conButtonLogExecuteTime": "Ausführungszeiten",
    "lang.ark.externalDevice.device_own_type_desc": "Wenn Roboterkomponenten und -ausrüstung ausgewählt sind, müssen Sie sie diese auf der Seite Verwaltung von Roboterkomponenten und -ausrüstung einpflegen!",
    "lang.ark.fed.binUsed": "Unterwegs (Container betriebt)",
    "lang.ark.warehouse.theMatrialPointExistsAny": "Wellenauswahl verteilt auf mehrere Produktionslinien! Bitte stornieren Sie redundante Wellenauswahl!",
    "lang.ark.fed.currentStatus": "Aktueller Status",
    "lang.ark.fed.workflowNode": "Prozessknoten",
    "lang.ark.fed.screen.workflowInfo.responseParamDetail": "",
    "lang.ark.fed.outer": "Externe Schnittstelle",
    "lang.ark.fed.waitPoint": "Wartepunkt",
    "lang.ark.apiCommonCode.robotRecoverFailed": "Roboterwiederherstellung fehlgeschlagen",
    "lang.ark.fed.menu.exceptionHandling": "",
    "lang.ark.workflow.arriveOrientation": "Die von Regal verlangte Ausrichtung",
    "lang.ark.fed.movingShelvesToWorkstations": "Die Regale auf die Arbeitsstationen verschieben ",
    "lang.ark.fed.teakDetail": "Aufgabendetails",
    "lang.ark.fed.shelfAttribute.PNAE": "",
    "lang.ark.fed.maximumSpeed": "Maximale Geschwindigkeit",
    "lang.ark.fed.english": "Deutsch",
    "lang.ark.fed.unconnectedNodeExist": "Vorgang fehlgeschlagen. Unverbundener Knotenpunkt existiert.",
    "lang.ark.fed.pickUpTheTask": "Aufgaben übernehmen",
    "lang.ark.fed.parameterGrouping": "Parametergruppierung",
    "lang.gles.baseData.baseGoodsPosition": "",
    "lang.mwms.fed.wcs": "Steuerung",
    "lang.ark.fed.targetPoint": "Zielpunkt",
    "lang.ark.fed.areaName": "Bereichsname",
    "lang.ark.workflow.task.status.node.undoing": "Zurückziehen",
    "lang.ark.fed.taskStage": "Aufgabenphase",
    "lang.ark.fed.default": "Standard",
    "lang.ark.workflow.task.status.moving": "Roboter unterwegs",
    "lang.ark.workflow.manulChoice": "Wählen Sie nachfolgende Aufgaben manuell aus",
    "lang.ark.fed.automatic": "Automatisierung",
    "lang.ark.deliverOrder.positiveSequence": "Positive Produktionslinienfolge",
    "lang.ark.apiRobotTaskCode.robotTaskIdNotEmpty": "Die Roboter-Task-ID darf nicht null sein",
    "lang.ark.fed.screen.systemConfig.commonGroup": "Systemkonfiguration",
    "lang.ark.fed.openAll": "Alle aktivieren",
    "lang.ark.fed.condition": "Zustand",
    "lang.ark.fed.pullDownTheCargoPosition": "Normal/gesperrt/belegt von Aufgabe",
    "lang.ark.fed.speed": "Geschwindigkeit",
    "lang.ark.fed.oneway": "Einseitig",
    "lang.ark.fed.passingPoint": "Durchfahren-Punkt",
    "lang.ark.workflow.autoReleaseFactoryNullError": '"Der Hersteller unter automatischer Roboterfreigabe ist Null, bitte geben Sie den Hersteller ein."',
    "lang.ark.fed.networkTimeout": "Netzwerk-Timeout",
    "lang.ark.fed.backgroundMapEditing": "Hintergrundbildbearbeitung",
    "lang.ark.fed.arrivelExternalInteraction": "Externe Interaktion nach der Ankunft",
    "lang.ark.fed.isStopAllQueue": "Möchten Sie die Warteschlange für alle deaktivieren?",
    "lang.ark.robot.classfy.forklift": "Gabelstapler",
    "lang.ark.workflow.queue.noAvailableShelf": "Keine Regalpunkte verfügbar ",
    "lang.ark.warehouse.shelfDifferent": "Das aktuelle Gestell stimmt nicht mit dem Gestell bei Materialanforderung überein.",
    "lang.ark.fed.theWorkstationnodeidDidNotSelectAStopPoint": "Die Arbeitstation {nodeId} hat keinen Dockingpunkt ausgewählt.",
    "lang.ark.button.operation.command.removeContainer": "Austritt des Behälters",
    "lang.ark.fed.screen.flowNodeConfig.checkMsg.rule": "",
    "lang.ark.fed.takeAway": "Zurücknehmen",
    "lang.ark.interface.apiAddContainer": "Container-Eingang",
    "lang.ark.interface.apiPriorityAdjust": "Priorität der Workflow-Instanz angepasst",
    "lang.ark.fed.pleaseSelectTheAlarmLightPortNumber": "Bitte die Portnummer der Alarmleuchte auswählen",
    "lang.ark.apiStationCode.stationNotExists": "Arbeitsstation existiert nicht!",
    "lang.ark.fed.noPointToDeleteWasSelected": "Es werden keine zu löschenden Punkte ausgewählt",
    "lang.mwms.monitorRobotMsg.config": "Ausnahme der Prozesskonfigurationen. Bitte überprüfen Sie die Prozesskonfigurationen.",
    "lang.ark.fed.offsetInstructionMustSpecify": "Bei Offset-Anweisung muss eine spezielle Komponente angegeben werden",
    "lang.ark.warehouse.waveStatusCannotbyCancel": "Fahren Sie nicht fort mit abgebrochener Wellenauswahl.",
    "lang.ark.interface.businessCode": "Reaktionscode",
    "lang.ark.fed.excel.cellNotExist": "",
    "lang.ark.fed.containerBinFree": "Normal",
    "lang.ark.fed.hoursLater": "Nach Stunden",
    "lang.ark.fed.taskRunDetails": "Aufgabenausführungsdetails",
    "lang.ark.fed.sendMaterial": "Materialeinspeiseziel",
    "lang.ark.warehouse.taskHasExecute": '"Aufgabe ausgeführt, nicht wiederholen."',
    "lang.ark.fed.overAlarmType": "Alarmtyp",
    "lang.gles.StockInStore": "",
    "lang.ark.fed.switchToSuccess": "Erfolgreich zu {val} gewechselt",
    "lang.authManage.web.common.disabled": "Deaktivieren",
    "lang.ark.workflowTriggerMonitorStatus.executing": "Wird ausgeführt",
    "lang.ark.action.interface.boolean": "",
    "lang.ark.fed.flowCategory": "Prozessklassifizierung",
    "lang.ark.warehouse.deliveryStationLine": "Verteilerstation",
    "lang.ark.action.interface.taskCode": "",
    "lang.mwms.monitorRobotMsg21100": "Komponente Erhalt-Ausnahme",
    "lang.ark.fed.configCustomParams": "",
    "lang.ark.workflow.targetNode": "Zielpunkt",
    "lang.ark.fed.back": "Hinter",
    "lang.ark.workflow.task.status.completed": "Fertig",
    "lang.ark.fed.load": "Laden",
    "lang.ark.workflow.condition.lessThan": "Weniger als",
    "lang.ark.button.operation.command.start": "Start",
    "lang.ark.fed.multiplefactoryMultipleEntrances": "Einzeleingang für mehrere Hersteller",
    "lang.ark.fed.pleaseSelectTheParentNode": "Bitte den übergeordneten Knoten wählen!",
    "lang.ark.fed.noInstructUnderTheAction": "",
    "lang.ark.fed.interfaceInteraction": "Schnittstelleninteraktion",
    "lang.gles.materialClassify": "",
    "lang.ark.workflow.task.status.wait": "Warten",
    "lang.ark.workflow.paramValueCode.extraParam8": "extraParam8",
    "lang.mwms.monitorRobotMsg21111": "Position verloren",
    "lang.ark.workflow.paramValueCode.extraParam7": "extraParam7",
    "lang.mwms.monitorRobotMsg21110": "Gewichtsüberlastung oder unausgeglichene Beladung",
    "lang.auth.UserAPI.item1271": "Aktivieren",
    "lang.ark.fed.threeLight": "Dreifarbleuchte",
    "lang.ark.fed.locationOfRobotsWaitingToReceiveNewTasks": "Eine Position, die der Roboter andocken kann, während er auf eine neue Aufgabe wartet",
    "lang.ark.workflow.paramValueCode.extraParam9": "extraParam9",
    "lang.auth.UserAPI.item1270": "Deaktivieren",
    "lang.ark.workflow.paramValueCode.extraParam4": "extraParam4",
    "lang.ark.workflow.paramValueCode.extraParam3": "extraParam3",
    "lang.ark.workflow.paramValueCode.extraParam6": "extraParam6",
    "lang.ark.workflow.paramValueCode.extraParam5": "extraParam5",
    "lang.ark.workflow.paramValueCode.extraParam2": "extraParam2",
    "lang.ark.workflow.paramValueCode.extraParam1": "extraParam1",
    "lang.mwms.monitorRobotMsg21109": "Batterie tot",
    "lang.ark.workflow.wareHouseSupportBusiness": "Business",
    "lang.ark.task.log.export.title.endNode.name": "Name des Terminalpunkts",
    "lang.mwms.monitorRobotMsg21104": "Daten der Fischaugenkamera verloren",
    "lang.mwms.monitorRobotMsg21103": "Daten der Tiefenkamera verloren",
    "lang.ark.fed.locking": "Verriegeln",
    "lang.mwms.monitorRobotMsg21102": "Komponentenfehler",
    "lang.ark.auth.userHaveLoginOtherStation": "Der aktuelle Benutzer {0} hat sich an der Arbeitsstation Nr. {1} angemeldet!",
    "lang.mwms.monitorRobotMsg21101": "Komponenten-Kommunikation unterbrochen",
    "lang.ark.workflow.notFirstStation": '"Im aktiven Verteilungsmodus kann die Arbeitsstation, die sich nicht am ersten Knoten befindet, den Prozess nicht initiieren."',
    "lang.ark.fed.makeADetour": "Bypass",
    "lang.mwms.monitorRobotMsg21108": "Drücken zur Freigabe der Bandbremsen",
    "lang.ark.fed.logicAnd": "+ und-Operator (&)",
    "lang.mwms.monitorRobotMsg21107": "STO ausgelöst",
    "lang.mwms.monitorRobotMsg21106": "Antriebsdaten fehlen oder Gerätefehler",
    "lang.ark.fed.receiveGoodsNumLessThanRemain": "Regalfach {0} unterstützt einen max. Eingang von {1}.",
    "lang.mwms.monitorRobotMsg21105": "Netzwerkfehler",
    "lang.ark.workflow.workflowtypeNameOrCodeRepeat": "Prozessklassifizierungsname oder -code bereits vorhanden",
    "lang.ark.fed.mesInterfaceError": "Ausnahme {0} zurückgegeben von MES-API.",
    "lang.mwms.fed.viewSet": "Seitenkonfiguration",
    "lang.mwms.fed.inWarehouse": "Lagereingang-Tagesbericht",
    "lang.ark.hitStrategy.stack": "Last-in First-out",
    "lang.ark.fed.detail": "Details",
    "lang.ark.fed.closeAllTabs": "Alle schließen",
    "lang.ark.workflow.action.command.robot.FBShift": "Vorderer und hinterer Versatz",
    "lang.ark.fed.morePicking": "Materialanforderung für mehrere Roboter",
    "lang.ark.fed.column": "Spalte",
    "lang.ark.fed.orderSourceNum": "Ursprüngliche Bestellnummer",
    "lang.ark.fed.noRobotsHaveBeenAssignedYetPleaseClickToAdd": "Es wurden keine Roboter verteilt, bitte zum Hinzufügen klicken!",
    "lang.ark.fed.ruleConfig": "Regelkonfiguration",
    "geekplus.moving.uic.elTableWrapperVue2.actionBtn.confirmPopup.btnCancel": "Löschen",
    "lang.ark.fed.prompt": "Tipp",
    "lang.mwms.fed.supplier": "Lieferantenmanagement",
    "lang.ark.warehouse.estimateUseTimeUnit.minute": "Minute",
    "lang.ark.fed.exportFile": "Wird heruntergeladen",
    "lang.ark.fed.selectRobot": "Einen Roboter auswählen",
    "lang.ark.fed.workflowId": "Prozessnummer:",
    "lang.ark.warehouse.wave": "Wellenauswahl",
    "lang.ark.record.task.over": "",
    "lang.ark.stationCodeExist": "Der Arbeitsstation-Code kann nicht wiederholt werden",
    "lang.ark.fed.arriveEndCellCodeTime": "Ankunftszeit an Knoten",
    "lang.ark.workflow.template.validate.equipmentNodeNotFoundData": "Geräteknoten findet entsprechende Daten nicht. Prüfen Sie die Gerätedaten.",
    "lang.ark.fed.occupied": '"Gibt an, ob das Gerät belegt ist!"',
    "lang.ark.fed.releaseFull": "Freigeben",
    "lang.ark.fed.numberOfButtons": "Anzahl der Tasten",
    "lang.ark.fed.screen.hybridRobot.pointInfo": "Punktdaten",
    "lang.ark.workflow.areaNotHaveIdlePoint": "",
    "lang.ark.base.license.exceptionForLicenseValidating": "Zertifikatsüberprüfungsstörung!",
    "lang.ark.fed.cancelWait": "Warten abgebrochen",
    "lang.ark.fed.selectAll": "Alles wählen",
    "lang.ark.fed.missionEndpoint": "Aufgabenendpunkt",
    "lang.ark.trafficControl.stopFunction": "Not-Aus-Bereichs-Funktion",
    "lang.ark.fed.pleaseSaveEditTable": "Bitte überprüfen Sie, ob die aktuelle Liste Null oder nicht gespeicherte Daten enthält.",
    "lang.ark.fed.menu.heartbeat": "Pulsschlagverwaltung",
    "lang.ark.fed.facilityType": "Typ der Anlage:",
    "lang.ark.fed.screen.area.containGroup": "Gruppierung einschließen",
    "lang.mwms.fed.operate": "Kartenbetrieb",
    "lang.ark.fed.dashboardSetting": "Anzeigentafeleinstellung",
    "lang.ark.taskRecord.param.notblank": "",
    "lang.ark.operation.workflow.pauseWorkflow": "",
    "lang.ark.workflow.paramValueCode.export": "",
    "lang.ark.fed.cancellationProcess": "Prozess abbrechen",
    "lang.ark.fed.conButtonLogSocketPort": "Portnummern",
    "lang.ark.pda.function.transport.task": "Bewegungsaufgabe",
    "lang.mwms.fed.package": "Paket",
    "lang.ark.apiCommonCode.systemStopFailed": "System-Notaus fehlgeschlagen!",
    "lang.ark.interface.interfaceDesc.phaseTypeDesc": "Beschreibung: Format des Parameterwerts, der an den Vorschaltbereich zurückgegeben wird, wenn der Feldwert ein Aufzählungswert ist. Das Feld „taskPhase“ hat bspw. mehrere nummerierte Wertzustände. Der Vorschaltbereich sollte entweder die Werte 10, 20 und 30 zurückgeben, die verschiedenen Zuständen entsprechen, oder den Text „English“, der den Zuständen CREATE, EXECUTION und COMPLETED entspricht. Diese Einstellung gilt bei allen nummerierten Wertfeldern dieser Schnittstelle.",
    "lang.ark.trafficControl.queueFunction": "Warteschlangenbereichsfunktion",
    "lang.ark.fed.appointMentLock": "Termin gesperrt",
    "lang.ark.fed.executeDetail": "Ausführungsdetails",
    "lang.ark.fed.twoway": "Beiderseitig",
    "lang.ark.fed.stageOfCompletion": "Fertigstellung",
    "lang.ark.fed.ForkliftTray": "Gabelstaplerpalette",
    "lang.ark.fed.jackUpLayDown": "Heber oben/unten",
    "lang.ark.fed.screen.flowTemplate.specialNode": "Sonderpunkt",
    "lang.ark.workflow.notSupportAgvClass": "",
    "lang.ark.interface.interfaceDesc.detail": "Schnittstellendaten",
    "lang.ark.interface.callbackAddress": "Rückrufadresse",
    "lang.ark.fed.flowCreate": "Prozesserstellung",
    "lang.ark.fed.editMap": "Kartenbearbeitung",
    "lang.ark.fed.addProcess": "Prozess hinzufügen",
    "lang.mwms.fed.userManage": "Benutzerverwaltung",
    "lang.ark.fed.zoom": "Zoom",
    "lang.ark.workflow.template.validate.templateCannotSelectBranchOrderNode": "Die Dynamikvorlage kann die Filialknoten nicht anhand der Verkabelungsmethode auswählen",
    "lang.ark.fed.className": "Kategoriename",
    "lang.ark.executedNum": "Ausführungszeiten",
    "lang.ark.waveTaskStatus.cancel": "Stornieren",
    "lang.gles.receipt.tallyList.tallyList": "",
    "lang.ark.fed.loadGoods": "Zuführen",
    "lang.ark.warehouse.shelfNotFree": "Derzeit kein leeres Gestellt",
    "lang.ark.fed.triggerOtherSettings": "Andere Einstellungen auslösen",
    "lang.ark.fed.robotLessQueue": "Es gibt eine Aufgabenwarteschlange, wenn der Roboter nicht ausreicht",
    "lang.ark.warehouse.hasSameProductionLineCode": "Der gleiche Produktionsliniencode ist bereits vorhanden.",
    "lang.gles.logisticsConfig.workTemplate": "",
    "lang.ark.fed.goodsSend": "Verteilen",
    "lang.ark.fed.userAndRole": "Benutzer/Rolle",
    "lang.authManage.web.others.addNewIp": "IP und Port hinzufügen",
    "lang.authManage.fed.effectiveDate": "Startdatum",
    "lang.ark.fed.robotManagement": "Robotermanagement",
    "lang.ark.fed.clearLock": "Entsperren",
    "lang.ark.fed.typeMustBeDockPointWorkstationRackPointArea": "Typen müssen Dockingpunkt, Arbeitsstationen, Regalpunkte, Bereiche sein",
    "lang.ark.task.log.export.title.start.time": "Startzeit",
    "lang.ark.fed.taskLog": "Protokoll",
    "lang.ark.fed.menu.hybridRobot": "Cobot-Konfiguration",
    "lang.ark.workflow.task.status.ready": "Bereit",
    "lang.ark.fed.east": "Osten",
    "lang.mwms.monitorRobotMsg91001": "Stopp-Taste getrennt",
    "lang.ark.fed.confirmCharging": "Sind Sie sicher, aufzuladen?",
    "lang.ark.fed.homeViewUpload": "Startseite Upload",
    "lang.mwms.monitorRobotMsg91000": "Notfall-Stopp-Knopf ausgelöst",
    "lang.ark.interface.interfaceType": "Oberflächentyp",
    "lang.ark.fed.loadingPoint": "Einspeisepunkt",
    "lang.ark.fed.screen.flowNodeConfig.executeByCondition": "",
    "lang.ark.fed.serialLessMount": "Die Materialmenge darf 0 nicht unterschreiten.",
    "lang.ark.fed.orderPassPriority": "Wer zuerst kommt, erhält zuerst (Priorität zuerst)",
    "lang.ark.fed.nomsgIsAvailableDoYouWantToContinueGeneratingTasks": "Keine {msg} verfügbar. Möchten Sie weiterhin Aufgaben generieren?",
    "lang.ark.fed.callStation": '"Arbeitsstation, die das Material aufruft"',
    "lang.ark.fed.purpose": "Verwendungszweck",
    "lang.ark.fed.returnDistance": "Rückkehr",
    "lang.ark.fed.everyMonth": "{0} jedes Monats",
    "lang.ark.interface.requestSource": "Körper anfordern",
    "lang.ark.fed.taskBoardWaringSetting": "Kanban-Alarmbenachrichtigung",
    "lang.mwms.fed.skuOutInConfig": "Konfiguration von Lagereingang/Lagerausgang des Materals",
    "lang.ark.fed.pleaseEnter": "Bitte eingeben",
    "lang.mwms.fed.printSet": "Druckeinstellungen",
    "lang.ark.workflow.recycleTypeNoSupportManual": "",
    "lang.ark.fed.revokeButton": "Schaltfläche „Rückgängig“",
    "lang.ark.action.interface.paramName": "Parametername",
    "lang.ark.fed.generationTime": "Generationszeit",
    "lang.ark.fed.triggerTiming": "Auslösen-Zeitpunkt",
    "lang.ark.action.interface.assignType": "Zuweisungsmodus",
    "lang.ark.logType.waitPointTaskLog": "Anfrageprotokoll des Haltepunkts",
    "lang.ark.hand.push": "Manuell drücken auf",
    "lang.ark.singleCellStation.canNotDelete": "Einzelpunktstation kann nicht gelöscht werden",
    "lang.ark.fed.pleaseSelectRobot": "Bitte Roboter auswählen",
    "lang.ark.fed.selected.cellCode": '"Punktposition, Bereich, Gruppierung, Behältercode wählen"',
    "lang.ark.loadCarrier.loadCarrierParamsErr": "",
    "lang.mwms.rf.outbound": "Lagerausgang",
    "lang.ark.fed.batteryCurrent": "Batteriestrom",
    "lang.ark.workflowConfig.nodeActionDoNotExists": "Interaktive Aktion nicht konfiguriert",
    "lang.ark.fed.includePoints": "Enthaltene Punkte",
    "lang.ark.fed.isEnabledTrigger": "Möchten Sie diesen Auslöser aktivieren?",
    "lang.ark.fed.narrow": "Hineinzoomen",
    "lang.ark.fed.allowLiftUp": "{0} nur ein Aufbocken-Befehl erlaubt.",
    "lang.ark.fed.abnormalComplete": "Abnormal abgeschlossen",
    "lang.ark.workflow.wareHouseWorkflowType": "Prozessart",
    "lang.ark.fed.orderReAssign": "Umverteilen",
    "lang.ark.fed.thereIsNoWorkflowAtThisDockPointPosition": "Derzeit kein Workflow",
    "lang.ark.mechanical.arm.place": "Durch mechanischen Arm platziert",
    "lang.authManage.fed.screen.login.pwdExpireTipTitle": "",
    "lang.ark.fed.abnormalCancel": "Abbruchausnahme",
    "lang.ark.fed.oneClickExecution": "Oneclickgo",
    "lang.ark.fed.robotCode": "Robotercodierung",
    "lang.ark.workflowCode.exists": "Die Prozesscodierung kann nicht wiederholt werden",
    "lang.mwms.fed.stationControl": "Arbeitsstationsüberwachung",
    "lang.ark.station.rollerStation": "Rollstation",
    "lang.ark.fed.extraParam18": "extraParam18",
    "lang.ark.fed.extraParam17": "extraParam17",
    "lang.ark.fed.extraParam19": "extraParam19",
    "lang.ark.hitStrategy.default": "Standardstrategie",
    "lang.ark.fed.extraParam20": "extraParam20",
    "lang.ark.workflow.invalidWorkflowConfig": "Ungültige Workflow-Konfiguration",
    "lang.ark.fed.deleteWaveRow": "Sind Sie sicher, dass Sie die Strategie löschen wollen?",
    "lang.ark.fed.notAllow": "Nicht ausgewählt",
    "lang.ark.taskStatusCannotOperate": "Die Aufgabe befindet sich im Status {0} und es ist keine Bedienung zulässig!",
    "lang.ark.fed.unloadOrderDesc": "Voller Auftrag ablegen: Standardmäßig erfordern Arbeitsstationsablagen, dass alle Materialien aus dem Regalfach entfernt werden. Es gibt keine strengen Anforderungen an das Material und den Standort der Regalfächer.",
    "lang.ark.plugin.pluginType.returnContainer.way.full": "Volle Container senden",
    "lang.ark.fed.extraParam10": "extraParam10",
    "lang.ark.fed.menu.systemConfiguration": "Systemparameter",
    "lang.ark.fed.extraParam12": "extraParam12",
    "lang.ark.fed.extraParam11": "extraParam11",
    "lang.ark.fed.extraParam14": "extraParam14",
    "lang.ark.record.createTask": "",
    "lang.ark.fed.extraParam13": "extraParam13",
    "lang.ark.fed.taskNumber": "Aufgabenummer",
    "lang.ark.fed.extraParam16": "extraParam16",
    "lang.ark.fed.extraParam15": "extraParam15",
    "lang.authManage.fed.effectiveDays": "Trageanzahl des Gültigkeitszeitraum",
    "lang.ark.fed.screen.area.tipForEmptyGroup": "Nicht speicherfähig, da Gruppierung nicht angefügt",
    "lang.gles.receipt.warehousingExternalOrder": "",
    "lang.ark.fed.deleteFlow": "Prozesse löschen",
    "lang.ark.fed.numberOfCategories": "Anzahl der Typen",
    "lang.ark.fed.screen.LoginLog.realName": "",
    "lang.ark.fed.mediaPlay": "Stimmwiedergabe",
    "lang.ark.fed.nearTheWorkstationRobotsCanBeParkedAndWaitedForAPosition": "Eine Position in der Nähe einer Arbeitsstation, an der Roboter andocken und auf die Bedienung des Personals warten können",
    "lang.ark.apiContainerCode.containerCategoryNotExists": "Container-Typ existiert nicht",
    "lang.authManage.fed.screen.auth.cardNo": "",
    "lang.ark.areaCode.not.exist": "Gebietscodes existieren weder ganz noch teilweise.",
    "lang.ark.fed.processProcessNew": "Prozess",
    "lang.ark.workflow.action.command.robot.goTurnOfSide": "Drehung nach Ebene",
    "lang.ark.task.log.export.title.task.number": "Aufgabenummer",
    "lang.ark.fed.taskNumberNew": "Aufgabenummer",
    "lang.ark.shelfType.referenceByShelf": "Der Container-Typ ist einem Container zugeordnet",
    "lang.ark.fed.updateLocation": "Position aktualisieren",
    "lang.gles.stockInStore.factoryPositionStock": "",
    "lang.ark.fed.executiveInstruction": "Ausführungsanweisungen",
    "lang.ark.fed.baseInfo": "Grundinformation",
    "lang.ark.fed.endPointName": "Name des Terminalpunkts",
    "lang.ark.interface.requestId": "Nachrichten-Nr.",
    "lang.ark.fed.newTask": "Eine Aufgabe erstellen",
    "lang.ark.fed.areYouSureToDeleteThisNodeWorkflow": "Möchten Sie diesen Knoten {nodeId} und alle seine untergeordneten Knoten löschen?",
    "lang.ark.fed.sending": "Verteilen",
    "lang.authManage.web.common.roleDelSuc": "Die Rolle wurde erfolgreich gelöscht!",
    "lang.ark.fed.finish": "Fertig",
    "lang.ark.apiContainerCong.modifyAngleFail": "Modifizieren des Behälterwinkels fehlgeschlagen.",
    "lang.ark.fed.pinking": "Fütterung",
    "lang.ark.workflow.noAvailableAreaOrShelfPoint": "Es wurden keine Empfangsbereiche oder Regalpunkte gefunden",
    "lang.ark.robot.unbind.device": "Der Roboter ist nicht an eine obere Struktur gebunden",
    "lang.ark.fed.menu.containerEditor": "Container-Bearbeitung",
    "lang.gles.planTask": "",
    "lang.ark.containerNotExists": "Container existiert nicht!",
    "lang.ark.warehouse.TriggerCellCodeCanNotFindUnWaveOrder": "Kein Dokument verfügbar und Wellengruppierung fehlgeschlagen",
    "lang.ark.loadCarrier.loadCarrierModelIdIsEmpty": "",
    "lang.ark.workflow.lastTaskArrivedSimple": "Vorheriger Schritt",
    "lang.ark.container.inUsing": "Container {} wird verwendet. Bitte versuchen Sie es später noch einmal",
    "lang.ark.dynamicTemplate.previousNode": "Vorheriger Knoten",
    "lang.ark.workflow.wareHouseWorkflowTemplate": "Prozessvorlage",
    "lang.ark.fed.priorityStickTop": "Sticky-Priorität",
    "lang.ark.fed.nodeDeviceInfo": "",
    "lang.ark.common.importExcelFile": "Vorlage importieren",
    "lang.mwms.fed.accountManage": "Kontoverwaltung",
    "lang.ark.fed.containerAmount": "Anzahl der Container",
    "lang.ark.workflow.workflowInstanceNotExists": "Die Workflow-Instanz ist nicht vorhanden",
    "lang.ark.workflow.action.command.paramSourceType.clientAssign": "Gesandt vom Vorschaltbereich",
    "lang.ark.fed.stopPointIdExistent": "Die aktuelle Position ist bereits vorhanden. Versuchen sie eine andere.",
    "lang.ark.fed.specialAreaName": "Sonderbereichsname",
    "lang.ark.fed.materialNum": "Materialmenge",
    "lang.ark.fed.removeRack": "Regale entfernen",
    "lang.ark.fed.menu.templateManage": "Externe Aufgabe importieren",
    "lang.ark.fed.noWorkflowNodePleaseReedit": ", keine Prozessknoten, bitte erneut bearbeiten",
    "lang.ark.fed.preStep": "Vorheriger Schritt",
    "lang.ark.fed.height": "Höhe",
    "lang.ark.existsInUsedContainer": "Bereits eingegebener Container existiert",
    "lang.ark.warehouse.buttonExistsButNoMatch": "Die Schaltfläche wurde mit Workflow-Initiierung / Knotensteuerung versehen. Die Punkt- oder Containernummer muss konsistent gehalten werden.",
    "lang.ark.fed.menu.areaEditController": "Bereich",
    "lang.ark.fed.batchImport": "",
    "lang.ark.canNotSubmitRepeat": "Klicken Sie nicht wiederholt",
    "lang.ark.fed.parentWorkflowContainThis": "Prozess {0} enthält diesen Prozess. Es wurde in einen ungewöhnlichen Zustand überführt!",
    "lang.ark.fed.workstationName": "Name der Arbeitsstation",
    "lang.ark.fed.cellCodeLock": "Punktschloss",
    "lang.ark.workflow.manulChoiceSimple": "Manuelle Auswahl",
    "lang.ark.fed.interfaceAccessType": "",
    "lang.ark.shelf.addShelfFailed": "Container konnte nicht hinzugefügt werden",
    "lang.ark.workflow.mutiBeginOrEnd": "Es gibt viele Start- oder Endknoten",
    "lang.ark.fed.pleaseSelectOrder": "Bitte wählen Sie Dokumente aus",
    "lang.ark.fed.inTheTask": "Aufgabe in Bearbeitung",
    "lang.ark.fed.endOfProcess": "Der Prozesses ist beendet",
    "lang.ark.fed.drawingAMap": "Eine Karte zeichnen",
    "lang.ark.waveTriggerCondition.all": "Alles",
    "lang.mwms.fed.replenishmentWorkCreate": "Nachschub wird generiert",
    "lang.mwms.fed.pickWork": "Entnahmearbeiten",
    "lang.ark.interface.apiAreaList": "Bereichsabfrage",
    "lang.gles.systemManage.systemManage": "",
    "lang.ark.fed.loading": "Das System wird geladen ...",
    "lang.gles.baseData.workshop": "",
    "lang.ark.fed.updateTimeInterval": "Aktualisierungsintervall",
    "lang.ark.button.code.already.exist": "Diese Controller-Taste ist bereits vorhanden und kann nicht wiederholt hinzugefügt werden",
    "lang.ark.workflow.area.occupiedTime": "Obergrenze für ununterbrochene Belegungszeit",
    "lang.ark.fed.errorBins": "Vorratsbehälterfehler, bitte bestätigen Sie den Barcode.",
    "lang.ark.fed.save": "Speichern",
    "lang.ark.workflow.action.command.paramSourceType.manualConfiguration": "Manuelle Konfiguration",
    "lang.ark.fed.menu.robotManagement": "Robotermanagement",
    "lang.ark.fed.facility": "Einrichtung:",
    "lang.ark.trafficControl.enterType.multiFactorySingleEnter": "Einzeleingang für mehrere Hersteller",
    "lang.mwms.fed.strategyOrderAllocate": "Bestell-Hit-Strategie",
    "lang.ark.fed.nodeName": "Knotenname",
    "lang.ark.fed.openMoreGoodsInfo": "Weitere Materialinformationen anzeigen",
    "lang.ark.fed.codeValue": "Codewert",
    "lang.ark.api.template.startNodeNotExist": "Vorlagenstartpunkt existiert nicht.",
    "lang.ark.fed.typeName": "Typbezeichnung",
    "lang.ark.fed.processMode": "Prozessmodus",
    "lang.ark.fed.common.placeholder.input": "Geben Sie etwas ein",
    "lang.ark.apiRobotTaskCode.robotTaskNotExists": "Die Roboteraufgabe existiert nicht",
    "lang.ark.fed.trigger": "Auslösung",
    "lang.ark.fed.menu.editMap": "",
    "lang.authManage.web.common.loading": "Laden ...",
    "lang.ark.apiStationCode.stationStopPointIsEmpty": "Die Informationen zum Andockort in der Arbeitsstation sind Null",
    "lang.ark.fed.ruleConfiguration": "Regelkonfiguration",
    "lang.ark.fed.synchronizeAllRacks": "Alle Regale synchronisieren",
    "lang.ark.fed.singleCellStation": "Einzelpunktstation",
    "lang.ark.apiContainerCode.containerCategoryNotMatch": "containerCategory: {0} stimmt nicht mit der Containerkategorie im GMS überein.",
    "lang.ark.sameQueuePriorityNeedSameAndallowQueueRobotNumNeedSame": "Der gleiche Warteschlangenpunkt erfordert die gleiche Warteschlangenmenge und Priorität",
    "lang.mwms.fed.wave": "Wellenentnahmesmanagement",
    "lang.ark.fed.triggerLocking": "Externe Auslösung gesperrt",
    "lang.ark.fed.robotDashboard": "Roboter-Anzeigentafel",
    "lang.ark.fed.west": "Westen",
    "lang.ark.workflow.shelfExistsAndOccupied": "Derzeit wird ein Regal verwendet",
    "geekplus.moving.uic.elTableWrapperVue2.actionBtn.confirmPopup.btnConfirm": "OK",
    "lang.ark.fed.lingdan": "Erhalt über Zeitplan",
    "lang.ark.fed.dataStatus": "Datenstatus",
    "lang.ark.archiveType.interfaceRecord": "Schnittstellenprotokoll",
    "lang.ark.fed.havingSeparateBusinessAttributesAPhysicalOrLogical": "Eine physisch oder logisch partitionierte Position mit abhängigen Geschäftseigenschaften",
    "lang.ark.fed.robotStat": "Roboterstatus",
    "lang.ark.fed.typeMustBeDockPointWorkstationRackPointArea1": "Der Typ muss ein(e) Punkt, Arbeitsstation, Regalpunkt, Bereich sein ",
    "lang.ark.fed.shelfFlow": "Lademodus",
    "lang.ark.fed.areYouSureYouWantToRedrawTheMap": "Sind Sie sicher, die Karte neu zu zeichnen?",
    "lang.ark.warehouse.goods": "Materialzufuhr",
    "lang.ark.interface.interfaceDesc.phaseType": "Rückrufformat des nummerierten Wertfeldes",
    "lang.ark.fed.productModel": "Modellnummer",
    "lang.gles.receipt.tallyList.externalTallyList": "",
    "lang.mwms.fed.monitorAll": "Gesamte Lagerüberwachung",
    "lang.ark.fed.manualConfig": "Manuelle Konfiguration",
    "lang.ark.fed.redistributionSure": "Sind Sie sicher, dass Sie einen neu hinzugefügten, aber gleichen Dokumentdatensatz für die Weiterverteilung verwenden wollen?",
    "lang.ark.workflowAction.default": "Standard",
    "lang.ark.workflow.workflowNodeConfigNotExists": "Die Konfiguration des Workflow-Knotens ist nicht vorhanden",
    "lang.ark.lang.ark.controlNodeType.point": "Punkt",
    "lang.ark.workflow.subflowCancellationNotAllowed": "",
    "lang.mwms.fed.outManage": "Lagerausgang-Management",
    "lang.ark.workflowTrigger.logType.operation": "Betriebsprotokoll",
    "lang.ark.fed.recoveryToInitialStatus": "Zum Ausgangszustand zurückkehren",
    "lang.ark.fed.callStationTaskDetails": "Details zum Aufgabenblatt für Materialaufrufe",
    "lang.ark.fed.containerEntry": "Eingang",
    "lang.ark.warehouse.stationLineDeliveryPriority": "Verteilungspriorität",
    "lang.ark.fed.pleaseSelectFlow": "Bitte einen Workflow auswählen!",
    "lang.ark.workflow.manul": "Manuelle Auslösung nachfolgender Aufgaben",
    "lang.ark.fed.estimatedTime": "Ankunft; erwartete Zeit",
    "lang.ark.fed.childFlow": "Unterprozess",
    "lang.ark.fed.parameterType": "Parametertyp",
    "lang.ark.fed.addRackPoints": "Regalpunkte hinzufügen",
    "lang.ark.fed.controlArea": "Kontrollbereich",
    "lang.mwms.fed.inventoryManage": "Bestandsverwaltung",
    "lang.ark.rollerStation.canNotEdit": "Roller-Station kann nicht editiert werden",
    "lang.ark.workflow.extendRobotTrue": "Ja",
    "lang.ark.workflow.workflowDropNotSupport": "",
    "lang.ark.fed.setMiddlePoint": "Als Durchfahren-Punkt festlegen",
    "lang.ark.fed.lockingSure": "Regalfach-Entsperrung bestätigen",
    "lang.ark.fed.screen.flowNodeConfig.judgingByTask": "",
    "lang.authManage.fed.preAlertDays": "Frühwarntage",
    "lang.ark.workflowConfig.cellFunctions.rest": "Dock",
    "lang.ark.workflow.workflowNotExists": "Stornierung fehlgeschlagen. Prozess nicht vorhanden",
    "lang.ark.curNodeExitsShelf": 'Derzeit befinden sich hier Regale und der Modus "Kein Regal" kann nicht verwendet werden',
    "lang.ark.action.interface.retry": "Erneut versuchen",
    "lang.ark.loadCarrier.loadCarrierCodeGenerateErr": "",
    "lang.mwms.fed.exception": "Störungbehandlung",
    "lang.ark.fed.signalDisplay": "Signalanzeige",
    "lang.ark.fed.set": "Einstellungen",
    "lang.ark.warehouse.materialPreparePointType": "Einspeisepunkt-Notentyp",
    "lang.ark.fed.pleaseChooseRack": "Bitte Regal auswählen",
    "lang.ark.fed.start": "Start",
    "lang.ark.shelfTypeRefWorkflowNodeAction": "Fehler beim Löschen. Wird von der interaktiven Konfiguration verwendet! Name der Interaktionskonfiguration: {0}",
    "lang.mwms.fed.allocationRule": "Entnahmeregel",
    "lang.ark.fed.businessModel": "Geschäftsmodus",
    "lang.ark.fed.operationInstruction": "Bedienungsanleitung",
    "lang.ark.fed.wirelessCallModule": "Funkrufmodul ",
    "lang.ark.base.license.nolicense": "Bitte importieren Sie das Zertifikat!",
    "lang.gles.receipt.upAndDownMaterialOrder": "",
    "lang.gles.baseData.baseContainerArchives": "",
    "lang.ark.fed.forkLiftSetCommponent": "Eine Komponentenbewegung muss in der Gabelstaplerbefehlsliste verfügbar sein.",
    "lang.ark.action.interface.retrySize": "bei",
    "lang.ark.fed.setTriggerTime": "Auslöserzeit einstellen",
    "lang.ark.workflow.recycleTypeNoConfigAction": "",
    "lang.ark.fed.sendMaterialRepertory": "Welche Materialien können an diesem Einspeisepunkt verteilt werden?",
    "lang.ark.fed.addPointPosition": "Punkt hinzufügen",
    "lang.ark.plugin.pluginType.fetchContainer.way.empty": "Nimm leere Container",
    "lang.ark.fed.empty": "Leer",
    "lang.ark.fed.bussinessModel": "Geschäftsmodus",
    "lang.ark.workflow.area.ContinuousPassage": "Eins nach dem anderen / kontinuierliches übergeben",
    "lang.ark.workflow.area.end": "Von hinten in Warteschlange hinzufügen",
    "lang.ark.action.interface.responseParamName": "Name des Rückgabeparameters",
    "lang.ark.fed.screen.area.groupCode": "Gruppierungscode",
    "lang.ark.fed.brushSize": "Hubgröße",
    "lang.authManage.web.common.surePassword": "Kennwort bestätigen",
    "lang.ark.fed.startDeliveryTime": "Liefer-Startzeit",
    "lang.ark.fed.operationFlow": "Betriebsablauf",
    "lang.ark.fed.workFlow": "Arbeitsablauf",
    "lang.ark.fed.workflowTriggerMonitor": "Überwachung des Aufgabe-Auslösers",
    "lang.ark.fed.leaved": "Links",
    "lang.ark.fed.pleaseFlowNode": "Bitte fügen Sie einen Workflow-Knoten hinzu",
    "lang.ark.fed.cacheNodeActions": "Zwischenspeicherkonfiguration",
    "lang.ark.fed.reconnectToTheSystem": "Erneut eine Verbindung zum System herstellen",
    "lang.ark.fed.simulationButton": "Taste Auslösung",
    "lang.ark.trafficControl.enterStrategy.byTimePriority": "Wer zuerst kommt, erhält zuerst (Priorität zuerst)",
    "lang.ark.workflow.area.increase": "Füllen Sie die freie Stelle in der richtigen Reihenfolge",
    "lang.ark.fed.firstLetterUppercasePlus6Digits": "Schreiben Sie den ersten Buchstaben groß plus 6 Ziffern!",
    "lang.ark.fed.common.btn.detail": "Angaben",
    "lang.ark.fed.earlyWarningMode": "Frühe Warnmethode",
    "lang.ark.fed.menu.containerLog": "Container-Protokoll",
    "lang.ark.robot.classfy.roller": "Rollenroboter",
    "lang.ark.fed.carModel": "Fahrzeugtyp",
    "lang.ark.fed.youCanRemotelySelectTheRobot": "Sie können den ausgewählten Roboter fernsteuern",
    "lang.ark.fed.strategy": "Strategie",
    "lang.ark.fed.dockPointName": "Name des Dockingpunkts",
    "lang.ark.roller.docking.bating": "Rollenendeaustastung",
    "lang.mwms.fed.arrangeMoveNote": "Bewegungsblattmanagement",
    "lang.ark.externalDevice.instructionRule": "Befehlsregel",
    "lang.ark.fed.workingNotAllowClickCell": "Laufende Aufgaben sind vorhanden. Nicht auf die Zelle klicken.",
    "lang.ark.fed.canAddshelfFlag": "Ein Regal hinzufügen",
    "lang.ark.fed.export": "Exportieren",
    "lang.ark.fed.selectLocation": "Wählen Sie einen Vorratsbehälter",
    "lang.ark.workflow.area.factoryControl": "Kein Zugang zu Herstellern erlaubt",
    "lang.ark.fed.externalNumber": "Externe Blattnummer",
    "lang.ark.fed.update": "Aktualisieren",
    "lang.ark.fed.operationLog": "Betriebsprotokoll",
    "lang.ark.workflowConfig.cellFunctions.dropCell": "DROP_CELL",
    "lang.ark.fed.equipmentNode": "Geräteknoten",
    "lang.ark.externalDevice.device_own_type": "Gerichtsbarkeit der Ausrüstung",
    "lang.ark.fed.templateDownloading": "Template wird herunterladen",
    "lang.ark.fed.contents.flowConfig.recycleType.autoRecoveryRule.add": "",
    "lang.ark.fed.menu.instance": "Beispiel des Gerätes",
    "lang.ark.workflow.task.status.suspension": "Aussetzen",
    "lang.ark.workflow.denseStorageTemplateAreaFull": '"Die Endstelle befindet sich in einem kompakten Lagerbereich, der bereits vollständig belegt ist."',
    "lang.ark.groupStrategy": "Gruppenauswahlstrategie",
    "lang.ark.fed.allValuesOrRelationshipsWithinTheCollection": "Alle Werte in der Sammlung haben eine ODER-Beziehung",
    "lang.ark.rpc.syncNodeErr": "Knoteninformation konnte nicht abgerufen werden!",
    "lang.ark.fed.commonMaterials": "Gängige Materialien",
    "lang.ark.fed.eitherOrRobotAndType": "Wählen Sie entweder ein Robotermodell oder einen bestimmten Roboter",
    "lang.ark.fed.importFileFormatError": "",
    "lang.ark.fed.productPolice": "Alarm gegen Dummheit",
    "lang.mwms.monitorRobotMsg.other": "Andere Ausnahme",
    "lang.ark.fed.deleteAuthWorkStation": "Sind Sie sich sicher, dass sie die Berechtigung löschen wollen?",
    "lang.ark.fed.generalNode": "Kartenknoten",
    "lang.ark.base.license.licenseExpiredErrorMsg": "Zertifikat abgelaufen. Sie können das System nicht verwenden!",
    "lang.ark.fed.stationConfig": "Stationskonfiguration",
    "lang.ark.fed.screen.equipmentAssociatedInfo.labelEquipType": "Gerätetyp",
    "lang.ark.container.containerBatchAddPartSuccessful": "{} Container wurden erfolgreich hinzugefügt und {} Container schlugen fehl (Container-Code {} existiert bereits)",
    "lang.ark.fireStop.areaCodeAlreadyExistPleaseCheck": '"Gebietscode {0} vorhanden, bitte überprüfen!"',
    "lang.ark.apiRobotTaskCode.robotTaskNotOnlyOne": "Fehler in der übereinstimmung mit einer eindeutigen robotTaskId: {}",
    "lang.ark.fed.conButtonLogMessage": "Nachrichteninhalt",
    "lang.ark.fed.sacnFailAndCancelTask": "Die Aufgabe wurde abgebrochen, da die Erkennung des Behälter-Codes fehlgeschlagen ist.",
    "lang.ark.workflow.workflowNotFound": "Startpunkt des Prozesses nicht erkannt",
    "lang.ark.immediate.bating": "Direkte Austastung",
    "lang.gles.receipt.receiptOutWarehouseOrder": "",
    "lang.ark.waveTaskStatus.finished": "Fertig",
    "lang.ark.fed.component.workflow.tooltip.specifyNodeType": 'Wenn „Nicht angegeben“ gewählt ist, sind standardmäßig alle Knotentypen eingeschlossen. Die Auswahl von "Nicht angegeben" wird empfohlen, wenn nur ein externes Gerät vorhanden ist.',
    "lang.authManage.web.common.password": "Passwort",
    "lang.ark.fed.deviceAccessType": "",
    "lang.ark.fed.waitingTime": "Wartezeit",
    "lang.ark.fed.queueOrder": "Warteschlangenfolge",
    "lang.ark.fed.deliveryMaterial": "Liefermaterial",
    "lang.ark.base.license.ipStrIsNull": "IP-Portnummer ist null!",
    "lang.ark.workflowConfig.cellFunctions.firePass": "Feuerkontrolle",
    "lang.ark.loadCarrier.loadCarrierModelCodeDuplicated": "",
    "lang.mwms.fed.efficiency": "Effizienzmanagement",
    "lang.ark.fed.workingNotAllowChangeFloor": "Laufende Aufgaben sind vorhanden. Keine Etagen tauschen.",
    "lang.ark.interface.containerNo": "Container-Nr.",
    "lang.ark.fed.businessCode": "Geschäftscode",
    "lang.ark.fed.createManually": "Manuelle Erstellung",
    "lang.ark.fed.speedLimit": "Erlaubte Höchstgeschwindigkeit",
    "lang.ark.fed.picking": "Erforderlich",
    "lang.gles.workflow.receipt": "",
    "lang.mwms.fed.inventoryStockshot": "Bestandskontoprüfung",
    "lang.authManage.web.menu.roleList": "Rollenprofil",
    "lang.ark.record.robotCallback.turnOfSide": "",
    "lang.ark.fed.commandCode": "Anweisungs-Code",
    "lang.ark.fed.commandName":"Befehlsname",
    "lang.ark.fed.conButtonLogExecuteContent": "Ausführungsinhalt",
    "lang.ark.fed.stationPoint": "Stationspunkt",
    "lang.ark.fed.upTaskNumber": "Anstehende Aufgabennummer",
    "lang.ark.fed.queueRobotNum": "Anzahl der Roboter, die sich anstellen",
    "lang.ark.api.areaNodeExit": "Regalbereich existiert nicht",
    "lang.ark.workflow.area.accessWay": "Zugangsmodus",
    "lang.ark.fed.pleaseSelectShelfType": "Bitte wählen Sie den Regaltyp aus oder geben Sie den Regalcode ein.",
    "lang.ark.fed.currentWorkflow": "Aktueller Prozess",
    "lang.ark.fed.showContents": "Inhalt anzeigen",
    "lang.ark.workflow.task.status.cancelExecuting": "Abbrechen",
    "lang.ark.workflow.template.validate.templateCodeIsExist": "Vorlagencode existiert bereits.",
    "lang.ark.fed.rotate": "Drehen",
    "lang.ark.api.template.finishNodeNotExist": "Vorlagenendpunkt existiert nicht.",
    "lang.ark.fed.orderNum": "Dokument Nr.",
    "lang.ark.fed.setTop": "Oben",
    "lang.ark.fed.parameterClassification": "Parameterklassifizierung",
    "lang.ark.fed.operator": "Betreiber",
    "lang.ark.fed.addDockPoints": "Dockingpunkte hinzufügen",
    "lang.ark.fed.selectImage": "Bild auswählen",
    "lang.ark.fed.clickUpload": "zum Upload klicken",
    "lang.ark.fed.menu.nodeDeviceInfo": "",
    "lang.ark.fed.lineCoding": "Produktionsliniencode",
    "lang.ark.fed.screen.flowNodeConfig.judgingByRule": "",
    "lang.ark.fed.month": "Monat",
    "lang.ark.fed.electricQuantity": "Elektrische Menge",
    "lang.ark.workflow.area.order": "Sequentiell in Warteschlange hinzufügen",
    "lang.ark.fed.userEnquiry": "Benutzeranfrage",
    "lang.ark.fed.idle": "Leerlauf",
    "lang.gles.interface.interfaceManager": "",
    "lang.ark.fed.week": "Wochentag",
    "lang.ark.fed.forbid": "Deaktivieren",
    "lang.ark.workflow.cancelLocationCodeInvalid": "",
    "lang.ark.apiContainerCode.numberLessThanZero": "Der Wert von numberOfContainer muss größer als 0 sein",
    "lang.ark.workflow.successHandler": "Die Logik nach dem Erfolg verarbeiten",
    "lang.ark.warehouse.warehouseHaveNoBinInfo": "Kein Bestand verfügbar.",
    "lang.ark.fed.alarmInfo": "Alarminformationen",
    "lang.ark.fed.shelfAttribute.PNAC": "",
    "lang.authManage.web.common.newItem": "Neu",
    "lang.ark.fed.serialExcessMount": "Die Materialmenge darf {0} nicht überschreiten. Bitte wechseln Sie zu einem anderen Material.",
    "lang.ark.warehouse.preparationEnableFailed": '"Einspeisepunktmaterial oder Verteilungsziel ist Null, Aktivierung fehlgeschlagen!"',
    "lang.ark.fed.screen.hybridRobot.robotBody": "Körper des Roboters",
    "lang.ark.workflow.notFoundDenseStorageTemplate": "Die Anfangs- oder Endstelle befindet sich in einem kompakten Lagerbereich. Die konfigurierte Dynamikpunktvorlage für die kompakte Lagerung liegt nicht vor.",
    "lang.ark.fed.waveTaskDashboard": "Anzeigentafel für Wellenauswahl-Aufgaben",
    "lang.ark.fed.syncInform": "Informationen synchronisieren",
    "lang.ark.fed.extendDevice": "Externe Ausrüstung",
    "lang.ark.workflow.chooseStrategy.normal": "Normal abgeschlossen",
    "lang.ark.fed.rotationAngle": "Drehwinkel",
    "lang.ark.fed.happensEveryOnceInAwhile": "Einmal alle {0} Tage",
    "lang.ark.fed.triggerEntity": "Instanz auslösen",
    "lang.ark.workflowTrigger.logType.robot": "Roboterprotokoll",
    "lang.ark.fed.makeSureToDeleteTheCurrentRule": "Sind Sie sicher, die aktuelle Regel zu löschen?",
    "lang.ark.fed.abnormalCompletion": "Abnormal abgeschlossen",
    "lang.ark.fed.left": "Links",
    "lang.ark.fed.stationCoding": "Stationscode",
    "lang.ark.fed.screen.flowNodeConfig.instructExeCondition": "",
    "lang.ark.fed.closeLeftTabs": "Die linke Seite schließen",
    "lang.ark.fed.deviceCode": "",
    "lang.ark.workflow.area.stopRange": "Not-Aus-Bereich",
    "lang.ark.fed.triggerStopPointId": "Arbeitsstation starten",
    "lang.ark.element.has.boundNodeAction": "Dieses Element wurde an den Workflow gebunden",
    "lang.ark.workflow.stopPointNotExists": "Punkt existiert nicht",
    "lang.ark.fed.unloadingPoint": "Blindpunkt",
    "lang.ark.pda.function.container.list": "Containerliste",
    "lang.authManage.web.common.save": "Speichern",
    "lang.gles.baseData.factory": "",
    "lang.ark.warehouse.doubleContainerSide": "Doppelseitig",
    "lang.ark.fed.handlingRack": "Regale bewegen",
    "lang.ark.fed.chromeScanSetting": "1. Geben Sie chrome://flags in die Suchleiste Ihres Browsers ein, finden Sie die Option unsafely-treat-insecure-origin-as-secure, konfigurieren Sie sie auf Aktiviert, fügen Sie den erforderlichen Domainnamen oder IP hinzu, um die Kamera im Eingabefeld zu aktivieren und starten Sie dann Ihren Browser neu.",
    "lang.ark.interface.apiPause": "Workflow angehalten",
    "lang.ark.base.license.getHarewareFail": "Fehler beim Abrufen von Hardwareinformationen. Bitte überprüfen Sie die IP-Konfiguration und den Jar-Paketstart!",
    "lang.ark.fed.taskJiXu": '"Klicken Sie, um die ausgesetzte Aufgabe wieder aufzunehmen"',
    "lang.ark.apiCallbackReg.all": "Alle",
    "lang.ark.fed.ruleOpreator": "Betreiber",
    "lang.ark.fed.noItemSelected": "Keine ausgewählten Items",
    "lang.ark.fed.emergencyStopSuccess": "System-Notaus erfolgreich!",
    "lang.ark.fed.interfaceAccessType.fixation": "",
    "lang.ark.workflow.noAvailableNode": "Es wurden keine verfügbaren Knoten gefunden",
    "lang.ark.fed.clearWay": "Löschmodus",
    "lang.ark.fed.menu.vens.dmpInstance": "",
    "lang.ark.fed.return": "Zurück",
    "lang.ark.fed.menu.deviceModel": "Gerätemodell",
    "lang.ark.workflow.recoveryAreaType.appointCell": "Ausgewiesener Rückholbereich",
    "lang.ark.apiStationCode.stationTypeNotBlank": "Die Arbeitsstation-Kategorie darf nicht Null sein",
    "lang.ark.workflow.failure": "Gescheitert",
    "lang.ark.fed.screen.flowNodeConfig.checkMsg2": "",
    "lang.ark.operation.workflow.deleteWorkflow": "",
    "lang.ark.fed.screen.flowNodeConfig.checkMsg0": "",
    "lang.ark.fed.screen.flowNodeConfig.checkMsg1": "",
    "lang.ark.fed.workstation.loading.logout": "",
    "lang.ark.button.command.pressDown": "Drücken",
    "lang.ark.fed.asc": "Aufsteigende Reihenfolge",
    "lang.ark.workflow.template.validate.dynamicUnitTemplateFormatError": "Vorlagenfehler in dynamischer Zellaufgabe",
    "lang.ark.fed.taskStatus": "Aufgabenstatus",
    "lang.gles.logisticsConfig.tallyConfig": "",
    "lang.ark.fed.once": "Einmal",
    "lang.ark.workflow.dataArchiving": "Daten-Archivierung",
    "lang.ark.fed.monitoringAndManagement": "Überwachungsmanagement",
    "lang.ark.fed.floor": "Etage",
    "lang.ark.fed.menu.robotInformation": "Roboterdaten",
    "lang.ark.fed.workstationstationid": "Arbeitsstations-Nr. {stationId} ",
    "lang.ark.workflow.deviceTaskNotExistOrUnException": "",
    "lang.ark.fed.runMode.unload": "",
    "lang.ark.trafficControl.noStayRange": "Verkehrskontrollbereich",
    "lang.ark.button.operation.command.fetch": "Holen",
    "lang.mwms.api.menu.item0001": "RHINO-WEB",
    "lang.ark.fed.desc": "Absteigende Reihenfolge",
    "lang.authManage.web.common.reset": "Zurücksetzen",
    "lang.ark.fed.screen.flowNodeConfig.sourceTurnSide": "Quelle der Drehebene",
    "lang.ark.workflow.wareHouseConfigurationEnable": "Konfiguration aktiviert",
    "lang.ark.workflow.notAllowBreak": "Bei der Kommissionierung und Platzierung des Roboters ist keine Unterbrechung zulässig",
    "lang.authManage.web.common.phone": "Telefon",
    "lang.ark.workflow.taskSplit": "Aufgabenteilung",
    "lang.ark.fed.configName": "Konfigurationsname",
    "lang.gles.batchProperty": "",
    "lang.ark.fed.airRunProcess": "Leerer laufender Prozess",
    "lang.ark.fed.operatorPositionInProductionLine": "Eine Position (Station), an der der Bediener Produktionsvorgänge auf der Produktionslinie ausführt",
    "lang.ark.fed.screen.flowNodeConfig.deviceInstruct": "",
    "lang.ark.fed.all": "Alle",
    "lang.ark.workflow.paramValueCode.binCode": "binCode",
    "lang.ark.workflow.paramValueCode.offsetX": "offsetX",
    "lang.ark.apiNodeActionCode.successHandlerIsAuto": "Die Erfolgsverarbeitungslogik der Knoteninteraktionskonfiguration wird automatisch ausgelöst",
    "lang.ark.workflow.noAvailableRobot": "Keine Roboter verfügbar",
    "lang.ark.waveTriggerCondition.workstation": "Arbeitsstation ",
    "lang.ark.workflow.arrive.action.robotGoTurnOfAngle": "",
    "lang.ark.fed.startCharging": "Ladevorgang starten",
    "lang.ark.fed.theSaveSourceOnlySameTriggerHandle": "Wenn die Verbindungslinien den Gleichen Startpunkt aufweisen, konfigurieren Sie nur eine Auslösezeit!",
    "lang.ark.warehouse.policyNumberExists": "Es gab einen Wellenauswahl-Strategiecode",
    "lang.ark.workflowConfig.cellFunctions.shelfCell": "SHELF_CELL",
    "lang.ark.fed.emergencyStop": "Not-Aus gedrückt",
    "lang.ark.fed.screen.flowNodeConfig.SourceOffsetValue": "Quelle des Versatzwerts",
    "lang.ark.fed.sourceDocuments": "Dokumentquelle",
    "lang.mwms.fed.seedRule": "Pflege von Spezifikationen des Säen-Mauer",
    "lang.ark.fed.nodeWaiting": "Knoten wartet",
    "lang.ark.apiContainerCode.locationAndContainerAreEmpty": "containerCode und locationCode sind null und einer von ihnen ist zu kennzeichnen.",
    "lang.ark.fed.emptyItAll": "Alles bereinigt",
    "lang.ark.fed.operation": "Betrieb",
    "lang.ark.api.workflow.idOrTaskCodeIsNull": "Task-Code oder Workflow-ID dürfen nicht leer sein",
    "lang.ark.plugin.pluginType.returnContainer.way.manual": 'Manuell "leer" oder "voll" auswählen',
    "lang.ark.fed.menu.vens.dmpTemplateInstance": "",
    "lang.mwms.fed.workStationCharts": "Stationseffizienzmanagement",
    "lang.ark.fed.theLogicalArea": "Logikbereich",
    "lang.ark.fed.flowEdgeType": "Bewegungsart",
    "lang.ark.warehouse.goodsNoMatchZagvdbm": '"Einspeisepunkt nicht übereinstimmend, Materialcode: {0}"',
    "lang.ark.workflow.initiateNextTask": "Initiierung der nächsten Aufgabe",
    "lang.ark.fed.road": "Pfad",
    "lang.ark.fed.receiveSure": "Bestellanforderung bestätigen",
    "lang.ark.workflow.childNodeListIsEmpty": "Die untergeordnete Knotensammlung ist leer",
    "lang.ark.workflow.denseStorageEndPointTaskUpperLimit": "Die Endstelle ist der kompakte Lagerbereich, der die Höchstzahl der angenommenen Aufgaben erreicht hat",
    "lang.gles.systemManage.systemParam": "",
    "lang.ark.workflowConfig.status.deleted": "Gelöscht",
    "lang.ark.fed.waveSetting": "Wellenauswahl-Generierung",
    "lang.ark.fed.waveNum": "Wellen ID",
    "lang.ark.interface.config.field.locationToDesc": "",
    "lang.ark.interface.config.taskCallback.field.msgTypeDesc": "",
    "lang.ark.interface.config.taskCallback.field.exceptionFailReasonDesc": "",
    "lang.ark.sys.config.values.show": "",
    "lang.ark.sys.config.values.rpcStop": "",
    "lang.ark.sys.config.values.sync": "",
    "lang.ark.externalDevice.instructionRule7": "",
    "lang.ark.externalDevice.instructionRule6": "",
    "lang.ark.externalDevice.instructionRule5": "",
    "lang.ark.interface.config.taskCallbackDesc": "",
    "lang.ark.sys.config.rmsQueryCurrentMapAddress": "",
    "lang.ark.interface.config.taskCallback.field.parentInstanceIdDesc": "",
    "lang.ark.sys.config.values.noneStop": "",
    "lang.ark.sys.config.callbackMessageRetryInterval": "",
    "lang.ark.interface.config.field.containerCodeDesc": "",
    "lang.ark.fed.uploadFile": "",
    "lang.ark.sys.config.denseStorageTemplateForAreaToArea": "",
    "lang.ark.sys.config.simpHost": "",
    "lang.ark.interface.config.movingMulti.field.destsDesc": "",
    "lang.ark.fed.excel.data.null": "",
    "lang.ark.task.nodeDevice.export.param6.value": "",
    "lang.ark.interface.config.field.priorityDesc": "",
    "lang.ark.interface.config.taskCallback.field.robotTaskIdDesc": "",
    "lang.ark.binStopPoint.deviceType": "",
    "lang.ark.task.nodeDevice.export.param1.value": "",
    "lang.ark.task.plugin.take.fullContainer": "",
    "lang.ark.sys.config.values.oldFormat": "",
    "lang.ark.sys.config.strictOrderMode": "",
    "lang.ark.sys.config.buttonFilterTime": "",
    "lang.ark.sys.config.callbackMessageRetryTimes": "",
    "lang.ark.sys.config.values.close": "",
    "lang.ark.sys.config.rmsWsAddress": "",
    "lang.ark.task.plugin.take.emptyContainer": "",
    "lang.ark.interface.config.taskCallback.field.instancePriorityDesc": "",
    "lang.ark.fed.containerBinStatus": "",
    "lang.ark.sys.config.rmsQueryAreaAddress": "",
    "lang.ark.fed.binStopPoint.file.excel.name": "",
    "lang.ark.sys.config.modbusEnable": "",
    "lang.ark.task.exception.startPoint.containerCode.mustOne": "",
    "lang.ark.interface.config.movingMulti": "",
    "lang.ark.interface.config.field.containerCategoryDesc": "",
    "lang.ark.interface.config.taskCallback.field.waitNextLocationDesc": "",
    "lang.ark.sys.config.robotMediaApi": "",
    "lang.ark.task.exception.templateTask.empty": "",
    "lang.ark.sys.config.stationNoticeCycle": "",
    "lang.ark.interface.config.taskCallback.field.robotDesc": "",
    "lang.ark.sys.config.values.exactMatch": "",
    "lang.ark.interface.config.taskCallback.field.instanceIdDesc": "",
    "lang.ark.task.nodeDevice.export.param8.name": "",
    "lang.ark.sys.config.denseStorageTemplateForAreaToPoint": "",
    "lang.ark.sys.config.overtimeTaskIntervalHours": "",
    "lang.ark.sys.config.values.accountLogin": "",
    "lang.ark.sys.config.values.async": "",
    "lang.ark.interface.config.field.needTimeDesc": "",
    "lang.ark.sys.config.stationConfig": "",
    "lang.ark.task.nodeDevice.export.interfaceOrDevice": "",
    "lang.ark.interface.config.taskCallback.field.robotPhaseDesc": "",
    "lang.ark.sys.config.denseStorageTemplateForPointToArea": "",
    "lang.ark.fed.frontend": "",
    "lang.ark.fed.updateUser": "",
    "lang.ark.interface.config.taskCallback.field.workflowCodeDesc": "",
    "lang.ark.interface.config.field.requestTimeDesc": "",
    "lang.ark.workflow.noAvailableOnlineDevices": "",
    "lang.ark.sys.config.filterChannelButton": "",
    "lang.ark.sys.config.showExceptionTab": "",
    "lang.ark.interface.config.movingMultiDesc": "",
    "lang.ark.sys.config.modbusPort": "",
    "lang.ark.fed.excel.data.binCodeAndOrderError": "",
    "lang.ark.sys.config.loginUrl": "",
    "lang.ark.fed.updateTime": "",
    "lang.ark.sys.config.shelfOnShare": "",
    "lang.ark.task.nodeDevice.export.param3.name": "",
    "lang.ark.sys.config.values.version2": "",
    "lang.ark.sys.config.values.version1": "",
    "lang.ark.fed.containerColumn": "",
    "lang.ark.task.nodeDevice.export.param5.value": "",
    "lang.ark.task.exception.endpoint.empty": "",
    "lang.ark.task.exception.templateCode.empty": "",
    "lang.ark.sys.config.warehousePointRelatedScheduled": "",
    "lang.ark.sys.config.values.withoutFloor": "",
    "lang.ark.sys.config.httpTimeOut": "",
    "lang.ark.sys.config.values.adaptive": "",
    "lang.ark.sys.config.authUrl": "",
    "lang.ark.interface.config.movingDesc": "",
    "lang.ark.sys.config.callbackMessageRetryAndConfirm": "",
    "lang.ark.sys.config.values.doNotSplitTheRollerFetchCommand": "",
    "lang.ark.interface.config.field.languageDesc": "",
    "lang.ark.fed.containerLayer": "",
    "lang.ark.interface.config.dynamicUnitMoving.field.robotIdDesc": "",
    "lang.ark.sys.config.values.mapStation": "",
    "lang.ark.sys.config.clientId": "",
    "lang.ark.sys.config.asynCount": "",
    "lang.ark.sys.config.rmsUrl": "",
    "lang.ark.fed.excel.data.binCodeExist": "",
    "lang.ark.flowNodeConfig.rollerFetchCommand.checkMsg": "",
    "lang.ark.interface.config.taskCallback.field.nodeCodeToDesc": "",
    "lang.ark.sys.config.smpAlterIp": "",
    "lang.ark.task.nodeDevice.export.param5.name": "",
    "lang.ark.interface.config.taskCallback.field.locationToDesc": "",
    "lang.ark.task.plugin.deliver.manuallyChoose": "",
    "lang.ark.sys.config.values.notLeave": "",
    "lang.ark.interface.config.movingMulti.field.msgTypeDesc": "",
    "lang.ark.sys.config.ningdeHttpSoapUserName": "",
    "lang.ark.interface.config.dynamicUnitMoving.field.msgTypeDesc": "",
    "lang.ark.fed.templateTask": "",
    "lang.ark.sys.config.isSynInterfaceRecord": "",
    "lang.ark.binStopPoint.name": "",
    "lang.ark.task.exception.robotType.empty": "",
    "lang.ark.fed.excel.data.deviceType.nonNumericFormat": "",
    "lang.ark.interface.config.field.taskCodeDesc": "",
    "lang.ark.sys.config.values.leaveByTaskDest": "",
    "lang.ark.interface.config.moving": "",
    "lang.ark.interface.config.taskCallback.field.scanningInformationDesc": "",
    "lang.ark.task.plugin.deliver.fullContainer": "",
    "lang.ark.interface.config.field.requestIdDesc": "",
    "lang.ark.task.exception.priority.gt": "",
    "lang.ark.sys.config.values.pc": "",
    "lang.ark.interface.interfaceDesc.button": "Betrieb",
    "lang.ark.task.nodeDevice.export.param7.value": "",
    "lang.ark.interface.config.moving.field.msgTypeDesc": "",
    "lang.ark.sys.config.robotMediaCatalogue": "",
    "lang.ark.sys.config.canBackTaskFlag": "",
    "lang.ark.sys.config.trafficControlInterfaceType": "",
    "lang.ark.sys.config.values.cancelInPlace": "",
    "lang.ark.binStopPoint.dropHeight": "",
    "lang.ark.sys.config.waitPointTaskShowNum": "",
    "lang.ark.task.nodeDevice.export.param3.value": "",
    "lang.ark.interface.config.taskCallback.field.robotErrorDesc": "",
    "lang.ark.sys.config.values.notStrictOrder": "",
    "lang.ark.interface.config.dynamicUnitMoving.field.robotProductDesc": "",
    "lang.ark.apiCommonCode.name.overLengthError": "",
    "lang.ark.sys.config.robotDefaultPort": "",
    "lang.ark.sys.config.rmsRetryCode": "",
    "lang.ark.interface.config.field.channelIdDesc": "",
    "lang.ark.sys.config.checkFreeRobotFlag": "",
    "lang.ark.interface.config.dynamicUnitMoving.field.containerCategoryDesc": "",
    "lang.ark.task.nodeDevice.export.param4.name": "",
    "lang.ark.sys.config.canCancelDoneTaskFlag": "",
    "lang.ark.sys.config.arkRoot": "",
    "lang.ark.interface.config.field.clientCodeDesc": "",
    "lang.ark.task.exception.startPoint.notMatch": "",
    "lang.ark.sys.config.callbackMsgTransFlag": "",
    "lang.ark.interface.config.taskCallback.field.scanCodeDesc": "",
    "lang.ark.fed.excel.data.name.overLength": "",
    "lang.ark.sys.config.movingApiResponsePattern": "",
    "lang.ark.fed.flowNodeConfig.beforeTaskArrived.destDistance.label": "",
    "lang.ark.interface.config.dynamicUnitMovingDesc": "",
    "lang.ark.sys.config.ningdeHttpSoapPassword": "",
    "lang.ark.binStopPoint.pickHeight": "",
    "lang.ark.task.rule.saveFloor": "",
    "lang.ark.fed.binStopPoint.file.excel.sheet1.name": "",
    "lang.ark.interface.config.dynamicUnitMoving.field.isEndDesc": "",
    "lang.ark.interface.config.taskCallback.field.locationFromDesc": "",
    "lang.ark.sys.config.taskScheduleInterval": "",
    "lang.ark.task.rule.default": "",
    "lang.ark.sys.config.stationDisplayMode": "",
    "lang.ark.sys.config.masterKey": "",
    "lang.ark.sys.config.marsRoot": "",
    "lang.ark.interface.config.field.taskTypeDesc": "",
    "lang.ark.interface.config.dynamicUnitMoving": "",
    "lang.ark.sys.config.stationNoticeTimes": "",
    "lang.ark.sys.config.values.strictOrder": "",
    "lang.ark.sys.config.isSyncHandleApi": "",
    "lang.ark.task.nodeDevice.export.param10.name": "",
    "lang.ark.sys.config.modbusIp": "",
    "lang.ark.interface.config.field.instanceIdDesc": "",
    "lang.ark.task.plugin.name.take.emptyContainer": "",
    "lang.ark.sys.config.values.fullStation": "",
    "lang.ark.sys.config.rmsHttpAddress": "",
    "lang.ark.interface.config.taskCallback.field.exceptionStateDesc": "",
    "lang.ark.sys.config.ningdeHttpSoap": "",
    "lang.ark.task.nodeDevice.export.param4.value": "",
    "lang.ark.workflow.action.commandExecutePhase.beforeTaskArrived": "",
    "lang.ark.interface.config.dynamicUnitMoving.field.toBinTypeDesc": "",
    "lang.ark.task.nodeDevice.export.param9.value": "",
    "lang.ark.task.plugin.deliver.emptyContainer": "",
    "lang.ark.task.nodeDevice.export.param9.name": "",
    "lang.ark.sys.config.loginType": "",
    "lang.ark.task.plugin.name.take.fullContainer": "",
    "lang.ark.task.nodeDevice.export.param2.name": "",
    "lang.ark.fed.menu.loginLog": "",
    "lang.ark.binStopPoint.dockingHeight": "",
    "lang.ark.interface.config.taskCallback.field.taskPhaseDesc": "",
    "lang.ark.task.nodeDevice.export.param6.name": "",
    "lang.ark.sys.config.matchWorkflowStrategy": "",
    "lang.ark.fed.excel.nodeCodeNotExists": "",
    "lang.ark.sys.config.authNoPerUrl": "",
    "lang.ark.workflow.startPickUp": "Abholung beginnen",
    "lang.ark.fed.excel.data.stopPointCodeNotExist": "",
    "lang.ark.sys.config.changepwUrl": "",
    "lang.ark.interface.config.taskCallback": "",
    "lang.ark.interface.config.taskCallback.field.workflowExceptionCodeDesc": "",
    "lang.ark.interface.config.dynamicMovingDesc": "",
    "lang.ark.sys.config.values.splitTheRollerFetchCommand": "",
    "lang.ark.interface.config.movingMulti.field.flowStrategyDesc": "",
    "lang.ark.task.plugin.name.deliver.emptyContainer": "",
    "lang.ark.trigger.logClearDesc": "",
    "lang.ark.sys.config.values.callbackToTaskChannel": "",
    "lang.ark.sys.config.authCode": "",
    "lang.ark.interface.config.dynamicUnitMoving.field.robotSideDesc": "",
    "lang.ark.fed.excel.data.nonNumericFormat": "",
    "lang.ark.sys.config.dmpSetContainerAngle": "",
    "lang.ark.sys.config.canDeleteTaskFlag": "",
    "lang.ark.interface.config.dynamicUnitMoving.field.toBinOrderDesc": "",
    "lang.ark.interface.config.field.scanCodeDesc": "",
    "lang.ark.task.plugin.name.deliver.fullContainer": "",
    "lang.ark.sys.config.stationService": "",
    "lang.ark.sys.config.language": "",
    "lang.ark.sys.config.authId": "",
    "lang.ark.sys.config.globalCanDeleteTaskFlag": "",
    "lang.ark.sys.config.values.disable": "",
    "lang.ark.sys.config.robotReceiveUrl": "",
    "lang.ark.interface.config.dynamicUnitMoving.field.robotRuleDesc": "",
    "lang.ark.sys.config.values.notShow": "",
    "lang.ark.task.exception.taskType.empty": "",
    "lang.ark.fed.arkVersion": "",
    "lang.ark.sys.config.recycleFunctionSwitch": "",
    "lang.ark.trigger.dataArchivingDesc": "",
    "lang.ark.sys.config.rollerFetchCommandSplitConfiguration": "",
    "lang.ark.sys.config.values.open": "",
    "lang.ark.trigger.logClear": "",
    "lang.ark.sys.config.rmsChannelId": "",
    "lang.ark.task.exception.priority.empty": "",
    "lang.ark.sys.config.dataArchiveMaxNum": "",
    "lang.ark.sys.config.shelfMovingCallbackMsgFlag": "",
    "lang.ark.task.exception.templateCode.notEqual": "",
    "lang.ark.apiCommonCode.stopPointCodeExistError": "",
    "lang.ark.sys.config.ningdeHttpLes": "",
    "lang.ark.sys.config.newShelfCodePre": "",
    "lang.ark.fed.excel.data.stopPointCodeExist": "",
    "lang.ark.sys.config.isFloorRobot": "",
    "lang.ark.fed.flowNodeConfig.beforeTaskArrived.destDistance.checkMsg": "",
    "lang.ark.sys.config.values.mobile": "",
    "lang.ark.sys.config.systemStatusConfig": "",
    "lang.ark.task.rule.diffFloor": "",
    "lang.ark.sys.config.values.withFloor": "",
    "lang.ark.sys.config.callbackMsgOvertime": "",
    "lang.ark.interface.config.taskCallback.field.taskIdDesc": "",
    "lang.ark.task.plugin.take.manuallyChoose": "",
    "lang.ark.fed.flowNodeConfig.beforeTaskArrived.destDistance.action": "",
    "lang.ark.sys.config.isFrklift": "",
    "lang.ark.fed.flowNodeConfig.beforeTaskArrived.destDistance.condition": "",
    "lang.ark.task.exception.endpoint.notMatch": "",
    "lang.ark.interface.config.taskCallback.field.workflowExceptionDesc": "",
    "lang.ark.fed.backend": "",
    "lang.ark.task.nodeDevice.export.desc": "",
    "lang.ark.sys.config.values.enable": "",
    "lang.ark.interface.config.taskCallback.field.waitLocationDesc": "",
    "lang.ark.apiCommonCode.binCodeAndOrderError": "",
    "lang.ark.interface.config.dynamicMoving.field.msgTypeDesc": "",
    "lang.ark.sys.config.values.cardLogin": "",
    "lang.ark.task.nodeDevice.export.param8.value": "",
    "lang.ark.interface.config.taskCallback.field.waitDirDesc": "",
    "lang.ark.task.plugin.deliver.autoReturn": "",
    "lang.ark.binStopPoint.stopPointCode": "",
    "lang.ark.sys.config.callbackMessageRetryAndConfirmFilter": "",
    "lang.ark.sys.config.values.forceDelete": "",
    "lang.ark.fed.excel.nodeTypeError": "",
    "lang.ark.sys.config.deleteTaskIfNeedRemoveShelf": "",
    "lang.ark.sys.config.imageUploadPath": "",
    "lang.ark.sys.config.canUndoTaskFlag": "",
    "lang.ark.task.nodeDevice.export.param2.value": "",
    "lang.ark.sys.config.iniChainDefinitionTimeout": "",
    "lang.ark.interface.config.taskCallback.field.workflowPhaseDesc": "",
    "lang.ark.trigger.dataArchiving": "",
    "lang.ark.sys.config.vensVersion": "",
    "lang.ark.fed.inventoryInfo": "",
    "lang.ark.task.nodeDevice.export.param10.value": "",
    "lang.ark.fed.bundleDate": "",
    "lang.ark.interface.config.field.locationFromDesc": "",
    "lang.ark.interface.config.taskCallback.field.nodeCodeFromDesc": "",
    "lang.ark.interface.config.dynamicMoving": "",
    "lang.ark.binStopPoint.binOrder": "",
    "lang.ark.task.nodeDevice.export.param1.name": "",
    "lang.ark.sys.config.wAreaStation": "",
    "lang.ark.sys.config.loginByCardNoUrl": "",
    "lang.ark.interface.config.dynamicUnitMoving.field.instructionDesc": "",
    "lang.ark.record.robotCallback.action.completed": "",
    "lang.ark.sys.config.forceCancelUseAthenaInstruction": "",
    "lang.ark.sys.config.authUrlServer": "",
    "lang.ark.binStopPoint.binCode": "",
    "lang.ark.sys.config.values.fuzzyMatch": "",
    "lang.ark.sys.config.values.callbackToAll": "",
    "lang.ark.sys.config.values.liteStation": "",
    "lang.ark.sys.config.stationUnique": "",
    "lang.ark.task.nodeDevice.export.param7.name": "",
    "lang.ark.task.exception.containerCode.notMatch": "",
    "lang.ark.sys.config.values.leaveByTaskLast": "",
    "lang.ark.workflow.pickupCompleted": "Abholung abgeschlossen",
    "lang.ark.sys.config.values.standardFormat": "",
    "lang.ark.sys.config.values.websocketStop": "",
}