<template>
  <div class="tw-flex tw-flex-col tw-relative tw--mt-4">
    <!-- 搜索框 -->
    <div class="tw-flex tw-justify-end tw-gap-2 tw-my-4">
      <StorageFilter @change="handleFilterChange">
        <template #suffix>
          <ButtonEnhanced type="primary" @click="handleAdd">
            {{ $t("lang.ark.fed.addNew") }}
          </ButtonEnhanced>
        </template>
      </StorageFilter>
    </div>
    <!-- 表格数据 -->
    <table-wrapper v-bind="tableState"> </table-wrapper>
    <!-- 这个是弹框组件 -->
    <DialogAddStorage
      :visible="state.failDialogVisible"
      :init-options="allOptions"
      :mode="modeVal"
      :options="optionsVal"
      :selected-cell-codes="selectedCellCodes"
      :disabled-station-cell-code="disabledStationCellCode"
      :selected-service-cell-codes="selectedServiceCellCodes"
      :pallet-code-hashmap="palletCodeHashmap"
      @update:visible="handleResultConfirm"
      @save-success="handleSaveSuccess"
    />
  </div>
</template>
<script>
import { defineComponent, reactive, computed, onMounted, ref } from "vue";
// 组件&工具
import StorageFilter from "./storage-filter.vue";
import { uniq, uniqBy, forEach } from "lodash";
// import R from "lodash";
import { ButtonEnhanced } from "gms-components";
import { useI18n } from "gms-hooks";
import TableWrapper, { useTableState } from "@srpe-fe/uic-el-table-wrapper-vue2";
import DialogAddStorage from "./dialog-add-storage.vue";
// API接口
import { deleteAreaInfo, queryAllAreaInfo, queryAreaInfoList } from "gms-apis/area";
import { getGenerateCode } from "gms-apis/commons";
import { queryCellCodeList, checkWhenDeleteNode, queryAllWorkstationInfo } from "gms-apis/worksite";
import { getListNoPage, getPalletCodeList } from "gms-apis/container";
// 公共配置文件
import { getTableColumns, fetchCommonDict } from "../common.js";
import { filter } from "lodash";
import { Message } from "geekplus-ui";
import myTransform from "@/utils/transform";
import { GEEK_FACILITY_DICT_reserve } from "@/constants/facility";
// 这里效仿的是 容器列表的搜索 把表格的数据给封装上了
export default defineComponent({
  name: "StorageList",
  components: {
    StorageFilter,
    ButtonEnhanced,
    TableWrapper,
    DialogAddStorage,
  },
  setup() {
    const t = useI18n();
    // 分组策略
    const groupStrategyDict = ref([]);
    const hitStrategyDict = ref({}); //挑选策略
    const queueStrategyDict = ref({}); //队列策略
    const increaseStrategyDict = ref({}); //增长策略
    const trafficFunctionTypeDict = ref({}); //交通功能类型
    const modeVal = ref("add");
    const cellCodeDict = ref([]);
    const containerTypeListDict = ref([]); // 货架模型的列表
    const hostCellCodeDict = ref([]); //外部编码
    const selectedServiceCellCodes = ref([]);
    const areaNameDict = ref([]); //区域名称
    const disabledStationCellCode = ref([]); // 选择排队策略是需要避免工作占已选择的点
    // 托盘位编码哈希表，key 为内部编码，value 为外部编码或内部编码（若无外部编码）
    const palletCodeHashmap = ref({});

    const state = reactive({
      // 这是控制是否显示弹框的状态
      failDialogVisible: false,
      // 数据的查询参数
      filterParams: {},
      palletCodeList: [],
      pointCodeList: [],
    });
    const allOptions = reactive({
      // 编辑的id
      id: "",
      // 区域名
      areaName: "",
      cellType: "AREA_CELL",
      // 区域编码，
      hostCellCode: "",
      childrenNode: [],
      childrenType: 3,
      increaseStrategy: "",
      belongNodes: [],
      // 这个是存储策略的数据
      hitStrategyDict: [],
      // 这个是港道命中策略的数据
      groupStrategyDict: [],
    });
    const selectedCellCodes = ref([]);
    // // 这里是用来请求数据的
    // queueStrategy", "increaseStrategy", "trafficFunctionType", "groupStrategy
    // 表格请求函数
    const requestData = async (params) => {
      const { recordCount, recordList } = await queryAreaInfoList({
        ...params,
        ...state.filterParams,
      });

      return {
        data: recordList,
        total: recordCount,
      };
    };

    // 获取已被区域占用的点位
    const getSelectedCellCodes = (recordList) => {
      const codes = recordList.reduce((result, curVal) => {
        if (curVal.childrenNode) {
          return result.concat(curVal.childrenNode);
        }
        return result;
      }, []);

      selectedCellCodes.value = uniq(codes);
    };

    const getSelectedServiceCellCodes = (recordList) => {
      selectedServiceCellCodes.value = recordList.reduce((result, curVal) => {
        if (curVal.belongNodes) {
          return result.concat(curVal.belongNodes.map((i) => `${i.cellType}-${i.id}`));
        }
        return result;
      }, []);
    };
    // 获取区域编码和区域名称的下拉列表数据
    const getSearchDropList = async () => {
      const data = await queryAllAreaInfo();

      const forHostCellCode = uniqBy(data, "hostCellCode").filter((i) => !!i.hostCellCode);
      const forAreaName = uniqBy(data, "areaName");

      hostCellCodeDict.value = myTransform.arrToStringOptions(forHostCellCode, "hostCellCode", "hostCellCode");
      areaNameDict.value = myTransform.arrToStringOptions(forAreaName, "areaName", "areaName");
      getSelectedCellCodes(data);
      getSelectedServiceCellCodes(data);
    };
    // 查询所有的工作占点位已经选过的
    const querydisabledStationCellCode = async () => {
      const data = await queryAllWorkstationInfo();
      disabledStationCellCode.value = data.reduce((result, curVal) => {
        if (curVal.stopPointIds) {
          return result.concat(curVal.stopPointIds.split(","));
        }
        return result;
      }, []);
    };

    /** 获取所有的托盘位，提供托盘位的外部编码和内部编码映射 */
    const getPalletCode = async () => {
      const data = await getPalletCodeList();

      forEach(data, ({ palletLatticeCode, palletLatticeHostCode }) => {
        // palletLatticeHostCode 外部编码
        // palletLatticeCode 内部编码
        // GMS 后端使用内部编码，但前端显示优先展示外部编码
        palletCodeHashmap.value[palletLatticeCode] = palletLatticeHostCode || palletLatticeCode;
      });
    };

    // 删除的函数
    const handleDeleteConfirm = async (row) => {
      const { cellType, id } = row;
      try {
        await checkWhenDeleteNode({
          nodeId: id,
          nodeType: GEEK_FACILITY_DICT_reserve[cellType],
        });
        await deleteAreaInfo(row.id);
        refreshTable();
        Message.success(t("lang.ark.fed.deleteSuccessfully"));
      } catch (error) {
        console.error("Delete failed:", error);
      }
    };
    // 编辑的函数
    const handleEdit = (row, model) => {
      const newData = {
        ...allOptions, // 先保留原有的属性（包括 groupStrategy 等字典数据）
        ...row, // 然后用 row 的数据覆盖
        hitStrategyDict: allOptions.hitStrategyDict,
        groupStrategyDict: allOptions.groupStrategyDict,
      };

      // 使用 Object.keys 逐个更新属性，以确保触发响应式
      Object.keys(newData).forEach((key) => {
        allOptions[key] = newData[key];
      });
      modeVal.value = model; // 设置模式为编辑
      state.failDialogVisible = true; // 显示编辑对话框
    };
    // 表格初始化
    const tableState = useTableState({
      columns: getTableColumns({ handleEdit, handleDelete: handleDeleteConfirm }),
      fetchData: requestData,
    });
    const handleFilterChange = (filterParams) => {
      state.filterParams = filterParams;
      tableState.query({ currentPage: 1 });
    };
    // 添加的函数
    const handleAdd = async () => {
      const areaCode = await getGenerateCode("A");
      // 使用 Object.assign 来确保响应式更新
      Object.assign(allOptions, {
        id: "", // 新增时无id
        areaName: "",
        hostCellCode: areaCode,
        storageType: 10,
        hitStrategy: "",
        groupStrategy: "",
        portChannelCount: 1,
        childrenNode: [],
        palletChildNodes: "",
        childrenAreas: [],
        increaseStrategy: "",
        queueStrategy: "",
        containerTypes: [],
        containerTypeCodes: [],
        ynGroup: 0,
        cellType: "AREA_CELL",
        belongNodes: [],
        // 字典类字段保留原有（如 allOptions.hitStrategyDict、groupStrategyDict）
        hitStrategyDict: allOptions.hitStrategyDict,
        groupStrategyDict: allOptions.groupStrategyDict,
      });
      modeVal.value = "add";
      state.failDialogVisible = true;
    };
    // 刷新列表的逻辑
    const refreshTable = () => {
      tableState.query();
      getSearchDropList();
    };
    // 关闭弹框的逻辑
    const handleResultConfirm = () => {
      state.failDialogVisible = false;
    };
    // 保存成功的处理
    const handleSaveSuccess = () => {
      refreshTable();
    };
    const optionsVal = computed(() => {
      // const trafDdict = JSON.parse(JSON.stringify(trafficFunctionTypeDict.value));
      // R.remove(trafDdict, (i) => ["10", "40"].includes(String(i.value)));
      return {
        hitStrategy: hitStrategyDict.value,
        queueStrategy: queueStrategyDict.value,
        increaseStrategy: increaseStrategyDict.value,
        trafficControlEnterType: "",
        trafficControlEnterPattern: "",
        trafficControlEnterStrategy: "",
        cellCode: cellCodeDict.value,
        containerTypeCodes: containerTypeListDict.value,
        // trafficFunctionType:trafDdict,
        groupStrategy: groupStrategyDict.value,
      };
    });
    const getCellCodeDict = async () => {
      const data = await queryCellCodeList();
      cellCodeDict.value = myTransform.arrToStringOptions(data, "cellCode", "cellCode");
    };
    // // 加载货架模型
    const getShelfType = async () => {
      // 加载货架类型
      const shelfListData = await getListNoPage();
      if (shelfListData) {
        containerTypeListDict.value = myTransform.arrToStringOptions(shelfListData, "name", "code");
      }
    };
    // 这里请求的是获取下拉菜单的数据
    const objectCodes = ["hitStrategy", "queueStrategy", "increaseStrategy", "trafficFunctionType", "groupStrategy"];
    onMounted(async () => {
      tableState.query();
      await Promise.all([
        getSearchDropList(),
        getCellCodeDict(),
        getShelfType(),
        querydisabledStationCellCode(),
        getPalletCode(),
      ]);
      const dicts = await fetchCommonDict({ objectCodes });
      allOptions.hitStrategyDict = dicts.hitStrategy_dict;
      allOptions.groupStrategyDict = dicts.groupStrategy_dict;
      hitStrategyDict.value = dicts.hitStrategy_dict;
      groupStrategyDict.value = dicts.groupStrategy_dict;
      queueStrategyDict.value = dicts.queueStrategy_dict;
      increaseStrategyDict.value = dicts.increaseStrategy_dict;
      trafficFunctionTypeDict.value = dicts.trafficFunctionType_dict;
    });
    return {
      t,
      state,
      tableState,
      handleAdd,
      handleFilterChange,
      handleResultConfirm,
      allOptions,
      handleSaveSuccess,
      handleDeleteConfirm,
      handleEdit,
      groupStrategyDict,
      hitStrategyDict,
      queueStrategyDict,
      increaseStrategyDict,
      trafficFunctionTypeDict,
      modeVal,
      optionsVal,
      cellCodeDict,
      containerTypeListDict,
      selectedCellCodes,
      hostCellCodeDict,
      selectedServiceCellCodes,
      areaNameDict,
      disabledStationCellCode,
      palletCodeHashmap,
    };
  },
});
</script>
<style lang="scss" module="cn"></style>
