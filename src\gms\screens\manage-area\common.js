import axios from "axios";
import myTransform from "@/utils/transform";
import apiStore from "@/common/apiStore";
import { constantList } from "@/gms/commons/constants/constant-list";
import { columnTypes } from "@srpe-fe/uic-el-table-wrapper-vue2";
import {
  AGV_CLASS,
  equipmentTypes,
  locationTypes,
  workflowTempalteTypes,
  workstationPositionCodeSubTypes,
} from "gms-constants";
import { useI18n } from "gms-hooks";
const t = useI18n();
export const ADD_BTN = "add";

export const LEAVE_BTN = "leave";

export const ENTRY_BTN = "entry";

export const DELETE_BTN = "delete";
export const confirmMsg = {
  [`${ENTRY_BTN}Title`]: t("geekplus.gms.client.screen.container.tips.confirmEntry"),
  [`${LEAVE_BTN}Title`]: t("geekplus.gms.client.screen.container.tips.confirmLeave"),
  [`${LEAVE_BTN}Content`]: t("geekplus.gms.client.screen.container.tips.leaveTipMessage"),
  [`${DELETE_BTN}Title`]: t("geekplus.gms.client.commons.tips.confirmDelete"),
  [`${DELETE_BTN}Content`]: t("geekplus.gms.client.screen.container.tips.containerDeleteTipMessage"),
};
// import { WK_CONFIG_STATUS } from "gms-constants";
export const AreaNodeTypeDict = constantList([
  // 基础点位
  { key: "BASE", value: "base", labelI18nKey: "lang.ark.fed.selectPoints", useButton: true },
  // 托盘位点位
  { key: "PALLET", value: "pallet", labelI18nKey: "lang.ark.fed.selectPalletPosition", useButton: true },
]);
/** 流程创建方式 */
export const STORAGE_CREATE_TYPE = constantList([
  // 全部
  {
    key: "ALL",
    value: "1",
    labelI18nKey: "geekplus.gms.client.screen.flowConfig.manualConfigFlow",
    component: "",
  },
  // 存储区
  {
    key: "STORAGE",
    value: "2",
    labelI18nKey: "geekplus.gms.client.screen.areaEditIndex.storageArea",
    component: "StorageList",
  },
  // 排队区
  {
    key: "QUEUE",
    value: "3",
    labelI18nKey: "geekplus.gms.client.screen.areaEditIndex.queueArea",
    component: "QueueList",
  },
]);
// 获取存储区的表格数据
export const getTableColumns = ({ handleDelete, handleEdit }) => [
  // 区域编码
  {
    prop: "hostCellCode",
    label: t("lang.ark.fed.areaCode"),
  },
  // 区域名称
  {
    prop: "areaName",
    label: t("lang.ark.fed.areaName"),
  },
  // 存储区类型目前还没有添加上
  {
    prop: "storageType",
    label: t("geekplus.gms.client.screen.areaEditIndex.storageAreaType"),
    formatter: (row) => {
      if (row.storageType === 10) return t("geekplus.gms.client.screen.areaEditIndex.regularStorageArea");
      if (row.storageType === 20) return t("geekplus.gms.client.screen.areaEditIndex.denseStorageArea");
      return row.storageType;
    },
  },
  // 编辑人
  {
    prop: "updatorUsername",
    label: t("lang.ark.fed.editor"),
  },
  // 编辑时间
  {
    prop: "updateTime",
    label: t("lang.ark.fed.editingTime"),
  },
  {
    type: columnTypes.GEEK_ACTION_BUTTONS,
    displayCount: 4,
    width: 200,
    options: [
      {
        text: t("lang.ark.fed.details"),
        handleClick: ({ row }) => handleEdit(row, "view"),
      },
      {
        text: t("lang.authManage.web.common.edit"),
        handleClick: ({ row }) => handleEdit(row, "edit"),
      },
      {
        text: t("lang.ark.fed.delete"),
        confirm: {
          title: confirmMsg[`${DELETE_BTN}Content`],
          handleConfirm: ({ row }) => handleDelete(row),
        },
      },
    ],
  },
];

//
export const getNodesWithPoints = () => {
  // 包含地图点位和托盘位置
  return {
    groups: [locationTypes.GENERAL_POINT, locationTypes.PALLET_POINT],
    itemFilter: (item) => item.$group === locationTypes.GENERAL_POINT || locationTypes.PALLET_POINT,
  };
};

/**
 * 创建包含点位的标签文本
 * @param {object} item
 * @param {string | null} item.palletChildNodes 托盘位内部编码
 * @param {string[] | null} item.palletChildrenNode 托盘位内部编码（数组形式）
 * @param {string[] | null} item.childrenNode 普通点位编码
 * @param {Record<string, string>} palletHashmap 托盘内外编码映射表
 * @returns {string}
 */
export function createChildrenNodeLabel(item, palletHashmap) {
  const { palletChildNodes, palletChildrenNode, childrenNode } = item;
  const palletArray = palletChildNodes ? palletChildNodes.split(",") : palletChildrenNode;

  if (palletArray && palletArray.length > 0) {
    return palletArray.map((item) => palletHashmap[item]).join(",");
  }

  if (childrenNode && childrenNode.length) {
    return childrenNode.join(",");
  }

  return "";
}

// 将vuex3.0 版本的获取字典的接口给抽离出来了 因为新版页面用的都是vue3 的语言为了保持语法一直就把这段给分类出来了
/**
 * 获取并格式化字典数据
 * @param {Object} payload
 * @param {string[]} payload.objectCodes – 要拉取的字典 code 列表
 * @returns {Promise<Object>} – 返回一个 map，key 为 `${code}_dict`，value 为 [{ label, value }]
 */
export async function fetchCommonDict(payload = {}) {
  console.log("payload======", payload);
  const { objectCodes } = payload;
  if (!Array.isArray(objectCodes) || !objectCodes.length) {
    throw new Error("必须传入 objectCodes 数组");
  }

  const { data, code } = await axios.post(apiStore.common.queryDetails.url, { enable: true, ...payload });
  if (code) {
    throw new Error(code);
  }

  // 遍历每个字典 code，组装成 { hitStrategy_dict: [...] } 形式
  const result = {};
  objectCodes.forEach((codeName) => {
    const dict = data[codeName] || {};
    let details = [];
    try {
      details = dict.details;
    } catch (e) {
      console.warn(`${codeName} 字典数据缺失`, e);
    }
    const options = myTransform.arrToOptions(details, "fieldCode", "fieldValue").map((c) => ({
      value: c.value,
      label: window.vm.$t(c.label), // 或直接传入 i18n 实例翻译
    }));
    result[`${codeName}_dict`] = options;
  });

  return result;
}
