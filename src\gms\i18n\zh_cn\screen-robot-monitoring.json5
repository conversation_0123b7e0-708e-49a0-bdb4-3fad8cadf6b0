/**
 *   支架页面国际化字符集
 *   key 必须以 'geekplus.gms.client.screen.robotMonitoring.' 开头
 */
{
  "lang.ark.fed.screen.robotMonitoring.robotList": "机器人列表",
  "lang.ark.fed.screen.robotMonitoring.robotTask": "机器人任务",
  "geekplus.gms.client.screen.robotMonitoring.filters.robotTaskId": "请输入机器人任务号",
  "geekplus.gms.client.screen.robotMonitoring.filters.taskType": "任务类型",
  "geekplus.gms.client.screen.robotMonitoring.filters.createTime": "创建时间",
  "geekplus.gms.client.screen.robotMonitoring.filters.robotId": "机器人",
  "geekplus.gms.client.screen.robotMonitoring.filters.locationEntity.placeholder": "起点/终点",
  "geekplus.gms.client.screen.robotMonitoring.filters.createTime": "创建时间",
  "geekplus.gms.client.screen.robotMonitoring.filters.loadCode": "容器编码",
  "geekplus.gms.client.screen.robotMonitoring.filters.status": "状态",

  "geekplus.gms.client.screen.robotMonitoring.columns.task": "机器人任务",
  "geekplus.gms.client.screen.robotMonitoring.columns.robotNum": "机器人",
  "geekplus.gms.client.screen.robotMonitoring.columns.loadCode": "容器编码",
  "geekplus.gms.client.screen.robotMonitoring.columns.currentStage": "当前阶段",
  "geekplus.gms.client.screen.robotMonitoring.columns.instruction": "执行指令",
  "geekplus.gms.client.screen.robotMonitoring.columns.exceptionDesc": "异常描述",

  "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.taskType": "任务类型",
  "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.robotId": "机器人",
  "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.createTime": "创建时间",
  "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.start": "起点",
  "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.loadCode": "容器编码",
  "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.updateTime": "更新时间",
  "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.end": "终点",
  "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.priority": "优先级",
  "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.elapsedTime": "任务耗时",
  "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.currentStage": "当前阶段",
  "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.exceptionDesc": "异常描述",

  "geekplus.gms.client.screen.robotMonitoring.stat.all": "全部",
  "geekplus.gms.client.screen.robotMonitoring.stat.create": "创建",
  "geekplus.gms.client.screen.robotMonitoring.stat.allocation": "已分配",
  "geekplus.gms.client.screen.robotMonitoring.stat.executing": "执行中",
  "geekplus.gms.client.screen.robotMonitoring.stat.complete": "已完成",
  "geekplus.gms.client.screen.robotMonitoring.stat.cancel": "已取消",
  "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.taskId.label": "机器人任务号",
  "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.taskType.label": "任务类型",
  "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.robotId.label": "机器人",
  "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.createTime.label": "创建时间",
  "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.start.label": "起点",
  "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.loadCode.label": "容器编码",
  "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.updateTime.label": "更新时间",
  "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.end.label": "终点",
  "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.priority.label": "优先级",
  "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.elapsedTime.label": "任务耗时",
  "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.currentStage.label": "当前阶段",
  "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.exceptionDesc.label": "异常描述",

  "geekplus.gms.client.screen.robotMonitoring.detail.taskProcess.contentFilter": "内容筛选",
  "geekplus.gms.client.screen.robotMonitoring.detail.taskProcess.tabs.process": "执行过程",
  "geekplus.gms.client.screen.robotMonitoring.detail.taskProcess.tabs.log": "操作日志",
  "geekplus.gms.client.screen.robotMonitoring.detail.taskProcess.tabs.process.updateTask": "更新任务",

  "geekplus.gms.client.screen.robotMonitoring.systemError.confirmToCancelTask": "确定要取消当前任务？",
  "geekplus.gms.client.screen.robotMonitoring.systemError.forceCancel.cancelFailed": "取消失败",
  "geekplus.gms.client.screen.robotMonitoring.systemError.taskCanceling": "任务取消中，请稍后...",
  "geekplus.gms.client.screen.robotMonitoring.systemError.forceCancel.cancelTip": "强制取消后，需要按以下步骤处理：",
  "geekplus.gms.client.screen.robotMonitoring.systemError.forceCancel.cancelStep1": "1、检查机器人，如有货物请先处理，确保无掉料、撞料风险",
  "geekplus.gms.client.screen.robotMonitoring.systemError.forceCancel.cancelStep2": "2、解锁机器人",
  "geekplus.gms.client.screen.robotMonitoring.systemError.forceCancel.cancelTitle": "机器人任务取消失败或超时，您可以强制取消",
  "geekplus.gms.client.screen.robotMonitoring.systemError.cancelTaskTip": "任务取消后不可恢复，请谨慎操作",
  "geekplus.gms.client.screen.robotMonitoring.systemError.cancelSuccess": "机器人任务已取消",

  "geekplus.gms.client.screen.robotMonitoring.operationLog.cancel": "取消任务",
  "geekplus.gms.client.screen.robotMonitoring.operationLog.forceCancel": "强制取消任务",

  "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStatus.all": "全部",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStatus.create": "创建",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStatus.allocating": "已分配",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStatus.executing": "执行中",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStatus.completed": "已完成",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStatus.canceled": "已取消",

  "geekplus.gms.client.screen.robotMonitoring.constants.rmsRobotType.goSomewhereToStay": "机器人移动",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsRobotType.goMaintainToStay": "维修任务",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsRobotType.goChargeYourself": "充电任务",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsRobotType.moveShelf": "货架移动",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsRobotType.deliverShelf": "货架搬运",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsRobotType.goWork": "复合机器人任务",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsRobotType.carryPackage": "辊筒机器人任务",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsRobotType.deliverPallet": "叉车托盘任务",

  "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.goFetching": "正在取货架",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.shelfFetched": "货架已经取到",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.goDelivering": "正在搬运货架",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.queuing": "正在排队",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.shelfArrived": "货架已经抵达目的地",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.goReturn": "货架归还中",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.shelfTurning": "货架旋转",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.moving": "正在移动",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.charging": "正在充电",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.arrivedReceive": "滚筒机器人到达取货位,准备取货",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.arrivedDrop": "滚筒机器人到达卸货位,准备卸货",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.arrivedWaitPoint": "到达等待点",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.leavingWaitPoint": "离开等待点",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.receiveFinish": "滚筒机器人收货完成",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.dropFinish": "滚筒机器人卸货完成",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.boxFetched": "取箱子完成",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.boxArrived": "送箱子到达",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.arrivedElevatorEntry": "到达电梯门口的等待点(电梯外),等待进入电梯",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.enteredElevator": "已经进入电梯",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.leavedElevator": "离开电梯",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.arrived": "到达目的地",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.fetched": "任务已经完成",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.robotDelivering": "四向车业务线中机器人正在搬运容器",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.deviceDelivering": "四向车业务线中设备正在搬运容器",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.containerArrived": "四向车业务线中容器到达目标位置",

  "geekplus.gms.client.screen.robotMonitoring.constants.rmsExecutionType.create": "任务创建",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsExecutionType.allocate": "任务分配",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsExecutionType.execute": "任务执行",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsExecutionType.update": "任务更新",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsExecutionType.finish": "任务结束",

  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12000": "机器人无法链接",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12001": "子任务发送超时",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12002": "规划路径失败",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12003": "机器人不在地图",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12004": "规划路径的起点和终点是障碍",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12005": "途中有障碍机器人或障碍货架",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12006": "此时的路径资源正被其他机器人占据",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12007": "无路径可以规划",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12203": "规划路径不合理",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12036": "有单元格高度不满足，不能通过",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12008": "任务异常",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12009": "取货架超时",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12010": "未到达货架位置取货架",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12011": "长时间未到达等待点",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12012": "机器人充电过程中长时间电量不上涨",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12013": "机器人达到低电比",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12014": "没有匹配到充电站",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12015": "没有空闲充电站",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12016": "充电站不可用",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12017": "充电站单元格被其他机器人占用",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12018": "无法跨区域充电",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12019": "无法跨楼层充电",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12020": "任务阻塞，无法执行充电任务",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12021": "任务未完成，无法执行充电任务",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12022": "充电时间过长",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12023": "机器人电池过温",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12024": "充电完成没有找到停靠点",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12025": "机器人锁定",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12026": "机器人电池低温",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12038": "连续充电失败次数过多",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12027": "机器人路径调度停止",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12028": "机器人超过路径调度最远分配点限制",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12029": "机器人路径调度调度途中有障碍物",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12030": "机器人路径调度调度死锁避让",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12031": "机器人路径调度机器人当前位置不在规划路径上",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12032": "二维码机器人无法运行在slam路径上",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12033": "机器人处在没有出度的节点上",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12034": "机器人正在被强制操作",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12035": "外部货架未生成任务",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12101": "机器人处于死锁环中",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12102": "机器人长时间未动",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12103": "机器人碰撞",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12104": "机器人处于急停状态",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12105": "机器人处于暂停状态",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12106": "机器人需要重启",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12039": "系统程序异常导致的机器人调度异常",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12040": "高可用切换时的路径调度恢复异常",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12107": "robot#jobIds存在warehouse里找不到相应job",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12108": "job#stageIds存在warehouse里找不到相应的stage",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12109": "robot#taskIds存在warehouse里找不到相应的task",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12110": "robot#exeTaskIds存在warehouse里找不到相应的exeTask",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12111": "机器人到达终点二维码（前后+角度）调整后，左右偏差过大",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12113": "机器人楼层状态未确认",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12112": "机器人楼层状态确认异常",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12114": "robot task 长时间未完成",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12201": "机器人识别到终点有托盘",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12202": "机器人识别到起点没有托盘",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12301": "机器人任务恢复失败",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12302": "机器人任务预占托盘数据丢失",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.3004": "货箱任务要去取箱时检测到抱叉上已经有箱子",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.3005": "货箱任务去取箱时检测到货架内无箱子",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.3006": "货箱任务还箱时检测到货架上或背篓上已经有箱子了",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.3007": "货箱任务还箱时检测到托盘上面没有箱子",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.21602": "模型下发失败",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.3008": "货箱任务要取内箱的时候发现外部货位有障碍物",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.21603": "货架严重超载",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.21604": "货架严重偏载",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.3009": "长时间识别不了货位二维码",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.3037": "取箱时检测到可恢复异常",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.3038": "还箱时检测到可恢复异常",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.10020": "机器人发生异常累计次数达到上限，停止作业",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.23113": "取箱失败",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.23114": "还箱失败",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.23115": "上限位触发",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.23116": "伸缩叉两边不同步",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.23117": "机器人角度超限",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.23118": "终点校准未识别到地面二维码",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.23119": "终点校准多次失败",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.23120": "抱叉内有异物",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.23121": "校验失败返回: 货箱不在PopPick工作站货位无法完成任务",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.23122": "工作站当前业务模式不支持还箱，请先切换成综合模式再归还",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.23123": "查询该货箱有没有关联的未完成的任务，如果有，则返回前端: 货箱有关联任务未完成",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.32032": "不允许货架进入工作品",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.32033": "PopPick 切换手动模式",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.32034": "PopPick 硬件旋坏",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.23124": "机器人停止模式下编码器角度变化检测。如: 机器人停止状态下被外力转动则可能报错",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.32035": "单次事务巡检货架数量不可大于5000",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.32036": "巡检货架{0},类型不是PopPick货架",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.32037": "工作站{0}，处于停用状态",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.32038": "工作站{0}，不是PopPick工作站",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.23125": "机器人任务分配暂停异常码",
  "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.3039": "货箱任务还箱时检测到背篓上已经有箱子",
}
