<template>
  <div class="tw-flex tw-justify-center tw-items-center tw-h-full">
    <div v-if="!isNoStandard" class="tw-relative tw-flex tw-justify-center tw-items-center">
      <img :src="shelfImg" :class="cn.img" alt="" />
      <HighLightBox
        :styles="{
          top: '9px',
          left: '25px',
          width: '155px',
          height: '40px',
          borderDirection: 'borderBottom',
          display: 'flex',
          transform: 'rotate(-28deg)',
          justifyContent: 'center',
        }"
        :value="surfaceWidth"
        :is-active="$props.active === 'surfaceWidth'"
      />
      <!-- $props.active === 'surfaceWidth' -->
      <HighLightBox
        :styles="{
          top: '-18px',
          left: '202px',
          width: '188px',
          height: '40px',
          borderDirection: 'borderBottom',
          display: 'flex',
          justifyContent: 'center',
          transform: 'rotate(9deg)',
        }"
        :is-active="$props.active === 'surfaceLength'"
        :value="surfaceLength"
      />
      <!-- $props.active === 'surfaceLength' -->
      <HighLightBox
        :styles="{
          bottom: '214px',
          left: '-164px',
          width: '356px',
          height: '36px',
          transform: 'rotate(-90deg)',
          borderDirection: 'borderBottom',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          paddingLeft: '50px',
        }"
        :is-active="$props.active === 'surfaceHeight'"
        :value="surfaceHeight"
      />
      <!-- $props.active === 'surfaceHeight' -->
      <HighLightBox
        :styles="{
          bottom: '-18px',
          left: '224px',
          width: '14px',
          height: '34px',
          borderDirection: 'borderTop',
          transform: 'rotate(16deg)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'flex-end',
        }"
        :is-active="$props.active === 'legLength'"
        :value="legLength"
      />
      <!-- $props.active === 'legLength' -->
      <HighLightBox
        :styles="{
          bottom: '-14px',
          left: '250px',
          width: '14px',
          height: '36px',
          borderDirection: 'borderTop',
          transform: 'rotate(-33deg)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'flex-end',
        }"
        :is-active="$props.active === 'legWidth'"
        :value="legWidth"
      />
      <!-- $props.active === 'legWidth' -->
      <HighLightBox
        :styles="{
          bottom: '20px',
          left: '255px',
          width: '36px',
          height: '36px',
          borderDirection: 'borderBottom',
          transform: 'rotate(90deg)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }"
        :is-active="$props.active === 'legHeight'"
        :value="legHeight"
      />
      <!-- $props.active === 'legHeight' -->
    </div>
    <NoStandardAnnotation
      v-if="isNoStandard"
      v-bind="$props"
      @update:active="$emit('update:active', $event)"
      @change="$emit('update:formValues', $event)"
    />
  </div>
</template>
<script>
import { defineComponent, computed } from "vue";
import { containerModel } from "gms-constants";
import getContainerImage from "@/utils/containerImages";
const shelfImg = getContainerImage("roll-car.svg");
import NoStandardAnnotation from "./no-standard-annotation.vue";
import HighLightBox from "./highligt-box.vue";

export default defineComponent({
  name: "CabinetsAnnotation",
  components: { NoStandardAnnotation, HighLightBox },
  props: {
    formValues: { type: Object, default: () => ({}) },
    active: { type: String, default: "" },
  },
  emits: ["update:active", "update:formValues"],
  setup(props) {
    const isNoStandard = computed(() => props.formValues.modelingMethod === containerModel.NON_STANDARD);
    const surfaceWidth = computed(() => props.formValues.modelInfo.surfaceWidth);
    const surfaceLength = computed(() => props.formValues.modelInfo.surfaceLength);
    const surfaceHeight = computed(() => props.formValues.modelInfo.surfaceHeight);
    const legLength = computed(() => props.formValues.modelInfo.legLength);
    const legWidth = computed(() => props.formValues.modelInfo.legWidth);
    const legHeight = computed(() => props.formValues.modelInfo.legHeight);

    return { isNoStandard, shelfImg, surfaceWidth, surfaceLength, surfaceHeight, legLength, legWidth, legHeight };
  },
});
</script>
<style lang="scss" module="cn">
.img {
  height: 500px;
}
</style>
