{
  "geekplus.gms.client.menus.dashboard": "Overview",
  "geekplus.gms.client.menus.modeling": "Setup",
  "geekplus.gms.client.menus.operating": "Operation",
  "geekplus.gms.client.menus.map": "Site",
  "geekplus.gms.client.menus.mapConfig": "Map Settings",
  "geekplus.gms.client.menus.areaConfig": "Area Settings",
  "geekplus.gms.client.menus.stockConfig": "Pallet Position Settings",
  "geekplus.gms.client.menus.waitNodeConfig": "Waiting Position Settings",
  "geekplus.gms.client.menus.workstations": "Workstations",
  "geekplus.gms.client.menus.robot": "AGV/AMRs",
  "geekplus.gms.client.menus.robotConfig": "AGV/AMR Settings",
  "geekplus.gms.client.menus.compositeRobotConfig": "复合机器人配置",
  "geekplus.gms.client.menus.robotParams": "机器人参数",
  "geekplus.gms.client.menus.robotStrategy": "机器人策略",
  "geekplus.gms.client.menus.robotSoftwareVersion": "软件版本",
  "geekplus.gms.client.menus.robotUpgradeLog": "升级日志",
  "geekplus.gms.client.menus.container": "Load Carriers",
  "geekplus.gms.client.menus.scaffold": "Racks",
  "geekplus.gms.client.menus.scaffoldModeling": "Rack Modeling",
  "geekplus.gms.client.menus.material": "Materials",
  "geekplus.gms.client.menus.equipment": "Devices",
  "geekplus.gms.client.menus.equipmentModeling": "Device Modeling",
  "geekplus.gms.client.menus.equipmentInstance": "Device Instances",
  "geekplus.gms.client.menus.elevatorBind": "提升机关联信息",
  "geekplus.gms.client.menus.templateInstance": "设备实例模板",
  "geekplus.gms.client.menus.taskManagement": "设备任务",
  "geekplus.gms.client.menus.appMaintenance": "应用维护",
  "geekplus.gms.client.menus.heartbeatManagement": "设备心跳",
  "geekplus.gms.client.menus.pdaConfig": "PDA配置",
  "geekplus.gms.client.menus.callmakerConfig": "呼叫器配置",
  "geekplus.gms.client.menus.buttonModel": "按钮模型配置",
  "geekplus.gms.client.menus.buttonFunction": "按钮功能配置",
  "geekplus.gms.client.menus.process": "Workflows",
  "geekplus.gms.client.menus.processConfig": "流程配置(新)",
  "geekplus.gms.client.menus.processNodeConfig": "交互配置",
  "geekplus.gms.client.menus.processRuleConfig": "规则配置",
  "geekplus.gms.client.menus.processTemplate": "流程模板",
  "geekplus.gms.client.menus.processConfigOld": "流程",
  "geekplus.gms.client.menus.processCategory": "流程分类",
  "geekplus.gms.client.menus.interface": "System APIs",
  "geekplus.gms.client.menus.callbackConfig": "API Callback Management",
  "geekplus.gms.client.menus.interfaceAdapt": "Adaptive API Settings",
  "geekplus.gms.client.menus.systemConfig": "System Settings",
  "geekplus.gms.client.menus.systemParamsConfig": "系统参数配置",
  "geekplus.gms.client.menus.dispatchParamsConfig": "Scheduling Settings",
  "geekplus.gms.client.menus.sensorTriggerTask": "传感器触发任务",
  "geekplus.gms.client.menus.licenseManagement": "证书管理",
  "geekplus.gms.client.menus.intervalTaskConfig": "定时任务配置",
  "geekplus.gms.client.menus.personalizedConfig": "系统个性化配置",
  "geekplus.gms.client.menus.monitoring": "Monitoring",
  "geekplus.gms.client.menus.taskDashboard": "Job Insight",
  "geekplus.gms.client.menus.siteMonitoring": "Site Monitoring",
  "geekplus.gms.client.menus.taskMonitoring": "Job Monitoring",
  "geekplus.gms.client.menus.limitingAreaMonitoring": "Traffic Area Monitoring",
  "geekplus.gms.client.menus.robotMonitoring": "AGV/AMR Monitoring",
  "geekplus.gms.client.menus.chargeStationMonitoring": "AGV/AMR Charger Monitoring",
  "geekplus.gms.client.menus.log": "Logs",
  "geekplus.gms.client.menus.taskLog": "Job Execution Logs",
  "geekplus.gms.client.menus.operationLog": "User Operation Logs",
  "geekplus.gms.client.menus.interfaceLog": "API Calling Logs",
  "geekplus.gms.client.menus.containerLog": "Load Carrier Usage Logs",
  "geekplus.gms.client.menus.triggerLog": "Job Trigger Logs",
  "geekplus.gms.client.menus.callmakerLog": "AGV/AMR Caller Logs",
  "geekplus.gms.client.menus.permission": "Access Management",
  "geekplus.gms.client.menus.userManagement": "User Management",
  "geekplus.gms.client.menus.roleManagement": "Role Management",
  "geekplus.gms.client.menus.settings.account": "Account  Management",
  "geekplus.gms.client.menus.settings.languages": "Change Screen Language",
  "geekplus.gms.client.menus.settings.themes": "Change Theme",
  "geekplus.gms.client.menus.settings.logout": "Logout",
  "geekplus.gms.client.menus.language.zh-cn": "Chinese Simplified",
  "geekplus.gms.client.menus.language.en-us": "English (General)",
  "geekplus.gms.client.menus.theme.system": "System",
  "geekplus.gms.client.menus.theme.light": "Light",
  "geekplus.gms.client.menus.theme.dark": "Dark",
  "geekplus.gms.client.menus.downloads.tip": "Scan to download",
  "geekplus.gms.client.menus.downloads.document": "Help Documentation",
  "geekplus.gms.client.menus.downloads.operation": "GMS Manuals",
  "geekplus.gms.client.menus.downloads.interface": "GMS API Guide",
  "geekplus.gms.client.menus.downloads.PDADocument": "GMS PDA App Manual",
  "geekplus.gms.client.menus.downloads.downloadApp": "Download App",
  "geekplus.gms.client.menus.downloads.PDA": "PDA App",
  "geekplus.gms.client.menus.downloads.deviceKeyValue": "Code table (device instructions)",
  "geekplus.gms.client.menus.downloads.device": "Equipment code chart",
  "geekplus.gms.client.menus.stopControllerManage": "Emergency stop controller",
  "geekplus.gms.client.menus.schedule": "Dispatch",
  "geekplus.gms.client.menus.scheduleParamConfig": "Scheduling Parameter Configuration",
  "geekplus.gms.client.menus.regionalScheduleManage": "Partition scheduling management",
  "geekplus.gms.client.menus.deviceTaskMonitoring": "Device task monitoring",
  "geekplus.gms.client.menus.maintenanceSchedule": "Maintenance Schedule",
  "geekplus.gms.client.menus.downloads.aboutGMS": "About GMS",
  "geekplus.gms.client.menus.apiDoc": "Interface documentation and debugging",
  "geekplus.gms.client.menus.interfaceConfig": "Interface parameter configuration",
}
