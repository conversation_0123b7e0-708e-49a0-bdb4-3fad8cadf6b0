<template>
  <div>
    <section v-if="isStandard" class="tw-flex tw-gap-4 tw-flex-col tw-mt-6">
      <h3 class="tw-relative tw-font-bold tw-text-l">
        {{ $t("geekplus.gms.client.screen.container.form.modelInformation") }}
      </h3>
      <div class="tw-flex tw-justify-between tw-items-center tw-gap-2 tw--mt-4">
        <form-item v-bind="formGroup.modelingMethod" />
        <gp-button type="text" @click="exampleVisible = true">{{
          $t("geekplus.gms.client.screen.container.form.checkExample")
        }}</gp-button>
      </div>
      <div class="tw-border tw-border-solid tw-border-gray-300 tw-p-2 tw-mb-4 tw-rounded">
        <h3>{{ $t("geekplus.gms.client.screen.container.form.containerSize") }}</h3>
        <div class="tw-flex tw-flex-col" :class="viewClass">
          <div class="tw-flex tw-items-center tw-gap-2">
            <form-item v-bind="formGroup.surfaceLength" />
            <span>mm</span>
          </div>
          <div class="tw-flex tw-items-center tw-gap-2">
            <form-item v-bind="formGroup.surfaceWidth" />
            <span>mm</span>
          </div>
          <div class="tw-flex tw-items-center tw-gap-2">
            <form-item v-bind="formGroup.surfaceHeight" />
            <span>mm</span>
          </div>
        </div>
      </div>
      <div class="tw-border tw-border-solid tw-border-gray-300 tw-p-2 tw-mb-4 tw-rounded">
        <h3>{{ $t("geekplus.gms.client.screen.container.form.containerLeg") }}</h3>
        <div class="tw-flex tw-flex-col" :class="viewClass">
          <div class="tw-flex tw-items-center tw-gap-2">
            <form-item v-bind="formGroup.legLength" />
            <span>mm</span>
          </div>
          <div class="tw-flex tw-items-center tw-gap-2">
            <form-item v-bind="formGroup.legWidth" />
            <span>mm</span>
          </div>
          <div class="tw-flex tw-items-center tw-gap-2">
            <form-item v-bind="formGroup.legHeight" />
            <span>mm</span>
          </div>
        </div>
      </div>
    </section>
    <NoStandardModel
      v-if="!isStandard"
      :form-group="$props.formGroup"
      :form-values="$props.formValues"
      :form-group-list="$props.formGroupList"
      :active="$props.active"
      :mode="$props.mode"
      @update:formGroupList="$emit('update:formGroupList', $event)"
      @update:formValues="$emit('update:formValues', $event)"
      @update:active="$emit('update:active', $event)"
    />
    <ExampleModeExplain :visible.sync="exampleVisible" />
  </div>
</template>
<script>
import { defineComponent, computed, ref } from "vue";
import { FormItem } from "@srpe-fe/uic-el-form-wrapper-vue2";
import { containerModel } from "gms-constants";
import ExampleModeExplain from "../../example-model-explain.vue";
import NoStandardModel from "./no-standard-model";

export default defineComponent({
  name: "NoPalletModel",
  components: { NoStandardModel, FormItem, ExampleModeExplain },
  props: {
    formGroup: { type: Object, default: () => ({}) },
    formValues: { type: Object, default: () => ({}) },
    formGroupList: { type: Array, default: () => [] },
    active: { type: String, default: "" },
    mode: { type: String, default: "add" },
  },
  emit: ["update:formValues"],
  setup(props) {
    const exampleVisible = ref(false);
    const isStandard = computed(() => props.formValues.modelingMethod === containerModel.STANDARD);
    const viewClass = computed(() => [props.mode !== "view" ? "tw-gap-4" : ""]);
    return { viewClass, exampleVisible, isStandard };
  },
});
</script>
