<template>
  <section class="tw-flex tw-flex-col tw-mt-4" :class="[gapClass]">
    <h3 class="tw-relative tw-font-bold tw-text-l tw-mt-4">
      {{ $t("geekplus.gms.client.screen.container.form.modelInformation") }}
    </h3>
    <div class="tw-flex tw-items-center tw-gap-1">
      <form-item v-bind="$props.formGroup.modelingMethod">
        <template #labelHelp>
          <h3>{{ $t("geekplus.gms.client.commons.constants.palletSpeciType.standard") }}</h3>
          <ul class="tw-leading-normal">
            <li>{{ $t("geekplus.gms.client.screen.container.tips.specification.face") }}</li>
            <li>{{ $t("geekplus.gms.client.screen.container.tips.specification.material") }}</li>
            <li>{{ $t("geekplus.gms.client.screen.container.tips.specification.color") }}</li>
            <li>
              {{ $t("geekplus.gms.client.screen.container.tips.specification.pillarSize") }}
              <ul :style="{ textIndent: '2em' }">
                <li>{{ $t("geekplus.gms.client.screen.container.tips.specification.palletModel1") }}</li>
                <li>{{ $t("geekplus.gms.client.screen.container.tips.specification.palletModel2") }}</li>
              </ul>
            </li>
          </ul>
          <h3 class="tw-mt-2">{{ $t("geekplus.gms.client.commons.constants.palletSpeciType.special") }}</h3>
          <ul class="tw-leading-normal">
            <li>{{ $t("geekplus.gms.client.screen.container.tips.specification.special.usageCases") }}</li>
            <li>{{ $t("geekplus.gms.client.screen.container.tips.specification.face") }}</li>
            <li>{{ $t("geekplus.gms.client.screen.container.tips.specification.special.material") }}</li>
            <li>{{ $t("geekplus.gms.client.screen.container.tips.specification.special.color") }}</li>
            <li>
              {{ $t("geekplus.gms.client.screen.container.tips.specification.pillarSize") }}
              <ul :style="{ textIndent: '2em' }">
                <li>{{ $t("geekplus.gms.client.screen.container.tips.specification.special.palletModel1") }}</li>
                <li>{{ $t("geekplus.gms.client.screen.container.tips.specification.special.palletModel2") }}</li>
              </ul>
            </li>
          </ul>
        </template>
      </form-item>
    </div>
    <div class="tw-flex tw-items-center">
      <form-item v-bind="$props.formGroup.palletStructure" />
    </div>
    <div class="tw-flex tw-items-center tw-gap-1">
      <form-item v-bind="$props.formGroup.handleableRobotModel" />
    </div>
    <div class="tw-flex tw-items-center tw-gap-1">
      <form-item v-bind="$props.formGroup.detectionDistance" />
    </div>

    <template v-if="$props.formValues.modelInfo.palletStructure">
      <form-item v-bind="$props.formGroup.materialType">
        <template #edit="scope">
          <MSelect
            :value="scope.value"
            :disabled="$attrs.mode === 'view'"
            size="medium"
            :options="getMaterialOptions($props.formValues.modelInfo.modelingMethod)"
            @change="handleChangeCustomized(scope, 'materialType', $event)"
          />
        </template>
        <template #view="scope">{{ palletMaterialType.getLabelByValue(scope.value) }}</template>
      </form-item>
      <form-item v-bind="$props.formGroup.surfaceColor">
        <template #edit="scope">
          <MSelect
            :value="scope.value"
            :disabled="$attrs.mode === 'view'"
            size="medium"
            :options="getPalletColorOptions($props.formValues.modelInfo.modelingMethod)"
            @change="handleChangeCustomized(scope, 'surfaceColor', $event)"
          />
        </template>
        <template #view="scope">{{ palletColorType.getLabelByValue(scope.value) }}</template>
      </form-item>
      <!-- </div> -->

      <div class="tw-flex tw-items-center tw-gap-2">
        <form-item v-bind="$props.formGroup.surfaceLength" />
        <span>mm</span>
      </div>
      <div class="tw-flex tw-items-center tw-gap-2">
        <form-item v-bind="$props.formGroup.surfaceWidth" />
        <span>mm</span>
      </div>
      <div class="tw-flex tw-items-center tw-gap-2">
        <form-item v-bind="$props.formGroup.edgePillar" />
        <span>mm</span>
      </div>
      <div v-if="isDoubleHole" class="tw-flex tw-items-center tw-gap-2">
        <form-item v-bind="$props.formGroup.centerPillar" />
        <span>mm</span>
      </div>
      <div class="tw-flex tw-items-center tw-gap-2">
        <form-item v-bind="$props.formGroup.holeHeight" />
        <span>mm</span>
      </div>
      <div class="tw-flex tw-items-center tw-gap-2">
        <form-item v-bind="$props.formGroup.holeWidth" />
        <span>mm</span>
      </div>
    </template>
  </section>
</template>
<script>
import { cloneDeep } from "lodash";
import { computed, defineComponent } from "vue";
import { FormItem } from "@srpe-fe/uic-el-form-wrapper-vue2";
import { palletSpeciType, palletStructureType, palletMaterialType, palletColorType } from "gms-constants";
import MSelect from "@/gms/screens/container/components/m-select.vue";

const getMaterialOptions = (specification) => {
  const options = palletMaterialType.toLabelValueList();
  return specification === palletSpeciType.STANDARD
    ? options.filter((v) => v.value !== palletMaterialType.METAL)
    : options;
};

const getPalletColorOptions = (specification) => {
  const options = palletColorType.toLabelValueList();
  return specification === palletSpeciType.STANDARD ? options.filter((v) => v.value !== palletColorType.GRAY) : options;
};
export default defineComponent({
  name: "PalletModel",
  components: { FormItem, MSelect },
  props: {
    formValues: { type: Object, default: () => ({}) },
    formGroup: { type: Object, default: () => ({}) },
    mode: { type: String, default: "add" },
  },
  setup(props, { emit }) {
    const isSpecial = computed(() => props.formValues.modelInfo.modelingMethod === palletSpeciType.SPECIAL);
    const isDoubleHole = computed(() => props.formValues.modelInfo.palletStructure === palletStructureType.DOUBLE);
    const gapClass = computed(() => (props.mode !== "view" ? "tw-gap-4" : ""));
    const handleChangeCustomized = (scope, key, value) => {
      scope.handleChange(value);
      const formValues = cloneDeep(props.formValues);
      emit("update:formValues", { ...formValues, modelInfo: { ...formValues.modelInfo, [key]: value } });
    };

    return {
      props,
      isSpecial,
      isDoubleHole,
      gapClass,
      getMaterialOptions,
      getPalletColorOptions,
      handleChangeCustomized,
      palletMaterialType,
      palletColorType,
    };
  },
});
</script>
