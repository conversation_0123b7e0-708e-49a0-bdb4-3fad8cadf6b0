/**
 * 托盘架模型页面国际化字符集
 * key 必须以 'geekplus.gms.client.screen.palletModel.' 开头
 */
 {
  "geekplus.gms.client.screen.palletModel.palletModelCode": "托盘架模型编码",
  "geekplus.gms.client.screen.palletModel.palletModelName": "托盘架模型名称",
  "geekplus.gms.client.screen.palletModel.palletModelType": "托盘架模型类型",
  "geekplus.gms.client.screen.palletModel.containerType": "容器类别",
  "geekplus.gms.client.screen.palletModel.numOfTiers": "层数",
  "geekplus.gms.client.screen.palletModel.addPalletModel": "新增托盘架模型",
  "geekplus.gms.client.screen.palletModel.editPalletModel": "编辑托盘架模型",
  "geekplus.gms.client.screen.palletModel.viewPalletModel": "查看托盘架模型",
  "geekplus.gms.client.screen.palletModel.movable": "支持移动",
  "geekplus.gms.client.screen.palletModel.canMove": "可移动",
  "geekplus.gms.client.screen.palletModel.cannotMove": "不可移动",
}
