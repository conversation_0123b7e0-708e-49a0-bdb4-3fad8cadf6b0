{
  "geekplus.gms.client.screen.bracket.btns.addBracket": "New bracket",
  "geekplus.gms.client.screen.bracket.btns.addBracketModel": "New bracket model",
  "geekplus.gms.client.screen.bracket.columns.containerShape": "Bracket form",
  "geekplus.gms.client.screen.bracket.columns.containerName": "Bracket name",
  "geekplus.gms.client.screen.bracket.columns.containerModel": "Bracket type",
  "geekplus.gms.client.screen.bracket.columns.mapCode": "Associated map",
  "geekplus.gms.client.screen.bracket.columns.cellCode": "Associated position",
  "geekplus.gms.client.screen.bracket.tips.notAllowDelete": "Cannot be deleted",
  "geekplus.gms.client.screen.bracket.tips.notAllowDeleteMsg": "The current model is associated with the point information and cannot be deleted. Disassociate the point first before deleting the model.",
  "geekplus.gms.client.screen.bracket.form.bracketTitle": "Bracket",
  "geekplus.gms.client.screen.bracket.form.bracketSubTitle": "The bracket is a support structure on the ground to ensure that the robot can accurately calculate the position and stably pick and place goods.",
  "geekplus.gms.client.screen.bracket.form.mapName": "Map Name",
  "geekplus.gms.client.screen.bracket.form.cellPoints": "Location",
  "geekplus.gms.client.screen.bracket.form.bracketCenter": "Bracket center",
  "geekplus.gms.client.screen.bracket.form.bracketElements": "Bracket elements",
  "geekplus.gms.client.screen.bracket.form.bracketSize": "Bracket dimensions",
  "geekplus.gms.client.screen.bracket.form.bracketFaceLength": "Bracket length",
  "geekplus.gms.client.screen.bracket.form.bracketFaceWidth": "Bracket width",
  "geekplus.gms.client.screen.bracket.form.bracketLeg": "Bracket leg",
  "geekplus.gms.client.screen.bracket.form.offset": "Offset of pallet position",
  "geekplus.gms.client.screen.bracket.form.offsetY": "Y-axis offset",
  "geekplus.gms.client.screen.bracket.example.groundBracketExplain": "Ground bracket: immovable bracket fixed on the ground.",
  "geekplus.gms.client.screen.bracket.example.groundBracketDiagram": "Example of ground bracket:",
  "geekplus.gms.client.screen.bracket.example.groundBracketModel": "The ground bracket is mainly configured with two parts: side and legs. The configured ground bracket model shall be associated with the point cell in map editing.",
  "geekplus.gms.client.screen.bracket.example.groundBracketModelFace": "Bracket side: mainly for PNC path planning",
  "geekplus.gms.client.screen.bracket.example.groundBracketModelLeg": "Bracket leg: mainly for recognition for docking with the robot",
  "geekplus.gms.client.screen.bracket.example.standardBracketModel": "[Standard ground bracket]",
  "geekplus.gms.client.screen.bracket.example.standardBracketExplain": "Definition: The bracket showing side and four legs in regular rectangle from its top view is called standard ground bracket.",
  "geekplus.gms.client.screen.bracket.example.specialBracketModel": "[Special-shaped ground bracket]",
  "geekplus.gms.client.screen.bracket.example.specialBracketExplain": "Definition: The bracket showing irregular rectangular side or with more than four legs from its top view is called special-shaped ground bracket. For such bracket, the leg coordinates from the center point need to be measured.",
  "geekplus.gms.client.screen.bracket.example.specialBracketDiagram": "Example:",
  "geekplus.gms.client.screen.bracket.example.special.bracketExplain": "I. Find the bracket side and legs with reference to the actual bracket, as shown in the figure:",
  "geekplus.gms.client.screen.bracket.example.special.tips": "Precautions:",
  "geekplus.gms.client.screen.bracket.example.special.tips1": "1. Bracket side: It is used for obstacle detection and path planning of the robot and needs to be measured.",
  "geekplus.gms.client.screen.bracket.example.special.tips2": "2. Bracket leg: All bracket legs that can be scanned at laser height shall be configured for bracket recognition for docking with the robot before entry.",
  "geekplus.gms.client.screen.bracket.example.special.tips3": "3. For irregular rectangular side and legs, the dimensions of the maximum rectangular surface of the side and legs shall prevail.",
  "geekplus.gms.client.screen.bracket.example.special.configExplain": "II. Configuration description of side and leg",
  "geekplus.gms.client.screen.bracket.example.special.configExplainDetail": "Measure leg-side margins 1 and 2 in mm.",
  "geekplus.gms.client.screen.bracket.tips.palletModel": "For vehicle models that handle pallets, the requirement of the minimum column width is different.",
  "geekplus.gms.client.screen.bracket.tips.recognizableDistance": "The distance for robot laser to recognize the pallet is mainly used for algorithm R&D.",
  "geekplus.gms.client.screen.bracket.tips.specification.face": "Pallet side: no limit for both the length and width",
  "geekplus.gms.client.screen.bracket.tips.specification.material": "Material: wooden/plastic/others",
  "geekplus.gms.client.screen.bracket.tips.specification.color": "Color: wood color/blue/others",
  "geekplus.gms.client.screen.bracket.tips.specification.pillarSize": "Column size:",
  "geekplus.gms.client.screen.bracket.tips.specification.palletModel1": "F12ML/F14L/F16L/F35C: Both the edge column and the middle column shall be more than or equal to 60 mm wide.",
  "geekplus.gms.client.screen.bracket.tips.specification.palletModel2": "F20T/F20MT: Both the edge column and the middle column shall be more than or equal to 80 mm wide.",
  "geekplus.gms.client.screen.bracket.tips.specification.special.usageCases": "The column size is applicable to the site with just one type of pallet and smaller than the width of the edge column and middle column of standard pallet.",
  "geekplus.gms.client.screen.bracket.tips.specification.special.material": "Material: wooden/plastic/metal/others",
  "geekplus.gms.client.screen.bracket.tips.specification.special.color": "Color: wood color/blue/black/gray/others",
  "geekplus.gms.client.screen.bracket.tips.specification.special.palletModel1": "F12ML/F14L/F16L/F35C: Both the edge column and the middle column shall be more than or equal to 50 mm wide.",
  "geekplus.gms.client.screen.bracket.tips.specification.special.palletModel2": "F20T/F20MT: Both the edge column and the middle column shall be more than or equal to 70 mm wide.",
}
