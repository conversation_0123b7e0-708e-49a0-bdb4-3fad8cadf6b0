<template>
  <gp-dialog
    :title="robotTypeLabel + $t('lang.ark.fed.processOperation')"
    :top="'60px'"
    :visible="true"
    :width="step === 0 ? '60%' : '90%'"
    center
    :footer="showFooter"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="closeDialog"
  >
    <div class="dialog-content">
      <form-box
        v-show="step === 0"
        ref="formBox"
        :init-value="initFormData"
        :is-roll-robot="isRollRobot"
        :disabled="!mode"
        :options-dict="optionsDict"
        :robot-type="rowData.robotType"
        class="tw-mb-8"
      ></form-box>
      <edit-editor
        v-show="step === 1"
        ref="myEditor"
        :visible="step === 1"
        :is-roll-robot="isRollRobot"
        :flow-list="rowData"
        :disabled="!mode"
        :options-dict="optionsDict"
      ></edit-editor>
    </div>
    <template v-if="showFooter">
      <!-- 底部按钮 -->
      <div v-if="mode" slot="footer" class="dialog-footer">
        <!-- 取消 -->
        <gp-button v-if="step === 0" class="w100" @click="closeDialog">
          {{ $t("lang.ark.fed.cancel") }}
        </gp-button>
        <!-- 上一步 -->
        <gp-button v-if="step === 1" class="w100" @click="step = 0">
          {{ $t("lang.ark.fed.preStep") }}
        </gp-button>
        <!-- 下一步 -->
        <gp-button v-if="step === 0" class="w140" type="primary" @click="nextStep">
          {{ $t("lang.ark.fed.nextStep") }}
        </gp-button>
        <!-- 发布并保存 -->
        <gp-button v-if="step === 1" class="w140" type="primary" :loading="saveLoading" @click="saveDialog(true)">
          {{ $t("lang.ark.fed.publishAndSave") }}
        </gp-button>
        <!-- 保存 -->
        <gp-button v-if="step === 1" type="primary" :loading="saveLoading" class="w140" @click="saveDialog(false)">
          {{ $t("lang.ark.fed.save") }}
        </gp-button>
      </div>
      <div v-else slot="footer" class="dialog-footer">
        <!-- 取消 -->
        <gp-button class="w100" @click="closeDialog">
          {{ $t("lang.ark.fed.cancel") }}
        </gp-button>
        <!-- 上一步 -->
        <gp-button v-if="step === 1" class="w100" @click="step = 0">
          {{ $t("lang.ark.fed.preStep") }}
        </gp-button>
        <!-- 下一步 -->
        <gp-button v-if="step === 0" class="w140" type="primary" @click="step = 1">
          {{ $t("lang.ark.fed.nextStep") }}
        </gp-button>
      </div>
    </template>
  </gp-dialog>
</template>

<script>
import { ARRIVE_OPERATION, ROBOT_TYPE, CMD_TYPE } from "gms-constants";
import { translateMessage } from "@/utils/help";
import EditEditor from "@/components/SlamEditor/editEditor";
import { commonValidate } from "@/components/SlamEditor/validations";
import FormBox from "./form-box.vue";
import { checkWorkflowCode, saveAndReleaseWorkflow, saveWorkflow } from "gms-apis/flow-config";

export default {
  name: "EditDialog",

  components: {
    FormBox,
    EditEditor,
  },

  props: {
    rowData: {
      type: Object,
      default: null,
    },
    // 表单模式：true编辑、false只读
    mode: {
      type: Boolean,
      default: true,
    },
    optionsDict: {
      type: [Object, Array],
      default: null,
    },
  },
  data() {
    return {
      initValue: null,
      flowListData: [],
      step: 0,
      saveLoading: false,
      showFooter: true,
    };
  },
  computed: {
    robotTypeLabel() {
      return ROBOT_TYPE.getLabelByValue(this.rowData?.robotType);
    },

    isRollRobot() {
      return String(this.rowData.robotType) === ROBOT_TYPE.ROLLER;
    },
    initFormData() {
      const data = this.rowData;
      if (data.appiontRobotList && data.appiontRobotList.length) {
        // 分配机器人回显，如果有循环取出来回显，否则空数组显示无数据 2020-08-24 by luoyaodong
        // 机器人型号传参与回显修改，2020-08-31
        if (data.appiontRobotList[0].robotId !== null) {
          data.appiontRobotList = this._.map(data.appiontRobotList, "robotId");
          data.robotMachineType = [];
        } else if (data.appiontRobotList[0].robotMachineType !== null) {
          data.robotMachineType = this._.map(data.appiontRobotList, "robotMachineType");
          data.appiontRobotList = [];
        } else {
          data.appiontRobotList = [];
          data.robotMachineType = [];
        }
      }
      if (data.runningMode !== null && data.runningMode !== undefined) {
        data.runningMode = String(data.runningMode);
      }
      if (data.robotType !== null && data.robotType !== undefined) {
        data.robotType = String(data.robotType);
      }
      return data;
    },
  },
  watch: {
    step(newVal) {
      // 如果改变div尺寸；
      if (newVal && newVal === 1) {
        this.$nextTick(() => {
          this.$refs.myEditor.resize();
        });
      }
    },
  },
  created() {
    // 初始化数据
    this.init();
  },
  methods: {
    // 初始化数据
    init() {},
    // 关闭当前弹框
    closeDialog() {
      this.$emit("close", false);
      const { workflowCodeAsFuzz } = this.$route.query;
      if (workflowCodeAsFuzz) {
        this.$emit("success");
        this.$router.replace("/flowConfig");
      }
    },
    async nextStep() {
      const res = await this.$refs.formBox.getFormData();
      if (res) {
        try {
          await checkWorkflowCode(res);
          this.step = 1;
          this.initValue = res;
        } catch (e) {
          console.error(e);
        }
      }
    },
    verifyLastNode(flow) {
      if (!flow.nodeList.length) {
        return false;
      }
      let sign = true;
      const endNodes = flow.nodeList.find((i) => i.nodeType === 0);
      if (endNodes) {
        const linkNodes = flow.segmentList.filter((i) => String(endNodes.id) === String(i.target));
        if (!linkNodes || !linkNodes.length) {
          sign = false;
          this.$message.error(this.$t("lang.ark.fed.unconnectedNodeExist"));
          return sign;
        }
        if (linkNodes.length) {
          const ids = linkNodes.map((i) => String(i.source));
          // 验证最后一个节点不校验，子流程。
          const getLinkEndNodesActionParm = flow.nodeList.filter(
            (i) => ids.includes(String(i.id)) && String(i.nodeType) !== "4"
          );
          const actionsDict = this.$refs.myEditor.actionsDict;

          for (let i = 0; i < actionsDict.length; i++) {
            const item = actionsDict[i];
            const nodeArr = getLinkEndNodesActionParm.filter((i) => String(i.nodeActionId) === String(item.id));
            if (nodeArr.length) {
              if (["1", "2"].includes(String(item.canAddshelfFlag))) {
                sign = false;
                const msgContainer = this.returnI18nMsg(nodeArr, "container");
                this.$message.error(translateMessage(msgContainer));
                break;
              } else if (item.commandList && item.commandList.length) {
                const componentList = item.commandList.filter((a) => String(a.commandType) === CMD_TYPE.COMPONENT);
                if (
                  componentList.length &&
                  [ARRIVE_OPERATION.LIFT, ARRIVE_OPERATION.FETCH_STUFF].includes(
                    String(componentList[componentList.length - 1].action)
                  )
                ) {
                  if (String(item.robotType) !== ROBOT_TYPE.ROLLER) {
                    sign = false;
                    const msg = this.returnI18nMsg(nodeArr, "allowToUp");
                    this.$message.error(translateMessage(msg));
                    break;
                  }
                }
              }
            }
          }
        }
      }
      return sign;
    },
    returnI18nMsg(parm, diffData) {
      const msg = {};
      const getLinkEndNodesNames = parm.map((i) => String(i.workflowNodeName));
      const tipNames = getLinkEndNodesNames.join(",");
      const nodeTipNames = [];
      nodeTipNames.push(tipNames);
      msg.p = nodeTipNames;
      if (diffData === "allowToUp") {
        msg.c = "lang.ark.fed.actionsNotAllowToUp";
      } else if (diffData === "removeRobot") {
        msg.c = "lang.ark.fed.actionsErrorNeedRemoveRobot";
      } else if (diffData === "container") {
        msg.c = "lang.ark.fed.actionsNotAllowAddContainer";
      }
      const msgs = JSON.stringify(msg);
      return msgs;
    },
    // 保存
    saveDialog(isRelease) {
      this.saveLoading = true;
      const cancel = () =>
        setTimeout(() => {
          this.saveLoading = false;
        }, 500);
      this.$nextTick(() => {
        this.$refs.myEditor
          .getWorkFlowData()
          .then(commonValidate)
          .then(([flowList, isValid]) => {
            if (!isValid) {
              cancel();
              return;
            }

            if (!this.verifyLastNode(flowList)) {
              cancel();
              return;
            }

            const params = { ...this.initValue, ...flowList };
            if ("cellFunction" in params) {
              params.cellFunction = Number(params.cellFunction) || "";
            }

            const api = isRelease ? saveAndReleaseWorkflow : saveWorkflow;
            api(params)
              .then(() => {
                this.closeDialog();
                this.$emit("success");
              })
              .finally(() => {
                cancel();
              });
          })
          .catch(cancel);
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.flowBox {
  height: 469px;
}
.flowChart-box {
  height: 420px;
}
.dialog-footer {
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
}
</style>
