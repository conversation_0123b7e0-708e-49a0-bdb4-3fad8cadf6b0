<template>
  <gms-dialog
    :visible="visible"
    width="50%"
    :is-need-footer="false"
    :title="$t('geekplus.gms.client.screen.container.form.viewExample')"
    @closed="$emit('update:visible', false)"
  >
    <div :class="cn.dialog_body">
      <section :class="cn.section_wrapper">
        <h3 class="tw-title">{{ $t("geekplus.gms.client.screen.container.form.standardModel") }}</h3>
        <p>{{ $t("geekplus.gms.client.screen.container.form.standardContainerModel") }}</p>
      </section>
      <section :class="cn.section_wrapper">
        <h3 class="tw-title">{{ $t("geekplus.gms.client.screen.container.form.noStandardModel") }}</h3>
        <p>{{ $t("geekplus.gms.client.screen.container.form.noStandardContainerModel") }}</p>
      </section>
      <section :class="cn.section_wrapper">
        <div :class="cn.red_tips">
          <p>{{ $t("geekplus.gms.client.screen.bracket.example.special.tips") }}</p>
          <p>{{ $t("geekplus.gms.client.screen.container.form.shelfSurfaceApplied") }}</p>
          <p>{{ $t("geekplus.gms.client.screen.container.form.shelfLegsApplied") }}</p>
          <p>{{ $t("geekplus.gms.client.screen.container.form.regularRectangleApplied") }}</p>
        </div>
      </section>
      <section :class="cn.section_wrapper" class="tw-flex tw-flex-col tw-gap-1 tw-items-center">
        <p class="tw-self-start">{{ $t("geekplus.gms.client.screen.container.form.laserScanningReferenceDiagram") }}</p>
        <img :src="laserScanning" alt="" class="tw-w-96" />
        <img :src="laserScanning1" alt="" class="tw-w-96" />
      </section>

      <section :class="cn.section_wrapper">
        <p>{{ $t("geekplus.gms.client.screen.container.form.exampleShapedContainer") }}</p>
        <img :src="shapedContainer" alt="" :style="{ width: '100%' }" />
      </section>
    </div>
  </gms-dialog>
</template>

<script>
import { defineComponent } from "vue";
import getContainerImage from "@/utils/containerImages";
import laserScanning from "@/gms/assets/images/container/laser-scanning.png";
const laserScanning1 = getContainerImage("laser-scanning1.png");
import shapedContainer from "@/gms/assets/images/container/shaped-container.png";

export default defineComponent({
  props: {
    visible: { type: Boolean, default: false },
  },
  setup() {
    return {
      laserScanning,
      laserScanning1,
      shapedContainer,
    };
  },
});
</script>
<style lang="scss" module="cn">
.dialog_body {
  font-size: 14px;
  .section_wrapper {
    margin: 30px 20px;
    &:first-child {
      margin-top: 0;
    }
    .red_tips {
      color: #cb2421;
      margin: 20px 0;
    }
  }
}
h3,
.title {
  font-size: 14px;
  font-weight: 700;
  line-height: 2;
}
.big_title {
  font-size: 18px;
}
</style>
