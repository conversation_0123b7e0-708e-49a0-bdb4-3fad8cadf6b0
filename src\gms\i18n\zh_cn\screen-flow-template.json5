/**
 * 流程模板页国际化字符集
 * key 必须以 'geekplus.gms.client.screen.flowTemplate.' 开头
 */

{
  "geekplus.gms.client.screen.flowTemplate.title": "流程模板",
  "geekplus.gms.client.screen.flowTemplate.subtitle": "根据常见业务抽象出来的一套机器人工作流程模板，通过流程模板可以生成流程进而驱动机器人执行搬运任务",
  "geekplus.gms.client.screen.flowTemplate.edgeName": "连接线名称",
  "geekplus.gms.client.screen.flowTemplate.slamEditor.nodeSelected.rollerNodeActionCheckMsg": "非辊筒工位，交互动作不支持取放货",
  "geekplus.gms.client.screen.flowTemplate.slamEditor.currentNode": "当前点位",
  "geekplus.gms.client.screen.flowTemplate.slamEditor.specifyNodeType": "指定点位类型",
  "geekplus.gms.client.screen.flowTemplate.slamEditor.specifyNodeType.tooltip": '当选择"不指定"时，默认包含全场所有点位类型，适用于只一个外部设备场景。',
  "geekplus.gms.client.screen.flowTemplate.slamEditor.nodeAction": "点位交互动作",
  "geekplus.gms.client.screen.flowTemplate.slamEditor.defaultQueueArea": "默认排队区",
}
