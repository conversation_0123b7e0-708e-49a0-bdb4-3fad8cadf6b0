/**
 *   容器页面国际化字符集
 *   key 必须以 'geekplus.gms.client.screen.container.' 开头
 */
{
  "geekplus.gms.client.screen.container.tab.bracketModel": "支架模型",
  "geekplus.gms.client.screen.container.containerManagement": "容器管理",
  "geekplus.gms.client.screen.container.containerType": "容器类型",
  "geekplus.gms.client.screen.container.containerCode": "容器编码",
  "geekplus.gms.client.screen.container.btns.addContainerType": "新增容器类型",
  "geekplus.gms.client.screen.container.btns.addContainer": "新增容器",
  "geekplus.gms.client.screen.container.btns.editContainer": "编辑容器",
  "geekplus.gms.client.screen.container.btns.containerEntry": "入场",
  "geekplus.gms.client.screen.container.btns.containerLeave": "离场",
  "geekplus.gms.client.screen.container.btns.batchAdd": "批量添加",
  "geekplus.gms.client.screen.container.btns.saveAndEntry": "保存并入场",
  "geekplus.gms.client.screen.container.btns.cancelView": "取消查看",
  "geekplus.gms.client.screen.container.btns.viewContainerType": "查看容器规格信息",

  "geekplus.gms.client.screen.container.form.searchParams": "请输入容器编码",
  "geekplus.gms.client.screen.container.form.containerTitle": "容器",
  "geekplus.gms.client.screen.container.form.containerSubTitle": "容器是机器人用来搬运货物的载具，支架是地面上用于取放货物的支撑结构。可根据业务场景创建不同类型的容器和支架。容器列表用于管理现有的容器资源，并可用于控制容器的入场和离场。",
  "geekplus.gms.client.screen.container.form.pleaseSelectContainerTypeName": "请选择容器类型",
  "geekplus.gms.client.screen.container.columns.containerStatus": "容器状态",
  "geekplus.gms.client.screen.container.columns.status": "状态",
  "geekplus.gms.client.screen.container.columns.containerCode": "容器编码",
  "geekplus.gms.client.screen.container.columns.code": "编码",
  "geekplus.gms.client.screen.container.columns.containerTypeName": "容器类型名称",
  "geekplus.gms.client.screen.container.columns.containerAngle": "容器角度",
  "geekplus.gms.client.screen.container.columns.angle": "角度",
  "geekplus.gms.client.screen.container.columns.materialType": "物料类别",
  "geekplus.gms.client.screen.container.columns.containerLoadingStatus": "容器空满状态",
  "geekplus.gms.client.screen.container.columns.loadingStatus": "空满状态",
  "geekplus.gms.client.screen.container.columns.containerCurrentPosition": "容器当前位置",
  "geekplus.gms.client.screen.container.columns.currentPosition": "当前位置",
  "geekplus.gms.client.screen.container.columns.belongsArea": "所属区域",
  "geekplus.gms.client.screen.container.columns.belongsGroup": "所属分组",
  "geekplus.gms.client.screen.container.columns.entryEndPoint": "入场终点",
  "geekplus.gms.client.screen.container.columns.containerAmount": "容器数量",
  "geekplus.gms.client.screen.container.columns.containerEntryAngle": "容器入场角度",

  "geekplus.gms.client.screen.container.tips.confirmLeave": "确认离场",
  "geekplus.gms.client.screen.container.tips.confirmEntry": "确认入场",
  "geekplus.gms.client.screen.container.tips.leaveTipMessage": "容器离场后，容器空满状态将被清空，确认是否离场？",
  "geekplus.gms.client.screen.container.tips.containerDeleteTipMessage": "删除后将无法恢复，确认是否删除？",
  "geekplus.gms.client.screen.container.tips.containerEntry": "容器入场",
  "geekplus.gms.client.screen.container.tips.canDelete": "以下容器可删除",
  "geekplus.gms.client.screen.container.tips.canEnter": "以下容器可入场",
  "geekplus.gms.client.screen.container.tips.canLeave": "以下容器可离场",
  "geekplus.gms.client.screen.container.tips.cannotDelete": "以下容器已入场或工作中，无法删除",
  "geekplus.gms.client.screen.container.tips.cannotEnter": "以下容器已入场或工作中，无法再次入场",
  "geekplus.gms.client.screen.container.tips.cannotLeave": "以下容器已离场或工作中，无法再次离场",
  "geekplus.gms.client.screen.container.tips.index": "序号",
  "geekplus.gms.client.screen.container.tips.editContainerCodeTip": "指定容器编码{code}最多新增一个容器，确定添加？",
  "geekplus.gms.client.screen.container.tips.noData": "暂无数据",
  "geekplus.gms.client.screen.container.tips.entryFailed": "入场失败",
  "geekplus.gms.client.screen.container.tips.operationFailed": "操作失败",
  "geekplus.gms.client.screen.container.tips.atLeast2Legs": "请至少添加2条腿的信息",

  "geekplus.gms.client.screen.container.form.stepOne": "第1步",
  "geekplus.gms.client.screen.container.form.stepTwo": "第2步",
  "geekplus.gms.client.screen.container.form.pleaseSelectContainerShape": "请选择容器形态",
  "geekplus.gms.client.screen.container.form.container": "容器",
  "geekplus.gms.client.screen.container.form.containerInfo": "容器信息",
  "geekplus.gms.client.screen.container.form.modelInformation": "规格信息",
  "geekplus.gms.client.screen.container.form.containerModel": "容器类型",
  "geekplus.gms.client.screen.container.form.standardModel": "标准容器",
  "geekplus.gms.client.screen.container.form.noStandardModel": "异形容器",
  "geekplus.gms.client.screen.container.form.containerSurfaceLong": "长",
  "geekplus.gms.client.screen.container.form.containerSurfaceWide": "容器面：宽",
  "geekplus.gms.client.screen.container.form.containerLegsLong": "容器腿：长",
  "geekplus.gms.client.screen.container.form.containerLegsWide": "容器腿：宽",
  "geekplus.gms.client.screen.container.form.BasicInformation": "基础信息",
  "geekplus.gms.client.screen.container.form.codeType": "编码类型",
  "geekplus.gms.client.screen.container.form.codeName": "编码名称",
  "geekplus.gms.client.screen.container.form.containerForm": "容器形态",
  "geekplus.gms.client.screen.container.form.shelves": "货架",
  "geekplus.gms.client.screen.container.form.feedTruck": "料车",
  "geekplus.gms.client.screen.container.form.bin": "料箱",
  "geekplus.gms.client.screen.container.form.cages": "笼车",
  "geekplus.gms.client.screen.container.form.handlingPalletM": "搬运托盘(M系列)",
  "geekplus.gms.client.screen.container.form.forkliftPalletF": "叉车托盘(F系列)",
  "geekplus.gms.client.screen.container.form.palletSpecifications": "托盘规格",
  "geekplus.gms.client.screen.container.form.standardPalletRegular": "标准托盘-常规(推荐)",
  "geekplus.gms.client.screen.container.form.standardPalletSpecial": "标准托盘-特殊",
  "geekplus.gms.client.screen.container.form.palletStructure": "托盘结构",
  "geekplus.gms.client.screen.container.form.twoHoleTray": "双孔托盘",
  "geekplus.gms.client.screen.container.form.singleHoleTray": "单孔托盘",
  "geekplus.gms.client.screen.container.form.identifyDistances": "识别距离",
  "geekplus.gms.client.screen.container.form.palletMaterial": "托盘材质",
  "geekplus.gms.client.screen.container.form.wood": "木质",
  "geekplus.gms.client.screen.container.form.plastics": "塑料",
  "geekplus.gms.client.screen.container.form.other": "其他",
  "geekplus.gms.client.screen.container.form.trayColor": "托盘颜色",
  "geekplus.gms.client.screen.container.form.woody": "木色",
  "geekplus.gms.client.screen.container.form.blue": "蓝色",
  "geekplus.gms.client.screen.container.form.black": "黑色",
  "geekplus.gms.client.screen.container.form.palletModels": "托盘车型",
  "geekplus.gms.client.screen.container.form.palletSideLong": "托盘面长",
  "geekplus.gms.client.screen.container.form.palletSideWide": "托盘面宽",
  "geekplus.gms.client.screen.container.form.edgeColumnWide": "边缘立柱宽",
  "geekplus.gms.client.screen.container.form.middleColumnWide": "中间立柱宽",
  "geekplus.gms.client.screen.container.form.holesHigh": "孔洞高",
  "geekplus.gms.client.screen.container.form.holesWide": "孔洞宽",
  "geekplus.gms.client.screen.container.form.shelfElements": "容器元素",
  "geekplus.gms.client.screen.container.form.shelvesLegs": "货架-腿",
  "geekplus.gms.client.screen.container.form.shelfFace": "货架-面",
  "geekplus.gms.client.screen.container.form.sizeLong": "容器面长",
  "geekplus.gms.client.screen.container.form.sizeWide": "容器面宽",
  "geekplus.gms.client.screen.container.form.sizeHeight": "容器高度",
  "geekplus.gms.client.screen.container.form.xAxisFromCenter": "距中心X轴距离",
  "geekplus.gms.client.screen.container.form.yAxisFromCenter": "距中心Y轴距离",
  "geekplus.gms.client.screen.container.form.shelfCenterOriginX": "中心原点：x",
  "geekplus.gms.client.screen.container.form.shelfCenterOriginY": "中心原点：y",
  "geekplus.gms.client.screen.container.form.shelfCenter": "容器中心点",
  "geekplus.gms.client.screen.container.form.rightConfig": "请先选择右侧配置",
  "geekplus.gms.client.screen.container.form.checkExample": "查看示例",
  "geekplus.gms.client.screen.container.form.onlyRectangularSupported": "目前只支持四边的不规则矩形的货架配置。",
  "geekplus.gms.client.screen.container.form.referActualShelfFigure": "一、参照实际货架找到货架的面和腿，如图：",
  "geekplus.gms.client.screen.container.form.notes": "注意事项：",
  "geekplus.gms.client.screen.container.form.shelfSurfaceApplied": "1、容器面：应用于机器人搬运时计算旋转范围",
  "geekplus.gms.client.screen.container.form.shelfLegsApplied": "2、容器腿：以激光高度能扫描到的高度为横截面，激光扫描到的容器腿 (包括轮子) 的高度应为激光扫描的截面，都需要配置，应用于机器人搬运前的识别。",
  "geekplus.gms.client.screen.container.form.regularRectangleApplied": "3、关于测量：当面和腿是不规则的矩形时，容器面的测量要以容器面和容器腿中的最大矩形面的尺寸为准。容器面和容器腿的尺寸，要包含轮子转动时占用的尺寸，避免货架旋转时撞人或者撞货。",
  "geekplus.gms.client.screen.container.form.exampleDiagram": "示例图：",
  "geekplus.gms.client.screen.container.form.configurationFaceLegs": " 二、面和腿的配置说明",
  "geekplus.gms.client.screen.container.form.measureDistanceLeg": "需要测量出腿到面的边距1、2距离，单位mm",
  "geekplus.gms.client.screen.container.form.containersRobotsInclude": "常见机器人可搬运的容器包含：货架、料车、料柜、笼车、托盘等；",
  "geekplus.gms.client.screen.container.form.containerModelExplanation": "容器模型名词解释",
  "geekplus.gms.client.screen.container.form.mainlyConfiguresInformation": "容器模型主要配置“容器面”和“容器腿”的信息",
  "geekplus.gms.client.screen.container.form.containerSurface": "容器面：",
  "geekplus.gms.client.screen.container.form.usedEnvelopeHandling": "用于搬运时的包络计算。",
  "geekplus.gms.client.screen.container.form.containerLegs": "容器腿：",
  "geekplus.gms.client.screen.container.form.usedRobotOntology": "用于机器人本体识别容器。",
  "geekplus.gms.client.screen.container.form.standardContainerModel": "定义：当容器面和容器腿都是规则矩形时，且为4条腿，每条腿都位于矩形的一个顶角，机器人的对接点也在货架的矩形中心时称为标准容器。",
  "geekplus.gms.client.screen.container.form.noStandardContainerModel": "定义：从容器的俯视角度看，容器面或容器腿中任意一个是不规则的矩形，或多于4条腿时，称为异形容器。",
  "geekplus.gms.client.screen.container.form.containerLegsConfigured": "注意事项：激光高度能扫描到的容器腿(包含轮子)都需要配置。",
  "geekplus.gms.client.screen.container.form.laserScanningReferenceDiagram": "激光扫描参考图：",
  "geekplus.gms.client.screen.container.form.exampleShapedContainer": "异形容器示例：",
  "geekplus.gms.client.screen.container.form.viewExample": "查看示例",
  "geekplus.gms.client.screen.container.form.containerFace": "容器面",
  "geekplus.gms.client.screen.container.form.containerLeg": "容器腿",
  "geekplus.gms.client.screen.container.form.surfaceLength": "容器面长",
  "geekplus.gms.client.screen.container.form.surfaceWidth": "容器面宽",
  "geekplus.gms.client.screen.container.form.surfaceHeight": "容器高度",
  "geekplus.gms.client.screen.container.form.length": "长",
  "geekplus.gms.client.screen.container.form.width": "宽",
  "geekplus.gms.client.screen.container.form.height": "高",
  "geekplus.gms.client.screen.container.form.shelfHeight": "容器高度",
  "geekplus.gms.client.screen.container.form.codeRule": "编码规则",
  "geekplus.gms.client.screen.container.form.codePrefix": "编码前缀",
  "geekplus.gms.client.screen.container.form.containerMaterialInfo": "容器物料信息",
  "geekplus.gms.client.screen.container.form.shelfLeg": "货架腿",
  "geekplus.gms.client.screen.container.form.shelfOverLookDiagram": "容器上方平面俯视图",
  "geekplus.gms.client.screen.container.form.bracket.shelfOverLookDiagram": "支架上方平面俯视图",
  "geekplus.gms.client.screen.container.form.measureYAxisMethod": "(以机器人进入方向为Y轴测量)",
  "geekplus.gms.client.screen.container.form.specialShelfModelConfig": "异形容器模型配置",
  "geekplus.gms.client.screen.container.form.bracket.specialShelfModelConfig": "异形支架模型配置",
  "geekplus.gms.client.screen.container.form.shelf3DDiagram": "容器立体示意图",
  "geekplus.gms.client.screen.container.form.bracket.shelf3DDiagram": "支架立体示意图",
  "geekplus.gms.client.screen.container.form.containerType.step2": "第2步：编辑规格信息",
  "geekplus.gms.client.screen.container.form.containerList": "容器列表",
  "geekplus.gms.client.screen.container.form.containerSize": "容器尺寸",

  "geekplus.gms.client.screen.container.placeholder.upTo4Bits": "请输入最多4个字母",

  "geekplus.gms.client.screen.container.columns.typeCode": "类型编码",
  "geekplus.gms.client.screen.container.columns.typeName": "类型名称",
  "geekplus.gms.client.screen.container.columns.details": "详情",
  "geekplus.gms.client.screen.container.columns.containerType": "容器类型",
  "geekplus.gms.client.screen.container.columns.containerShape": "容器形态",
  "geekplus.gms.client.screen.container.columns.containerModel": "容器类型",

  // 提示信息
  "geekplus.gms.client.screen.container.tips.failReason": "失败原因",
  "geekplus.gms.client.screen.container.tips.containerAmount": "容器数量，支持批量新增，一次最多50个",
  "geekplus.gms.client.screen.container.tips.containerCode": "容器唯一标识，当系统自动生成编码时，多个容器支持批量自增，如指定编码，多个容器数量时，一次只能生成1个容器。",
  "geekplus.gms.client.screen.container.tips.materialType": "用于区分容器承载不同类别的物料，该数据来源于物料管理菜单。",
  "geekplus.gms.client.screen.container.tips.codeRule": "容器编码默认自动生成，如需修改请直接在输入框修改为指定的容器编码",
  "geekplus.gms.client.screen.container.tips.containerNum": "容器数量，支持批量新增，一次最多50个，预置数字：1",
  "geekplus.gms.client.screen.container.tips.recognizableDistance": "机器人本体激光识别托盘的距离，通常该参数主要是算法研发使用。",
  "geekplus.gms.client.screen.container.tips.palletModel": "托盘被哪种车型搬运，不同车型搬运的托盘，最小立柱宽要求不一样。",
  "geekplus.gms.client.screen.container.tips.containerFace": "用于搬运时的包络计算。",
  "geekplus.gms.client.screen.container.tips.containerLeg": "用于机器人本体识别容器。",
  "geekplus.gms.client.screen.container.tips.specification.face": "托盘面：长宽不限",
  "geekplus.gms.client.screen.container.tips.specification.material": "材质：木质/塑料/其他",
  "geekplus.gms.client.screen.container.tips.specification.color": "颜色：木色/蓝色/其他",
  "geekplus.gms.client.screen.container.tips.specification.pillarSize": "立柱尺寸：",
  "geekplus.gms.client.screen.container.tips.specification.palletModel1": "F12ML/F14L/F16L/F35C：边缘立柱宽和中间立柱宽≥60mm",
  "geekplus.gms.client.screen.container.tips.specification.palletModel2": "F20T/F20MT：边缘立柱宽和中间立柱宽≥80mm",
  "geekplus.gms.client.screen.container.tips.specification.special.usageCases": "适用于场地仅一种托盘场地，且比标准托盘-常规的边缘和中间立柱宽要小。",
  "geekplus.gms.client.screen.container.tips.specification.special.material": "材质：木质/塑料/金属/其他",
  "geekplus.gms.client.screen.container.tips.specification.special.color": "颜色：木色/蓝色/黑色/灰色/其他",
  "geekplus.gms.client.screen.container.tips.specification.special.palletModel1": "F12ML/F14L/F16L/F35C：边缘立柱宽和中间立柱宽≥50mm",
  "geekplus.gms.client.screen.container.tips.specification.special.palletModel2": "F20T/F20MT：边缘立柱宽和中间立柱宽≥70mm",

  "geekplus.gms.client.screen.bracket.form.offset.tooltip.desc": "地面支架是固定在地面上的支架，由于上方托盘重心偏离等原因，导致机器人的停留位置，可能不在几何中心点，这时就需要配置Y轴的前后偏离量。",
  "geekplus.gms.client.screen.bracket.form.offset.tooltip.tips": "注意：配置时要以机器人进入的方向为Y轴来测量"
}
