/**
 *   工作站页面国际化字符集
 *   key 必须以 'geekplus.gms.client.screen.workstations.' 开头
 */
{
  "geekplus.gms.client.screen.workstations.pageTitle": "工作站",
  "geekplus.gms.client.screen.workstations.pageSubTitle": "工作站是用于实际业务操作的区域，通常由一个或多个工作点位组成。",
  "geekplus.gms.client.screen.workstations.btns.addStation": "新增工作站",
  "geekplus.gms.client.screen.workstations.btns.configStation": "工作站功能配置",

  "geekplus.gms.client.screen.workstations.columns.stationCode": "编码",
  "geekplus.gms.client.screen.workstations.columns.stationName": "名称",
  "geekplus.gms.client.screen.workstations.columns.stationType": "类型",
  "geekplus.gms.client.screen.workstations.columns.points": "包含点位",
  "geekplus.gms.client.screen.workstations.columns.deliveryType": "配送模式",
  "geekplus.gms.client.screen.workstations.columns.editor": "编辑人",
  "geekplus.gms.client.screen.workstations.columns.editTime": "编辑时间",
  "geekplus.gms.client.screen.workstations.columns.actions": "操作",

  "geekplus.gms.client.screen.workstations.tooltip.listView": "列表视图",
  "geekplus.gms.client.screen.workstations.tooltip.cardView": "卡片视图",
  "geekplus.gms.client.screen.workstations.tooltip.copyLink": "复制工作站地址",
  "geekplus.gms.client.screen.workstations.tooltip.visitPage": "访问工作站页面",
  "geekplus.gms.client.screen.workstations.tooltip.removeWorkstation": "删除工作站",
  "geekplus.gms.client.screen.workstations.tooltip.configWorkstation": "配置工作站",
  "geekplus.gms.client.screen.workstations.placeholder.search": "请输入工作站名称/点位/编码",

  "geekplus.gms.client.screen.workstations.removeConfirm.title": "删除确认",
  "geekplus.gms.client.screen.workstations.removeConfirm.desc": "确认要删除此工作站吗?",
  "geekplus.gms.client.screen.workstations.removeConfirm.btnConfirm": "确认删除",
  "geekplus.gms.client.screen.workstations.removeConfirm.btnCancel": "取消",

  "geekplus.gms.client.screen.workstations.dialogDetail.titleAdd": "新增工作站",
  "geekplus.gms.client.screen.workstations.dialogDetail.titleEdit": "编辑工作站",
  "geekplus.gms.client.screen.workstations.dialogDetail.titleDelivery": "选择工作站配送模式",
  "geekplus.gms.client.screen.workstations.dialogDetail.titlePosition": "选择工作站包含点位",
  "geekplus.gms.client.screen.workstations.dialogDetail.titlePositionDesc": "选择的点位顺序就是工作站内的显示顺序",
  "geekplus.gms.client.screen.workstations.dialogDetail.titleQueue": "开启工作站排队功能",
  "geekplus.gms.client.screen.workstations.dialogDetail.titleQueueDesc": "当工作站有节拍要求，需要安排机器人提前待命时，可开启排队功能",
  "geekplus.gms.client.screen.workstations.dialogDetail.labelDockPosition": "排队点",
  "geekplus.gms.client.screen.workstations.dialogDetail.labelRobotCount": "排队机器人数量",
  "geekplus.gms.client.screen.workstations.dialogDetail.labelPriority": "优先级",
  "geekplus.gms.client.screen.workstations.dialogDetail.btnAddQueue": "+ 添加排队点",
  "geekplus.gms.client.screen.workstations.dialogDetail.cardDescA": "经过工作站的所有流程该工作站都能发起",
  "geekplus.gms.client.screen.workstations.dialogDetail.cardDescB": "工作站只能发起从该工作站开始的流程",
  "geekplus.gms.client.screen.workstations.dialogDetail.labelStationCode": "工作站编码",
  "geekplus.gms.client.screen.workstations.dialogDetail.labelPointType": "点位类型",
  "geekplus.gms.client.screen.workstations.dialogDetail.labelStationName": "工作站名称",
  "geekplus.gms.client.screen.workstations.dialogDetail.labelPointList": "点位列表",
  "geekplus.gms.client.screen.workstations.dialogDetail.labelVisitAuth": "配置工作站访问权限",
  "geekplus.gms.client.screen.workstations.dialogDetail.labelAuthUsers": "访问权限",
  "geekplus.gms.client.screen.workstations.dialogDetail.labelAuthUserList": "可访问用户列表",
  "geekplus.gms.client.screen.workstations.dialogDetail.authUsersOption.user": "用户",
  "geekplus.gms.client.screen.workstations.dialogDetail.authUsersOption.role": "用户角色",

  "geekplus.gms.client.screen.workstations.dialogSettings.title": "工作站功能配置",
  "geekplus.gms.client.screen.workstations.dialogSettings.titleDesc": "可根据不同的场景，灵活选择需要开启或关闭的功能",
  "geekplus.gms.client.screen.workstations.dialogSettings.subTitleSelectWorksite": "选择生效的工作站",
  "geekplus.gms.client.screen.workstations.dialogSettings.subTitleTask": "任务监控模块",
  "geekplus.gms.client.screen.workstations.dialogSettings.subTitleProcess": "流程操作",
  "geekplus.gms.client.screen.workstations.dialogSettings.cfgReturn": "退回按钮",
  "geekplus.gms.client.screen.workstations.dialogSettings.cfgReturnDesc": "显示任务退回按钮，可发起任务退回流程",
  "geekplus.gms.client.screen.workstations.dialogSettings.cfgRollback": "撤销按钮",
  "geekplus.gms.client.screen.workstations.dialogSettings.cfgRollbackDesc": "显示任务撤销按钮，可对发起的任务进行撤销操作",
  "geekplus.gms.client.screen.workstations.dialogSettings.cfgCancel": "取消按钮",
  "geekplus.gms.client.screen.workstations.dialogSettings.cfgCancelDesc": "显示任务取消按钮，在现场任务变动的情况下对任务进行取消操作",
  "geekplus.gms.client.screen.workstations.dialogSettings.cfgShowProcessType": "按流程类别展示",
  "geekplus.gms.client.screen.workstations.dialogSettings.cfgShowProcessTypeDesc": "按照流程类别展示流程列表，可根据类别快速发起流程",
  "geekplus.gms.client.screen.workstations.dialogSettings.cfgProcessRestart": "流程再启动",
  "geekplus.gms.client.screen.workstations.dialogSettings.cfgProcessRestartDesc": "对执行完毕的流程再次启动",
  "geekplus.gms.client.screen.workstations.dialogSettings.cfgProcessRestartPDA": "流程再启动-PDA",
  "geekplus.gms.client.screen.workstations.dialogSettings.cfgProcessRestartPDADesc": "在PDA上启用流程再启动的操作",
  "geekplus.gms.client.screen.workstations.dialogSettings.cfgShowSubProcess": "显示子流程",
  "geekplus.gms.client.screen.workstations.dialogSettings.cfgShowSubProcessDesc": "是否在流程列表显示流程所关联的子任务",
  "geekplus.gms.client.screen.workstations.dialogSettings.submitConfirmMsg": "保存并应用设置后将立即生效，可能影响现场正常运行，请确保在非工作时段进行",
  "geekplus.gms.client.screen.workstations.dialogSettings.submitConfirmBtn": "确认并应用",
  "geekplus.gms.client.screen.workstations.dialogSettings.submitBtn": "保存并应用",
  "geekplus.gms.client.screen.workstations.dialogSettings.labelApplyMethod": "生效范围",
  "geekplus.gms.client.screen.workstations.dialogSettings.labelApplyWorksites": "生效工作站",
}
