<template>
  <div class="tw-flex tw-justify-center tw-items-center tw-h-full">
    <div class="tw-relative tw-flex tw-justify-center tw-items-center">
      <img :src="shelfImg" :class="cn.img" alt="" />
      <HighLightBox
        :styles="{
          top: '300px',
          left: '-10px',
          width: '372px',
          height: '20px',
          borderDirection: 'borderTop',
          display: 'flex',
          transform: 'rotate(18deg)',
          justifyContent: 'center',
        }"
        :value="surfaceLength"
        :is-active="$props.active === 'surfaceLength'"
      />
      <!-- $props.active === 'surfaceLength' -->
      <HighLightBox
        :styles="{
          top: '266px',
          left: '264px',
          width: '353px',
          height: '20px',
          borderDirection: 'borderTop',
          display: 'flex',
          justifyContent: 'center',
          transform: 'rotate(-33.5deg)',
        }"
        :value="surfaceWidth"
        :is-active="$props.active === 'surfaceWidth'"
      />
      <!-- $props.active === 'surfaceWidth' -->
    </div>
  </div>
</template>
<script>
import { defineComponent, computed } from "vue";
import shelfImg from "@/gms/assets/images/container/move-pallet.svg";
import HighLightBox from "./highligt-box.vue";

export default defineComponent({
  name: "PalletAnnotation",
  components: { HighLightBox },
  props: {
    formValues: { type: Object, default: () => ({}) },
    active: { type: String, default: "" },
  },
  setup(props) {
    const surfaceLength = computed(() => props.formValues.modelInfo.surfaceLength);
    const surfaceWidth = computed(() => props.formValues.modelInfo.surfaceWidth);

    return { shelfImg, surfaceLength, surfaceWidth };
  },
});
</script>
<style lang="scss" module="cn">
.img {
  width: 600px;
}
</style>
