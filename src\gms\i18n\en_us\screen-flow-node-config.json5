/**
 * 交互配置页国际化字符集
 * key 必须以 'geekplus.gms.client.screen.flowNodeConfig.' 开头
 */

{
  "geekplus.gms.client.screen.flowNodeConfig.title": "Interactive configuration",
  "geekplus.gms.client.screen.flowNodeConfig.subtitle": "Define the combination of action instructions and related strategy configurations that a robot needs to do after arriving at a certain process node.",
  "geekplus.gms.client.screen.flowNodeConfig.refFlowCount": "Number of processes",
  // 增加指令的按钮文本
  "geekplus.gms.client.screen.flowNodeConfig.button.addWaitCommand": "Add Wait Command",
  "geekplus.gms.client.screen.flowNodeConfig.button.addDeviceCommand": "Add Device Command",
  "geekplus.gms.client.screen.flowNodeConfig.commandInfo": "Command information",
  "geekplus.gms.client.screen.flowNodeConfig.relatedStrategy": "Association strategy",
  "geekplus.gms.client.screen.flowNodeConfig.autoAddContainer": "Add the container automatically",
  "geekplus.gms.client.screen.flowNodeConfig.needSelectMaterialCategory": "Select the type of material",
  "geekplus.gms.client.screen.flowNodeConfig.specifyContainerOrientation": "Specify the container orientation",
  "geekplus.gms.client.screen.flowNodeConfig.containerExitAfterCompletion": "Remove the container after completion",
  "geekplus.gms.client.screen.flowNodeConfig.robotReleaseAfterCompletion": "Release the robot after completion",
  "geekplus.gms.client.screen.flowNodeConfig.fetchNumber": "Pickup quantity",
  "geekplus.gms.client.screen.flowNodeConfig.unloadNumber": "Qty placed",
  "geekplus.gms.client.screen.flowNodeConfig.deviceOccupied": "Whether the device is occupied",
  "geekplus.gms.client.screen.flowNodeConfig.command.fetchStart": "Start fetching",
  "geekplus.gms.client.screen.flowNodeConfig.command.fetch": "Pickup",
  "geekplus.gms.client.screen.flowNodeConfig.command.fetchEnd": "Finish pitching",
  "geekplus.gms.client.screen.flowNodeConfig.command.bodyRotateSide": "Body rotation by plane",
  "geekplus.gms.client.screen.flowNodeConfig.command.bodyFBShift": "Front and rear offset of body",
  "geekplus.gms.client.screen.flowNodeConfig.command.bodyLRShift": "Left and right offset of body",
  "geekplus.gms.client.screen.flowNodeConfig.command.roller.checkMsg": '"Start fetching" and "Finish fetching" must be configured at the same time and used together.',
  "geekplus.gms.client.screen.flowNodeConfig.command.roller.requiredCheckMsg": "Please configure and save the robot command",
  "geekplus.gms.client.screen.flowNodeConfig.refreshDeviceInstruct": "Refresh the device command",

  // 接口指令
  "geekplus.gms.client.screen.flowNodeConfig.paramsToPassIn": "Parameters to be passed in",
  "geekplus.gms.client.screen.flowNodeConfig.passInMethod": "Value passing method",
  "geekplus.gms.client.screen.flowNodeConfig.returnedParams": "Returned parameters",
  "geekplus.gms.client.screen.flowNodeConfig.processMethod": "Processing method",
  "geekplus.gms.client.screen.flowNodeConfig.passInFromSystemField": "Get the value from the system field and pass it in",
  "geekplus.gms.client.screen.flowNodeConfig.passInFixedValue": "Pass fixed value",
  "geekplus.gms.client.screen.flowNodeConfig.saveInSystemField": "Store value into system field",
  "geekplus.gms.client.screen.flowNodeConfig.compareWithSystemField": "Compare with system field values",
  "geekplus.gms.client.screen.flowNodeConfig.compareWithFixedValue": "Compare with fixed value",

  // 设备指令
  "geekplus.gms.client.screen.flowNodeConfig.noRequestValue": "Not transitive",
  "geekplus.gms.client.screen.flowNodeConfig.noProcess": "Not processed",
  "geekplus.gms.client.screen.flowNodeConfig.requestedDeviceParams": "Device parameters to be passed in",
  "geekplus.gms.client.screen.flowNodeConfig.returnedDeviceParams": "Returned device parameters",
}
