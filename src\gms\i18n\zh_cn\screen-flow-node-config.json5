/**
 * 交互配置页国际化字符集
 * key 必须以 'geekplus.gms.client.screen.flowNodeConfig.' 开头
 */

{
  "geekplus.gms.client.screen.flowNodeConfig.title": "交互配置",
  "geekplus.gms.client.screen.flowNodeConfig.subtitle": "定义机器人到达某个流程节点后，需要做的动作指令组合以及相关策略配置",
  "geekplus.gms.client.screen.flowNodeConfig.refFlowCount": "引用流程数",
  "geekplus.gms.client.screen.flowNodeConfig.commandInfo": "指令信息",
  "geekplus.gms.client.screen.flowNodeConfig.relatedStrategy": "关联策略",
  "geekplus.gms.client.screen.flowNodeConfig.autoAddContainer": "自动添加容器",
  "geekplus.gms.client.screen.flowNodeConfig.needSelectMaterialCategory": "需选择物料类别",
  "geekplus.gms.client.screen.flowNodeConfig.specifyContainerOrientation": "指定容器朝向",
  "geekplus.gms.client.screen.flowNodeConfig.containerExitAfterCompletion": "完成后容器离场",
  "geekplus.gms.client.screen.flowNodeConfig.robotReleaseAfterCompletion": "完成后释放机器人",
  "geekplus.gms.client.screen.flowNodeConfig.fetchNumber": "取货数量",
  "geekplus.gms.client.screen.flowNodeConfig.unloadNumber": "放货数量",
  "geekplus.gms.client.screen.flowNodeConfig.deviceOccupied": "设备是否占用",

  // 增加指令的按钮文本
  "geekplus.gms.client.screen.flowNodeConfig.button.addWaitCommand": "新增等待指令",
  "geekplus.gms.client.screen.flowNodeConfig.button.addDeviceCommand": "新增设备指令",

  // 新的指令文本
  "geekplus.gms.client.screen.flowNodeConfig.command.fetchStart": "开始取货",
  "geekplus.gms.client.screen.flowNodeConfig.command.fetch": "取货",
  "geekplus.gms.client.screen.flowNodeConfig.command.fetchEnd": "完成取货",
  "geekplus.gms.client.screen.flowNodeConfig.command.bodyRotateSide": "本体按面旋转",
  "geekplus.gms.client.screen.flowNodeConfig.command.bodyFBShift": "本体前后偏移",
  "geekplus.gms.client.screen.flowNodeConfig.command.bodyLRShift": "本体左右偏移",
  "geekplus.gms.client.screen.flowNodeConfig.command.roller.checkMsg": "“开始取货”和“完成取货”必须同时配置，不能单独使用",
  "geekplus.gms.client.screen.flowNodeConfig.command.roller.requiredCheckMsg": "请先配置完机器人指令，再保存",

  "geekplus.gms.client.screen.flowNodeConfig.refreshDeviceInstruct": "刷新设备指令",

  // 接口指令
  "geekplus.gms.client.screen.flowNodeConfig.paramsToPassIn": "需传入的参数",
  "geekplus.gms.client.screen.flowNodeConfig.passInMethod": "传值方式",
  "geekplus.gms.client.screen.flowNodeConfig.returnedParams": "返回的参数",
  "geekplus.gms.client.screen.flowNodeConfig.processMethod": "处理方式",
  "geekplus.gms.client.screen.flowNodeConfig.passInFromSystemField": "从系统字段取值传入",
  "geekplus.gms.client.screen.flowNodeConfig.passInFixedValue": "传固定值",
  "geekplus.gms.client.screen.flowNodeConfig.saveInSystemField": "将值存入到系统字段",
  "geekplus.gms.client.screen.flowNodeConfig.compareWithSystemField": "与系统字段的值对比",
  "geekplus.gms.client.screen.flowNodeConfig.compareWithFixedValue": "与固定值对比",

  // 设备指令
  "geekplus.gms.client.screen.flowNodeConfig.noRequestValue": "不传值",
  "geekplus.gms.client.screen.flowNodeConfig.noProcess": "不处理",
  "geekplus.gms.client.screen.flowNodeConfig.requestedDeviceParams": "需传入的设备参数",
  "geekplus.gms.client.screen.flowNodeConfig.returnedDeviceParams": "返回的设备参数",
}
