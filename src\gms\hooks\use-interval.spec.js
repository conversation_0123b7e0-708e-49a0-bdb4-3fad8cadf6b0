import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";
import { useInterval } from "./use-interval";

beforeEach(() => {
  vi.useFakeTimers();
});

afterEach(() => {
  vi.clearAllMocks();
  vi.clearAllTimers();
});

describe("hook: useInterval", () => {
  it("should run between interval", async () => {
    const task = vi.fn().mockImplementation((done) => done());
    const interval = useInterval({ interval: 1000, task });
    interval.start();
    expect(task).toHaveBeenCalledTimes(1);
    await vi.advanceTimersByTimeAsync(1000);
    expect(task).toHaveBeenCalledTimes(2);
    await vi.advanceTimersByTimeAsync(1000);
    expect(task).toHaveBeenCalledTimes(3);
    await vi.advanceTimersByTimeAsync(1000);
    expect(task).toHaveBeenCalledTimes(4);
  });

  it("interval time should works in async task", async () => {
    const task = vi.fn().mockImplementation((done) => setTimeout(done, 1000));
    const interval = useInterval({ interval: 1000, task });
    interval.start();
    expect(task).toHaveBeenCalledTimes(1);
    await vi.advanceTimersByTimeAsync(1000);
    expect(task).toHaveBeenCalledTimes(1);
    await vi.advanceTimersByTimeAsync(1000);
    expect(task).toHaveBeenCalledTimes(2);
    await vi.advanceTimersByTimeAsync(1000);
    expect(task).toHaveBeenCalledTimes(2);
    await vi.advanceTimersByTimeAsync(1000);
    expect(task).toHaveBeenCalledTimes(3);
  });

  it("stop timer", async () => {
    const task = vi.fn().mockImplementation((done) => done());
    const interval = useInterval({ interval: 1000, task });
    interval.start();
    expect(task).toHaveBeenCalledTimes(1);
    interval.stop();
    await vi.advanceTimersByTimeAsync(1000);
    expect(task).toHaveBeenCalledTimes(1);
  });

  it("cun in run", async () => {
    const task = vi.fn().mockImplementation((done) => done());
    const interval = useInterval({ interval: 1000, task });
    interval.start();
    expect(task).toHaveBeenCalledTimes(1);
    await vi.advanceTimersByTimeAsync(500);
    interval.cutInRun({ param: 1 });
    expect(task.mock.lastCall[1]).toEqual({ param: 1 });
    expect(task).toHaveBeenCalledTimes(2);
    await vi.advanceTimersByTimeAsync(500);
    expect(task).toHaveBeenCalledTimes(2);
    await vi.advanceTimersByTimeAsync(500);
    expect(task).toHaveBeenCalledTimes(3);
    expect(task.mock.lastCall[1]).toEqual(undefined);
  });

  it("restart interval timer", async () => {
    const task = vi.fn().mockImplementation((done) => done());
    const interval = useInterval({ interval: 1000, task });
    interval.start();
    expect(task).toHaveBeenCalledTimes(1);
    await vi.advanceTimersByTimeAsync(500);
    interval.restartInterval();
    await vi.advanceTimersByTimeAsync(500);
    expect(task).toHaveBeenCalledTimes(1);
    await vi.advanceTimersByTimeAsync(500);
    expect(task).toHaveBeenCalledTimes(2);
  });
});
