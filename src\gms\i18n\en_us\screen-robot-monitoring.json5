{"lang.ark.fed.screen.robotMonitoring.robotList": "Robot list", "lang.ark.fed.screen.robotMonitoring.robotTask": "Robot Tasks", "geekplus.gms.client.screen.robotMonitoring.filters.robotTaskId": "Please enter the robot task ID", "geekplus.gms.client.screen.robotMonitoring.filters.taskType": "Task type", "geekplus.gms.client.screen.robotMonitoring.filters.createTime": "Creation time", "geekplus.gms.client.screen.robotMonitoring.filters.robotId": "Robot", "geekplus.gms.client.screen.robotMonitoring.filters.locationEntity.placeholder": "Starting point/destination", "geekplus.gms.client.screen.robotMonitoring.filters.loadCode": "Container code", "geekplus.gms.client.screen.robotMonitoring.filters.status": "State", "geekplus.gms.client.screen.robotMonitoring.columns.task": "Robot Tasks", "geekplus.gms.client.screen.robotMonitoring.columns.robotNum": "Robot", "geekplus.gms.client.screen.robotMonitoring.columns.loadCode": "Container code", "geekplus.gms.client.screen.robotMonitoring.columns.currentStage": "Current stage", "geekplus.gms.client.screen.robotMonitoring.columns.instruction": "Execution instruction", "geekplus.gms.client.screen.robotMonitoring.columns.exceptionDesc": "Exception description", "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.taskType": "Task type", "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.robotId": "Robot", "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.createTime": "Creation time", "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.start": "Starting point", "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.loadCode": "Container code", "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.updateTime": "Update Time", "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.end": "Destination", "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.priority": "Priority", "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.elapsedTime": "Task time", "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.currentStage": "Current stage", "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.exceptionDesc": "Exception description", "geekplus.gms.client.screen.robotMonitoring.stat.all": "All", "geekplus.gms.client.screen.robotMonitoring.stat.create": "Create", "geekplus.gms.client.screen.robotMonitoring.stat.allocation": "Allocated", "geekplus.gms.client.screen.robotMonitoring.stat.executing": "Executing", "geekplus.gms.client.screen.robotMonitoring.stat.complete": "Completed", "geekplus.gms.client.screen.robotMonitoring.stat.cancel": "Canceled", "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.taskId.label": "Robot task ID", "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.taskType.label": "Task type", "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.robotId.label": "Robot", "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.createTime.label": "Creation time", "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.start.label": "Starting point", "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.loadCode.label": "Container code", "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.updateTime.label": "Update Time", "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.end.label": "Destination", "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.priority.label": "Priority", "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.elapsedTime.label": "Task time", "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.currentStage.label": "Current stage", "geekplus.gms.client.screen.robotMonitoring.detail.basicInfo.exceptionDesc.label": "Exception description", "geekplus.gms.client.screen.robotMonitoring.detail.taskProcess.contentFilter": "Content filtering", "geekplus.gms.client.screen.robotMonitoring.detail.taskProcess.tabs.process": "Execution process", "geekplus.gms.client.screen.robotMonitoring.detail.taskProcess.tabs.log": "Log", "geekplus.gms.client.screen.robotMonitoring.detail.taskProcess.tabs.process.updateTask": "Update task", "geekplus.gms.client.screen.robotMonitoring.systemError.confirmToCancelTask": "Are you sure to cancel the current task?", "geekplus.gms.client.screen.robotMonitoring.systemError.forceCancel.cancelFailed": "Cancellation failed", "geekplus.gms.client.screen.robotMonitoring.systemError.taskCanceling": "Canceling task, please wait...", "geekplus.gms.client.screen.robotMonitoring.systemError.forceCancel.cancelTip": "Follow the steps below after the task is forcibly canceled:", "geekplus.gms.client.screen.robotMonitoring.systemError.forceCancel.cancelStep1": "1. Check for goods carried by the robot, and handle them to prevent dropping and collision.", "geekplus.gms.client.screen.robotMonitoring.systemError.forceCancel.cancelStep2": "2. Unlock the robot", "geekplus.gms.client.screen.robotMonitoring.systemError.forceCancel.cancelTitle": "In case of failure or timeout of canceling the robot task, you can forcibly cancel it.", "geekplus.gms.client.screen.robotMonitoring.systemError.cancelTaskTip": "Tasks cannot be recovered once canceled. Please operate with caution.", "geekplus.gms.client.screen.robotMonitoring.systemError.cancelSuccess": "Robot task has been canceled.", "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStatus.all": "All", "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStatus.create": "Create", "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStatus.allocating": "Allocated", "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStatus.executing": "Executing", "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStatus.completed": "Completed", "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStatus.canceled": "Canceled", "geekplus.gms.client.screen.robotMonitoring.constants.rmsRobotType.goSomewhereToStay": "Robot movement", "geekplus.gms.client.screen.robotMonitoring.constants.rmsRobotType.goMaintainToStay": "Maintenance task", "geekplus.gms.client.screen.robotMonitoring.constants.rmsRobotType.goChargeYourself": "Charging task", "geekplus.gms.client.screen.robotMonitoring.constants.rmsRobotType.moveShelf": "Move Shelf", "geekplus.gms.client.screen.robotMonitoring.constants.rmsRobotType.deliverShelf": "<PERSON><PERSON>", "geekplus.gms.client.screen.robotMonitoring.constants.rmsRobotType.goWork": "Co-robot task", "geekplus.gms.client.screen.robotMonitoring.constants.rmsRobotType.carryPackage": "Roller robot task", "geekplus.gms.client.screen.robotMonitoring.constants.rmsRobotType.deliverPallet": "Forklift pallet task", "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.create": "Task creation", "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.allocation": "Task Assignment", "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.execute": "Task execution", "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.end": "End of task", "geekplus.gms.client.screen.robotMonitoring.operationLog.cancel": "Cancel task", "geekplus.gms.client.screen.robotMonitoring.operationLog.forceCancel": "Force cancel task", "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.goFetching": "Picking up the rack", "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.shelfFetched": "<PERSON><PERSON> has been picked up", "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.goDelivering": "Transporting the rack", "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.queuing": "Queuing", "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.shelfArrived": "<PERSON><PERSON> has arrived at the destination", "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.goReturn": "Returning the rack", "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.shelfTurning": "Rack rotation", "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.moving": "Moving", "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.charging": "Charging", "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.arrivedReceive": "The drum robot has arrived at the pickup location, ready to pick up goods", "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.arrivedDrop": "The drum robot has arrived at the unloading position, ready to unload", "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.arrivedWaitPoint": "Arrived at the waiting point", "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.leavingWaitPoint": "Left the waiting point", "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.receiveFinish": "The drum robot has completed receiving goods", "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.dropFinish": "The drum robot has completed unloading", "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.boxFetched": "Picking up the box is complete", "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.boxArrived": "Delivery of the box has arrived", "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.arrivedElevatorEntry": "Arrived at the waiting point in front of the elevator door (outside the elevator), waiting to enter the elevator", "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.enteredElevator": "Has entered the elevator", "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.leavedElevator": "Exiting the elevator", "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.arrived": "Arrived at the destination", "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.fetched": "The task has been completed", "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.robotDelivering": "In the four-way vehicle business line, robots are transporting containers", "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.deviceDelivering": "In the four-way vehicle business line, equipment is transporting containers", "geekplus.gms.client.screen.robotMonitoring.constants.rmsTaskStage.containerArrived": "In the four-way vehicle business line, the container has arrived at the target location", "geekplus.gms.client.screen.robotMonitoring.constants.rmsExecutionType.create": "Task creation", "geekplus.gms.client.screen.robotMonitoring.constants.rmsExecutionType.allocate": "Task assignment", "geekplus.gms.client.screen.robotMonitoring.constants.rmsExecutionType.execute": "Task execution", "geekplus.gms.client.screen.robotMonitoring.constants.rmsExecutionType.update": "Task update", "geekplus.gms.client.screen.robotMonitoring.constants.rmsExecutionType.finish": "Task completion", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12000": "Robot cannot connect", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12001": "Subtask sending timeout", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12002": "Path planning failed", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12003": "Robot not on the map", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12004": "The starting and ending points of the planned path are obstacles", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12005": "There are obstacles such as robots or racks along the way", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12006": "The path resources are currently occupied by other robots", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12007": "No path available for planning", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12203": "Planned path is unreasonable", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12036": "Some cells are too tall to pass through", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12008": "Task anomaly", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12009": "Picking up the rack timeout", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12010": "Failure to reach the rack position for pickup", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12011": "Long delay in reaching the waiting point", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12012": "Robot's battery level does not increase during a prolonged charging process", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12013": "Robot has reached low battery level", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12014": "No matching charging station found", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12015": "No free charging stations available", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12016": "Charging station is unavailable", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12017": "Charging station cell occupied by another robot", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12018": "Unable to charge across regions", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12019": "Unable to charge across floors", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12020": "Task blocked, unable to perform charging task", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12021": "Task incomplete, unable to perform charging task", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12022": "Charging time too long", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12023": "Robot battery overheating", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12024": "Unable to find a docking point after charging is complete", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12025": "Robot locked", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12026": "Robot battery low temperature", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12038": "Excessive number of consecutive charging failures", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12027": "Robot path scheduling stopped", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12028": "Robot exceeds the furthest allocation point limit in path scheduling", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12029": "Obstacles encountered during robot path scheduling", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12030": "Deadlock avoidance during robot path scheduling", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12031": "Robot's current location is not on the planned path during path scheduling", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12032": "QR code robot unable to run on SLAM path", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12033": "Robot is on a node with no outgoing paths", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12034": "Robot is being forcibly operated", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12035": "External rack did not generate a task", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12101": "<PERSON> is in a deadlock loop", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12102": "Robot has been stationary for a long time", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12103": "Robot collision", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12104": "Robot in emergency stop status", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12105": "Robot in pause status", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12106": "Robot needs to restart", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12039": "Robot scheduling anomaly caused by system program exception", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12040": "Path scheduling recovery anomaly during high availability switch", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12107": "robot#jobIds exist in the warehouse but the corresponding job cannot be found", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12108": "job#stageIds exist in the warehouse but the corresponding stage cannot be found", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12109": "robot#taskIds exist in the warehouse but the corresponding task cannot be found", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12110": "robot#exeTaskIds exist in the warehouse but the corresponding exeTask cannot be found", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12111": "After the robot arrives at the endpoint QR code and adjusts (forward/backward + angle), the left/right deviation is too large", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12113": "Robot floor status unconfirmed", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12112": "Robot floor status confirmation anomaly", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12114": "robot task takes an unusually long time to complete", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12201": "Robot detects a pallet at the destination", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12202": "Robot detects no pallet at the starting point", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12301": "Robot task recovery failed", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.12302": "Robot task pre-occupied pallet data lost", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.3004": "Box task detects a box already on the fork when going to pick up a box", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.3005": "Box task detects no box in the rack when going to pick up a box", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.3006": "Box task detects a box already on the rack or backpack when returning a box", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.3007": "Box task detects no box on the pallet when returning a box", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.21602": "Model distribution failed", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.3008": "Box task detects an obstacle at the external position when going to pick up an inner box", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.21603": "<PERSON><PERSON> severely overloaded", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.21604": "<PERSON><PERSON> severely unbalanced", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.3009": "Unable to recognize the location QR code for a long time", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.3037": "Detect recoverable exception when picking up a box", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.3038": "Detect recoverable exception when returning a box", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.10020": "The accumulated number of robot anomalies has reached the limit, stopping operations", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.23113": "Box picking failed", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.23114": "Box returning failed", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.23115": "Upper limit switch triggered", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.23116": "Telescopic forks are not synchronized on both sides", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.23117": "Robot angle exceeds limit", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.23118": "Endpoint calibration failed to recognize ground QR code", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.23119": "Endpoint calibration failed multiple times", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.23120": "Foreign object in the fork", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.23121": "Verification failed return: The box is not at the PopPick workstation position and cannot complete the task", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.23122": "The current business mode of the workstation does not support box returning, please switch to comprehensive mode before returning", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.23123": "Check if the box is associated with any incomplete tasks, if so, return to the front end: The box has associated tasks that are not completed", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.32032": "Racks are not allowed to enter the work area", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.32033": "Pop<PERSON>ick switched to manual mode", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.32034": "PopPick hardware failure", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.23124": "Robot stop mode encoder angle change detection. For example: If the robot is rotated by external forces while in a stopped state, an error may be reported", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.32035": "The number of racks checked in a single transaction cannot exceed 5000", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.32036": "The rack {0} being inspected is not a PopPick rack", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.32037": "Workstation {0} is in a disabled state", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.32038": "Workstation {0} is not a PopPick workstation", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.23125": "Robot task allocation pause exception code", "geekplus.gms.client.screen.robotMonitoring.constants.rmsErrorCodes.3039": "Box task detects a box already on the backpack when returning a box"}