<template>
  <div class="tw-px-1">
    <div :class="co.title1">{{ $t("geekplus.gms.client.screen.callerConfiguration.FourButtonCallerType") }}</div>
    <div class="bgpicture tw-bg-slate-100 tw-w-full tw-h-32 tw-rounded-lg"></div>
    <div>
      <!-- 这是新建呼叫器的表单 -->
      <form-item class="tw-mb-3.5" v-bind="step0Item.controllerName" />
      <form-item class="tw-mb-3.5" v-bind="step0Item.controllerCode" />
    </div>
  </div>
</template>
<script>
import { defineComponent, onMounted, ref, watch } from "vue";
import { createFormItemGroup, validators, FormItem } from "@srpe-fe/uic-el-form-wrapper-vue2";
import { buttonCodeList } from "gms-apis/caller-configuration";
import { useI18n } from "@/hooks";
export default defineComponent({
  name: "CallerBaseForm",
  components: { FormItem },
  props: {
    init_value: {
      type: Array,
      default: [],
    },
  },
  setup(props, { emit }) {
    const t = useI18n();
    // 编码列表的数据
    const codelist = ref([]);
    const step0Item = createFormItemGroup({
      controllerName: {
        type: "el-input",
        labelText: t("geekplus.gms.client.screen.callerConfiguration.callerName.description"),
        value: "",
        labelPosition: "top",
        placeholder: t("geekplus.gms.client.screen.callerConfiguration.inputPrompt.description"),
        validators: [validators.required],
      },
      controllerCode: {
        type: "el-select",
        labelText: t("geekplus.gms.client.screen.callerConfiguration.callerid"),
        value: "",
        labelPosition: "top",
        options: codelist,
        placeholder: t("geekplus.gms.client.screen.callerConfiguration.inputPrompt.description"),
        labelHelp: t("geekplus.gms.client.screen.callerConfiguration.callerBoxUniqueCode"),
        validators: [validators.required],
      },
    });
    watch(
      () => props.init_value,
      (newVal) => {
        const { code, name } = newVal[0] || {};
        if (code && name) {
          step0Item.$setData({
            controllerName: name,
            controllerCode: code,
          });
        }
      },
      { immediate: true }
    );
    const getCodeList = async () => {
      const data = await buttonCodeList();
      const result = data.map((item) => ({
        label: item, 
        value: item, 
      }));
      codelist.value = result;
    };
    onMounted(() => {
      getCodeList();
    });
    return {
      step0Item,
      t,
      codelist,
    };
  },
});
</script>
<style lang="scss" module="co" src="./common.scss"></style>
<style lang="scss" scoped>
.bgpicture {
  background-image: url("@/assets/images/pager.png");
  background-repeat: no-repeat;
  background-position: center;
  background-size: auto 85%;
}
</style>
