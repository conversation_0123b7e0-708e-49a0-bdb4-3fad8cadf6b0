{"geekplus.gms.client.screen.flowConfig.title": "Workflow", "geekplus.gms.client.screen.flowConfig.subtitle": "Different scenarios can be programmed into the robot's workflow to drive the robot to perform handling tasks.", "geekplus.gms.client.screen.flowConfig.manualConfigFlow": "Manual configuration workflow", "geekplus.gms.client.screen.flowConfig.templateBasedFlow": "Workflow template generation workflow", "geekplus.gms.client.screen.flowConfig.notCheckIfIdleRobots": "Make no judgment whether there are idle robots", "geekplus.gms.client.screen.flowConfig.taskQueueIfNoIdleRobots": "Tasks queue up due to no idle robots available.", "geekplus.gms.client.screen.flowConfig.taskFailIfNoIdleRobots": "Task failed due to no idle robot available.", "geekplus.gms.client.screen.flowConfig.taskLaunchRule": "Task initiation strategy", "geekplus.gms.client.screen.flowConfig.nodeForm.workstationCode": "Workstation coding", "geekplus.gms.client.screen.flowConfig.nodeForm.areaCode": "Area code", "geekplus.gms.client.screen.flowConfig.nodeForm.flow": "Workflow", "geekplus.gms.client.screen.flowConfig.nodeForm.equipmentPoint": "Device location", "geekplus.gms.client.screen.flowConfig.nodeForm.pallet": "Pallet position", "geekplus.gms.client.screen.flowConfig.nodeForm.mapCode": "Location code", "geekplus.gms.client.screen.flowConfig.nodeForm.queueAreaActions": "Interaction in the queuing area", "geekplus.gms.client.screen.flowConfig.nodeType.equipment": "Device location", "geekplus.gms.client.screen.flowConfig.dragItem.equipment": "Device location", "geekplus.gms.client.screen.flowConfig.dragItem.mapPoint": "Map location", "geekplus.gms.client.screen.flowConfig.dragItem.pallet": "Pallet position", "geekplus.gms.client.screen.flowConfig.nodeForm.nodeType": "Location type", "geekplus.gms.client.screen.flowConfig.nodeForm.nodeCode": "Location code"}