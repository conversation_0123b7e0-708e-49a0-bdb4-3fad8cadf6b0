/**
 *   任务监控页面国际化字符集
 *   key 必须以 'geekplus.gms.client.screen.taskMonitoring.' 开头
 */
{
  "geekplus.gms.client.screen.taskMonitoring.page.title": "任务监控",
  "geekplus.gms.client.screen.taskMonitoring.page.subtitle": "实时监测任务的执行状况，并可对任务的异常进行干预和处理",

  "geekplus.gms.client.screen.taskMonitoring.filters.robot": "机器人",
  "geekplus.gms.client.screen.taskMonitoring.filters.robotTaskID": "机器人任务号",
  "geekplus.gms.client.screen.taskMonitoring.filters.loadCode": "容器编码",
  "geekplus.gms.client.screen.taskMonitoring.filters.point": "点位",
  "geekplus.gms.client.screen.taskMonitoring.filters.workflowEndMethod": "结束方式",
  "geekplus.gms.client.screen.taskMonitoring.filters.timeRange": "创建时间",
  "geekplus.gms.client.screen.taskMonitoring.filters.timeRange.start.placeholder": "开始时间",
  "geekplus.gms.client.screen.taskMonitoring.filters.timeRange.end.placeholder": "结束时间",
  "geekplus.gms.client.screen.taskMonitoring.filters.fuzzySearch.placeholder": "请输入任务号/任务名称/上游任务号",
  "geekplus.gms.client.screen.taskMonitoring.filters.locationEntity.placeholder": "起点/终点",

  "geekplus.gms.client.screen.taskMonitoring.columns.task": "任务",
  "geekplus.gms.client.screen.taskMonitoring.columns.taskID": "任务号",
  "geekplus.gms.client.screen.taskMonitoring.columns.taskName": "任务名称",
  "geekplus.gms.client.screen.taskMonitoring.columns.taskName.title": "点击查看任务详情",
  "geekplus.gms.client.screen.taskMonitoring.columns.upstreamTaskNum": "上游任务号",
  "geekplus.gms.client.screen.taskMonitoring.columns.start": "起",
  "geekplus.gms.client.screen.taskMonitoring.columns.end": "终",
  "geekplus.gms.client.screen.taskMonitoring.columns.startPoint": "起点",
  "geekplus.gms.client.screen.taskMonitoring.columns.endPoint": "终点",
  "geekplus.gms.client.screen.taskMonitoring.columns.robotNum": "机器人",
  "geekplus.gms.client.screen.taskMonitoring.columns.robotTaskID": "机器人任务号",
  "geekplus.gms.client.screen.taskMonitoring.columns.container": "容器",
  "geekplus.gms.client.screen.taskMonitoring.columns.container.title": "点击查看容器详情",
  "geekplus.gms.client.screen.taskMonitoring.columns.taskStatus": "状态",
  "geekplus.gms.client.screen.taskMonitoring.columns.workflowEndMethod": "结束方式",
  "geekplus.gms.client.screen.taskMonitoring.columns.currentStage": "当前阶段",
  "geekplus.gms.client.screen.taskMonitoring.columns.exceptionDesc": "异常描述",
  "geekplus.gms.client.screen.taskMonitoring.columns.createTime": "创建时间",
  "geekplus.gms.client.screen.taskMonitoring.columns.create": "创建",
  "geekplus.gms.client.screen.taskMonitoring.columns.completeTime": "完成时间",
  "geekplus.gms.client.screen.taskMonitoring.columns.priority": "优先级",
  "geekplus.gms.client.screen.taskMonitoring.columns.actions": "操作",

  "geekplus.gms.client.screen.taskMonitoring.tabs.realTimeTask": "实时任务",
  "geekplus.gms.client.screen.taskMonitoring.tabs.historyTask": "历史任务",

  "geekplus.gms.client.screen.taskMonitoring.actions.recover.popconfirm": "确认恢复吗？",
  "geekplus.gms.client.screen.taskMonitoring.actions.pause.popconfirm": "确认暂停吗？",
  "geekplus.gms.client.screen.taskMonitoring.actions.delete.popconfirm": "确认删除吗？",
  "geekplus.gms.client.screen.taskMonitoring.actions.cancel.popconfirm": "确认取消吗？",
  "geekplus.gms.client.screen.taskMonitoring.actions.more": "更多",
  "geekplus.gms.client.screen.taskMonitoring.actions.recover": "恢复",
  "geekplus.gms.client.screen.taskMonitoring.actions.pause": "暂停",
  "geekplus.gms.client.screen.taskMonitoring.actions.priority": "优先级调为最高",
  "geekplus.gms.client.screen.taskMonitoring.actions.export": "导出",

  "geekplus.gms.client.screen.taskMonitoring.realTimeTaskStatus.create": "待下发",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTaskStatus.executing": "执行中",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTaskStatus.exception": "异常",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTaskStatus.pause": "已暂停",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTaskStatus.cancelExecuting": "取消中",

  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.currentStage.waitingToSend": "待下发rms",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.currentStage.robotAllocating": "已下发rms，待分配机器人",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.currentStage.robotAllocated": "已分配到机器人，待执行",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.currentStage.movingToStartPoint": "机器人正在前往起点",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.currentStage.arrivedStartPoint": "机器人已到达起点",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.currentStage.fetched": "取货完成",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.currentStage.prepareToEndPoint": "准备前往终点",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.currentStage.movingToEndPoint": "机器人正在前往终点",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.currentStage.arrivedEndPoint": "机器人已到达终点",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.currentStage.readyToLeave": "放货完成，准备离开",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.currentStage.movingToCancelPoint": "机器人正在前往取消后的放货点",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.currentStage.arrivedCancelPoint": "机器人已到达取消后的放货点",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.currentStage.waitingManualHandle": "等待人工处理",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.currentStage.loadedAndMovingToEndPoint": "取货完成,机器人正在前往终点",

  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.error.category": "类型",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.error.level": "级别",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.error.msg": "异常描述",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.error.deviceTaskError": "设备任务下发失败，原因：{msg}",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.error.schedulerTaskError": "调度任务下发失败，原因：{msg}",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.error.type.deviceDriver": "驱动器",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.error.type.deviceModule": "模块",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.error.type.deviceBehavior": "行为",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.error.type.deviceCommunication": "通讯",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.error.type.pathPlan": "路径规划",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.error.type.deviceTask": "任务",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.error.type.deviceComponent": "组件",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.error.type.deviceLED": "灯带异常",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.error.type.task": "机器人业务任务",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.error.type.communication": "机器人网络通信",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.error.type.deviceDispatch": "路径调度",
  "geekplus.gms.client.screen.taskMonitoring.realTimeTask.error.type.deviceBattery": "电池异常",

  "geekplus.gms.client.screen.taskMonitoring.historyTaskStatus.completed": "已完成",
  "geekplus.gms.client.screen.taskMonitoring.historyTaskStatus.canceled": "已取消",
  "geekplus.gms.client.screen.taskMonitoring.historyTaskStatus.exception": "手动完成",
  "geekplus.gms.client.screen.taskMonitoring.historyTaskStatus.closed": "已完成",

  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.ready": "预备",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.create": "任务待下发",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.waitQueueRobot": "下发工作站排队点任务",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.suspension": "暂停下发",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.waitingSendRPC": "任务排队",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.taskSended": "任务已下发",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.moving": "机器人已上路",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.fetched": "取到容器",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.executing": "执行中",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.interruptWaiting": "任务中断，等待触发继续",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.commandExecuting": "指令执行中",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.passWaitPoint": "待机器人反馈离开",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.deviceExecuting": "外部设备运输中",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.nodeWaiting": "等待发起下一任务",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.pause": "任务暂停",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.undoing": "撤销中",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.backing": "退回中",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.cancelExecuting": "取消中",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.cancelExecution": "取消中",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.completed": "已完成",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.exceptionCompleted": "已完成",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.canceled": "已取消",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.breakCompleted": "接口触发任务中断",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.cancelFailed": "取消失败",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.breakFailed": "接口触发任务中断失败",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.taskUnitComplete": "子任务完成",
  "geekplus.gms.client.screen.taskMonitoring.subTaskStatus.closed": "已完成",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.taskStop": "任务暂停",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.subTaskPause": "子任务暂停",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorCategory.systemDispatch": "系统(调度模块)",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorCategory.systemEquipment": "系统(设备模块)",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorCategory.systemOther": "系统(业务模块)",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorCategory.robot": "机器人",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorCategory.container": "货架",

  "geekplus.gms.client.screen.taskMonitoring.taskErrorLevel.warn": "警告",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorLevel.errorNeedCatch": "错误，需要干预",

  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21000": "末端二维码定位侧向偏差>20mm；角度大于2°",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21001": "直线行走角度偏差超过3°",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21002": "旋转积分角度和编码器角度的差值超限制",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21003": "机器人中心位置偏移超限制",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21004": "旋转时驱动轮打滑",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21005": "控制指令方向和实际角度旋转方向不一致。如：可能指令错误，可能负载过大",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21006": "根据点拟合圆弧时，轨迹平滑度累计误差超限制",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21007": "进田字格场景，目标姿态和停止姿态误差超限制",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21008": "旋转货架角度超过180°",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21009": "货架二维码和地面二维码相对误差超限制",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21010": "举升货架时举歪",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21011": "机器人停止模式下陀螺积分检测。如：机器人停止状态下被外力转动则可能报错",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21012": "机器人停止模式下图像融合角度变化检测。如：机器人停止状态下被外力转动则可能报错",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21013": "机器人停止模式下编码器角度变化检测。如：机器人停止状态下被外力转动则可能报错",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21014": "机器人走到充电站位置偏差xy坐标20mm",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21015": "充电站位置xy偏差整点坐标20mm",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21016": "充电后退方向不对",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21017": "举升时高度参数不对",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21018": "下降时高度参数不对",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21019": "机器人当前点与路径起始位置超过限制",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21020": "旋转时调整货架拍到货架位置比较差",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21021": "放货架时货架二维码位置很差就不往下放，防止撞到旁边的货架",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21022": "丢失地面二维码超过2个",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21023": "获取dsp数据ceil rect超时",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21024": "获取dsp数据ceil decode超时",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21025": "规划小路径时角度错误",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21026": "手动模式收到了去执行任务错误",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21027": "充电失败",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21028": "行走过程中，向上货架二维码不在视野范围内",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21029": "切换二维码导航后踩不到第1个地面二维码",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21030": "运动时车轮打滑",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21031": "货架模型识别异常",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21032": "急停开关触发",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21033": "进入了手动模式",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21057": "前防撞条触发",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21059": "后防撞条触发",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21072": "向上货架二维码解码超时",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21060": "机器人避障",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21034": "避障盒/上位机未收到避障传感器数据",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21035": "避障盒与主控通讯链路异常",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21036": "避障盒/上位机收到错误避障传感器数据",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21037": "电源盒与主控通讯链路异常",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21038": "电源盒收到错误的电池数据",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21039": "CAN1通讯故障。如：400ms内未收到数据",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21040": "CAN2通讯故障。如：400ms内未收到数据",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21041": "驱动器失联",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21042": "编码器失联",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21043": "DSP失联。如：40s内未收到DSP数据",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12000": "机器人无法链接",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12001": "子任务发送超时",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21044": "陀螺温度变化过大",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21045": "陀螺前后两次标定的基准值变化过大",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21046": "旋转时驱动轮打滑",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21047": "2s之内没收到避障数据更新",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21048": "200s之内没收到电池数据更新",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21049": "驱动轮锁死故障。如：在直线、弧线、旋转等运动模式下，超过2秒没有运动，即认为驱动轮锁死",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21050": "直线运动时左轮打滑",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21051": "直线运动时右轮打滑",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21052": "保存图像时出错，如：地面二维码校准误差>10°，侧边误差>4cm",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21053": "非二维码区域图像反馈，如：丢码时报错",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21054": "提升电机无法举升。如：在举升状态下20s内没有举升托盘即报错",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21055": "驱动轮编码器脉冲数未更新。如：直线或弧线运动下，编码器故障",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21056": "驱动轮编码器脉冲数溢出，即计数超过上限",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21058": "货架二维码解码错误。如：已解出黑框，但是码值错误",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21061": "相机上传的图像数据解析头尾校验失败",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21062": "相机上传的图像数据解析头尾校验和失败",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21063": "充电无电流",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21064": "充电传感器故障",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21065": "2s内避障原始数据未更新",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21066": "驱动轮充电过流",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21067": "驱动轮电机过流",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21068": "提升电机过流",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21069": "DSP丢失心跳，在正确二维码位置没有反馈",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21070": "DSP数据反馈错误",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21071": "货架二维码解码失败。如：连黑框都未解析出来",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21073": "驱动器过温",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21074": "激光雷达数据丢失，或器件故障",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21075": "电池数据丢失",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21076": "电池过温保护",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21077": "电机模组可恢复故障",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21078": "电机模组不可恢复故障（换件）",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21079": "绝对值编码器电池低电量",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21080": "称重传感器数据丢失",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21081": "主控与工控通信中断",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21082": "任务状态机切换错误",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21083": "任务结束时给出错误指令",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21084": "任务步骤stage错误",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12002": "路径规划失败",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12003": "机器人不在地图",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12004": "任务起点或终点是障碍",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12005": "途中有障碍机器人或障碍货架",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12006": "路径资源可能正被占用",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12007": "无路径可以规划",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21085": "驱动器指令错误",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21086": "驱动器电机相位错误",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21087": "驱动器跟踪错误",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21088": "驱动器反馈错误",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21089": "驱动器欠电压",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21090": "驱动器过电压",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21091": "驱动器过电流",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21092": "驱动器电流短路",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21093": "驱动器电机温度过高",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21094": "驱动器电机温度过低",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21095": "驱动器温度过高",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21096": "驱动器温度过低",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21097": "驱动器超速报警",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21098": "驱动器地址错误",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21099": "组件发货异常",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21100": "组件收货异常",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21101": "组件通信中断",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21102": "组件自身故障",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12009": "机器人取货架动作超时",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12010": "未到达货架位置取货架",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12011": "长时间未到达等待点",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12012": "机器人电量长时间不上涨",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12013": "机器人达到低电比",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12014": "没有匹配到充电站，充电任务无法执行",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12015": "没有空闲充电站，充电任务无法执行",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12016": "没有可用充电站，充电任务无法执行",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12017": "充电站单元格被其他机器人占用",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12018": "跨区域充电失败",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12019": "跨楼层充电失败",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12020": "当前任务阻塞，无法执行充电任务",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12021": "当前任务未完成，无法执行充电任务",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12022": "充电时间过长",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12023": "机器人电池温度过高",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12024": "找不到停靠位",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.16000": "机器人异常",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21103": "深度相机数据丢失",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21104": "鱼眼相机数据丢失",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21105": "网络中断",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21106": "驱动器数据丢失，或器件故障",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21107": "触发STO",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21108": "按下解抱闸",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21109": "电量耗尽",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21110": "秤重超载或偏载超限",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.21111": "定位丢失",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12025": "机器人被锁定",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12026": "机器人电池温度过低",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.12027": "货箱任务出现空取空放异常",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.22000": "RMS通信中断",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.22001": "CAN通信中断(充电模块)",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.22002": "屏幕通信中断",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.22003": "RMS数据异常",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.22004": "RMS命令异常",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.22005": "叉车充电站按下急停",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.22006": "叉车充电站没检测到传感器",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.22007": "充电模块过温",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.22008": "自动模式充电电流为0",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.22009": "充电模块警告状态",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.22010": "充电模块错误状态",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.22011": "充电站上报充电站不可用状态",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.13000": "充电站掉线(短暂网络断)",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.13001": "充电站离线(长时间失联)",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.13002": "开始充电站指令发送超时",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.13003": "结束充电站指令发送超时",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.13004": "机器人充电失败太多次",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.13005": "充电桩重启中",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.13006": "充电桩升级中",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.14000": "货架位置待确认",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.81000": "全场机器人电量偏低",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.91000": "急停按钮触发",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.91001": "急停按钮断开连接",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.110": "RMS正在初始化",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2224": "根据流程（流程模板）指定的机器人型号/ID未匹配到可用机器人",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2014": "货架还未就绪",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2020": "机器人任务编号不能为空",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2021": "机器人任务不存在",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2030": "机器人编号不能为空",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2031": "根据流程（流程模板）指定的机器人型号/ID未匹配到可用机器人",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2040": "货架编码不能为空",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2041": "内外部货架编码不匹配",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2042": "无效的货架编码",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2043": "货架编码不存在",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2044": "货架还有任务未完成",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2060": "工作站编号不能为空",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2062": "无效的工作站编号",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2063": "工作站不存在",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2061": "内外部工作站不匹配",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2080": "终点区域编号不能为空",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2081": "终点区域不存在",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2090": "终点不能为空",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2091": "内外部终点坐标不匹配",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2093": "无效的终点二维码",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2094": "无效的终点单元格",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2100": "终点二维码编码不能为空",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2101": "终点二维码编码不存在",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2110": "暂停点二维码编码不能为空",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2111": "暂停点二维码不存在",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2140": "无效的取消动作",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2141": "取消任务失败",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2150": "电梯Id不能为空",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2151": "电梯不存在",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2152": "相关楼层不被电梯连通",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2160": "机器人未到达电梯入口",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2161": "机器人不在电梯",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.3020": "货架编码不能为空",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.3021": "非法的货架编码",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.3023": "货架不存在",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.3022": "内外部货架不匹配",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.3024": "仓库货架入场中",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.3025": "仓库货架已存在",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.3030": "货架入场点超出地图范围",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.3031": "内外部货架入场点不匹配",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.3032": "此入场点已存在货架",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.3033": "入场点不能为空",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.3034": "入场点单元格不合法",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.3035": "仓库请求区域不存在",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.3051": "此区域已满",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.3040": "仓库请求单元格编码不能为空",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.3041": "仓库请求无效的单元格编码",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.3043": "仓库请求单元格不存在",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.3042": "仓库请求内外部单元格不匹配",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.4000": "查询指令不能为空",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.4001": "非法的查询指令",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.4002": "查询指令已过期",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.4003": "不支持的查询指令",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.4500": "没有找到查询数据",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.1201": "原始任务号不能为空",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.1514": "系统暂停",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.1200": "设备信息为空",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.1216": "设备禁用",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.1211": "查询设备信息为空",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.1213": "查询设备任务信息为空",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.1230": "设备任务操作内容为空",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.1226": "参数错误",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.1231": "入参值为空",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.1207": "body里的params字段未传或格式不正确",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.1208": "入参指令未转ASCII,只能填纯数字",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.1232": "映射指令参数值错误",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.1302": "非法协议",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.1301": "非法的控制指令",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.10001000": "系统急停",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.10002000": "流程配置异常",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.10002001": "货架处于非空闲状态",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.10002002": "无可用货架",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.10002003": "目标点(目标区域)不空闲",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.10002004": "无可用机器人",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.10002005": "任务发送RMS异常",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.10002006": "未识别到容器码",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.10002007": "识别到的容器码与下发的不一致",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.10002008": "其它异常",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.10003001": "排队等待机器人",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.10003002": "接口指令【{0}】执行异常",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.10003004": "目标点{0}被任务{1}占用，请检查该任务有无异常并处理",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.10003005": "目标点{0}被容器{1}占用，请前往场地监控/容器列表中更新该容器到其他位置或移除",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.10003006": "目标区域{0}无空闲位置",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.10003007": "没有可用提升机",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.10003008": "容器{0}被锁定，任务无法执行，请前往场地监控解锁或处理任务",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.10003009": "排队区{0}无空闲位置",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2050": "非法的货架面",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2051": "货架面不能为空",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2092": "终点坐标超出地图边界",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2120": "货架分数超出范围",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.2130": "任务优先级超出范围",
  "geekplus.gms.client.screen.taskMonitoring.taskErrorMsg.1219": "原任务还有未完成的关联任务",

  "geekplus.gms.client.screen.taskMonitoring.detail.page.title": "任务详情",
  "geekplus.gms.client.screen.taskMonitoring.detail.page.tableTitle": "子任务信息",

  "geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.taskId.label": "任务号",
  "geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.taskId.copy": "复制",
  "geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.copySuccess": "复制成功",
  "geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.taskName": "任务名称",
  "geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.taskName.value.tooltip": "点击查看该任务关联的流程配置",
  "geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.from": "来源",
  "geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.priority": "优先级",
  "geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.creator": "创建人",
  "geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.start": "起点",
  "geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.upstreamTaskNum": "上游任务号",
  "geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.robotTaskID": "机器人任务号",
  "geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.end": "终点",
  "geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.createTime": "创建时间",
  "geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.completedTime": "完成时间",
  "geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.currentStage": "当前阶段",
  "geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.workflowEndMethod": "结束方式",
  "geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.exceptionDesc": "异常描述",

  "geekplus.gms.client.screen.taskMonitoring.detail.columns.subTaskId": "子任务号",
  "geekplus.gms.client.screen.taskMonitoring.detail.columns.start": "起点",
  "geekplus.gms.client.screen.taskMonitoring.detail.columns.target": "目标点",
  "geekplus.gms.client.screen.taskMonitoring.detail.columns.robotNum": "机器人",
  "geekplus.gms.client.screen.taskMonitoring.detail.columns.container": "容器编码",
  "geekplus.gms.client.screen.taskMonitoring.detail.columns.subTaskStatus": "状态",
  "geekplus.gms.client.screen.taskMonitoring.detail.columns.createTime": "创建时间",
  "geekplus.gms.client.screen.taskMonitoring.detail.columns.completeTime": "完成时间",
  "geekplus.gms.client.screen.taskMonitoring.detail.columns.actions": "操作",

  "geekplus.gms.client.screen.taskMonitoring.detail.taskProcess.tabs.process": "执行过程",
  "geekplus.gms.client.screen.taskMonitoring.detail.taskProcess.tabs.log": "操作日志",

  "geekplus.gms.client.screen.taskMonitoring.statistics.task.all": "全部",
  "geekplus.gms.client.screen.taskMonitoring.statistics.task.create": "待下发",
  "geekplus.gms.client.screen.taskMonitoring.statistics.task.executing": "执行中",
  "geekplus.gms.client.screen.taskMonitoring.statistics.task.pause": "已暂停",
  "geekplus.gms.client.screen.taskMonitoring.statistics.task.exception": "异常",
  "geekplus.gms.client.screen.taskMonitoring.statistics.task.exceptionCompleted": "手动完成",
  "geekplus.gms.client.screen.taskMonitoring.statistics.task.completed": "已完成",
  "geekplus.gms.client.screen.taskMonitoring.statistics.task.canceled": "已取消",
  "geekplus.gms.client.screen.taskMonitoring.statistics.task.cancelExecuting": "取消中",

  "geekplus.gms.client.screen.taskMonitoring.taskProcess.ajax.requestUrl": "请求地址：{requestUrl}",
  "geekplus.gms.client.screen.taskMonitoring.taskProcess.ajax.title": "接口调用详情",
  "geekplus.gms.client.screen.taskMonitoring.taskProcess.ajax.req.name": "发送方",
  "geekplus.gms.client.screen.taskMonitoring.taskProcess.ajax.req.time": "发送时间",
  "geekplus.gms.client.screen.taskMonitoring.taskProcess.ajax.req.detail": "发送消息",
  "geekplus.gms.client.screen.taskMonitoring.taskProcess.ajax.res.name": "接收方",
  "geekplus.gms.client.screen.taskMonitoring.taskProcess.ajax.res.time": "接收时间",
  "geekplus.gms.client.screen.taskMonitoring.taskProcess.ajax.res.detail": "响应消息",

  "geekplus.gms.client.screen.taskMonitoring.taskSource.dio": "上游接口",
  "geekplus.gms.client.screen.taskMonitoring.taskSource.pda": "PDA",
  "geekplus.gms.client.screen.taskMonitoring.taskSource.station": "工作站",
  "geekplus.gms.client.screen.taskMonitoring.taskSource.workflowInitiation": "主流程触发",
  "geekplus.gms.client.screen.taskMonitoring.taskSource.device": "传感器",
  "geekplus.gms.client.screen.taskMonitoring.taskSource.triggerTask": "系统触发器",
  "geekplus.gms.client.screen.taskMonitoring.taskSource.wifiButton": "呼叫器",

  "geekplus.gms.client.screen.taskMonitoring.locationTypes.start": "起点",
  "geekplus.gms.client.screen.taskMonitoring.locationTypes.stopPoint": "停靠点",
  "geekplus.gms.client.screen.taskMonitoring.locationTypes.workstation": "工作站",
  "geekplus.gms.client.screen.taskMonitoring.locationTypes.shelfPoint": "货架点",
  "geekplus.gms.client.screen.taskMonitoring.locationTypes.area": "区域",
  "geekplus.gms.client.screen.taskMonitoring.locationTypes.other": "其他",
  "geekplus.gms.client.screen.taskMonitoring.locationTypes.generalPoint": "地图点位",
  "geekplus.gms.client.screen.taskMonitoring.locationTypes.palletPoint": "托盘位",
  "geekplus.gms.client.screen.taskMonitoring.locationTypes.equipmentPoint": "设备点位",
  "geekplus.gms.client.screen.taskMonitoring.locationTypes.queueArea": "排队区",
  "geekplus.gms.client.screen.taskMonitoring.locationTypes.denseStorageArea": "密集存储区",

  "geekplus.gms.client.screen.taskMonitoring.deviceExecutionStatus.success": "成功",
  "geekplus.gms.client.screen.taskMonitoring.deviceExecutionStatus.deviceOffline": "设备离线",
  "geekplus.gms.client.screen.taskMonitoring.deviceExecutionStatus.signalTrigger": "标志位触发",
  "geekplus.gms.client.screen.taskMonitoring.deviceExecutionStatus.timeout": "超时",
  "geekplus.gms.client.screen.taskMonitoring.deviceExecutionStatus.cancel": "取消",
  "geekplus.gms.client.screen.taskMonitoring.deviceExecutionStatus.failed": "失败",

  "geekplus.gms.client.screen.taskMonitoring.workflowPhase.create": "创建",
  "geekplus.gms.client.screen.taskMonitoring.workflowPhase.executing": "执行中",
  "geekplus.gms.client.screen.taskMonitoring.workflowPhase.completed": "已完成",
  "geekplus.gms.client.screen.taskMonitoring.workflowPhase.closed": "已完成",
  "geekplus.gms.client.screen.taskMonitoring.workflowPhase.canceled": "已取消",
  "geekplus.gms.client.screen.taskMonitoring.workflowPhase.canceling": "取消中",

  "geekplus.gms.client.screen.taskMonitoring.robotPhase.goFetching": "正在取货架（容器）",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.shelfFetched": "货架已经取到",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.goDelivering": "正在搬运货架",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.queuing": "正在排队",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.shelfArrived": "货架已经抵达目的地",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.goReturn": "货架归还中",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.shelfTurning": "货架旋转",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.moving": "正在移动",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.arrivedWaitPoint": "到达等待点",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.leavingWaitPoint": "离开等待点",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.receiveFinish": "辊筒机器人收货完成",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.dropFinish": "辊筒机器人卸货完成",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.arrivedElevatorEntry": "到达电梯门口的等待点(电梯外),等待进入电梯",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.enteredElevator": "已经进入电梯",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.leavedElevator": "离开电梯",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.arrived": "到达目的地",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.fetched": "任务已经完成",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.arrivedAreaEntry": "请求进入区域",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.enteredArea": "已进入区域",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.leavedArea": "已离开区域",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.turnFinished": "完成转面",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.toWaitbit": "去等待位",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.arrivedWaitbit": "到达等待位",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.arrivedOperate": "到达对接位",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.operateEntry": "请求进入对接位",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.operateAllowed": "准备进入对接位",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.adjustOperate": "对接位姿态调整完成",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.operateFinished": "对接完成/对接位（机台）动作完成",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.operateUnitCompleted": "离开对接位，单元动作完成",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.goOffsetFinish": "复合机器人前后偏移完成",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.passWaitPoint": "经过途经点",
  "geekplus.gms.client.screen.taskMonitoring.robotPhase.robotTurning": "旋转本体",

  "geekplus.gms.client.screen.taskMonitoring.execution.name.startNode": "任务启动",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.waitNode": "等待点：{endLocationCode}",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.autoAssignmentNode": "自动赋值点：{endLocationCode}",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.passNode": "途经点：{endLocationCode}",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.arrivedNode": "已到达{endLocationCode}",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.completed": "任务已完成",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.closed": "任务已手动完成{operator}",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.deleted": "任务已删除{operator}",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.canceled": "任务已被人工取消{operator}",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.robotRemoved": "任务已被人工取消（移除机器人取消）",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.robotTerminalCanceled": "任务已被人工取消（机器人端取消）",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.robotTerminalDeliveryCompleted": "任务已通过机器人端手动完成",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.robotManualDeliveryCompleted": "任务已通过机器人端手动完成",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.cancelWorkflowCompleted": "任务已被人工取消{operator}",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.scanCancel": "任务已被自动取消{operator}",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.forceManualFinished": "任务已被强制完成{operator}",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.manualFinished": "手动完成后的货物已处理完，并解除锁定{operator}",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.manualOneStepFinished": "任务已手动完成{operator}",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.forceCancelFinished": "任务已被强制取消{operator}",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.locationFinished": "取消后的货物已处理完，并解除锁定{operator}",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.locationAutoPopFinished": "取消操作已完成{operator}",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.currentFinished": "货物已处理完成并解除锁定{operator}",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.receiveRobotTerminalDeliveryCompleted": "放货完成（机器人端操作”放货完成“）",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.undoCanceled": "撤销到起点，任务自动取消",
  "geekplus.gms.client.screen.taskMonitoring.execution.name.operator": "（操作人：{operator}）",

  "geekplus.gms.client.screen.taskMonitoring.startLocationCode.-1": "机器人初始位置",

  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.createTask.flowTemplate": "上游通过流程模板下发任务",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.createTask.flowConfig": "上游通过流程下发任务",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.createTask.transportationTask": "运输任务下发",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.createStationTask": "工作站发起任务(操作人：{operator})",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.nextTask": "上游触发任务继续",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.createExeTask": "子任务创建（{beginCellCode} → {endCellCode}）",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.backExecution": "回退子任务（{beginCellCode} → {endCellCode}）",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.undoExecution": "撤销子任务（{beginCellCode} → {endCellCode}）",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveRobotMoving": "机器人{robotId}开始移动",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveRobotFetched": "机器人{robotId}已取到货",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveRobotShelfArrived": "机器人{robotId}已放货完成",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveRobotArrived": "机器人{robotId}到达{endLocationCode}",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveRobotArriveWaitPoint": "机器人{robotId}到达等待点{waitCellCode}",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.leavingWaitPoint": "机器人{robotId}离开等待点{waitCellCode}",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveRobotActionCompleted": "{command}完成",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveRobotActionCompleted.rollerRotate": "辊筒上装开始滚动",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveRobotPassWaitPoint": "机器人{robotId}到达途经点{passByCellCode}",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.sendDmpTask": "给{deviceName}发送设备指令【{deviceActionTitle}】",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.sendDmpTaskFail": "给{deviceName}发送设备指令【{deviceActionTitle}】失败 ({msg})",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.retryDmpTask": "给{deviceName}重发设备指令【{deviceActionTitle}】(操作人：{operator})",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.completeDmpTask": "手动完成{deviceName}的设备指令【{deviceActionTitle}】(操作人：{operator})",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.retryDmpSubTask": "在子流程{taskId}下，给{deviceName}重发设备指令【{deviceActionTitle}】(操作人：{operator})",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.completeDmpSubTask": "在子流程{taskId}下，手动完成{deviceName}的设备指令【{deviceActionTitle}】(操作人：{operator})",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.sendInterfaceTask": "接口指令【{command}】{result}",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.sendInterfaceTask.success": "执行完成",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.sendInterfaceTask.fail": "执行失败",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.sendRMSRobotTask": "下发机器人指令【{command}】{rotate}",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.sendRMSRobotTask.rotate": "(旋转至{neededSide}面)",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.sendRmsComponentCommandTask": "下发机器人指令【{action}】{rotate}",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.sendRmsComponentCommandTask.rotateAngle": "(旋转至{rotate}°)",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.clearWaitPoint": "等待点清除",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.dmpTaskTrigger": "设备触发指令【{deviceActionTitle}】执行成功",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.waitCommand": "任务执行中断，等待上游或人工触发“继续执行任务”的指令再启动",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.continue": "继续执行任务",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.taskOver": "任务结束",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.forceCancelSubWorkflow": "子流程{subWorkflowId}的任务已被强制取消（操作人：{operator}）",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.forceManualCompletedSubWorkflow": "子流程{subWorkflowId}的任务已手动完成（操作人：{operator}）",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.recoveryWorkflow": "任务已恢复(操作人：{operator})",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.recoveryExecution": "子任务{taskId}已恢复(操作人：{operator})",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.pauseWorkflow": "任务已暂停(操作人：{operator})",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.pauseExecution": "子任务{taskId}已暂停(操作人：{operator})",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.cancelWorkflow": "任务已被人工取消(操作人：{operator})",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.cancelSubWorkflow": "子流程{subWorkflowId}的任务已被人工取消(操作人：{operator})",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.completeWorkflow": "任务已手动完成(操作人：{operator})",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.manualCompleteWorkflowManualWaiting": "任务已手动完成，等待人工处理（操作人：{operator}）",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.adjustPriority": "任务优先级已调为最高(操作人：{operator})",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.pauseSubWorkflow": "子流程{taskId}的任务已暂停(操作人：{operator})",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.recoverySubWorkflow": "子流程{taskId}的任务已恢复(操作人：{operator})",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.pauseSubExecution": "子流程{taskId}下的子任务{subTaskId}已暂停(操作人：{operator})",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.recoverySubExecution": "子流程{taskId}下的子任务{subTaskId}已恢复(操作人：{operator})",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.trafficControlApplyEnter": "请求进入交通管制区【{trafficControlAreaName}】",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.trafficControlApplyEnterFail": "请求进入交通管制区【{trafficControlAreaName}】失败，原因（{msg}）",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveRobotTerminalDeliveryCompleted": "在机器人端操作放货完成",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveRobotTerminalPickupCompleted": "在机器人端操作取货完成",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.completeSubWorkflow": "子流程{taskId}的任务已手动完成(操作人：{operator})",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.sendRmsCancelTask": "下发取消指令【{command}】",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveRobotTaskCanceled": "机器人任务{taskId}已取消",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.internalCreateTask.triggerTask": "系统自动触发任务【{name}】",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.internalCreateTask.workflowInitiation": "任务通过主流程【{name}】执行时自动创建",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.internalCreateTask.nodeRelationTask": "传感器触发任务（起点：{startLocationCode}；终点：{endLocationCode}）",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.internalCreateTask.automaticReturnEmptyContainerTask": "系统自动触发空箱返还任务（空箱返还区：{returnType}）",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveRobotShelfFinished": "偏移完成",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveRobotScanContainerError.cancel": "扫码识别容器失败，系统将自动取消任务（原因：{reason}）",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveRobotScanContainerError.wait": "扫码识别容器失败，等待人工处理（原因：{reason}）",

  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveRobotTaskFetched": "机器人已取到货",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveRobotTaskLeaveStart": "机器人{robotId}开始移动",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveRobotTaskMove": "机器人{robotId}开始移动",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveRobotTaskArriveWaitPoint": "机器人到达等待点{waitCellCode}",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.sendRmsCommandTask": "下发移动至{endLocationCode}的{action}指令",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.sendRmsCommandTaskFail": "下发移动至{endLocationCode}的{action}指令失败（{msg}）",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveDmpCallbackSuccess": "设备指令【{deviceActionTitle}】执行成功",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveDmpCallbackFail": "设备指令【{deviceActionTitle}】执行失败({msg})",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus": "通知上游（{status}）",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatusFail": "通知上游失败（{status}）",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.workflowPhase": "任务状态：{workflowPhase}",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.taskPhase": "；⼦任务状态：{taskPhase}",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.robotPhase": "；机器人状态：{robotPhase}",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.exceptionState": "；异常状态：{exceptionState}",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.robotAssigned": "分配到机器人",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.robotArrived": "机器人到达",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.goToPickup": "去目标点取货",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.startPickingUp": "开始取货",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.pickupCompleted": "取货完成",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.goToUnloading": "去目标点放货",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.startUnloading": "开始放货",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.unloadingCompleted": "放货完成",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.robotLeaving": "机器人离开",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.jobWaitingContinue": "中断等待",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.jobWaitingUpdate": "等待任务更新",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.arrivedWaitPoint": "到达等待点",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.leavingWaitPoint": "离开等待点",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.passWaitPoint": "经过途径点",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.arrivedQueuePoint": "到达排队点",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.arrivedElevatorEntry": "到达电梯门口的等待点,等待进入电梯",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.enteredElevator": "已经进入电梯",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.leavedElevator": "已经离开电梯",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.scanError": "扫码异常",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.jobCompleted": "作业完成",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.taskCompleted":"任务完成",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.jobCancelExecuting": "任务取消中",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.jobCanceled": "任务取消",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.jobCancelFailed": "作业取消失败 ，需要人工处理",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.jobWaitingManual": "等待人工处理作业任务，处理容器",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.jobManualCompleteExecuting": "手动完成执行中",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.jobManualCompleted": "任务手动完成",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.jobManualCompleteFailed": "手动完成失败，需要人工处理",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.externalRequestFailed": "指令（{commandCode}）请求失败",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.externalRequestSuccess": "指令（{commandCode}）请求成功",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.jobError": "作业异常",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.executionCompleted": "一级任务完成",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.callbackStatus.subTaskCompleted":"子任务完成",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveRobotTaskArrived": "机器人{robotId}到达{endLocationCode}",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveRobotTaskAllocation": "已分配机器人（机器人ID：{robotId}）",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveRobotTaskShelfArrived": "机器人已放货完成",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.recordDmpTaskSuccess": "硬件指令【{deviceActionTitle}】执行成功",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.recordDmpTaskFail": "硬件指令【{deviceActionTitle}】执行失败({msg})",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.recordDmpTaskTitle": "dmp指令执行详情",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.trafficControlEntered": "机器人已进入交通管制区【{trafficControlAreaName}】",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.trafficControlEnteredFail": "机器人已进入交通管制区【{trafficControlAreaName}】失败，原因（{msg}）",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.trafficControlApplyLeave": "请求离开交通管制区【{trafficControlAreaName}】",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.trafficControlApplyLeaveFail": "请求离开交通管制区【{trafficControlAreaName}】失败，原因（{msg}）",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.trafficControlLeft": "机器人已离开交通管制区【{trafficControlAreaName}】",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.trafficControlLeftFail": "机器人已离开交通管制区【{trafficControlAreaName}】失败，原因（{msg}）",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.robotRemoved": "机器人{robotId}已下线",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.receiveRobotReassign": "已重新分配机器人（机器人ID：{robotId}）",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.cancelWorkflowManualWaiting": "任务已被人工取消，在当前位置处理货物（操作人：{operator}）",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.current.cancelWorkflowManualWaiting": "任务已被人工取消，在当前位置处理货物（操作人：{operator}）",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.location.toDest": "任务已被人工取消，货物将由机器人送往{endLocationCode}（操作人：{operator}）",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.location.toDestWaiting": "机器人到达{endLocationCode}，等待人工处理 ",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.forceManualCompleteWorkflow": "任务已被强制完成（操作人：{operator}）",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.robotTerminalCanceled": "在机器人端操作取消",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.robotIsLockedWaiting": "等待人工处理",
  "geekplus.gms.client.screen.taskMonitoring.subExecutionMsg.sendRmsCompleteTask": "下发取消指令【{instruction}】",

  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.deleteWorkflow": "删除任务",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.deleteExecution": "删除子任务{executionId}",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.recoveryWorkflow": "恢复任务",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.recoveryExecution": "恢复子任务{executionId}",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.pauseWorkflow": "暂停任务",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.pauseExecution": "暂停子任务{executionId}",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.adjustPriority": "将任务优先级调为最高",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.cancelWorkflow": "取消任务",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.cancelExecution": "取消子任务{executionId}",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.pauseSubWorkflow": "暂停子流程{id}的任务",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.recoverySubWorkflow": "恢复子流程{id}的任务",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.cancelSubWorkflow": "取消子流程{id}的任务",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.completeSubWorkflow": "手动完成子流程{id}的任务",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.pauseSubExecution": "暂停子流程{subflow}下的子任务{subTask}",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.recoverySubExecution": "恢复子流程{subflow}下的子任务{subTask}",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.completeWorkflow": "手动完成任务",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.retryDmpSubTask": "在子流程{subflow}下，给{deviceName}重发设备指令【{command}】",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.completeDmpSubTask": "在子流程{subflow}下，手动完成{deviceName}的指令【{command}】",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.completeDmpTask": "手动完成{deviceName}的设备指令【{command}】",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.retryDmpTask": "给{deviceName}重发设备指令【{command}】",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.current.waiting": "取消任务，货物选择在当前位置处理",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.completeWorkflowManualWaiting": "完成并解除锁定",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.startCancel": "取消任务，货物选择送到起点",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.presetStartCancel": "取消任务，货物自动送回预设地点：起点",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.locationCancel": "取消任务，货物选择送到指定位置：{endLocationCode}",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.presetLocationCancel": "取消任务，货物自动送回预设地点：{endLocationCode}",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.forceCancelWorkflow": "强制取消任务",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.forceManualCompleteWorkflow": "强制完成任务",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.robotTerminalDeliveryCompleted": "在机器人端操作放货完成",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.robotTerminalPickupCompleted": "在机器人端操作取货完成",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.forceCancelSubWorkflow": "强制取消子流程{subWorkflowId}的任务",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.forceManualCompleteSubWorkflow": "手动完成子流程{subWorkflowId}的任务",

  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.adjustPriority.t": "调至最高",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.adjustPriority.b": "调至最低",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.adjustPriority.u": "上调一级",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.adjustPriority.d": "下调一级",

  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.backExecution": "回退子任务",
  "geekplus.gms.client.screen.taskMonitoring.taskOperateType.undoExecution": "撤销子任务",

  "geekplus.gms.client.screen.taskMonitoring.subflows.id": "子流程任务号",
  "geekplus.gms.client.screen.taskMonitoring.subflows.workflowStatus": "子流程任务状态",
  "geekplus.gms.client.screen.taskMonitoring.subflows.loop": "循环次数",
  "geekplus.gms.client.screen.taskMonitoring.subflows.loop.content": "设置{total}次，已执行{loop}次",

  "geekplus.gms.client.screen.taskMonitoring.parentWorkflow.loop": "循环次数",
  "geekplus.gms.client.screen.taskMonitoring.parentWorkflow.loop.content": "设置{total}次，已执行{loop}次",
  "geekplus.gms.client.screen.taskMonitoring.parentWorkflow.id": "所属主流程",

  "geekplus.gms.client.screen.taskMonitoring.componentCommands.14": "组件按角度旋转",
  "geekplus.gms.client.screen.taskMonitoring.componentCommands.15": "组件按面旋转",
  "geekplus.gms.client.screen.taskMonitoring.componentCommands.1": "顶升",
  "geekplus.gms.client.screen.taskMonitoring.componentCommands.2": "放下",
  "geekplus.gms.client.screen.taskMonitoring.componentCommands.11": "取货",
  "geekplus.gms.client.screen.taskMonitoring.componentCommands.12": "放货",
  "geekplus.gms.client.screen.taskMonitoring.componentCommands.13": "偏移",
  "geekplus.gms.client.screen.taskMonitoring.componentCommands.16": "翻转",
  "geekplus.gms.client.screen.taskMonitoring.componentCommands.17": "复位",
  "geekplus.gms.client.screen.taskMonitoring.componentCommands.18": "翻转-复位",
  "geekplus.gms.client.screen.taskMonitoring.componentCommands.20": "本体按角度旋转",
  "geekplus.gms.client.screen.taskMonitoring.componentCommands.21": "开始取货",
  "geekplus.gms.client.screen.taskMonitoring.componentCommands.22": "完成取货",

  "geekplus.gms.client.screen.taskMonitoring.componentCommands.rotateAngle": "（旋转角度:{param}）",
  "geekplus.gms.client.screen.taskMonitoring.componentCommands.rotateDirection": "（旋转至{param}面）",

  "geekplus.gms.client.screen.taskMonitoring.RMSRobotTasks.mediaPlay": "语音播放",
  "geekplus.gms.client.screen.taskMonitoring.RMSRobotTasks.turnOfSide": "按面旋转",
  "geekplus.gms.client.screen.taskMonitoring.RMSRobotTasks.FBShift": "前后偏移",
  "geekplus.gms.client.screen.taskMonitoring.RMSRobotTasks.mediaStop": "语音停止播放",

  "geekplus.gms.client.screen.taskMonitoring.filter.locationOpts.group.workstation": "工作站",
  "geekplus.gms.client.screen.taskMonitoring.filter.locationOpts.group.area": "区域",
  "geekplus.gms.client.screen.taskMonitoring.filter.locationOpts.group.stock": "托盘位",
  "geekplus.gms.client.screen.taskMonitoring.filter.locationOpts.group.point": "地图点位",

  "geekplus.gms.client.screen.taskMonitoring.systemError.systemErrorHasCanceled": "系统任务已取消",
  "geekplus.gms.client.screen.taskMonitoring.systemError.taskCanceled": "任务已取消",
  "geekplus.gms.client.screen.taskMonitoring.systemError.checkRobotAndRestart": "请检查机器人上是否有货并处理、重启机器人",
  "geekplus.gms.client.screen.taskMonitoring.systemError.taskDoneByManual": "任务手动完成",
  "geekplus.gms.client.screen.taskMonitoring.systemError.robotReleased": "机器人已释放!",
  "geekplus.gms.client.screen.taskMonitoring.systemError.taskCanceling": "任务取消中，请稍后...",
  "geekplus.gms.client.screen.taskMonitoring.systemError.taskOperating": "处理中，请稍后...",
  "geekplus.gms.client.screen.taskMonitoring.systemError.confirmToCancelTask": "确定要取消当前任务？",
  "geekplus.gms.client.screen.taskMonitoring.systemError.cancelTaskTip": "任务取消后不可恢复，请谨慎操作",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forceCancel.cancelFailed": "取消失败",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forceCancel.operateFailed": "操作失败",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forceCancel.abortCancelTask": "放弃取消操作",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.loaded.selectSendLocation": "请选择任务取消后，货物要送到的位置",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.loaded.current.canceledTip": "点击完成后，系统将释放机器人、点位，如有需要请手动更新库存状态",
  "geekplus.gms.client.screen.taskMonitoring.systemError.operationPrompt": "操作提示",
  "geekplus.gms.client.screen.taskMonitoring.systemError.robotLoaded.selectSendLocation": "请选择任务取消后，货物要送到的位置",
  "geekplus.gms.client.screen.taskMonitoring.systemError.resendTask.resendUseTipTitle": "请手动恢复现场设备，确认设备能够正常工作后再重发",
  "geekplus.gms.client.screen.taskMonitoring.systemError.resendTask.resendUseTip": "确认后系统将重新发送执行失败的设备指令，该设备指令中的所有硬件指令都将重新执行。",
  "geekplus.gms.client.screen.taskMonitoring.systemError.manualCompleteTask.useTipTitle": "请手动打开设备，确认机器人可以通行后再跳过",
  "geekplus.gms.client.screen.taskMonitoring.systemError.manualCompleteTask.useTip": "跳过该设备指令，系统将清除等待点让机器人继续前行",
  "geekplus.gms.client.screen.taskMonitoring.systemError.manualCompleteTask.confirmSkip": "确定跳过",
  "geekplus.gms.client.screen.taskMonitoring.systemError.fork.takeTipTitle": "请手动拿走货物，点击完成",
  "geekplus.gms.client.screen.taskMonitoring.systemError.fork.takeTip": "拿走货物点击完成后，任务才真正取消，释放机器人",
  "geekplus.gms.client.screen.taskMonitoring.systemError.composite.takeTipTitle": "请确认货物已手动拿走",
  "geekplus.gms.client.screen.taskMonitoring.systemError.manualComplete.useTip": "当机器人或设备故障，任务不能通过机器人继续完成时，可使用该功能。",
  "geekplus.gms.client.screen.taskMonitoring.systemError.manualComplete.releaseTipTitle": "请先人工将货物送到目的地，再按以下步骤操作",
  "geekplus.gms.client.screen.taskMonitoring.systemError.manualComplete.releaseTip1": "1、如有容器，请将容器手动更新到目的地",
  "geekplus.gms.client.screen.taskMonitoring.systemError.manualComplete.releaseTip2": "2、点击“完成并解除锁定”，系统将释放机器人、容器、点位",
  "geekplus.gms.client.screen.taskMonitoring.systemError.manualComplete.releaseTip3": "3、如有需要，请手动更新库存",
  "geekplus.gms.client.screen.taskMonitoring.systemError.manualComplete.useTip3": "完成后系统自动释放机器人",
  "geekplus.gms.client.screen.taskMonitoring.systemError.manualComplete.taskHasCompleted": "任务已完成，无法再操作手动完成了",
  "geekplus.gms.client.screen.taskMonitoring.systemError.manualComplete.taskHasCanceled": "任务已取消，不能手动完成",
  "geekplus.gms.client.screen.taskMonitoring.systemError.manualComplete.cancelExecuting": "任务正在取消中，不能手动完成",
  "geekplus.gms.client.screen.taskMonitoring.systemError.manualComplete.completeSuccessfully": "任务已完成",
  "geekplus.gms.client.screen.taskMonitoring.systemError.manualComplete.forceCompleteSuccessfully": "系统任务已完成",
  "geekplus.gms.client.screen.taskMonitoring.systemError.autpPop.sendSuccessTitle": "货物已送达",
  "geekplus.gms.client.screen.taskMonitoring.systemError.autpPop.sendSuccessTip": "“{name}”任务，取消后的货物已送到指定位置，请前往历史任务中查看该任务的详情",
  "geekplus.gms.client.screen.taskMonitoring.systemError.roller.takedGoods": "请确认货物已手动拿走",
  "geekplus.gms.client.screen.taskMonitoring.systemError.roller.releaseRobotTips": "拿走货物点击完成后，机器人才释放",
  "geekplus.gms.client.screen.taskMonitoring.systemError.roller.current.releaseRobotTips": "点击完成后，系统将释放机器人、点位",
  "geekplus.gms.client.screen.taskMonitoring.systemError.roller.current.releaseRobotTipTitle": "任务已取消，请先移走货物，再点击完成",
  "geekplus.gms.client.screen.taskMonitoring.systemError.roller.location.cancelSuccessToLocation": "货物已送到：{0}",
  "geekplus.gms.client.screen.taskMonitoring.systemError.roller.location.cancelSuccessToLocationTip1": "请手动操作放货，放货后点击完成，系统将释放机器人、点位",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.fetching.cancelUseageTitle": "机器人在取放货对接中，如需取消请按以下步骤操作：",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.fetching.cancelUseageTip1": "1、先按急停，让机器人和上装停止动作",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.fetching.cancelUseageTip2": "2、将机器人和上装切为“手动模式”",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.fetching.cancelUseageTip3": "3、处理好货物，确保不会掉料、撞料",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.fetching.cancelUseageTip4": "4、遥控机器人，使其跟机台或缓存架分离",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.fetching.cancelUseageTip5": "执行完以上步骤，再点击“确定”",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.fetching.cancelSuccessTitle": "任务已取消",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.fetching.cancelSuccessTip": "请确认货物不在机器人上，再点击按钮解锁机器人，如有需要，请手动更新库存",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.releaseTips": "请确认货物已放好，不在机器人上，再点击按钮释放机器人",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.location.cancelSuccessToLocation": "货物已送到：{0}",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.location.cancelSuccessToLocationTip": "请手动操作放货，放货后点击完成，系统将释放机器人、点位，如有需要请手动更新库存状态",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.current.cancelSuccessTitle": "任务已取消，请先移走货物，再点击完成",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.current.cancelSuccessTip": "点击完成后，系统将释放机器人、点位，如有需要请手动更新库存状态",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.doubleLoads.cancelTipTitle": "请手动拿走空料筒，",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.doubleLoads.cancelTip": "选择满料卷要送到的位置",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.loads.cancelTip": "请选择任务取消后，货物要送到的位置",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.forceCancel.unloads.cancelTitle": "机器人任务取消失败或超时，您可以强制取消系统任务",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.forceCancel.unloads.cancelTip": "强制取消后，需要按以下步骤处理：",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.forceCancel.unloads.cancelTip1": "1、检查机器人，如有货物请先处理，确保无掉料、撞料风险",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.forceCancel.unloads.cancelTip2": "2、重启机器人",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.forceCancel.loads.failReason": "机器人任务取消失败或超时，您可以强制取消系统任务",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.forceCancel.loads.forceCancelTaskTip": "强制取消后，需要按以下步骤处理：",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.forceCancel.loads.step1": "1、检查机器人，如有货物请先处理，确保无掉料、撞料风险",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.forceCancel.loads.step2": "2、重启机器人",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.forceCancel.loads.step3": "3、在场地监控页面中将机器人解锁",
  "geekplus.gms.client.screen.taskMonitoring.systemError.topModule.forceCancel.loads.step4": "4、如有需要，请手动更新库存",
  "geekplus.gms.client.screen.taskMonitoring.systemError.roller.forceCancel.failReason": "机器人任务取消失败或超时，您可以强制取消系统任务",
  "geekplus.gms.client.screen.taskMonitoring.systemError.roller.forceCancel.forceCancelTaskTip": "强制取消后，需要按以下步骤处理：",
  "geekplus.gms.client.screen.taskMonitoring.systemError.roller.forceCancel.checkAndHandleGoods": "1、检查机器人，如有货物请先处理，确保无掉料、撞料风险",
  "geekplus.gms.client.screen.taskMonitoring.systemError.roller.forceCancel.restartRobot": "2、重启机器人",
  "geekplus.gms.client.screen.taskMonitoring.systemError.roller.forceCancel.loads.failReason": "机器人任务取消失败或超时，您可以强制取消系统任务",
  "geekplus.gms.client.screen.taskMonitoring.systemError.roller.forceCancel.loads.forceCancelTaskTip": "强制取消后，需要按以下步骤处理：",
  "geekplus.gms.client.screen.taskMonitoring.systemError.roller.forceCancel.loads.step1": "1、检查机器人，如有货物请先处理，确保无掉料、撞料风险",
  "geekplus.gms.client.screen.taskMonitoring.systemError.roller.forceCancel.loads.step2": "2、重启机器人",
  "geekplus.gms.client.screen.taskMonitoring.systemError.roller.forceCancel.loads.step3": "3、在场地监控页面中将机器人解锁",
  "geekplus.gms.client.screen.taskMonitoring.systemError.roller.fetching.cancelTip": "机器人在取放货对接中，如需取消任务，请确保无掉料、撞料风险后，再点击“确定”",
  "geekplus.gms.client.screen.taskMonitoring.systemError.roller.fetching.releaseTip": "请确认货物不在机器人上，再点击按钮解锁机器人",
  "geekplus.gms.client.screen.taskMonitoring.systemError.roller.fetching.releaseTitle": "任务已取消",
  "geekplus.gms.client.screen.taskMonitoring.systemError.roller.autoPop.cancelSuccessTitle": "取消成功！",
  "geekplus.gms.client.screen.taskMonitoring.systemError.roller.autoPop.cancelSuccessTip": "货物将由机器人送往所选位置~",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.autoRecycle.cancelSuccessTip": "货物将由机器人送往预设位置：{location}",
  "geekplus.gms.client.screen.taskMonitoring.systemError.roller.autoPop.sendSuccessTitle": "货物已送达",
  "geekplus.gms.client.screen.taskMonitoring.systemError.roller.autoPop.sendSuccessTip": "“{name}”,任务，取消后的货物已送到指定位置，请前往历史任务中查看该任务的详情",
  "geekplus.gms.client.screen.taskMonitoring.systemError.deviceTask.resendSuccess": "任务重发成功",
  "geekplus.gms.client.screen.taskMonitoring.systemError.deviceTask.manualCompleteSuccess": "设备任务已手动完成",
  "geekplus.gms.client.screen.taskMonitoring.systemError.currentStateNotSupportCancel": "当前阶段已不支持取消",
  "geekplus.gms.client.screen.taskMonitoring.systemError.taskHasCompleted": "任务已完成，无法取消",
  "geekplus.gms.client.screen.taskMonitoring.systemError.taskHasFinished": "任务已结束",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.goodsReturnedToScheduledPlace": "任务取消后，货物将由机器人送回预设地点",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.taskHasDeleted": "系统任务已删除",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.deleteConfirm": "确定要删除当前任务？",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.deleteStepTitle": "删除系统任务后，需要按以下步骤处理：",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.deleteStep1": "1、检查机器人，如有货物请先处理，确保无掉料、撞料风险",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.deleteStep2": "2、重启机器人",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.deleteStep3": "3、手动更新容器位置",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.unloads.cancelTaskTip": "任务取消后不可恢复，请谨慎操作",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.loadChangeTip": "机器人已从“负载状态”变成“空载状态”，请重新操作！",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.selectSendLocation": "请选择送回点位",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.taskCompletedCannotDeleted": "任务已完成，无法删除",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.load.current.cancelSuccessTip": "点击完成后，系统将释放机器人、容器、点位",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.load.wait.cancelSuccessTitle": "任务已取消，请先移走货物，更新容器位置，再点击完成",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.forceCancel.unload.useTip": "机器人任务取消失败或超时，您可以强制取消系统任务",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.forceCancel.unload.useStep": "强制取消后，需要按以下步骤处理：",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.forceCancel.unload.step1": "1、检查机器人，如有货物请先处理，确保无掉料、撞料风险",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.forceCancel.unload.step2": "2、重启机器人",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.forceCancel.load.useTip": "机器人任务取消失败或超时，您可以强制取消系统任务",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.forceCancel.load.useStep": "强制取消后，需要按以下步骤处理：",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.forceCancel.load.step1": "1、检查机器人，如有货物请先处理，确保无掉料、撞料风险",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.forceCancel.load.step2": "2、手动更新容器位置",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.forceCancel.load.step3": "3、重启机器人",
  "geekplus.gms.client.screen.taskMonitoring.systemError.forklift.forceCancel.load.step4": "4、在场地监控页面中将机器人解锁",
  "geekplus.gms.client.screen.taskMonitoring.systemError.manualComplete.forceCancel.useTip": "机器人任务处理失败或超时，您可以强制完成系统任务",
  "geekplus.gms.client.screen.taskMonitoring.systemError.manualComplete.forceCancel.useStep": "强制完成后，需要按一下步骤处理",
  "geekplus.gms.client.screen.taskMonitoring.systemError.manualComplete.forceCancel.step1": "1、检查机器人，如有货物请先处理，确保无调料、撞料风险",
  "geekplus.gms.client.screen.taskMonitoring.systemError.manualComplete.forceCancel.step2": "2、如有容器，请手动更新容器位置",
  "geekplus.gms.client.screen.taskMonitoring.systemError.manualComplete.forceCancel.step3": "3、重启机器人",
  "geekplus.gms.client.screen.taskMonitoring.systemError.manualComplete.forceCancel.step4": "4、在场地监控页面中将机器人解锁",
  "geekplus.gms.client.screen.taskMonitoring.systemError.arrivedCancelPoint.location": "机器人已到达取消后的放货点，等待人工处理",
  "geekplus.gms.client.screen.taskMonitoring.systemError.arrivedCancelPoint.autoPop": "机器人已到达取消后的放货点",
  "geekplus.gms.client.screen.taskMonitoring.systemError.arrivedCancelPoint.current": "等待人工处理",
  "geekplus.gms.client.screen.taskMonitoring.systemError.button.toHandle": "去处理",
  "geekplus.gms.client.screen.taskMonitoring.systemError.tips.operateSuccess": "操作成功",

  "geekplus.gms.client.screen.taskMonitoring.systemError.location.cancelSuccessToLocation": "取消成功，机器人正在前往：{location}",
  "geekplus.gms.client.screen.taskMonitoring.systemError.location.cancelSuccessToLocationTip": "请等机器人送到货后，再手动操作完成",

  "geekplus.gms.client.screen.taskMonitoring.componentTaskPhase.receiveFinish": "取货",
  "geekplus.gms.client.screen.taskMonitoring.componentTaskPhase.dropFinish": "放货",
  "geekplus.gms.client.screen.taskMonitoring.componentTaskPhase.goOffsetFinish": "偏移",
  "geekplus.gms.client.screen.taskMonitoring.componentTaskPhase.turnFinished": "旋转",

  "geekplus.gms.client.screen.taskMonitoring.sendRmsCommandTask.action.goFetch": "取货",
  "geekplus.gms.client.screen.taskMonitoring.sendRmsCommandTask.action.goReturn": "放货",

  "geekplus.gms.client.screen.taskMonitoring.systemName.upstream": "上游系统",
  "geekplus.gms.client.screen.taskMonitoring.systemName.upstream.clientCode": "上游系统({clientCode})",

  "geekplus.gms.client.screen.taskMonitoring.containerDetail.loadingStatus.label": "满（有货，物料类别：{materialType}）",
  "geekplus.gms.client.screen.taskMonitoring.taskProcess.recordDmpTaskError.actionType": "操作类型：{value}",
  "geekplus.gms.client.screen.taskMonitoring.taskProcess.recordDmpTaskError.plcAddress": "寄存器地址：{value}",
  "geekplus.gms.client.screen.taskMonitoring.taskProcess.recordDmpTaskError.configWriteValue": "配置的写入值：{value}",
  "geekplus.gms.client.screen.taskMonitoring.taskProcess.recordDmpTaskError.expectReadValue": "期望读到值：{value}",
  "geekplus.gms.client.screen.taskMonitoring.taskProcess.recordDmpTaskError.actualWriteValue": "实际写入值：{value}",
  "geekplus.gms.client.screen.taskMonitoring.taskProcess.recordDmpTaskError.actualReadValue": "实际读到值：{value}",
  "geekplus.gms.client.screen.taskMonitoring.taskProcess.recordDmpTaskError.startTime": "开始执行时间：{value}",
  "geekplus.gms.client.screen.taskMonitoring.taskProcess.recordDmpTaskError.endTime": "执行完成时间：{value}",
  "geekplus.gms.client.screen.taskMonitoring.taskProcess.recordDmpTaskError.taskStatus": "状态",
  "geekplus.gms.client.screen.taskMonitoring.taskProcess.recordDmpTaskError.errorMsg": "异常信息",
  "geekplus.gms.client.screen.taskMonitoring.taskProcess.recordDmpTaskError.table.plcAddress": "寄存器地址",
  "geekplus.gms.client.screen.taskMonitoring.taskProcess.recordDmpTaskError.table.plcValue": "寄存器值",
  "geekplus.gms.client.screen.taskMonitoring.taskProcess.recordDmpTaskError.table.plcAddressGroup": "寄存器地址：寄存器组",

  "lang.venus.common.plc.err.ioException": "io异常",
  "lang.venus.common.plc.err.slaveException": "slave异常",
  "lang.venus.common.plc.err.noConnect": "离线异常",
  "lang.venus.common.plc.err.otherException": "其他异常，请联系开发人员",
  "lang.venus.common.plc.err.notSupportWrite": "不支持写",

  "geekplus.gms.client.screen.taskMonitoring.scanAbnormal.30001": "未识别到容器码",
  "geekplus.gms.client.screen.taskMonitoring.scanAbnormal.30002": "识别到的容器码与下发的不一致",

  "geekplus.gms.client.screen.taskMonitoring.emptyContainerReturnType.workflowStartNode": "流程七点",
  "geekplus.gms.client.screen.taskMonitoring.emptyContainerReturnType.containerEnterMapDest": "容器入场终点",
}
