{
    "geekplus.gms.client.components.dialogForm.closeConfirm.title": "Close confirmation",
    "geekplus.gms.client.components.dialogForm.closeConfirm.msg": "All changes made to the form will be lost after exiting. Are you sure to exit?",
    "geekplus.gms.client.components.dialogForm.closeConfirm.btnCancel": "Cancel",
    "geekplus.gms.client.components.dialogForm.closeConfirm.btnConfirm": "Confirm closing",
    "geekplus.gms.client.components.responseErrorWrapper.responseError": "API request error",
    "geekplus.gms.client.components.dialogHelp.usageManual": "User manual",
    "geekplus.gms.client.components.dialogHelp.usageManual.gms": "Geek+ Moving System (GMS) Function Manual",
    "geekplus.gms.client.components.dialogHelp.usageManual.pda": "Geek+ Moving System (GMS) PDA Operation Manual",
    "geekplus.gms.client.components.dialogHelp.api": "Interface document",
    "geekplus.gms.client.components.dialogHelp.api.gms": "GMS interface document",
    "geekplus.gms.client.components.dialogHelp.configTable": "Configuration specification table for device access",
    "geekplus.gms.client.components.dialogHelp.configTable.single": "Configuration table for I/O box of roller splitting, die cutting, and winding machines for docking with single cantilever robots",
    "geekplus.gms.client.components.dialogHelp.configTable.double": "Configuration table for I/O box of coating and roller splitting machines for docking with low-precision double lifting robots",
    "geekplus.gms.client.components.dialogHelp.configTable.plc": "Configuration table for PLC access of double lifting and single cantilever robots",
    "geekplus.gms.client.components.dialogHelp.configTable.door": "Configuration table for I/O box access of gate",
    "geekplus.gms.client.components.dialogAbout.gms": "About GMS",
    "geekplus.gms.client.components.dialogAbout.geek+": "Geekplus Technology Co., Ltd.",
    "geekplus.gms.client.components.dialogAbout.3rdLibs": "This product also contains the following templates covered by {0} license:",
    "geekplus.gms.client.components.dialogAbout.configLib": "Configuration template",
    "geekplus.gms.client.components.robotTypes.lift": "Latent",
    "geekplus.gms.client.components.robotTypes.roller": "Roller",
    "geekplus.gms.client.components.robotTypes.forklift": "Forklift",
    "geekplus.gms.client.components.robotTypes.hybrid": "Composite",
    "geekplus.gms.client.components.uploader.error.exceedLimit": "Upload a maximum of {0} files",
    "geekplus.gms.client.components.autoSetNode.placeholder.enterControlNodeEtc": "Enter the control node/current node/interactive action",
    "geekplus.gms.client.components.autoSetNode.currentNodeEquals": "The current node is",
    "geekplus.gms.client.components.autoSetNode.nodeAssignStrategy": "Node assignment policy",
    "geekplus.gms.client.components.autoSetNode.targetableMaterialPort": "Allocable feed gate",
    "geekplus.gms.client.components.autoSetNode.tooltip.assignStrategy": "Strategy ({index}): When {controlLogic} is {relatedNode}, the current node is {currentNode}.",
    "geekplus.gms.client.components.autoSetNode.filename.assignStrategyImportTemplate": "Assignment strategy import template",
    "geekplus.gms.client.components.autoSetNode.dialog.batchImportNodeAssignStrategy": "Assignment strategy for nodes imported in batch",
    "geekplus.gms.client.components.autoSetNode.error.duplicateRelatedNode": "Repeated control node",
    "geekplus.gms.client.components.autoSetNode.error.pleaseSelectRelatedNode": "Please select a control node",
    "geekplus.gms.client.components.autoSetNode.error.pleaseSelectCurrentNode": "Please select the current node",
    "geekplus.gms.client.components.autoSetNode.error.nthRowRelatedNodeExisted": "The control node in row ({row}) already exists.",
    "geekplus.gms.client.components.autoSetNode.error.twoRelatedNodesRowDuplicate": "The control node in row ({0}) and that in row ({1}) are repeated.",
    "geekplus.gms.client.components.positionSelect.group.mapPoint": "Map location",
    "geekplus.gms.client.components.positionSelect.group.workstation": "Workstation",
    "geekplus.gms.client.components.positionSelect.group.area": "Area",
    "geekplus.gms.client.components.positionSelect.group.pallet": "Pallet position",
    "geekplus.gms.client.components.positionSelect.group.equipment": "Device location",
    "geekplus.gms.client.components.positionSelect.reloadIconTip": "Click Refresh data",
    "geekplus.gms.client.components.positionSelect.searchResultTip": "{count} results",
    "geekplus.gms.client.components.positionSelect.selectedListTitle": "{Count} results are selected.",
    "geekplus.gms.client.components.positionSelect.sortIconTip": "When the selected quantity is less than {count}, they can be dragged for sorting.",
    "geekplus.gms.client.components.dialogHelp.api.usage": "Interface docking assistance",

    // 地图点位选择器
    "geekplus.gms.client.components.mapSelect.pleaseSelectCell": "Please select the cell",
}
