<template>
  <gp-tooltip :effect="effect" :open-delay="openDelay" :placement="placement">
    <template #content>
      <slot name="content">{{ content }}</slot>
    </template>
    <icon-question class="tw-cursor-pointer" />
  </gp-tooltip>
</template>

<script>
import IconQuestion from "./icons/icon-question.vue";

export default {
  name: "GmsTooltip",
  components: {
    IconQuestion,
  },
  props: {
    effect: {
      type: String,
      default: "dark",
    },
    content: {
      type: String,
      default: "",
    },
    placement: {
      type: String,
      default: "top",
    },
    openDelay: {
      type: Number,
      default: 200,
    },
  },
};
</script>
