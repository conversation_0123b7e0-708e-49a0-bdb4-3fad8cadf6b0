<template>
  <div class="tw-flex tw-justify-center tw-h-full">
    <div v-if="!isNoStandard" class="tw-flex tw-justify-center tw-items-center">
      <div class="tw-relative">
        <img :src="shelfImg" alt="" :class="cn.img" />
        <HighLightBox
          :styles="{
            top: '30px',
            left: '30px',
            width: '240px',
            height: '40px',
            borderDirection: 'borderBottom',
            display: 'flex',
            transform: 'rotate(-21.5deg)',
            justifyContent: 'center',
          }"
          :is-active="$props.active === 'surfaceWidth'"
          :value="surfaceWidth"
        />
        <!-- $props.active === 'surfaceWidth' -->
        <HighLightBox
          :styles="{
            top: '26px',
            left: '277px',
            width: '232px',
            height: '40px',
            borderDirection: 'borderBottom',
            display: 'flex',
            justifyContent: 'center',
            transform: 'rotate(21deg)',
          }"
          :is-active="$props.active === 'surfaceLength'"
          :value="surfaceLength"
        />
        <!-- $props.active === 'surfaceLength' -->
        <HighLightBox
          :styles="{
            bottom: '-5px',
            left: '252px',
            width: '14px',
            height: '34px',
            borderDirection: 'borderTop',
            transform: 'rotate(16deg)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'flex-end',
          }"
          :is-active="$props.active === 'legLength'"
          :value="legLength"
        />
        <!-- $props.active === 'legLength' -->
        <HighLightBox
          :styles="{
            bottom: '-7px',
            left: '277px',
            width: '8px',
            height: '36px',
            borderDirection: 'borderTop',
            transform: 'rotate(-30deg)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'flex-end',
          }"
          :is-active="$props.active === 'legWidth'"
          :value="legWidth"
        />
        <HighLightBox
          :styles="{
            bottom: '44px',
            left: '266px',
            width: '58px',
            height: '36px',
            borderDirection: 'borderBottom',
            transform: 'rotate(90deg)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }"
          :is-active="$props.active === 'legHeight'"
          :value="legHeight"
        />
      </div>
      <!-- $props.active === 'legHeight' -->
    </div>
    <NoStandardAnnotation v-if="isNoStandard" v-bind="props" @update:active="$emit('update:active', $event)" />
  </div>
</template>
<script>
import { defineComponent, computed } from "vue";
import { bracketModelType } from "gms-constants";
import getContainerImage from "@/utils/containerImages";
const shelfImg = getContainerImage("bracket.svg");
import NoStandardAnnotation from "./no-standard-annotation.vue";
import HighLightBox from "./highligt-box.vue";

export default defineComponent({
  name: "ShelfAnnotation",
  components: { NoStandardAnnotation, HighLightBox },
  props: {
    formValues: { type: Object, default: () => ({}) },
    active: { type: String, default: "" },
  },
  setup(props) {
    const isNoStandard = computed(() => props.formValues.modelingMethod === bracketModelType.NON_STANDARD);
    const surfaceWidth = computed(() => props.formValues.modelInfo.surfaceWidth);
    const surfaceLength = computed(() => props.formValues.modelInfo.surfaceLength);
    const legLength = computed(() => props.formValues.modelInfo.legLength);
    const legWidth = computed(() => props.formValues.modelInfo.legWidth);
    const legHeight = computed(() => props.formValues.modelInfo.legHeight);

    return { props, isNoStandard, shelfImg, surfaceWidth, surfaceLength, legLength, legWidth, legHeight };
  },
});
</script>
<style lang="scss" module="cn">
.img {
  width: 500px;
}
</style>
