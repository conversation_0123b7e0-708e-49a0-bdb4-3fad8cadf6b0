<template>
  <gp-checkbox :class="cn.root" v-bind="$attrs" v-on="$listeners">
    <slot></slot>
  </gp-checkbox>
</template>
<script>
import { defineComponent } from "vue";

export default defineComponent({
  name: "MCheck<PERSON>",
  setup() {},
});
</script>
<style lang="scss" module="cn">
.root {
  :global(.gp-checkbox__inner) {
    border-radius: 50%;
  }
  :global(.gp-checkbox__input) {
    position: absolute;
    right: 0;
  }
  :global(.gp-checkbox__label) {
    width: 100%;
  }
}
</style>
