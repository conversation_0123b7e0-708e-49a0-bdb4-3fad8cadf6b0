/**
 *   流程配置页面国际化字符集
 *   key 必须以 'geekplus.gms.client.screen.processConfig.' 开头
 */
{
  "geekplus.gms.client.screen.processConfig.column.guid": "流程编码",
  "geekplus.gms.client.screen.processConfig.column.name": "流程名称",
  "geekplus.gms.client.screen.processConfig.column.robotModel": "机器人型号",
  "geekplus.gms.client.screen.processConfig.column.status": "状态",
  "geekplus.gms.client.screen.processConfig.column.lastModifiedBy": "编辑人",
  "geekplus.gms.client.screen.processConfig.column.lastModifiedTime": "编辑时间",
  "geekplus.gms.client.screen.processConfig.column.priority": "优先级",
  "geekplus.gms.client.screen.processConfig.column.specifyRobot": "指定机器人",
  "geekplus.gms.client.screen.processConfig.column.cancelStrategy": "取消策略",
  "geekplus.gms.client.screen.processConfig.column.robotFree": "判断空闲机器人",
  "geekplus.gms.client.screen.processConfig.column.note": "备注",

  "geekplus.gms.client.screen.processConfig.btnCreateProcess": "新建流程",

  "geekplus.gms.client.screen.processConfig.dialogDesign.stepConfig": "基础配置",
  "geekplus.gms.client.screen.processConfig.dialogDesign.stepDesign": "流程设计",
  "geekplus.gms.client.screen.processConfig.dialogDesign.btn.release": "发布",

  "geekplus.gms.client.commons.constants.processStatus.designing": "设计中",
  "geekplus.gms.client.commons.constants.processStatus.released": "已发布",
}
