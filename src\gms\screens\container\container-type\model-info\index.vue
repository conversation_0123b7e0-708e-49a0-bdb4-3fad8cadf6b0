<template>
  <div>
    <NoPalletModel
      v-if="!isPallet"
      v-bind="$props"
      @update:formGroupList="$emit('update:formGroupList', $event)"
      @update:formValues="$emit('update:formValues', $event)"
      @update:active="$emit('update:active', $event)"
    />
    <PalletModel
      v-if="isForkPallet"
      v-bind="$props"
      @update:formGroupList="$emit('update:formGroupList', $event)"
      @update:formValues="$emit('update:formValues', $event)"
    />
    <MovePalletModel
      v-if="isMovePallet"
      v-bind="$props"
      @update:formGroupList="$emit('update:formGroupList', $event)"
      @update:formValues="$emit('update:formValues', $event)"
    />
  </div>
</template>
<script>
import { defineComponent, computed } from "vue";
import { pallets } from "../state";
import NoPalletModel from "./no-pallet-model";
import PalletModel from "./pallet-model";
import MovePalletModel from "./pallet-model/move-pallet.vue";
import { containerTypeShape } from "@/gms/commons/constants";

export default defineComponent({
  name: "ModelInfo",
  components: { NoPalletModel, PalletModel, MovePalletModel },
  inject: { formGroupList: { type: Array, default: () => [] } },
  props: {
    formValues: { type: Object, default: () => ({}) },
    formGroup: { type: Object, default: () => ({}) },
    formGroupList: { type: Array, default: () => [] },
    active: { type: String, default: "" },
    mode: { type: String, default: "add" },
  },
  emit: ["update:active", "update:formValues"],
  setup(props) {
    const isPallet = computed(() => pallets.includes(props.formValues.type));
    const isForkPallet = computed(() => props.formValues.type === containerTypeShape.FORKLIFT_PALLET);
    const isMovePallet = computed(() => props.formValues.type === containerTypeShape.PALLET);

    return { isPallet, isForkPallet, isMovePallet };
  },
});
</script>
