<template>
  <gms-dialog :visible="visible" width="400px" :show-footer-sep-line="false" @closed="handleClose">
    <template #title>
      <div class="tw-flex tw-items-center tw-gap-2">
        <gp-icon name="gp-icon-warning" class="tw-text-orange-400 tw-text-xl" />
        <h3 class="tw-text-sm tw-font-bold">
          {{ title }}
        </h3>
      </div>
    </template>
    <div>
      <p>{{ confirmContent }}</p>
      <div v-if="$props.operableList?.length > 1 && $props.unOperableList.length" class="tw-mt-2">
        <p>{{ $t(validTip) }}</p>
        <p>{{ $t("geekplus.gms.client.screen.container.containerCode") }}：{{ validCodes.join("，") }}</p>
      </div>
      <div v-if="$props.unOperableList?.length" class="tw-mt-2">
        <p class="tw-text-red-500">{{ $t(invalidTip) }}</p>
        <p class="tw-text-red-500">
          {{ $t("geekplus.gms.client.screen.container.containerCode") }}：{{ invalidCodes.join("，") }}
        </p>
      </div>
    </div>
    <template #footer>
      <gp-button plain @click="handleClose">
        {{ $t("geekplus.gms.client.commons.btn.cancel") }}
      </gp-button>
      <gp-button type="primary" @click="handleConfirm">
        {{ $t("geekplus.gms.client.commons.btn.confirm") }}
      </gp-button>
    </template>
  </gms-dialog>
</template>
<script>
import fp from "lodash/fp";
import { defineComponent, computed } from "vue";
import { confirmMsg, ENTRY_BTN, LEAVE_BTN, DELETE_BTN } from "./state";

const actionTips = {
  valid: {
    [ENTRY_BTN]: "geekplus.gms.client.screen.container.tips.canEnter",
    [LEAVE_BTN]: "geekplus.gms.client.screen.container.tips.canLeave",
    [DELETE_BTN]: "geekplus.gms.client.screen.container.tips.canDelete",
  },
  invalid: {
    [ENTRY_BTN]: "geekplus.gms.client.screen.container.tips.cannotEnter",
    [LEAVE_BTN]: "geekplus.gms.client.screen.container.tips.cannotLeave",
    [DELETE_BTN]: "geekplus.gms.client.screen.container.tips.cannotDelete",
  },
};

export default defineComponent({
  name: "DialogConfirm",
  props: {
    visible: { type: Boolean, default: false },
    unOperableList: { type: Array, default: () => [] },
    operableList: { type: Array, default: () => [] },
    btn: { type: String, default: "" },
  },
  emits: ["update:visible"],
  setup(props, { emit }) {
    const validCodes = computed(() => fp.map("code", props.operableList));
    const invalidCodes = computed(() => fp.map("code", props.unOperableList));
    const validTip = computed(() => fp.get(`valid.${props.btn}`, actionTips));
    const invalidTip = computed(() => fp.get(`invalid.${props.btn}`, actionTips));
    const confirmContent = computed(() => confirmMsg[`${props.btn}Content`]);
    const title = computed(() => confirmMsg[`${props.btn}Title`]);

    const handleClose = () => {
      emit("update:visible", false);
    };

    const handleConfirm = () => {
      const { btn, operableList } = props;
      emit("confirm", { btn, list: fp.map("id", operableList) });
    };

    return {
      title,
      validCodes,
      invalidCodes,
      validTip,
      invalidTip,
      confirmContent,
      handleClose,
      handleConfirm,
    };
  },
});
</script>
