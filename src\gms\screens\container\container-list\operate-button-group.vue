<template>
  <div>
    <gp-button type="primary" @click="$emit('confirm', { btn: ADD_BTN })">
      {{ $t("geekplus.gms.client.screen.container.btns.addContainer") }}
    </gp-button>
    <gp-button type="primary" :disabled="!isBatchEnterable" @click="handleBatchConfirm(ENTRY_BTN)">
      {{ $t("geekplus.gms.client.screen.container.btns.containerEntry") }}
    </gp-button>
    <gp-button type="primary" :disabled="!isBatchLeavable" @click="handleBatchConfirm(LEAVE_BTN)">
      {{ $t("geekplus.gms.client.screen.container.btns.containerLeave") }}
    </gp-button>
    <gp-button type="primary" :disabled="!isBatchDeletable" @click="handleBatchConfirm(DELETE_BTN)">
      {{ $t("geekplus.gms.client.commons.btn.delete") }}
    </gp-button>

    <dialog-confirm
      v-if="state.dialogConfirmVisible"
      :visible.sync="state.dialogConfirmVisible"
      :operable-list="state.operableList"
      :un-operable-list="state.unOperableList"
      :btn="state.batchBtn"
      @confirm="handleConfirm"
    />
  </div>
</template>
<script>
import fp from "lodash/fp";
import { defineComponent, computed, watchEffect, reactive } from "vue";
import { ADD_BTN, ENTRY_BTN, LEAVE_BTN, DELETE_BTN, enterableStatus, deletableStatus, leavableStatus } from "./state";
import DialogConfirm from "./dialog-confirm.vue";

const getOptMap = (btn, state) => {
  let operableList = [];
  let unOperableList = [];
  if (btn === ENTRY_BTN) {
    operableList = state.enterableRows;
    unOperableList = state.unEnterableRows;
  } else if (btn === LEAVE_BTN) {
    operableList = state.leavableRows;
    unOperableList = state.unLeavableRows;
  } else if (btn === DELETE_BTN) {
    operableList = state.deletableRows;
    unOperableList = state.unDeletableRows;
  }
  return { operableList, unOperableList };
};

export default defineComponent({
  name: "OperateButtonGroup",
  components: { DialogConfirm },
  props: {
    rowSelection: { type: Array, default: () => [] },
    record: { type: Object, default: () => ({}) },
  },
  emits: ["confirm"],
  setup(props, { emit }) {
    const state = reactive({
      enterableRows: [],
      unEnterableRows: [],
      leavableRows: [],
      unLeavableRows: [],
      deletableRows: [],
      unDeletableRows: [],
      operableList: [],
      unOperableList: [],
      dialogConfirmVisible: false,
    });
    const isBatchEnterable = computed(() => props.rowSelection.length && state.enterableRows.length);
    const isBatchLeavable = computed(() => props.rowSelection.length && state.leavableRows.length);
    const isBatchDeletable = computed(() => props.rowSelection.length && state.deletableRows.length);

    const getFilterRows = (filters, isNegate) => {
      const isInclude = (v) => filters.includes(v.status);
      return fp.pipe(
        fp.filter(isNegate ? fp.negate(isInclude) : isInclude),
        fp.map((v) => ({ id: Number(v.id), code: String(v.code) }))
      )(props.rowSelection);
    };

    watchEffect(() => {
      state.enterableRows = getFilterRows(enterableStatus);
      state.unEnterableRows = getFilterRows(enterableStatus, true);
      state.deletableRows = getFilterRows(deletableStatus);
      state.unDeletableRows = getFilterRows(deletableStatus, true);
      state.leavableRows = getFilterRows(leavableStatus);
      state.unLeavableRows = getFilterRows(leavableStatus, true);
    });

    const handleBatchConfirm = (btn) => {
      const { operableList, unOperableList } = getOptMap(btn, state);
      state.batchBtn = btn;
      state.operableList = operableList;
      state.unOperableList = unOperableList;

      const validIds = fp.map("id", operableList);

      if (btn === ENTRY_BTN && !state.unEnterableRows.length) {
        emit("confirm", { btn, list: validIds });
        return;
      }

      state.dialogConfirmVisible = true;
    };

    const handleConfirm = ({ btn, list }) => {
      emit("confirm", { btn, list });
      state.dialogConfirmVisible = false;
    };

    return {
      state,
      ADD_BTN,
      ENTRY_BTN,
      LEAVE_BTN,
      DELETE_BTN,
      isBatchEnterable,
      isBatchLeavable,
      isBatchDeletable,
      handleBatchConfirm,
      handleConfirm,
    };
  },
});
</script>

<style lang="scss" scoped>
.confirm-tip-message-box {
  :global(.gp-message-box__title) {
    text-align: center;
  }
  :global(.gp-message-box__container) {
    display: flex;
  }
  :global(.gp-message-box__status) {
    position: static;
    transform: none;
  }
  :global(.gp-message-box__status + .gp-message-box__message) {
    padding-left: 10px;
  }
}
</style>
