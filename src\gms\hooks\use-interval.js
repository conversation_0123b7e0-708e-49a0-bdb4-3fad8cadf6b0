import { reactive } from "vue";

/**
 * @param {Object} options
 * @param {Number} options.task
 * @param {Number} options.interval
 */
const noop = () => {};

export function useInterval(options) {
  const { task = noop, interval = 1000 } = options;
  let timer;

  const state = reactive({
    runArgs: undefined,

    start: () => {
      clearTimeout(timer);
      // run task
      task(() => {
        // rerun task after interval
        timer = setTimeout(() => {
          state.start();
        }, interval);
      }, ...(state.runArgs ?? []));
    },

    /**
     * stop timer
     */
    stop: () => {
      clearTimeout(timer);
    },

    /**
     * run task immediately and restart timer
     */
    cutInRun: (...args) => {
      clearTimeout(timer);
      state.runArgs = args;
      state.start();
      state.runArgs = undefined;
    },

    restartInterval: () => {
      clearTimeout(timer);
      timer = setTimeout(() => {
        state.start();
      }, interval);
    },
  });

  return state;
}
