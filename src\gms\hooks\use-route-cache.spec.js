import { uniqueId } from "lodash";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { routeResponseCache, useRouteCache } from "./use-route-cache";

const ajaxFn = vi.fn().mockImplementation(() => Promise.resolve("data"));
const ajaxErrorFn = vi.fn().mockImplementation(() => Promise.reject("some error"));
const switchRoute = () => routeResponseCache.updateRoutePath(uniqueId("route_"));

let requestHandler;
beforeEach(() => {
  ajaxFn.mockClear();
  ajaxErrorFn.mockClear();
  switchRoute();
  requestHandler = useRouteCache(ajaxFn);
});

describe("hook: useRouteCache", () => {
  it("should cache data in the same route", () => {
    const promise1 = requestHandler();
    expect(ajaxFn).toHaveBeenCalledTimes(1);
    const promise2 = requestHandler();
    expect(ajaxFn).toHaveBeenCalledTimes(1);
    expect(promise1).toBe(promise2);
  });

  it("should clear cache after route switch", () => {
    const promise1 = requestHandler();
    expect(ajaxFn).toHaveBeenCalledTimes(1);
    switchRoute();
    const promise2 = requestHandler();
    expect(ajaxFn).toHaveBeenCalledTimes(2);
    expect(promise1).not.toBe(promise2);
  });

  it("should remove current response cache if request encounter error", async () => {
    const errorRquest = useRouteCache(ajaxErrorFn);
    await errorRquest().catch(() => {});
    expect(ajaxErrorFn).toHaveBeenCalledTimes(1);
    await errorRquest().catch(() => {});
    expect(ajaxErrorFn).toHaveBeenCalledTimes(2);
  });
});
