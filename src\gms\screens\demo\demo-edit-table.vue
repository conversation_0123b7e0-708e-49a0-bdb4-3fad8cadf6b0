<template>
  <div>
    <div class="tw-flex tw-justify-between tw-align-middle tw-mb-4">
      <div>Edit Table</div>
      <gp-button type="primary" @click="handleAdd">Add Row</gp-button>
      <gp-button @click="handleValidate">Validate</gp-button>
    </div>
    <div>{{ state.tableData }}</div>
    <edit-table ref="editTableRef" v-model="state.tableData" :columns="state.columns" />
  </div>
</template>

<script>
import { defineComponent, reactive, ref } from "vue";
import EditTable from "gms-components/business/edit-table/index.vue";

export default defineComponent({
  name: "EditTableDemo",

  components: { EditTable },

  setup() {
    const genderOptions = [
      { label: "男", value: "m" },
      { label: "女", value: "f" },
    ];

    const maleFeatureOptions = [
      { label: "高", value: "tall" },
      { label: "矮", value: "short" },
    ];

    const femaleFeatureOptions = [
      { label: "黑", value: "black" },
      { label: "白", value: "white" },
    ];

    const state = reactive({
      tableData: [
        { name: "<PERSON>", gender: "m", feature: "tall" },
        { name: "Natasha", gender: "f", feature: "black" },
      ],
      columns: [
        { prop: "name", label: "姓名", component: "gp-input", required: true },
        {
          prop: "gender",
          label: "性别",
          component: "gp-select",
          options: genderOptions,
          handleChange: () => {
            // the returned value will be used to update form fields
            return { feature: "" };
          },
        },
        {
          prop: "feature",
          label: "特征",
          component: "gp-select",
          options: (row) => (row.gender === "m" ? maleFeatureOptions : femaleFeatureOptions),
        },
        // 删除按钮使用 component: "delete" 标明
        { prop: "action", label: "操作", component: "delete" },
      ],
    });

    const editTableRef = ref(null);

    function handleAdd() {
      editTableRef.value.addRow();
    }

    async function handleValidate() {
      const result = await editTableRef.value.validate();
      console.log("validate result:", result);
    }

    return { state, editTableRef, handleAdd, handleValidate };
  },
});
</script>
