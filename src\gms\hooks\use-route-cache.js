/**
 * 基于路由的请求响应缓存
 *
 * 使用场景：
 * ------
 * 在同一路由下的页面, 多次请求同一个接口需要缓存数据时可使用此缓存。大多数情况下，对于实时性要求不高的数据，
 * 比如 select 下拉选项，或是数据字典，但切换路由后才需要刷新数据，这更符合用户的习惯。
 *
 * - cache key: the request function
 * - cache value: the response promise

 }
 */
class RouteResponseCache {
  constructor() {
    this._cache = new WeakMap();
    this._routePath = null;
  }

  updateRoutePath(path) {
    // clear cache when route path changed
    if (this._routePath !== path) {
      this._routePath = path;
      this._cache = new WeakMap();
    }
  }

  add(requestFn) {
    this._cache.set(requestFn, requestFn());
    return this;
  }

  has(requestFn) {
    return this._cache.has(requestFn);
  }

  get(requestFn) {
    return this._cache.get(requestFn);
  }

  remove(requestFn) {
    this._cache.delete(requestFn);
  }
}

export const routeResponseCache = new RouteResponseCache();
/**
 * create a request function that cache response by route path
 *
 * sigature: (requestFn) => () => Promise
 *
 * usage:
 * -----------------
 *  const requestData = useRouteCache(() => ajax.get("/xxx"));
 *  const data = await requestData(); // this data will be cached by route path
 */
export function useRouteCache(requestFn) {
  function requestHander() {
    const dataPromise = routeResponseCache.has(requestFn)
      ? routeResponseCache.get(requestFn)
      : routeResponseCache.add(requestFn).get(requestFn);

    // if request fails, remove cache, so that next time it will be sent again
    dataPromise.catch((e) => {
      routeResponseCache.remove(requestFn);
      return e;
    });

    return dataPromise;
  }

  requestHander.clearCache = () => {
    routeResponseCache.remove(requestFn);
  };

  return requestHander;
}
