<template>
  <div class="tw-flex tw-flex-col tw-gap-6">
    <template v-if="showCodeForm">
      <!-- <gp-row :gutter="32">
        <gp-col :span="12">
          <FormItem v-bind="baseFormGroup.codeRule" />
        </gp-col>
        <gp-col :span="12">
          <FormItem v-bind="baseFormGroup.codePrefix" />
        </gp-col>
      </gp-row> -->
      <gp-row :gutter="32">
        <gp-col :span="12">
          <!-- <FormItem v-bind="baseFormGroup.num">
            <template #edit="scope">
              <div class="tw-flex tw-items-center">
                <gp-input
                  size="medium"
                  maxlength="2"
                  :value="scope.value"
                  @input="handleChangeNum(scope, 'num', $event)"
                >
                  <gp-button
                    slot="append"
                    type="primary"
                    :loading="machineState.matches('add.list.batching')"
                    @click="handleBatchAdd"
                  >
                    {{ $t("geekplus.gms.client.screen.container.btns.batchAdd") }}
                  </gp-button>
                </gp-input>
              </div>
            </template>
          </FormItem> -->
          <FormItem v-bind="baseFormGroup.num"></FormItem>
        </gp-col>
      </gp-row>
    </template>

    <template v-if="showEntryFrom">
      <gp-row :gutter="32">
        <gp-col :span="12">
          <FormItem :label-width="'160px'" v-bind="entryFormGroup.loadingStatus" />
        </gp-col>
        <gp-col :span="12">
          <FormItem :label-width="'160px'" v-bind="entryFormGroup.placementAngle">
            <template #labelHelp>
              <img :src="angleTooltip" alt="placementAngle" class="tw-h-80" />
            </template>
          </FormItem>
        </gp-col>
      </gp-row>
      <gp-row v-if="showMaterialType" :gutter="32">
        <gp-col :span="12">
          <FormItem class="tw-pl-4.5" v-bind="entryFormGroup.materialType" />
        </gp-col>
      </gp-row>
    </template>
  </div>
</template>

<script>
import { fill } from "lodash";
import { defineComponent, onMounted, computed, watch } from "vue";
import { FormItem, createFormItemGroup, formItemTypes, validators } from "@srpe-fe/uic-el-form-wrapper-vue2";
import { useI18n } from "gms-hooks";
import { codeRule, loadingStatus, entryAngle, containerStatus } from "gms-constants";
import { addContainer } from "@/gms/apis/container";
import getContainerImage from "@/utils/containerImages";
const angleTooltip = getContainerImage("angle-tip.png");
import { matchesSome } from "./machine/index.js";

export default defineComponent({
  name: "ContainerInfo",

  components: { FormItem },

  props: {
    record: { type: Object, default: () => ({}) },
    options: { type: Object, default: () => ({}) },
    machineState: { type: Object, default: null },
    send: { type: Function, default: null },
  },

  setup(props, { emit }) {
    const t = useI18n();

    /** 是否处于编辑模式 */
    // const isEdit = props.machineState.matches("edit");

    const labelWidth = "140px";
    const baseFormGroup = createFormItemGroup(
      {
        // codeRule: {
        //   type: formItemTypes.EL_RADIO_GROUP,
        //   labelText: t("geekplus.gms.client.screen.container.containerCode"),
        //   value: props.record?.codeRule ?? codeRule.DEFAULT,
        //   options: codeRule.toLabelValueList(),
        //   labelPosition: "left",
        //   labelHelp: t("geekplus.gms.client.screen.container.tips.codeRule"),
        //   labelWidth,
        //   validators: [validators.required],
        //   isHidden: isEdit,
        //   handleChange: (value) => {
        //     baseFormGroup.codePrefix.value = "";
        //     // 切换为自定义规则时，清空上一次的报错信息和前缀
        //     if (value === codeRule.CUSTOMIZED) {
        //       baseFormGroup.$clearMessage();
        //     }
        //   },
        // },
        // codePrefix: {
        //   type: formItemTypes.EL_INPUT,
        //   labelText: t("geekplus.gms.client.screen.container.form.codePrefix"),
        //   value: props.record?.codePrefix,
        //   labelPosition: "left",
        //   labelWidth,
        //   placeholder: t("geekplus.gms.client.screen.container.placeholder.upTo4Bits"),
        //   validators: [
        //     validators.required,
        //     ({ done, value }) => {
        //       done(!/^[A-Za-z]{1,4}$/g.test(value) ? t("geekplus.gms.client.commons.validator.upTo4Bits") : null);
        //     },
        //   ],
        //   maxlength: 4,
        //   isHidden: ({ formData }) => {
        //     return isEdit || formData.codeRule === codeRule.DEFAULT;
        //   },
        // },
        num: {
          // type: formItemTypes.CUSTOMIZED,
          type: formItemTypes.EL_INPUT_NUMBER,
          controlsPosition: "default", // 这个设
          labelText: t("geekplus.gms.client.screen.container.columns.containerAmount"),
          value: props.record.num || 1,
          labelPosition: "left",
          labelWidth,
          labelHelp: t("geekplus.gms.client.screen.container.tips.containerNum"),
          validators: [
            validators.required,
            // ({ done, value }) => {
            //   done(!/^[1-9]\d*$/.test(value) ? t("geekplus.gms.client.commons.validator.positiveInteger") : null);
            // },
            // ({ done, value }) => {
            //   done(
            //     Number(value) < 1 || Number(value) > 50
            //       ? t("geekplus.gms.client.commons.validator.pleaseInputNumbersFrom1To50")
            //       : null
            //   );
            // },
          ],
          min: 1, // 添加最小值限制
          max: 50, // 添加最大值限制
        },
      },
      { labelWidth: "70px", labelPosition: "top" }
    );

    const materialCode = props.record?.loadInfo?.materialCode ?? "";
    const entryFormGroup = createFormItemGroup({
      loadingStatus: {
        type: formItemTypes.EL_SELECT,
        labelText: t("geekplus.gms.client.screen.container.columns.containerLoadingStatus"),
        value: props.record?.loadingStatus || loadingStatus.UNLOADED,
        labelPosition: "left",
        labelWidth,
        options: loadingStatus.toLabelValueList(),
        validators: [validators.required],
      },
      placementAngle: {
        type: formItemTypes.EL_SELECT,
        labelText: t("geekplus.gms.client.screen.container.columns.containerEntryAngle"),
        value: props.record?.placementAngle ?? entryAngle["90_DEGREES"],
        labelPosition: "left",
        labelWidth: "120px",
        labelHelp: "placementAngle",
        labelHelpEffect: "light",
        options: entryAngle.toLabelValueList(),
        validators: [validators.required],
      },
      materialType: {
        type: formItemTypes.EL_SELECT,
        labelText: t("geekplus.gms.client.screen.container.columns.materialType"),
        value: materialCode ? Number(materialCode) : "",
        options: props.options.materialTypeList,
        // labelPosition: "left",
        labelHelp: t("geekplus.gms.client.screen.container.tips.materialType"),
        labelWidth,
        isHidden: ({ formData }) => {
          return formData.loadingStatus !== loadingStatus.LOADED;
        },
      },
    });

    // 是否显示物料类别
    const showMaterialType = computed(() => entryFormGroup.loadingStatus.value === loadingStatus.LOADED);

    // 是否显示容器编码表格
    const showCodeForm = computed(() => matchesSome(props.machineState, ["add.list"]));
    // 是否显示入场表格
    const showEntryFrom = computed(() => matchesSome(props.machineState, ["entry", "edit"]));

    // const handleChangeNum = (scope, name, value) => {
    //   scope.handleChange(value);
    // };

    // const handleBatchAdd = () => {
    //   baseFormGroup.$validate((valid) => {
    //     if (valid) {
    //       doBatchAdd();
    //     }
    //   });

    //   function doBatchAdd() {
    //     const prefix = baseFormGroup.codePrefix.value;

    //     const list = fill(Array(Number(baseFormGroup.num.value)), {
    //       code: prefix ? prefix : "",
    //       modelInfo: props.record.modelInfo,
    //       status: containerStatus.DRAFT,
    //       loadCarrierModelId: props.record.modelInfo.id,
    //     });

    //     props.send("BATCH");
    //     addContainer(list)
    //       .then((data) => emit("batchAdd", data))
    //       .finally(() => props.send("SUCCESS"));
    //   }
    // };
    const doBatchAdd = () => {
      // const prefix = baseFormGroup.codePrefix.value;
      const list = fill(Array(Number(baseFormGroup.num.value)), {
        code: "",
        modelInfo: props.record.modelInfo,
        status: containerStatus.DRAFT,
        loadCarrierModelId: props.record.modelInfo.id,
      });

      //props.send("BATCH"); // 通知状态机开始批量添加
      addContainer(list) // 调用 API
        .then((data) => emit("batchAdd", data)); // API 成功后发射事件给父组件
      //.finally(() => props.send("SUCCESS")); // API 结束（成功或失败）通知状态机
    };
    watch(
      () => baseFormGroup.num.value,
      (newNum, oldNum) => {
        // Only proceed if the new number is valid and different from the old number
        if (newNum !== oldNum && newNum != null && newNum > 0 && newNum <= 50) {
          baseFormGroup
            .$validate()
            .then((valid) => {
              if (valid) {
                // If the new number is less than the old number, we need to remove containers
                if (newNum < oldNum) {
                  // Emit an event to notify parent component to remove containers
                  emit("removeContainers", oldNum - newNum);
                } else {
                  // If the new number is greater than the old number, add new containers
                  doBatchAdd();
                }
              } else {
                console.error("编码规则或前缀验证失败，无法批量添加");
              }
            })
            .catch(() => {
              console.error("验证过程中发生错误");
            });
        }
      }
    );

    // onMounted(() => {
    //   const list = fill(Array(Number(baseFormGroup.num.value)), {
    //     modelInfo: props.record.modelInfo,
    //     status: containerStatus.DRAFT,
    //     loadCarrierModelId: props.record.modelInfo.id,
    //   });

    //   if (props.machineState.matches("add.list")) {
    //     addContainer(list).then((data) => emit("batchAdd", data));
    //   }
    // });
    onMounted(() => {
      // angleTooltip.value = getContainerImage("angle-tip.png");
      // 保留这里的逻辑，它处理组件在 'add.list' 状态下的初始批量添加
      // 当组件首次加载且状态为 'add.list' 时，根据初始数量（通常是1）执行一次批量添加
      if (props.machineState.matches("add.list")) {
        // 在初始加载时，确保表单验证通过后再调用 API
        baseFormGroup
          .$validate()
          .then((valid) => {
            if (valid) {
              doBatchAdd();
            } else {
              console.error("初始加载时表单验证失败，无法批量添加");
            }
          })
          .catch(() => {
            console.error("初始加载时验证过程中发生错误");
          });
      }
    });

    return {
      baseFormGroup,
      entryFormGroup,
      codeRule,
      angleTooltip,
      showCodeForm,
      showEntryFrom,
      showMaterialType,
      // handleChangeNum,
      // handleBatchAdd,
      doBatchAdd,
    };
  },
});
</script>
<style lang="scss" module="cn">
:global(.gp-input-group--append .gp-input-group__append) {
  color: #fff;
  background-color: #409eff;
  border-color: #409eff;
}

.img_tooltip {
  background: #fff;
  box-shadow: 0 1px 3px rgb(0 0 0 / 30%);
}
</style>
