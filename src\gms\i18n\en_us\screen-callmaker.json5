{"geekplus.gms.client.screen.callmaker.callmakerConfig": "Pager configuration", "geekplus.gms.client.screen.callmaker.intervalSetting": "Setting of task trigger interval", "geekplus.gms.client.screen.callmaker.callmakerName": "Pager name", "geekplus.gms.client.screen.callmaker.callmakerId": "Pager No.", "geekplus.gms.client.screen.callmaker.callmakerModel": "Pager model", "geekplus.gms.client.screen.callmaker.model4keys": "Four-button pager", "geekplus.gms.client.screen.callmaker.IpAddress": "IP address", "geekplus.gms.client.screen.callmaker.status": "State", "geekplus.gms.client.screen.callmaker.creator": "Creator", "geekplus.gms.client.screen.callmaker.createTime": "Creation time", "geekplus.gms.client.screen.callmaker.interval": "Task trigger interval", "geekplus.gms.client.screen.callmaker.createCallmaker": "New pager", "geekplus.gms.client.screen.callmaker.callmakerDetail": "Pager details", "geekplus.gms.client.screen.callmaker.trigger": "<PERSON><PERSON>", "geekplus.gms.client.screen.callmaker.btn.disabled": "Disabled", "geekplus.gms.client.screen.callmaker.btn.enabled": "Enabled", "geekplus.gms.client.screen.callmaker.label.unconfigured": "Not configured", "geekplus.gms.client.screen.callmaker.label.configured": "Configured", "geekplus.gms.client.screen.callmaker.label.buttonEnabled": "Button enabling", "geekplus.gms.client.screen.callmaker.label.buttonFunction": "Button function", "geekplus.gms.client.screen.callmaker.label.buttonType": "Button type", "geekplus.gms.client.screen.callmaker.label.buttonCode": "Button No.", "geekplus.gms.client.screen.callmaker.label.buttonType.selfRecovery": "Self-resetting", "geekplus.gms.client.screen.callmaker.label.buttonType.selfLock": "Self-locking", "geekplus.gms.client.screen.callmaker.label.flowControl": "Workflow Control", "geekplus.gms.client.screen.callmaker.label.flow": "Workflow", "geekplus.gms.client.screen.callmaker.label.nodeControl": "Node Control", "geekplus.gms.client.screen.callmaker.label.systemControl": "System Control", "geekplus.gms.client.screen.callmaker.label.operationCommand": "Operational instruction", "geekplus.gms.client.screen.callmaker.label.operationCommand.start": "Start", "geekplus.gms.client.screen.callmaker.label.triggerMethod": "Trigger mode", "geekplus.gms.client.screen.callmaker.label.codePoint": "Location", "geekplus.gms.client.screen.callmaker.label.pointCode": "Location code", "geekplus.gms.client.screen.callmaker.label.destType": "Destination type", "geekplus.gms.client.screen.callmaker.tip.intervalSetting": "Pressing a single button again within the task trigger interval will not trigger the task, to prevent multiple operations within a short time.", "geekplus.gms.client.screen.callmaker.tip.callmakerId": "The unique pager number can be obtained from the outer packing or button log.", "geekplus.gms.client.screen.callmaker.tip.confirmDelete": "Are you sure to delete {name}?", "geekplus.gms.client.screen.callmaker.tip.operationCommand": "Help document for operational instructions", "geekplus.gms.client.screen.callmaker.tip.triggerMethod": "Help document for trigger modes", "geekplus.gms.client.screen.callmaker.tip.targetPoint": "Help document for destination location"}