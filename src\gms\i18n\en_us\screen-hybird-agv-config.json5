{"geekplus.gms.client.screen.hybirdAGVConfig.sectionTitle.equipParams": "Device parameters", "geekplus.gms.client.screen.hybirdAGVConfig.formLabel.stopPointName": "Name", "geekplus.gms.client.screen.hybirdAGVConfig.msg.binCodeOrderEmpty": "Values area required or not required for [Shelf bin point code] and [Shelf bin SN] at the same time.", "geekplus.gms.client.screen.hybirdAGVConfig.formLabelHelp.binCodeRow1": "The upstream management system may customize the shelf bin point code, like YWLN1234. The shelf bin point code transferred to issue the task shall be bound with the location of the docking point on the map, so that GMS can query the map point through the shelf bin code and generate the task normally.", "geekplus.gms.client.screen.hybirdAGVConfig.formLabelHelp.binCodeRow2": "There are two usage scenarios:", "geekplus.gms.client.screen.hybirdAGVConfig.formLabelHelp.binCodeRow3": "1. The shelf bin code shall be bound with the map point for the upstream system to create the task through customized shelf bin code. With request parameters (including locationTo and toBinOrder) transferred, GMS converts the shelf bin code into the map point and issues it to the management system.", "geekplus.gms.client.screen.hybirdAGVConfig.formLabelHelp.binCodeRow4": "2. When the shelf bin code is inconsistent with the map point, the upstream system directly creates the task through the shelf bin code, while GMS directly issues the shelf bind code to the management system.", "geekplus.gms.client.screen.hybirdAGVConfig.formLabelHelp.binOrder": "The shelf bin SN is used to distinguish the scenario of multiple layers of shelf bin at the same point. For a single layer of shelf bin, the shelf bin SN is 1 by default.", "geekplus.gms.client.screen.hybirdAGVConfig.formLabelHelp.stopPointCode": "Map point code before the robot reaches the machine"}