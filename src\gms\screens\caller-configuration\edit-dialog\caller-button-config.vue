<template>
  <!-- 呼叫器配置 -->
  <div>
    <LoadingSpinner :visible="loadingFlag" />
    <div v-if="!loadingFlag">
      <div :class="co.title2">
        {{ $t("geekplus.gms.client.screen.callerConfiguration.callerid") }}：{{ setNewdeviceId }}
      </div>
      <gp-tabs
        v-model="currentButtonName"
        :class="co.tabs"
        type="border-card"
        tab-position="left"
        :before-leave="handleBeforeLeave"
      >
        <gp-tab-pane v-for="(button, index) in buttons" :key="index" :name="button.buttonCode">
          <template #label>
            <div class="button1">
              <img class="tw-w-9 tw-h-9" :src="button.icon" />
              <div class="buttonText">
                <span class="button1-1">{{ button.names }}</span>
                <gp-tag v-if="button.state" size="small" class="custom-tag tw-text-cyan-900" type="success">{{
                  $t("geekplus.gms.client.screen.callerConfiguration.started")
                }}</gp-tag>
                <gp-tag v-else size="small" type="info" class="tw-text-cyan-900">{{
                  $t("geekplus.gms.client.screen.callerConfiguration.not started")
                }}</gp-tag>
              </div>
            </div>
          </template>
          <!-- 每个按钮的配置 -->
          <ButtonContent
            :button="button"
            :mode="mode"
            :btn-index="index"
            :device-id="setNewdeviceId"
            :trigger-loading="triggerLoading"
            @trigger-button="handleTriggerButton"
            @change-node-code="handleChangeNodeCode"
          />
        </gp-tab-pane>
      </gp-tabs>
    </div>
  </div>
</template>
<script>
// 导入ramda 库

import { defineComponent, ref, reactive, computed, toRefs, onMounted, watch } from "vue";
import { createFormItemGroup, validators } from "@srpe-fe/uic-el-form-wrapper-vue2";
import LoadingSpinner from "@/gms/components/loading-spinner.vue";
import { queryFlowAndGroup } from "@/common/common";
import { getCommandList, getContainerType, physicalButtonTrigger } from "gms-apis/caller-configuration";
// 容器入场角度
import { getFirstNodeWithoutShelf } from "gms-apis/flow-config";
import { entryAngle } from "gms-constants";
import { useI18n } from "@/hooks";
import ButtonContent from "./components/button-content.vue";
import { BUTTON_CONFIGS } from "./common.js";
import { Message } from "geekplus-ui";

export default defineComponent({
  name: "CallerButtonConfig",
  components: { LoadingSpinner, ButtonContent },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    mode: {
      type: String,
      default: "add",
    },
    init_value: {
      type: Array,
      default: [],
    },
    loadingFlag: {
      type: Boolean,
      default: true,
    },
    deviceId: {
      type: String,
      default: "",
    },
  },
  setup(props, { emit }) {
    const { mode, step, visible, loadingFlag } = toRefs(props);
    const t = useI18n();
    const opreatecomlist = ref([]); // 这个是按钮功能列表的数据
    const processList = ref(""); // 获取流程列表的数据
    const processListAll = ref(""); // 存储没有转化的流程列表数据
    const triggerPositionList = ref([]); // 触发位置的数据列表
    const triggerPositonListEdit = ref([]); // 在编辑模式下单触发位置列表
    const containerTypeList = ref([]); // 容器类型列表
    const triggerPositionList2 = ref([]); //按钮二 触发位置的数据列表
    const triggerPositionList3 = ref([]); //按钮三 触发位置的数据列表
    const triggerPositionList4 = ref([]); //按钮四 触发位置的数据列表
    const originalContainerTypeList = ref([]); // 容器类型列表的副本
    // 当前选中的按钮
    const currentButtonName = ref("1");
    const entryAngleOptions = ref(entryAngle.toLabelValueList()); // 容器角度的定义
    const localLoadingFlag = ref(props.loadingFlag);
    const setNewdeviceId = ref(props.deviceId);
    // 按钮的配置项
    // 按钮配置模板
    const buttonConfigs = BUTTON_CONFIGS.map((config) => ({
      ...config,
      name: t(config.name),
    }));
    //  创建验证流程
    const createRequiredValidator = (buttonIndex) => {
      return ({ value, done }) => {
        if (!buttons[buttonIndex].state) {
          done();
        } else if (!value) {
          done(t("geekplus.gms.client.screen.callerConfiguration.fillRequiredFields"));
        } else {
          done("");
        }
      };
    };
    // 创建公共的流程处理函数
    const createWorkflowHandler = (buttonIndex, triggerPositionListRef) => {
      return async (val) => {
        buttons[buttonIndex].selectworkflowConfigId = val;
        if (!buttons[buttonIndex].selectworkflowConfigId) return true;
        buttons[buttonIndex].form.$setData({ triggerposition: "" }); // 清空之前选择的触发位置值
        const workflow = processListAll.value.find((item) => item.id === val);
        if (workflow.nodeList?.some((node) => node.cellType === 2)) {
          const [id, type] = workflow.type.split("_") || [];
          if (id && type) {
            const data = await getFirstNodeWithoutShelf({ id, type });
            const triggerData = getSelectData(data, "cellCode", "hostCellCode");
            triggerPositionListRef.value = triggerData;

            // 在编辑模式下，同时更新triggerPositonListEdit
            if (mode.value === "edit" && buttons[buttonIndex].editId) {
              triggerPositonListEdit.value = triggerData;
            }

            buttons[buttonIndex].triggerflag = true;
            if (workflow.isConcurrent === 0) {
              return;
            }
          }
          buttons[buttonIndex].triggerflag = true;
          return;
        }
        buttons[buttonIndex].triggerflag = false;
      };
    };
    // 控制触发位置的显示
    const showAngleFun = (idx) => {
      return !(buttons[idx].state && buttons[idx].buttonselectvalue == "43");
    };
    // 控制流程显示隐藏
    const showWorkflow = (idx) => {
      return !(buttons[idx].state && buttons[idx].buttonselectvalue == "10");
    };
    const createButtonConfig = (index, icon, name) => {
      const buttonIndex = index;

      return {
        key: `button${index + 1}`,
        names: name,
        buttonCode: String(index + 1),
        icon,
        state: false,
        buttonselectvalue: "",
        selectworkflowConfigId: "",
        triggerflag: false,
        defaulttriggerval: "",
        editId: "",
        errormsg: ref(t("geekplus.gms.client.screen.callerConfiguration.confirmDelete.description")),
        errorflag: ref(false),
        filtershelfCategory: [],
        form: createFormItemGroup({
          // 按钮启动
          buttonstart: {
            type: "el-switch",
            labelText: t("geekplus.gms.client.screen.callerConfiguration.buttonEnable"),
            value: false,
            handleChange: (value) => {
              buttons[buttonIndex].state = value;
              if (value === false) {
                buttons[buttonIndex].form.$setData({
                  operationCommand: "",
                  workflowConfigId: "",
                  triggerposition: "",
                  shelfCategoryId: "",
                  shelfAngle: "",
                });
                buttons[buttonIndex].positionState0 = [];
                buttons[buttonIndex].positionStatein = [];
                buttons[buttonIndex].positionStateleave = [];
              }
            },
            isHidden: computed(() => mode.value === "view"),
          },
          // 按钮功能
          operationCommand: {
            type: "el-select",
            labelText: t("geekplus.gms.client.screen.callerConfiguration.buttonFunction"),
            validators: [createRequiredValidator(buttonIndex)],
            placeholder: t("geekplus.gms.client.screen.callerConfiguration.pleaseSelect"),
            value: "",
            options: opreatecomlist,
            labelPosition: "top",
            disabled: computed(() => mode.value === "view" || buttons[buttonIndex].state === false),
            handleChange: (val) => {
              buttons[buttonIndex].buttonselectvalue = val;
            },
          },
          //   流程
          workflowConfigId: {
            type: "el-select",
            labelText: t("geekplus.gms.client.screen.callerConfiguration.processFlow"),
            value: "",
            options: processList,
            validators: [validators.required],
            labelPosition: "top",
            disabled: computed(() => mode.value === "view"),
            isHidden: ({ formData }) => showWorkflow(index, formData),
            handleChange: createWorkflowHandler(
              buttonIndex,
              buttonIndex === 0
                ? triggerPositionList
                : buttonIndex === 1
                ? triggerPositionList2
                : buttonIndex === 2
                ? triggerPositionList3
                : triggerPositionList4
            ),
          },
          //  触发位置
          triggerposition: {
            type: "el-select",
            labelText: t("geekplus.gms.client.screen.callerConfiguration.triggerLocation"),
            value: "",
            options: computed(() =>
              buttons[buttonIndex].editId === ""
                ? buttonIndex === 0
                  ? triggerPositionList
                  : buttonIndex === 1
                  ? triggerPositionList2
                  : buttonIndex === 2
                  ? triggerPositionList3
                  : triggerPositionList4
                : triggerPositonListEdit
            ),
            labelPosition: "top",
            disabled: computed(() => mode.value === "view"),
          },
          shelfCategoryId: {
            type: "el-select",
            labelText: t("geekplus.gms.client.screen.callerConfiguration.containerType"),
            value: "",
            options: computed(() =>
              buttons[buttonIndex].filtershelfCategory.length > 0
                ? buttons[buttonIndex].filtershelfCategory
                : containerTypeList.value
            ),
            labelPosition: "top",
            validators: [validators.required],
            disabled: computed(() => mode.value === "view" && buttons[buttonIndex].filtershelfCategory.length === 0),
            isHidden: () => showAngleFun(index),
          },
          shelfAngle: {
            type: "el-select",
            labelText: t("geekplus.gms.client.screen.callerConfiguration.containerAngle"),
            value: "",
            options: entryAngleOptions,
            labelPosition: "top",
            validators: [validators.required],
            disabled: computed(() => mode.value === "view"),
            isHidden: ({ formData }) => showAngleFun(index, formData),
          },
        }),
        positionState0: reactive([]),
        positionStatein: reactive([]),
        positionStateleave: reactive([]),
      };
    };
    // 生成每个按钮的配置项数组
    const buttons = reactive(buttonConfigs.map((config, index) => createButtonConfig(index, config.icon, config.name)));
    // 每个按钮的触发位置赋值默认值
    const triggerPositionLists = [
      triggerPositionList,
      triggerPositionList2,
      triggerPositionList3,
      triggerPositionList4,
    ];
    triggerPositionLists.forEach((list, index) => {
      watch(
        list,
        (newVal) => {
          if (newVal?.length > 0) {
            buttons[index].form.$setData({ triggerposition: newVal[0]?.value || "" }, { skipValidation: true });
          }
        },
        { immediate: true }
      );
    });
    watch(
      () => props.loadingFlag,
      (newVal) => {
        localLoadingFlag.value = newVal;
      }
    );
    // 获取按钮功能列表的数据
    const getOperateComList = async () => {
      try {
        const { data } = await getCommandList({ controllerType: 4 });
        if (data && data.code === 0) {
          opreatecomlist.value = getSelectData(data.data, "code", "name");
        }
      } catch (error) {
        console.error(error);
      }
    };
    // 定义了一个映射对象
    const triggerTypeMapping = {
      GENERAL_POINT: 0,
      WORKSTATION: 2,
      AREA: 1, // 如果没有对应的值，可以设置为 null 或其他默认值
      PALLET_POINT: 3,
      // 添加其他映射关系，如果有的话
    };
    // 获取流程的数据
    const getWorkFlowList = async () => {
      const res = await queryFlowAndGroup({ controllerType: 1 });
      processListAll.value = res.data;
      const selData = res.data.map((item) => {
        const newItem = {};
        newItem.type = item.type;
        newItem.id = item.id;
        newItem.name = item.workflowName || item.workflowgroupName;
        return newItem;
      });
      processList.value = getSelectData(selData, "id", "name");
    };
    // 获取下拉数据这是封装的一个方法
    const getSelectData = (res, dataName, dataValue) => {
      const sleOptions = [];
      for (let j = 0; j < (res || []).length; j += 1) {
        sleOptions.push({ label: res[j][dataValue], value: res[j][dataName] });
      }
      return sleOptions;
    };
    // 切换左侧按钮逻辑的时候验证
    const handleBeforeLeave = async (newTab, oldTab) => {
      const index = buttons.findIndex((b) => b.buttonCode === oldTab);
      const errorFlag = buttons[index].errorflag; // 获取 errorFlag
      if (index !== -1 && buttons[index].state) {
        const form = buttons[index].form;
        const { positionState0, positionStatein, positionStateleave } = buttons[index];
        const isValid = await new Promise((resolve) => {
          form.$validate((valid) => {
            resolve(valid);
          });
        });

        if (Number(buttons[index].buttonselectvalue) === 40) {
          if (positionState0.length === 0) {
            buttons[index].errorflag = true;
            return Promise.reject(new Error("positionState0 为空，阻止切换"));
          }
        } else if (Number(buttons[index].buttonselectvalue) === 43) {
          if (positionStatein.length === 0) {
            buttons[index].errorflag = true;
            return Promise.reject(new Error("positionStatein 为空，阻止切换"));
          }
        } else if (Number(buttons[index].buttonselectvalue) === 44) {
          if (positionStateleave.length === 0) {
            buttons[index].errorflag = true;
            return Promise.reject(new Error("positionStatein 为空，阻止切换"));
          }
        }

        if (!isValid) {
          // 返回 false 来阻止切换标签
          return Promise.reject(new Error("验证失败"));
          // return false;
        }
      }
      return true;
    };
    const getContainerTypeList = async () => {
      const { data } = await getContainerType();
      if (data && data.code === 0) {
        // originalContainerTypeList.value = JSON.parse(JSON.stringify(data.data));
        containerTypeList.value = getSelectData(data.data, "id", "name").map((item) => {
          item.disabled = true; // 设置每个项的 disabled 为 true
          return item; // 返回修改后的项
        });
        const filterContainerType = data.data.map((item) => {
          return {
            id: item.id,
            name: item.name,
            loadCarrierType: item.loadCarrierType,
          };
        });
        // 这是是在流程入场的时候我要根据如果选中地图点位  显示货架(潜伏车)  如果选中托盘位  就选择叉车的
        originalContainerTypeList.value = JSON.parse(JSON.stringify(filterContainerType));
      }
    };
    // 是否显示触发位置
    const showPositionSelect = (btn) => {
      // 例如：功能是20、43、44才显示位置选择
      const f = btn.buttonselectvalue;
      return btn.state && f && ["40", "44", "43"].includes(f);
      // return
    };
    // 触发位置的标题
    const positionLabel = (btn) => {
      const f = btn.buttonselectvalue;
      if (f === "40") return t("geekplus.gms.client.screen.callerConfiguration.triggerLocation");
      if (f === "43") return t("geekplus.gms.client.screen.callerConfiguration.entryLocation");
      if (f === "44") return t("geekplus.gms.client.screen.callerConfiguration.exitLocation");
      return "";
    };
    // 获取groups 的值
    const grpupsValue = (item) => {
      if (Number(item.buttonselectvalue) === 40) {
        return ["GENERAL_POINT", "PALLET_POINT"];
      } else if (Number(item.buttonselectvalue) === 43) {
        return ["GENERAL_POINT", "PALLET_POINT"];
      } else if (Number(item.buttonselectvalue) === 44) {
        return ["GENERAL_POINT", "PALLET_POINT", "AREA"];
      } else {
        return []; // 如果值不匹配，返回一个空数组或者其他默认值
      }
    };
    // 交互中的函数
    /**
     *
     * @param btnIndex
     * @param codeValue 下拉框选中的数据
     * @param codeItem
     * @param button 当前操作的按钮数据
     */
    const handleChangeNodeCode = ({ btnIndex, codeValue, codeItem, button }) => {
      if (codeValue === undefined) {
        if (String(button.buttonselectvalue) === "40") {
          button.positionState0 = [];
        } else if (String(button.buttonselectvalue) === "43") {
          button.positionStatein = [];
        } else if (String(button.buttonselectvalue) === "44") {
          button.positionStateleave = [];
        }
        button.errorflag = true;
        button.filtershelfCategory = [];
        button.form.$setData({ shelfCategoryId: "" });
        // 禁用 containerTypeList.value 的所有数据
        containerTypeList.value = filterContainerTypeAll.value.map((item) => ({
          ...item,
          disabled: true,
        }));
        return; // 直接返回，避免执行后续逻辑
      }
      const filterContainerTypeAll = JSON.parse(JSON.stringify(originalContainerTypeList.value));

      if (codeValue?.includes("PALLET_POINT")) {
        const generalPointData = filterContainerTypeAll.filter((item) => item.loadCarrierType === "PALLET");
        if (generalPointData) {
          button.form.$setData({ shelfCategoryId: "" });
          button.filtershelfCategory = generalPointData.map((item) => {
            return {
              label: item.name,
              value: item.id,
              disabled: false,
            };
          });
        }
      }

      // 如果 codeValue 包含 GENERAL_POINT，筛选 value === 1 的容器类型
      if (codeValue?.includes("GENERAL_POINT")) {
        const generalPointData2 = filterContainerTypeAll.filter((item) => item.loadCarrierType === "PALLET_RACKING");
        if (generalPointData2) {
          button.form.$setData({ shelfCategoryId: "" });
          button.filtershelfCategory = generalPointData2.map((item) => {
            return {
              label: item.name,
              value: item.id,
              disabled: false,
            };
          });
        }
      }

      if (Number(button.buttonselectvalue) === 40) {
        button.errorflag = false;
        return (button.positionState0 = [...codeValue]);
      } else if (Number(button.buttonselectvalue) === 43) {
        button.errorflag = false;
        return (button.positionStatein = [...codeValue]);
      } else if (Number(button.buttonselectvalue) === 44) {
        button.errorflag = false;
        return (button.positionStateleave = [...codeValue]);
      } else {
        return []; // 如果值不匹配，返回一个空数组或者其他默认值
      }
    };
    //数据回显
    watch([() => props.init_value, () => processListAll.value], async ([initVal, processList]) => {
      const firstItem = initVal[0] || {};
      /**
       * controllerConfigList    这是每个按钮的配置
       * processList  是获取流程的数据
       *
       *
       */
      const { code, name, controllerConfigList = [] } = firstItem;
      setNewdeviceId.value = code;
      const asyncOperations = controllerConfigList.map(async (item) => {
        // 获取对应的的按钮下标
        const idx = parseInt(item.buttonCode) - 1;
        // 获取对应每个按钮的配置
        const button = buttons[idx];
        // 如果没有对应的按钮，则直接返回
        if (!button) return;

        const workflow = processList.find((wf) => wf.id === item.workflowConfigId);
        // 判断是否有触发位置
        const hasTrigger = workflow?.nodeList?.some((node) => node.cellType === 2);
        Object.assign(button, {
          // 判断是显示触发位置
          triggerflag: !!hasTrigger,
          // 编辑的id
          editId: item.id,
          // 按钮启动是否启用
          state: item.status === 1,
          buttonselectvalue: item.operationCommand ?? "",
        });

        button.form.$setData({
          // 按钮功能
          operationCommand: item.operationCommand ?? "",
          // 流程
          workflowConfigId: item.workflowConfigId ?? "",
          // 容器类型
          shelfCategoryId: item.shelfCategoryId ?? "",
          // 容器角度
          shelfAngle: item.shelfAngle ?? "",
          // 按钮启动
          buttonstart: item.status === 1,
          // 触发位置
          triggerposition: hasTrigger ? item.stopPointId : "",
        });

        const defaultData = hasTrigger ? "" : defaultValue(item);
        const op = Number(item.operationCommand);
        if (op === 40) {
          button.positionState0 = defaultData;
        } else if (op === 43) {
          button.positionStatein = defaultData;
        } else if (op === 44) {
          button.positionStateleave = defaultData;
        }
        // 获取触发位置的数据
        if (hasTrigger && workflow.type) {
          const [id, type] = workflow.type.split("_");
          if (id && type) {
            const data = await getFirstNodeWithoutShelf({ id, type });
            triggerPositonListEdit.value = getSelectData(data, "cellCode", "hostCellCode");
          }
        }
      });

      await Promise.all(asyncOperations);
      loadingFlag.value = false;
    });

    const getKeyByValue = (map, value) => {
      return Object.keys(map).find((key) => map[key] === value);
    };
    // 交互数据的回显
    const defaultValue = (btn) => {
      const key = getKeyByValue(triggerTypeMapping, btn.triggerType);
      return btn.stopPointId ? [key, btn.stopPointId] : [];
    };
    // 点击盒子触发
    const handleTriggerButton = async (item) => {
      const triggerData = {
        buttonCode: item?.buttonCode,
        buttonCommand: "0",
        buttonType: "0",
        controllerCode: item?.deviceId,
      };
      const data = await physicalButtonTrigger(triggerData);
      localLoadingFlag.value = true;
      if (data.succ) {
        Message.success(t("geekplus.gms.client.screen.callerConfiguration.triggerSuccess"));
        localLoadingFlag.value = false;
      }
      localLoadingFlag.value = false;
    };

    onMounted(() => {
      getOperateComList();
      getWorkFlowList();
      getContainerTypeList(); // 获取容器类型的接口
    });
    return {
      opreatecomlist,
      processList,
      processListAll,
      triggerPositionList,
      triggerPositonListEdit,
      containerTypeList,
      currentButtonName,
      t,
      getOperateComList,
      getWorkFlowList,
      buttons,
      showPositionSelect,
      positionLabel,
      grpupsValue,
      handleBeforeLeave,
      handleChangeNodeCode,
      createWorkflowHandler,
      createRequiredValidator,
      handleTriggerButton,
      setNewdeviceId,
    };
  },
});
</script>
<style lang="scss" module="co" src="./common.scss"></style>
<style lang="scss" scoped>
.error_msg {
  font-size: 10px;
  color: gms.$colorDanger;
  margin-left: 6px;
}
.button-wrapper {
  display: flex;
  // color: #f56c6c;
  justify-content: space-between;
  align-items: center;
}

.code_label {
  width: 120px;
  &:before {
    color: gms.$colorDanger;
    content: "*";
    margin-right: 4px;
  }
}
.bgpicture {
  background-image: url("@/assets/images/pager.png");
  background-repeat: no-repeat;
  background-position: center;
  background-size: auto 85%;
}
.button1 {
  display: flex;
  align-items: center; /* 水平居中对齐 */
  justify-content: flex-start; /* 垂直居中对齐 */
  gap: 5px;
  width: 120px; /* 设置按钮的宽度，确保布局美观 */
  height: 87px; /* 设置按钮的高度，确保布局美观 */
}

.buttonicon {
  width: 40px;
  height: 40px;
}

.buttonText {
  display: flex;
  flex-direction: column; /* 垂直排列文本和标签 */
  align-items: flex-start; /* 子项宽度根据内容 */
}

.button1-1 {
  line-height: 1.2;
  display: flex;
  justify-content: flex-start;
}

.tag1 {
  margin-top: 4px; /* 标签与文本之间的间距 */
}
::v-deep {
  .classrequire .gp-input > input {
    border-color: #e5e7ea !important;
  }
  .classrequire div[class*="_message_"] {
    display: none;
  }
  //   按钮功能添加必须样式
  .feature div[class*="_label_box_"]::before {
    content: "*";
    color: gms.$colorDanger;
    margin-right: 4px;
  }
  ._form_item_wrapper_lonhs_1 ._label_box_lonhs_8 {
    font-size: 14px !important;
  }

  // 编辑的时候的交互样式
  .editclass ._tag_box_1372r_7 {
    border: 1px solid #f56c6c !important;
  }
  // 交互的样式
  .viewclass {
    margin: 5px 0px;
  }
  .viewclass ._tag_box_1372r_7 {
    background-color: #f1f2f5;
    color: #c8cdd3;
  }
  .viewclass ._tag_box_1372r_7 ._tag_item_1372r_27 {
    color: #c8cdd3;
  }
  .viewclass ._tag_box_1372r_7 ._content_box_1372r_20 {
    border: 1px solid #e5e7ea;
  }
  #tippy-1 {
    z-index: 3000 !important;
  }
  .gp-tabs--border-card > .gp-tabs__header .gp-tabs__item:not(.is-disabled):hover {
    color: black;
  }
  .gp-tag.gp-tag--success {
    color: black;
  }
  .gp-tabs__nav-prev {
    display: none;
  }
  /* 隐藏左右箭头 */
  .gp-tabs--left .gp-tabs__nav-wrap.is-left > .gp-tabs__nav-next {
    display: none !important;
  }
  .gp-tabs--right .gp-tabs__nav-wrap.is-right > .gp-tabs__nav-prev {
    display: none;
  }

  /* 左侧 tab 样式 */
  .gp-tabs--left {
    .gp-tabs__item {
      height: 87px;
      line-height: 88px;
      padding: 0px;
    }
    // 设置左侧的背景颜色
    .gp-tabs__header.is-left {
      margin-right: 0px;
      background-color: #fff;
      border-right: none;
    }

    /* 修正 `is-scrollable` 的间距 */
    .gp-tabs__nav-wrap.is-left.is-scrollable {
      padding: 0px !important;
      width: 129px;
    }
  }
  // 选择按钮一   按钮二的 颜色
  .gp-tabs--border-card > .gp-tabs__header .gp-tabs__item.is-active {
    color: black;
  }
  // 悬浮的字体颜色
  .gp-tabs--border-card > .gp-tabs__header .gp-tabs__item:not(.is-disabled):hover {
    color: "";
  }

  /* 右侧 tab 样式 */
  .gp-tabs--border-card {
    background-color: #f5f7fa;
    width: 100%;
    height: 100%;
    border: none;
    border-bottom: 1px solid #f5f7fa;
    --webkit-box-shadow: 0px;
    box-shadow: none;

    /* 选中的 tab */
    > .gp-tabs__header .gp-tabs__item.is-active {
      background-color: #f5f7fa;
      border-color: #f5f7fa;
      .button1-1 {
        color: #635f5f;
      }
      .gp-tag {
        color: #635f5f;
      }
    }

    /* tab 内容间距 */
    > .gp-tabs__content {
      padding: 0px 15px;
    }
  }

  /* 适配 `data-v-ae9cad96` */
  [data-v-ae9cad96] .gp-tabs--border-card {
    height: 110%;
  }
  // 修改这个页面的错误提示样式
  div[class*="_message_"] {
    font-size: 10px !important;
  }

  /* 移除表单 label 后面的 `:` */
  .el-form-item__label::after {
    content: none !important;
  }
  // 改变::before 的元素样式
  .gp-tabs--left .gp-tabs__item:before,
  .gp-tabs--right .gp-tabs__item:before {
    display: none;
  }
}

/* 局部样式 */
.dialog-content {
  height: 400px;
}

.step1 {
  width: 100%;
  height: 350px;
}
.gp-tabs__nav-wrap:after {
  display: none;
}
// }
</style>
