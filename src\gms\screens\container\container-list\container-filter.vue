<template>
  <FilterPanel :hide-field-fuzzy="true" :form-items="formItems" @change="handleGroupChange">
    <template #suffix>
      <slot name="suffix"></slot>
    </template>
  </FilterPanel>
</template>
<script>
import { defineComponent, computed } from "vue";
import { formItemTypes } from "@srpe-fe/uic-el-form-wrapper-vue2";
import { useI18n } from "gms-hooks";
import { containerStatus, loadingStatus } from "gms-constants";
import FilterPanel from "@/gms/components/filter-panel";

export default defineComponent({
  name: "ContainerFilter",
  components: { FilterPanel },
  props: {
    options: { type: Object, default: () => {} },
  },
  emit: ["update:filterParams"],
  setup(props, { emit }) {
    const t = useI18n();

    const modelInfoOptions = computed(
      () => props.options?.containerTypeList?.map(({ name, id }) => ({ label: name, value: String(id) })) || []
    );

    const formItems = computed(() => [
      {
        name: "code",
        type: formItemTypes.EL_INPUT,
        filterable: true,
        clearable: true,
        placeholder: t("geekplus.gms.client.screen.container.form.searchParams"),
        value: "",
        suffixIcon: "el-icon-search",
      },
      {
        name: "status",
        type: formItemTypes.EL_SELECT,
        filterable: true,
        clearable: true,
        placeholder: t("geekplus.gms.client.screen.container.columns.containerStatus"),
        options: containerStatus.toLabelValueList().filter((v) => v.value !== containerStatus.DRAFT),
        value: "",
      },
      {
        name: "loadingStatus",
        type: formItemTypes.EL_SELECT,
        filterable: true,
        clearable: true,
        placeholder: t("geekplus.gms.client.screen.container.columns.loadingStatus"),
        options: loadingStatus.toLabelValueList(),
        value: "",
      },
      {
        name: "loadCarrierModelId",
        type: formItemTypes.EL_SELECT,
        filterable: true,
        clearable: true,
        placeholder: t("geekplus.gms.client.screen.container.columns.containerTypeName"),
        options: modelInfoOptions.value,
        value: "",
      },
    ]);

    const handleGroupChange = ({ formData }) => {
      emit("change", Object.fromEntries(Object.entries(formData).filter(([, val]) => val)));
    };

    return { formItems, modelInfoOptions, handleGroupChange };
  },
});
</script>
