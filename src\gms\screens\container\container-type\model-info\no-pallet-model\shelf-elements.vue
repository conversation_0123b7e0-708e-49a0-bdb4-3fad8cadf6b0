<template>
  <div
    class="tw-relative tw-flex tw-flex-col tw-border tw-border-solid tw-border-gray-300 tw-p-2 tw-mb-4 tw-rounded"
    :class="[cn.border, $props.values.key === $attrs.active ? cn.active : '', $props.mode === 'view' ? '' : 'tw-gap-2']"
    :name="$props.values.key"
  >
    <div class="tw-flex tw-items-center tw-justify-between tw-gap-2">
      <div class="tw-flex tw-items-center tw-gap-1">
        <h3 class="tw-text-sm">
          {{ shelfElementType.getLabelByValue($props.values.type) }}{{ isLeg ? $attrs.index : "" }}
        </h3>
        <gms-tooltip effect="light" placement="left">
          <template #content>
            <img :src="measureImg" alt="measureMethod" :style="{ height: imgHeight }" />
          </template>
        </gms-tooltip>
      </div>
      <i
        v-if="isLeg"
        class="el-icon-delete tw-text-xl tw-cursor-pointe"
        :class="$props.mode === 'view' ? 'tw-text-gray-500' : 'tw-text-red-500'"
        @click.stop="handleDeleteElement"
      ></i>
    </div>
    <template v-if="isLeg">
      <div class="tw-flex tw-items-center tw-gap-1">
        <form-item v-bind="formGroup.x" />
        <span>mm</span>
      </div>
      <div class="tw-flex tw-items-center tw-gap-1">
        <form-item v-bind="formGroup.y" />
        <span>mm</span>
      </div>
    </template>
    <div class="tw-flex tw-items-center tw-gap-1">
      <form-item v-bind="formGroup.length" />
      <span>mm</span>
    </div>
    <div class="tw-flex tw-items-center tw-gap-1">
      <form-item v-bind="formGroup.width" />
      <span>mm</span>
    </div>
    <div class="tw-flex tw-items-center tw-gap-1">
      <form-item v-bind="formGroup.height" :labelText="getHeightLabelText($props.values.type)" />
      <span>mm</span>
    </div>
  </div>
</template>
<script>
import { cloneDeep } from "lodash";
import { defineComponent, ref, computed } from "vue";
import { FormItem, createFormItemGroup, validators } from "@srpe-fe/uic-el-form-wrapper-vue2";
import { shelfElementType } from "gms-constants";
import { useI18n } from "@/hooks";
import getContainerImage from "@/utils/containerImages";
const measureFace = getContainerImage("measure-face.png");
import measureLeg from "@/gms/assets/images/container/measure-leg.jpg";
import GmsTooltip from "gms-components/gms-tooltip.vue";

export default defineComponent({
  name: "ShelfElements",
  components: { FormItem, GmsTooltip },
  props: {
    values: { type: Object, default: () => ({}) },
    formValues: { type: Object, default: () => ({}) },
    mode: { type: String, default: "add" },
  },
  emit: ["update:formGroupList", "update:values"],
  setup(props, { emit }) {
    const t = useI18n();
    const imgHeight = ref("35rem");

    const measureImg = props.values.type === shelfElementType.FACE ? measureFace : measureLeg;

    const isFace = computed(() => props.values.type === shelfElementType.FACE);
    const isLeg = computed(() => props.values.type === shelfElementType.LEG);

    const handleGroupChange = ({ name, value }) => {
      emit("update:values", { ...props.values, [name]: value });
    };

    const handleDeleteElement = () => {
      if (props.mode === "view") return;
      const formValues = cloneDeep(props.formValues);
      const i = props.formValues.modelInfo.findIndex((v) => v.key === props.values.key);
      formValues.modelInfo.splice(i, 1);
      emit("update:formValues", formValues);
    };

    const formGroup = createFormItemGroup(
      {
        length: {
          type: "el-input-number",
          labelText: isFace.value
            ? t("geekplus.gms.client.screen.container.form.surfaceLength")
            : t("geekplus.gms.client.screen.container.form.length"),
          value: props.values.length ?? "",
          validators: [validators.required],
          labelWidth: "110px",
          size: "small",
          min: 1,
          max: 5000,
          disabled: props.mode === "view",
        },
        width: {
          type: "el-input-number",
          labelText: isFace.value
            ? t("geekplus.gms.client.screen.container.form.surfaceWidth")
            : t("geekplus.gms.client.screen.container.form.width"),
          value: props.values.width ?? "",
          validators: [validators.required],
          labelWidth: "110px",
          size: "small",
          min: 1,
          max: 5000,
          disabled: props.mode === "view",
        },
        height: {
          type: "el-input-number",
          labelText: t("geekplus.gms.client.screen.container.form.height"),
          value: props.values.height ?? "",
          validators: [validators.required],
          labelWidth: "110px",
          size: "small",
          min: 1,
          max: 10000,
          disabled: props.mode === "view",
        },
        x: {
          type: "el-input-number",
          labelText: t("geekplus.gms.client.screen.container.form.xAxisFromCenter"),
          value: props.values.x ?? "",
          validators: [validators.required],
          labelWidth: "110px",
          size: "small",
          min: -10000,
          max: 3000,
          disabled: props.mode === "view",
        },
        y: {
          type: "el-input-number",
          labelText: t("geekplus.gms.client.screen.container.form.yAxisFromCenter"),
          value: props.values.y ?? "",
          validators: [validators.required],
          labelWidth: "110px",
          size: "small",
          min: -10000,
          max: 3000,
          disabled: props.mode === "view",
        },
      },
      { handleGroupChange, labelPosition: "right", mode: props.mode === "view" ? "view" : "edit" }
    );
    const getHeightLabelText = (type) => {
      return type === shelfElementType.FACE
        ? t("geekplus.gms.client.screen.container.form.shelfHeight")
        : t("geekplus.gms.client.screen.container.form.height");
    };
    return {
      measureImg,
      imgHeight,
      shelfElementType,
      formGroup,
      isFace,
      isLeg,
      handleDeleteElement,
      getHeightLabelText,
    };
  },
});
</script>

<style lang="scss" module="cn">
:global(.gp-input-number.gp-input-number--small) {
  width: 100%;
}
.border {
  border: 2px solid #efefef;
}
.active {
  border: 2px solid #409eff;
}
</style>
