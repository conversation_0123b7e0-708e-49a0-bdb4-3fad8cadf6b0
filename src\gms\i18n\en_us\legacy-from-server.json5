{
  "lang.gles.baseData.warehouse": "Warehouse",
  "lang.ark.fed.common.deleteTipMsg": "It cannot be recovered after deletion. Are you sure to delete it?",
  "lang.ark.fed.detour": "Bypass",
  "lang.ark.fed.cutting": "Trimming",
  "lang.ark.fed.flowAndFlowTemplate": "Process/process template",
  "lang.ark.fed.deviceAccessType.fixation": "Fixed device",
  "lang.ark.fed.addNew": "New",
  "lang.ark.fed.shelfAttribute.MOVE": "MOVE",
  "lang.ark.fed.minutesLater": "In? minutes",
  "lang.ark.workflow.paramValueCode.floor": "floor",
  "lang.ark.fed.trafficAndStopNotwManage": '"The traffic control area or emergency stop area is already provided, and management area cannot be added"',
  "lang.ark.fed.verySerious": "Very severe",
  "lang.ark.fed.queuePointLevel": "Queuing point priority",
  "lang.ark.trafficControl.robotRange": "Traffic control area",
  "lang.ark.fed.chargingTime": "Charging time",
  "lang.ark.fed.theMapHasBeenSavedAndYouCanEditItNow": "The map has been saved. You can edit it.",
  "lang.ark.fed.locationOfRobotCharging": "Robot charging position",
  "lang.ark.fed.side": "Side",
  "lang.ark.fed.component.workflow.label.specifyNodeType": "Specify node type",
  "lang.ark.shelfTypeRefShelf": "Failed to delete. Used by shelf! Shelf No.: {0}",
  "lang.ark.apiContainerCode.containerNotExistsByLocation": "No container exist for locationCode:{0}",
  "lang.ark.apiCallbackReg.controlSendFrequency": "Whether to control the sending frequency",
  "lang.ark.workflow.containerLevel2Classifica": "Secondary class classification of containers",
  "lang.ark.fed.pleaseAddCacheArea": "Node buffer area not configured.",
  "lang.ark.fed.nodeConfirmedLeave.type": "Confirm that the robot is leaving",
  "lang.ark.fed.screen.equipmentAssociatedInfo.dialogDetail.labelPosition": "Point position",
  "lang.ark.workflow.completeBizAutoTriggerSimple": "Business selection",
  "lang.ark.waveType.alikeProduct": "Same production line",
  "lang.ark.fed.pickingUpRack": "Fetching shelves",
  "lang.ark.fed.leftBracket": "+ left parenthesis",
  "lang.mwms.fed.simulation": "Analog control",
  "lang.ark.workflowConfig.cellFunctions.turn": "Turn",
  "lang.ark.fed.rmsRange": "Synchronous area control with RMS",
  "lang.ark.fed.sendMaterialType": "Delivery destination matching method",
  "lang.ark.fed.pleaseChangeBins": "Please switch the stock bin",
  "lang.ark.action.interface.conditionExtraParam9": "extraParam9",
  "lang.ark.workflow.B": "B",
  "lang.ark.action.interface.conditionExtraParam8": "extraParam8",
  "lang.ark.action.interface.conditionExtraParam7": "extraParam7",
  "lang.ark.action.interface.conditionExtraParam6": "extraParam6",
  "lang.ark.fed.makeSure": "Yes",
  "lang.ark.action.interface.conditionExtraParam5": "extraParam5",
  "lang.ark.workflow.F": "F",
  "lang.ark.action.interface.conditionExtraParam4": "extraParam4",
  "lang.ark.action.interface.conditionExtraParam3": "extraParam3",
  "lang.ark.externalDevice.instructionRule4": "Machine offset beyond ±50 mm (exclusive)",
  "lang.ark.action.interface.conditionExtraParam2": "extraParam2",
  "lang.ark.externalDevice.instructionRule3": "Machine offset within ±50 mm (inclusive)",
  "lang.ark.action.interface.conditionExtraParam1": "extraParam1",
  "lang.gles.strategy.robotGoodsPosition": "Shelf bin strategy for robot",
  "lang.ark.fed.screen.hybridRobot.installEquipmentTip": "Data comes from DMP upper structure device",
  "lang.ark.fed.liveNoSaveGoods": "Material list not saved. Please save first!",
  "lang.ark.workflow.L": "L",
  "lang.ark.workflow.R": "R",
  "lang.ark.workflow.template.validate.templateOrderNodeMustGreaterThan1": "The number of dynamic template business nodes must be greater than 1.",
  "lang.ark.fed.uninstallSuccess": "Uninstalled successfully",
  "lang.ark.fed.redistribution": "Reallocate",
  "lang.authManage.web.others.expand": "Unfold",
  "lang.ark.singleCellStation": "Single-point Station",
  "lang.ark.fed.containerTypeExternalNo": "Container type external No.",
  "lang.ark.fed.selectPoints": "Select a point",
  "lang.ark.fed.templateCode": "Template code",
  "lang.gles.receipt.adjustOrder.adjustOrder": "Inventory adjustment order",
  "lang.mwms.monitorRobotMsg12009": "Robot fetched shelf timeout",
  "lang.ark.fed.goodsLocation": "Stock bin",
  "lang.mwms.monitorRobotMsg12006": "Path resources may be occupied",
  "lang.ark.operatelog.operatetype.fetch": "Fetch",
  "lang.mwms.monitorRobotMsg12007": "No path for planning",
  "lang.mwms.monitorRobotMsg12004": "The start or end point of the task is an obstacle",
  "lang.ark.fed.emergencyStopError": "System emergency stop failed!",
  "lang.mwms.monitorRobotMsg12005": "Robot or shelf getting in the way",
  "lang.mwms.monitorRobotMsg12013": "Robot achieves low electricity ratio",
  "lang.ark.workflow.buttonAlreadyConfigured": 'Delete failed! Physical button configuration exists. Please delete it in the "button configuration" page first',
  "lang.ark.fed.uploadViewSuccess": "Upload successful",
  "lang.mwms.monitorRobotMsg12014": "The charging task cannot be executed without matching to the charging station",
  "lang.ark.task.log.export.title.end.time": "End time",
  "lang.mwms.monitorRobotMsg12011": "Long time did not reach the waiting point",
  "lang.mwms.monitorRobotMsg12012": "Robot power does not increase for a long time",
  "lang.ark.fed.chargingCapacity": "Charging power",
  "lang.mwms.monitorRobotMsg12010": "Take the shelf without reaching the shelf position",
  "lang.ark.warehouse.containerConfigEmpty": "Container configuration information is empty",
  "lang.ark.record.rms.sendRobotTask": "Send robot task",
  "lang.ark.fed.screen.flowNodeConfig.ifExecuteTask": "If executed task is",
  "lang.ark.workflowConfig.cellFunctions.restart": "Restart",
  "lang.ark.fed.screen.workflowInfo.requestParamDetail": "Request Message",
  "lang.ark.workflow.exceptionHandler.idlePriority": "The idle is prior",
  "lang.ark.workflow.task.status.create": "Create",
  "lang.ark.fed.inWarehouse": "Manual stock-in",
  "lang.ark.fed.lineName": "Production line name",
  "lang.mwms.monitorRobotMsg12019": "Charging across floors fails",
  "lang.mwms.monitorRobotMsg12017": "The charging station cell is occupied by other robots",
  "lang.mwms.monitorRobotMsg12018": "Cross-region charging failed",
  "lang.ark.warehouse.noMatchZagvdbm": "No feeding point matched",
  "lang.mwms.monitorRobotMsg12015": '"Without an idle charging station, the charging task cannot be executed"',
  "lang.mwms.monitorRobotMsg12016": '"There is no available charging station, the charging task cannot be executed"',
  "lang.mwms.monitorRobotMsg12024": "No stops found",
  "lang.ark.workflow.arrive.action.goTurnOfAngle.pos180": "180°",
  "lang.mwms.monitorRobotMsg12022": "Charging time is too long",
  "lang.mwms.monitorRobotMsg12023": "Robot battery temperature is too high",
  "lang.mwms.monitorRobotMsg12020": "The current task is blocked and the charging task cannot be executed",
  "lang.mwms.monitorRobotMsg12021": "The current task is not completed and the charging task cannot be performed",
  "lang.ark.fed.whetherToWait": "Wait or not",
  "lang.ark.fed.goodsExComplete": "Completed abnormally",
  "lang.ark.fed.triggerCompletion": "Complete trigger",
  "lang.ark.fed.goToNextFlowNode": "Go to the next node",
  "lang.ark.fed.south": "South",
  "lang.ark.dynamicTemplate.cellCodeNotMatch": "No point matches the automatic value assignment node. Please check the configuration!",
  "lang.ark.fed.shelfLock": "Shelf lock",
  "lang.ark.fed.pickingTask": "Material receiving task",
  "lang.ark.fed.screen.hybridRobot.binInfo": "Stock bin information",
  "lang.ark.workflow.area.queueRange": "Queuing area",
  "lang.ark.fed.nodeType": "Node type",
  "lang.mwms.fed.warehouseInit": "Warehouse construction",
  "lang.ark.fed.common.placeholder.select": "Please select",
  "lang.ark.workflow.robotWaitFlag": "Whether to wait in-situ",
  "lang.ark.fed.waveStrategyName": "Strategy name",
  "lang.mwms.fed.shelfIn": "Shelf stock in",
  "lang.ark.fed.tripTo": "Outward voyage",
  "lang.ark.interface.notExist": "Log does not exist",
  "lang.mwms.fed.stocktakeException": "Inventory exception",
  "lang.ark.fed.end": "End",
  "lang.ark.fed.selectTriggerEvent": "Select trigger event",
  "lang.ark.fed.belongsToArea": "Jurisdiction area",
  "lang.ark.fed.shelfAttribute.RECALL": "RECALL",
  "lang.ark.interface.interfaceDesc.edit": "Edit interface information",
  "lang.mwms.monitorRobotMsg12002": "Path planning failed",
  "lang.mwms.monitorRobotMsg12003": "Robot not in map",
  "lang.ark.fed.blankingTimeout": "Discharging timeout",
  "lang.mwms.monitorRobotMsg12000": "Robot unable to be connected",
  "lang.mwms.monitorRobotMsg12001": "Timeout for subtask sending",
  "lang.ark.fed.areDeletionsConfirmed": "Are you sure to delete?",
  "lang.ark.fed.taskFrom.putTask": "Placing task",
  "lang.ark.fed.beforeExecuteSaveDayLog": '"Before each execution, keep the logs for the last {0} days"',
  "lang.ark.fed.customStartAndendNode": "Start point and end point can be specified",
  "lang.ark.fed.dataUpdate": "Data update",
  "lang.ark.workflow.template.validate.templateNotExist": "Template does not exist.",
  "lang.ark.fed.actionsErrorNeedRemoveRobot": "The {0} node has an interaction configuration error. The robot shall be released!",
  "lang.authManage.web.common.pleaseSelect": "Please select",
  "lang.ark.fed.demandQuantity": "Demand quantity",
  "lang.mwms.fed.arrangePlan": "Tallying schedule management",
  "lang.ark.robot.go.fetch.pallet": "To get pallet",
  "lang.ark.fed.takeTheRack": "Shelf",
  "lang.ark.fed.robotApplications": "Robot category",
  "lang.ark.fed.screen.LoginLog.roleName": "role",
  "lang.ark.fed.floorCode": "Layer code",
  "lang.ark.fed.screen.flowNodeConfig.offsetValue": "Offset value",
  "lang.ark.fed.scrollIsExistsInBoundRecord": "The reel number has an inventory record, and the stock-in failed",
  "lang.ark.bussinessModel.wave": "Wave picking",
  "lang.ark.fed.lackRobotQueue": "Task queue",
  "lang.ark.fed.require": "Material calling on demand",
  "lang.ark.fed.robotStatus": "Execution result",
  "lang.ark.fed.screen.equipmentAssociatedInfo.dialogDetail.labelEntryType": "Feed gate type",
  "lang.ark.rollerCellStation.canNotDelete": "Roller station cannot be deleted",
  "lang.ark.fed.thereIsARequiredItemNotFilled": ",the required fields are not filled out!",
  "lang.ark.fed.menu.systemSetting": "Scaffold",
  "lang.ark.fed.numberOfControllers": "Number of controllers",
  "lang.ark.action.interface.extraParam18": "extraParam18",
  "lang.ark.fed.abnormalCompleteSuccessfully": "Completed abnormally",
  "lang.ark.action.interface.extraParam17": "extraParam17",
  "lang.ark.fed.sendMaterialPayAttention": "Automatic matching by material: confirm the station to which the materials are distributed according to the material information bound to the production line station. Manually select a destination: manually select a destination for the feeding point to distribute materials to.? Distribution by receiving order is not affected by the configuration item.",
  "lang.ark.fed.configurationValue": "Configuration value",
  "lang.ark.action.interface.extraParam19": "extraParam19",
  "lang.ark.action.interface.extraParam14": "extraParam14",
  "lang.ark.action.interface.extraParam13": "extraParam13",
  "lang.ark.interface.messageNameDesc": "Interface name",
  "lang.ark.action.interface.extraParam16": "extraParam16",
  "lang.ark.action.interface.extraParam15": "extraParam15",
  "lang.ark.action.interface.extraParam10": "extraParam10",
  "lang.ark.fed.waitStatus": "Waiting status",
  "lang.ark.action.interface.extraParam12": "extraParam12",
  "lang.ark.action.interface.extraParam11": "extraParam11",
  "lang.ark.workflow.wareHouseStationConfig": "By workstation",
  "lang.ark.fed.exceptionHandler": "Exception handling",
  "lang.ark.apiRuleCode.defaultRuleNotExists": "Default rule does not exist",
  "lang.ark.operatelog.operatetype.send": "Deliver",
  "lang.authManage.web.common.toLoginPage": "Return to login page",
  "lang.ark.fed.templateType": "Template type",
  "lang.ark.common.failed": "Invocation failed",
  "lang.ark.fed.interruptInstruct": "Interrupt waiting command",
  "lang.ark.fed.hybridRobot.hybridRobotType.singleSpiralArm": "Single cantilever",
  "lang.ark.workflowPeriod.one": "Only once",
  "lang.authManage.web.common.creator": "Creator",
  "lang.ark.waveStatus.waveTaskCreateFail": "Failed to create task",
  "lang.ark.fed.loadingMount": "Feeding quantity",
  "lang.ark.fed.mapsAreBeingUpdated": "Being updating map",
  "lang.ark.fed.menu.buttonFunctionConfiguration": "Button FUN setting",
  "lang.ark.fed.sendMaterialDestination": "Select a distribution destination",
  "lang.ark.fed.selfCharging": "Self charging",
  "lang.ark.fed.contents.flowConfig.recycleType.autoRecoveryRule.alert": "Note: Multiple preset locations can be set, and the system will match them according to priority.",
  "lang.ark.warehouse.materialPreparePointCellCode": "Feeding point",
  "lang.ark.workflow.wareHouseManuallyCreate": "Manual creation",
  "lang.ark.warehouse.containerBin": "Container stock bin",
  "lang.ark.fed.serverAddress": "Server address",
  "lang.ark.fed.screen.flowNodeConfig.turnSideTo": "Rotate to",
  "lang.ark.fed.originAngle": "Original angle",
  "lang.ark.fed.editNode": "Edit node",
  "lang.ark.workflow.completeTrigger": "Complete automatic triggering of subsequent tasks",
  "lang.ark.fed.shelfAttribute": "Select the type of material.",
  "lang.ark.record.dmp.receiveCallBack": "Dmp callback",
  "lang.ark.interface.sendSucceed": '"Log has been sent successfully, and cannot be sent again."',
  "lang.ark.fed.secondsAndTime": "seconds/time",
  "lang.ark.fed.sound": "Sound",
  "lang.ark.fed.deleteActionFailedRef": "Interaction configuration cannot be deleted and the referenced process exists",
  "lang.ark.fed.queuePoint": "Queuing point",
  "lang.ark.warehouse.materialPreparePointName": "Feeding point name",
  "lang.ark.fed.receiveMaterial": "Receive and feed materials",
  "entry.shelf.failed": "Shelf entry failed. Please try again later!",
  "lang.ark.interface.interfaceName": "Interface name",
  "lang.ark.externalDevice.device_own_type.robotDeviceComponent": "Robot components",
  "lang.mwms.fed.pickException": "Picking exception",
  "lang.ark.fed.firstDay": "The first day",
  "lang.ark.robot.go.drop": "Unload",
  "lang.ark.fed.roadWidth": "Road width:",
  "lang.ark.waveTaskStatus.disCanceled": "Cancel",
  "lang.ark.fed.firstDrawWorkStop": "Please draw the first node of the procedure, the type of the first node is workstation, docking point",
  "lang.ark.trafficControl.trafficFunction": "Traffic control area function",
  "lang.ark.fed.source": "Source",
  "lang.ark.fed.backButton": "Return button",
  "lang.ark.fed.manualClean": "Manually clear",
  "lang.ark.fed.confirmGoods": "Confirm the material",
  "lang.ark.fed.menu.taskManagement": "Task monitoring",
  "lang.ark.workflow.init": "Init",
  "lang.ark.button.type.selfRecovery": "Self-resetting",
  "lang.ark.action.interface.fixedValue": "Fixed value",
  "lang.ark.fed.passbyPointType": "Thru points type:",
  "lang.ark.fed.screen.flowNodeConfig.ifNextPoint": "If next point is",
  "lang.ark.fed.selectionWorkflow": "Select a workflow",
  "lang.ark.fed.currentLocation": "Current position",
  "lang.ark.fed.uploadFileLimit500": "Only Excel files (xls/xlsx) of no more than 500kb can be uploaded",
  "lang.ark.fed.containPoints": "Contained points",
  "lang.ark.warehouse.poleCabinet": "Material cabinet with rods",
  "lang.ark.fed.chargingStrategy": "Charging strategy",
  "lang.ark.loadCarrier.loadCarrierModelCodeGenerateErr": "Container model ID generation encountered error.",
  "lang.authManage.web.others.subsystem": "Authorization subsystem",
  "lang.ark.fed.stopCharging": "Stop charging",
  "lang.ark.fed.dateRange": "date range",
  "lang.ark.fed.pleaseCreateARule": "Please create a rule",
  "lang.ark.fed.imageTypeJudge": "Picture type requirements: jpeg, jpg and png",
  "lang.ark.fed.north": "North",
  "lang.ark.interface.apiStart": "Workflow initiated",
  "lang.ark.fed.rollerRobot": "Roller robot",
  "lang.ark.fed.robotWaitFlag": "In-situ wait",
  "lang.ark.fed.specifyRobot": "Designate a robot",
  "lang.ark.warehouse.materialPreparePointOrder": "Feeding sequence",
  "lang.ark.fed.fullStation": "FULL version of workstation",
  "lang.ark.workflowConfig.status.uninstalled": "Uninstalled",
  "lang.ark.workflow.canDeleteshelfFlag": "Remove the shelves",
  "lang.ark.fed.screen.flowNodeConfig.tip.onlyWaitPoint": "Only applicable to waiting point",
  "lang.ark.fed.image": "Picture",
  "lang.ark.workTask.export.title.fileName": "Task query",
  "lang.ark.fed.pleaseEnterANumber": "Please enter the number",
  "lang.ark.fed.menu.workstationEditController": "Workstation",
  "lang.authManage.web.others.activePermission": "Enable permissions",
  "lang.ark.fed.isClearTrigger": "Are you sure to cancel the current trigger?",
  "lang.ark.fed.selectArea": "Select an area",
  "lang.ark.fed.byRackType": "According to shelf types",
  "lang.ark.action.interface.paramValue": "Parameter value",
  "lang.ark.fed.shangliao": "Feeding timeout",
  "lang.ark.fed.taskTriggerCycle": "Trigger period",
  "lang.ark.apiCommonCode.locationFromNotMatchStart": '"locationFrom: {0} failed to match the corresponding position by any point, workstation or area in the system"',
  "lang.ark.fed.surlpusGoods": "Remaining {0}",
  "lang.ark.common.exportTaskDetail": "Import task to view",
  "lang.ark.fed.currentTask": "Current task",
  "lang.ark.fed.taskOverTime": "Task completion time",
  "lang.ark.fed.DingTalk": "DingTalk",
  "lang.ark.workflow.queue.noAvailableStopPoint": "No point available",
  "lang.ark.fed.allowInterruptionOfCharging": "Allow interrupted charging",
  "lang.ark.robot.Task.send.failed": "Failed to send robot tasks",
  "lang.ark.fed.order": "Document information",
  "lang.ark.workflowTrigger.logType.taskRecordLog": "task execution log",
  "lang.ark.fed.pleaseSelectCellCode": "Please select a feeding point first",
  "lang.ark.fed.disCharingCell": "Material discharging station",
  "lang.ark.fed.shelfModel": "Shelf model",
  "lang.ark.fed.container.leaveFailure": "The following containers fail to exit. Please try again!",
  "lang.ark.warehouse.demandProductionLine": "Demand production line",
  "lang.mwms.rf.rfVersion": "PDA version control",
  "lang.ark.fed.virtualNode": "Virtual node",
  "lang.ark.workflow.nodeStatus.empty": "Empty (fail to pick up logistics container)",
  "lang.ark.fed.goBack": "Return",
  "lang.ark.fed.afterGoing": "Leave for",
  "lang.ark.fed.container": "Container",
  "lang.ark.fed.resume": "Resume running",
  "lang.ark.fed.screen.workflowInfo.dmpTaskId": "Dmp Task Id",
  "lang.ark.workflow.deviceTaskNotExistOrCompleted": "Closing is not allowed for device tasks that do not exist or have already been completed.",
  "lang.ark.fed.externalInterfaceInteraction": "External interface interaction",
  "lang.ark.workflow.authRoleHasUsed": "The following roles have set workstation permissions",
  "lang.ark.fed.optionalDockPoints": "Optional docking points",
  "lang.gles.receipt.tallyList": "Tally order",
  "lang.ark.fed.shelfAttribute.ENO": "ENO",
  "lang.ark.fed.screen.workflowInfo.responseParam": "Response",
  "lang.mwms.fed.shelfStrategy": "Putaway strategy",
  "lang.ark.warehouse.estimateUseTimeUnit": "Unit",
  "lang.ark.fed.pauseEnter": "Pause to enter",
  "lang.ark.fed.standbyPoint": "Standby point",
  "lang.ark.fed.arriveOperation": "Arrived in action",
  "lang.ark.fed.orderReceive": "Received",
  "lang.ark.apiNodeActionCode.successHandlerNotEmpty": "Success processing logic of node interaction configuration is empty",
  "lang.ark.task.log.export.title.fileName": "Workflow task log",
  "lang.ark.apiRobotTaskCode.waitPointExecutingTaskNotExists": "No task being executed at the waiting point",
  "lang.ark.fed.ruleOperator": "Operator",
  "lang.ark.trafficControl.enterType.singleFactorySingleEnter": "Single manufacturer with a single entrance",
  "lang.ark.workflow.externalInteraction": "External interaction upon arrival",
  "lang.ark.fed.uploadImageCutText": "To ensure the accuracy of barcode recognition, please crop the picture to the size of the barcode after uploading",
  "lang.ark.robot.classfy.noaction": "Motionless",
  "lang.ark.fed.screen.flowNodeConfig.ifExecuteRule": "If executed instruction rule is",
  "lang.ark.fed.noLoad": "No load",
  "lang.ark.workflow.containerNeedOrientation": "Container demand orientation",
  "lang.ark.fed.materialCode": "Material code",
  "lang.ark.apiContainerCode.isEmptyIsBlank": "isEmpty is empty",
  "lang.ark.fed.workstationConfig": "Workstation configuration",
  "lang.ark.manual.pick.up": "Manual removal",
  "lang.ark.interface.enableSuccess": "Successfully enabled",
  "lang.ark.waveStatus.create": "Create wave picking",
  "lang.ark.binStopPointRelation.binCellCode": "Stock bin point code",
  "lang.ark.trafficControl.triggerLock": "External triggering locking",
  "lang.ark.fed.siteManagement": "Site management",
  "lang.ark.workflow.workflowTaskSendPause": "Fail to operate the suspended workflow",
  "lang.authManage.web.permission.roleDesc": "Role description",
  "lang.ark.fed.conButtonLogPieceData": "Piece data",
  "lang.ark.fed.inventoryType": "Inventory type",
  "lang.ark.workflow.workflowStartNode": "Start point of process",
  "lang.ark.existWorkflowConfigUseTheArea": "A process configuration is using this area!",
  "lang.ark.fed.others": "Other",
  "lang.ark.fed.defaultSite": "Default site",
  "lang.ark.workflowConfig.cellFunctions.avoid": "Avoidance",
  "lang.ark.fed.obstacleAvoidance": "Front and rear obstacle avoidance",
  "lang.ark.warehouse.containerStockEdit": '"There is an inventory for this type of containers, which cannot be edited"',
  "lang.ark.common.ok": "Successful invocation",
  "lang.ark.fed.pointPosition": "Point",
  "lang.ark.fed.waveTaskCreateFail": "Failed to generate task",
  "lang.ark.apiCommonCode.instructionNotBlank": "instruction cannot be empty",
  "lang.ark.fed.containerLevelTwo": "Secondary class classification of containers",
  "lang.ark.fed.pleaseEnterAPositiveInteger": "Please enter a positive integer",
  "lang.ark.interface.apiDelete": "Workflow instance deleted",
  "lang.ark.fed.rackState": "Container status",
  "lang.ark.fed.cancelWaveSure": '"If you cancel the wave picking, the wave picking document cannot continue the distribution and wave grouping is required. Confirm to cancel?"',
  "lang.ark.fed.sendingNode": "Send a node",
  "lang.ark.warehouse.buttonOperationEndSystemUni": '"The button configuration already exists, and the operation command does not match the currently input operation command"',
  "lang.ark.fed.mobileLocation": "Move position",
  "lang.ark.fed.manageAreaNoTrayPointMsg0": "Co-existence of pallet position and point position is not supported for now!",
  "lang.ark.api.workflowConfigNoMatched": "No matching process",
  "lang.ark.fed.menu.vens.dmpDeviceModel": "Device model",
  "lang.ark.fed.businessRuleFlow": "Business rule flow",
  "lang.ark.warehouse.Baiting": "Material discharging",
  "lang.ark.fed.move": "Moving",
  "lang.ark.workflow.denseStorageTaskUpperLimit": '"The starting point is the dense storage area, which has reached the maximum number of accepted tasks"',
  "lang.ark.fed.conButtonLogIP": "IP",
  "lang.ark.fed.escDrawsBranchLines": "ESC Draw branch lines ",
  "lang.ark.fed.waitLeave": "Waiting to leave",
  "lang.ark.fed.mergeNode": "Connect existing nodes",
  "lang.ark.fed.responseParameterValue": "Processing of returned parameter",
  "lang.ark.fed.linkDisconnect": "Disconnected",
  "lang.ark.workflow.areaLocked": "The traffic control area is locked and cannot be deleted",
  "lang.ark.robot.classfy.cage": "Traction",
  "lang.ark.fed.pleaseCheckTheRequiredItems": "Please check the required fields",
  "lang.mwms.fed.strategyAllocate": "Hit strategy management",
  "lang.ark.fed.logManagement": "Log management",
  "lang.ark.action.interface.extraParam20": "extraParam20",
  "lang.ark.fed.nextStep": "Next step",
  "lang.ark.fed.editingWorkflow": "Workflow editing",
  "lang.ark.fed.conButtonLogID": "ID",
  "lang.ark.fed.circulationStrategy": "Transfer strategy",
  "lang.authManage.web.others.disabledPermisssion": "Disable permissions",
  "lang.ark.fed.startTime": "Start time",
  "lang.ark.fed.containerManage": "Container management",
  "lang.ark.apiCallbackReg.timeInterval": "Time interval",
  "lang.ark.fed.noGoodsChangeLocation": "There is no feeding record of the material. Please switch the stock bin",
  "lang.ark.fed.waitingEnter": "Waiting to enter",
  "lang.ark.fed.sourceProductionLine": "Source production line",
  "lang.ark.fed.taskTypeName": "Task type",
  "lang.ark.fed.containerBinUsed": "Task occupation",
  "lang.ark.element.has.bound": "This element has been bound to the workflow",
  "lang.ark.fed.menu.workstationaddress": "Access address",
  "lang.ark.fed.editComponentInterface": "Edit component command",
  "lang.ark.workflowTriggerMonitorStatus.create": "Create",
  "lang.ark.operation.workflow.recoveryWorkflow": "Recovery task",
  "lang.ark.fed.noGoodsInfo": "No materials",
  "lang.ark.fed.nodeEditing": "Node editing",
  "lang.ark.fed.menu.robotTypeManagement": "Robot configuration",
  "lang.ark.fed.menu.templateInstance": "Template example",
  "lang.ark.robot.robotBindDeviceExist": "The corresponding device has been bound with the robot.",
  "lang.ark.fed.length": "Length ",
  "lang.ark.systemErrCannot_operate": "System exception and no operation allowed",
  "lang.ark.fed.Wechat": "Enterprise WeChat",
  "lang.ark.fed.estimate": "The AGV is expected to arrive in",
  "lang.mwms.fed.SNPoolCharts": "SN (serial number)",
  "lang.ark.fed.productType": "Product model",
  "lang.mwms.fed.monitoring": "Warehouse monitoring",
  "lang.authManage.web.common.isDelRol": "Confirm to delete the role?",
  "lang.ark.apiCommonCode.needTimeFormatError": "needTime format error",
  "lang.ark.fed.optionalRackPoints": "Optional shelf points",
  "lang.ark.fed.autoSetNodeValue": "Automatic value assignment point",
  "lang.mwms.monitorRobotMsg.norobot": "No robots available",
  "lang.ark.robot.go.return.pallet": "To return pallet",
  "lang.ark.fed.screen.flowNodeConfig.pleaseSelectCondition": "Select judgment conditions",
  "lang.ark.fed.higherSpeed": "High speed",
  "lang.ark.loadCarrier.loadCarrierModelSyncErr": "Synchronizing container model to RMS encountered error.",
  "lang.ark.workflow.condition.unEqual": "Not equal to",
  "lang.ark.record.robotCallback.fetched": "Take to the shelf",
  "lang.ark.fed.remap": "Redraw the map",
  "lang.ark.fed.expression": "Expression",
  "lang.ark.fed.interfaceSetting": "Interface settings",
  "lang.ark.fed.orderCreate": "Create",
  "lang.ark.fed.pleaseSelectTheTaskRecord": "Please select a task record",
  "lang.ark.fed.menu.authManage": "Auth",
  "lang.ark.workflow.startNode": "Start point",
  "lang.ark.trafficControl.shelfRange": "Traffic control area",
  "lang.mwms.rf.receiveWithoutTask": "Stock in without warehousing entry",
  "lang.ark.fed.screen.flowNodeConfig.isDoubleLiftRobot": "Double lifting robot?",
  "lang.authManage.web.common.editor": "Editor",
  "lang.ark.interface.apiLocationList": "Point query",
  "lang.ark.fed.GRAVE": "Severe",
  "lang.ark.fed.createTriggerTask": "Create trigger task",
  "lang.ark.workflow.task.status.node.backing": "Returning",
  "lang.ark.fed.name": "Name:",
  "lang.ark.fed.contents.flowConfig.recycleType": "Return strategy",
  "lang.mwms.fed.pickWorkManualCreate": "Picking is manually generated",
  "lang.ark.task.log.export.title.robot.number": "Robot number",
  "lang.ark.fed.component.workflow.nodeType.equipment": "Device node",
  "lang.ark.binStopPointRelation.binOrder": "Stock bin SN",
  "lang.authManage.web.others.missPage": "Sorry, The page you are looking for cannot be found!",
  "lang.ark.fed.menu.robotControlStrategy": "Robot strategy",
  "lang.ark.workflow.recoveryAreaType.workflowStart": "Start point of process",
  "lang.ark.waveGeneratePattern.documentAuto": "Automatic wave grouping by document",
  "lang.ark.fed.menu.monitoringAndManagement": "Monitoring",
  "lang.ark.fed.waitSendGoods": "Material to be delivered",
  "lang.ark.robot.robotModelExist": "The co-robot type corresponding to the robot type already exists. Do not add it repeatedly.",
  "lang.ark.fed.productDate": "Production date",
  "lang.ark.fed.waveTaskCode": "Wave picking task code",
  "lang.ark.workflow.workflowRuleNameExist": "Duplicate rule name",
  "lang.ark.fed.completionTime": "Completion time",
  "lang.ark.fed.expiryDate": "Expiration date",
  "lang.ark.warehouse.canNotUseNodeToNode": "Point-to-point template cannot be used if there are multiple destination locations",
  "lang.ark.action.interface.integer": "int",
  "lang.ark.fed.orderAbnormalTip": "{successNum} items succeeded and {faildNum} items failed. Only documents whose task is in the abnormal suspended status can be operated",
  "lang.ark.fed.containerForm": "Container shape",
  "lang.ark.api.template.startNodeIsBlank": "Task start point is empty",
  "lang.ark.fed.processGroupNumber": "No. of workflow group ",
  "lang.ark.fed.viewTheWholeProcess": "View full workflow",
  "lang.authManage.web.common.oldPassword": "Old password",
  "lang.ark.interface.apiContainerCategoryList": "Container type query",
  "lang.ark.fed.contents.flowConfig.recycleType.manual": "Manually select the return location.",
  "lang.ark.workflow.area.autoRealease": "Automatic release of robot",
  "lang.ark.fed.conditionNumber": "Condition value",
  "lang.ark.fed.batteryVoltage": "Battery voltage",
  "lang.ark.fed.show": "Display",
  "lang.ark.fed.redistributionSuccessfully": "Reallocated successfully",
  "lang.ark.fed.personalizationOptionsTitle": '"After enabled, the material calling workstation allows the user to enter the estimated material use time of the demand materials"',
  "lang.ark.fed.sourcePoint": "Source station",
  "lang.ark.fed.pickListsPending": "Material requisition sheet to be processed",
  "lang.mwms.fed.user.add": "Add user",
  "lang.ark.robot.go.deliver.pallet": "To deliver pallet",
  "lang.ark.fed.activeDistribution": "Active distribution",
  "lang.ark.warehouse.getTaskHashCancle": "The material requisition sheet has been canceled",
  "lang.gles.workflow.receiptMonitor": "Order monitoring",
  "lang.ark.fed.screen.hybridRobot.pleaseInputNumber": "Please enter. Only numbers can be entered.",
  "lang.authManage.web.others.pwsavesuccess": "The password is reset successfully, and the new password is {0}",
  "lang.ark.fed.logControllerConfigId": "Controller config ID",
  "lang.authManage.web.common.edit": "Edit",
  "lang.ark.fed.openAutoFlow": "Open automatic process",
  "lang.ark.fed.controllerNumber": "Controller No.",
  "lang.ark.fed.enableEdit": "Editable",
  "lang.ark.fed.seriNum": "Node code",
  "lang.ark.fed.conditionalCoding": "Condition code",
  "lang.ark.fed.width": "Width ",
  "lang.ark.fed.editExtendDevice": "Edit external equipment",
  "lang.ark.apiStationCode.stationStopPointNotOnlyOne": '"There are more than one docking points in the workstation, and the specific point must be specified"',
  "lang.ark.action.interface.string": "string",
  "lang.ark.fed.scopestartEnd": "Range {start}~{end}",
  "lang.ark.fed.isFastStartFlow": "Are you want to quickly initiate the current process?",
  "lang.ark.fed.cellCode": "Point position code",
  "lang.ark.fed.pleaseInputGoodsCode": "Please enter or scan the material code",
  "lang.ark.fed.receivingPoint": "Material requisition station",
  "lang.ark.fed.waveStrategyTrigger": "Triggering conditions",
  "lang.ark.workflow.shelfCodeAlreadyExists": "External number of container type already exists",
  "lang.ark.workstationIsExist": "Workstation already exists",
  "lang.ark.fed.taskFrom.fetchTask": "Picking task",
  "lang.ark.fed.workstationPage": "Workstation page",
  "lang.ark.areaCellCodeUsed": "The point cannot belong to both the area where a queue strategy is selected and workstation. Please delete: {0}!",
  "lang.ark.workflow.arrive.action.component.commandPhaseIllegal": "Invalid command execution phase",
  "lang.ark.fed.materialDetails": "Material list",
  "lang.ark.fed.contents.flowConfig.recycleType.autoRecoveryRule.columns.position": "location",
  "lang.ark.workflow.containerSelectedByTask": "The container has been occupied by a task",
  "lang.ark.workflow.noLongerWaiting": "The current task is no longer in the?? waiting to enter?? status and cannot be paused. Please refresh",
  "lang.ark.fed.citeFlow": "Reference workflow",
  "lang.ark.fed.screen.flowNodeConfig.executeByNormal": "Normally execute",
  "lang.ark.fed.trafficAreaControlManagement": "Area monitoring",
  "lang.ark.fed.outGoodsNumMoreThanRemain": "Stock bin {0}: up to {1} can be stocked out",
  "lang.ark.fed.contents.flowConfig.autoRecyclePosition": "preset location",
  "lang.authManage.web.others.pinlessIp": "Bind device info",
  "lang.ark.workflow.workflowNotCancel": "The cancellation of the current process does not meet the cancellation rules",
  "lang.ark.fed.containBinIsOccupy": "The stock bin has been occupied",
  "lang.ark.fed.orderSend": "Distributing",
  "lang.ark.fed.modificationTime2": "Modification time",
  "lang.ark.fed.getMaterialDescribe": "For scenarios of active distribution by the feeding point",
  "lang.ark.fed.taskManagement": "Task monitoring",
  "lang.authManage.web.others.filterKey": "Please enter keywords to filter",
  "lang.ark.archiveType.containerChangeLog": "Container modification log",
  "lang.mwms.fed.inventoryAge": "Warehouse age report",
  "lang.ark.base.license.instanceIdIsNull": "Item instance is null!",
  "lang.ark.fed.billDashboard": "Document display board",
  "lang.ark.fed.restart": "Restart",
  "lang.ark.fed.newWorkflow": "Create a new workflow",
  "lang.ark.fed.operationDurationGreaterThan": "Operation duration>=",
  "lang.ark.fed.nodeConfirmedLeave": "Confirm that the robot leaves the node",
  "lang.ark.fed.fullGoods": "Available",
  "lang.ark.interface.dmpHandleDoor": "Unlock and lock the door",
  "lang.ark.fed.paramValueCode": "Parameter value coding",
  "lang.ark.base.license.customerIdIsNull": "Client ID is null!",
  "lang.ark.fed.placeItHere": "Put it here",
  "lang.ark.fed.interactiveList": "Interaction list",
  "lang.ark.fed.processNumber": "Work ID",
  "lang.ark.fed.unit": "Unit",
  "lang.ark.workflow.robotAllocationStrategy": "Robot rule",
  "lang.mwms.fed.user.edit": "Edit user",
  "lang.ark.fed.totalStock": "Inventory/Total quantity of materials",
  "lang.ark.fed.deliveryTime": "Distribution time",
  "rms.system.container.entry.failed": "Container entry failed",
  "lang.mwms.monitorRobotMsg21085": "Driver command error",
  "lang.mwms.monitorRobotMsg21084": "Task step stage error",
  "lang.mwms.monitorRobotMsg21083": "An incorrect command is given at the end of the task",
  "lang.mwms.monitorRobotMsg21082": "Task status machine switching error",
  "lang.ark.fed.executionCondition": "Execution condition",
  "lang.mwms.monitorRobotMsg21089": "Driver under voltage",
  "lang.ark.warehouseTask.loadTaskOverTime": "Warning of station feeding duration given for more than {0} minutes",
  "lang.mwms.monitorRobotMsg21088": "Driver feedback error",
  "lang.mwms.monitorRobotMsg21087": "Driver trace error",
  "lang.ark.fed.functionType": "Function type",
  "lang.mwms.monitorRobotMsg21086": "Driver motor phase error",
  "lang.ark.fed.takeNode": "Take a node",
  "lang.gles.baseData.productionLine": "Product line",
  "lang.ark.fed.manualSelectGoodsAndDest": "Manually select a destination",
  "lang.mwms.monitorRobotMsg21081": "Communication between master control and industrial control is interrupted",
  "lang.mwms.monitorRobotMsg21080": "Weighing sensor data is missing.",
  "lang.ark.fed.obstacleAvoidanceArea": "Obstacle avoidance area",
  "lang.ark.action.interface.conditionType": "Type of conditional value",
  "lang.mwms.fed.warehouse": "Warehouse",
  "lang.ark.fed.nodeControl": "Node control",
  "lang.ark.warehouse.demandLineStation": "Demand point",
  "lang.ark.workflow.hitStrategy": "Hit strategy",
  "lang.ark.fed.screen.container.belongsToGroup": "Grouping",
  "lang.ark.fed.commandDetail": "Command description",
  "lang.ark.action.interface.conditionContainerCode": "containerCode",
  "lang.ark.workflow.customCellNotMatchCell": "No idle point matches the custom recycling area. Cancellation failed!",
  "lang.mwms.monitorRobotMsg21079": "Absolute encoder battery is low",
  "lang.mwms.monitorRobotMsg21096": "Driver temperature is too low",
  "lang.mwms.monitorRobotMsg21095": "Driver temperature is too high",
  "lang.mwms.monitorRobotMsg21094": "Driver motor temperature is too low",
  "lang.mwms.monitorRobotMsg21093": "Driver motor temperature is too high",
  "lang.mwms.monitorRobotMsg21099": "Component delivery exception",
  "lang.mwms.monitorRobotMsg21098": "Driver address error",
  "lang.mwms.monitorRobotMsg21097": "Driver overspeed alarm",
  "lang.ark.fed.component.workflow.label.specifyEquip": "Specify device",
  "lang.mwms.monitorRobotMsg21092": "Driver current short circuit",
  "lang.mwms.monitorRobotMsg21091": "Driver over current",
  "lang.mwms.monitorRobotMsg21090": "Driver over voltage",
  "lang.ark.warehouse.hasSameCellCode": "The station point already exists",
  "lang.ark.fed.systemexceptionPleaseContactTheAdministrator": "System is exceptional and please contact administrator",
  "lang.ark.fed.orderNo": "Order No.",
  "lang.ark.fed.deliveryCompletionTime": "Completion time of distribution",
  "lang.ark.fed.robotDeviceComponent": "Robot component device",
  "lang.ark.fed.selected.containerCode": "Select point position or container code",
  "lang.ark.fed.screen.equipmentAssociatedInfo.dialogDetail.titleView": "View device association information",
  "lang.ark.fed.logicOr": "+ or operator (||)",
  "lang.mwms.monitorRobotMsg21063": "Charging without current",
  "lang.mwms.monitorRobotMsg21062": "The image data uploaded by the camera failed to parse header and tail checksum",
  "lang.mwms.monitorRobotMsg21061": "The image data uploaded by the camera failed to parse header and tail checksum",
  "lang.ark.fed.thereAreNoProcessNodesPleaseClickAdd": "No workflow node, please click Add~",
  "lang.ark.warehouse.transfer": "Moving",
  "lang.mwms.monitorRobotMsg21060": "Obstacle avoidance of the robot",
  "lang.ark.fed.screen.hybridRobot.offsetValueL": "Offset value in Plane L",
  "lang.ark.fed.errorSide": "Incorrect container side. Please confirm the barcode",
  "lang.mwms.monitorRobotMsg21067": "Drive wheel motor over-current",
  "lang.ark.thisWorkstationStopPointNotOnlyOne": "The workstation does not have points or is not unique",
  "lang.mwms.monitorRobotMsg21066": "The drive wheel is overcharged",
  "lang.ark.fed.containerCode": "Container code",
  "lang.mwms.monitorRobotMsg21065": "The original data of obstacle avoidance not updated within 2 seconds",
  "lang.mwms.monitorRobotMsg21064": "Charging sensor failure",
  "lang.ark.warehouse.workflowTemplateDescription": '"When bound with a process template, the workstation can deliver and move materials (management mode) according to the interaction mode of the bound template"',
  "lang.ark.fed.screen.hybridRobot.offsetValueR": "Offset value in Plane R",
  "lang.ark.fed.deliveryOrder": "Distribution order",
  "lang.ark.fed.isDefaultAction": "Default interaction",
  "lang.mwms.monitorRobotMsg21059": "Triggered by rear anti-collision strip",
  "lang.mwms.monitorRobotMsg21058": '"Shelf QR code decoding error. For example: the black box has been decoded, but the code value is wrong"',
  "lang.mwms.monitorRobotMsg21057": "Triggered by front anti-collision strip",
  "lang.ark.fed.interfaceLocation": "Interface address",
  "lang.ark.fed.goodsCode": "Material code",
  "lang.ark.warehouse.estimateUseTimeUnit.hour": "hour",
  "lang.ark.action.interface.failure": "Failed",
  "lang.mwms.monitorRobotMsg21074": '"Laser radar data loss, or device failure"',
  "lang.mwms.monitorRobotMsg21073": "Timeout in decoding of upwards QR code on shelf",
  "lang.ark.fed.tuesday": "Tuesday",
  "lang.mwms.monitorRobotMsg21072": "Timeout in decoding of upwards QR code on shelf",
  "lang.mwms.monitorRobotMsg21071": "Shelf QR code decoding failed. For example: Even the black box has not been parsed.",
  "lang.ark.fed.addStation": "Add station",
  "lang.mwms.monitorRobotMsg21078": "Unrecoverable fault of the motor module (replacement)",
  "lang.mwms.monitorRobotMsg21077": "Recoverable fault of the motor module",
  "lang.mwms.monitorRobotMsg21076": "Battery overtemperature protection",
  "lang.mwms.monitorRobotMsg21075": "Battery data loss",
  "lang.ark.fed.menu.apiSchema": "Interface setting",
  "lang.ark.fed.syncInformSuccess": "Information synchronized successfully.",
  "lang.mwms.monitorRobotMsg21070": "DSP data feedback error",
  "lang.ark.workflow.area.queueStrategy": "Queue strategy",
  "lang.ark.trafficControl.enterType": "Access mode",
  "lang.ark.fed.uncheckedDataToBeDeleted": "No data to be deleted is checked!",
  "lang.mwms.monitorRobotMsg21069": "DSP lost heartbeat. No feedback at the correct QR code position",
  "lang.mwms.monitorRobotMsg21068": "Lifting motor over-current",
  "lang.ark.areaCode.not.exist.stop.range": "There is no emergency stop area in this area",
  "lang.mwms.monitorRobotMsg21041": "Driver loss of connection",
  "lang.ark.roller.docking.feeding": "Roller butt feeding",
  "lang.ark.fed.menu.robotUpgradeLog": "Upgrade log",
  "lang.mwms.monitorRobotMsg21040": "CAN2 communication failure. For example: No data is received within 400ms.",
  "lang.ark.fed.selectMaterialAndQuantity": "Select material and quantity",
  "lang.mwms.monitorRobotMsg21045": "The base values of the two calibrations of gyro are very different",
  "lang.ark.workflow.workflowTaskFetchDuplicate": "Fetch operation repeated",
  "lang.mwms.monitorRobotMsg21044": "Gyro temperature changes too much.",
  "lang.mwms.fed.user.disable": "Enable/disable user",
  "lang.mwms.monitorRobotMsg21043": "DSP loss of connection. For example: No DSP data received within 40s",
  "lang.mwms.monitorRobotMsg21042": "Encoder loss of connection",
  "lang.ark.fed.pleaseSelectTheControlButtonToDelete": "Please select the control button to be deleted",
  "lang.ark.workflow.arrive.action.goTurnOfAngle.pos270": "270°",
  "lang.mwms.monitorRobotMsg21038": "The power box received incorrect battery data",
  "lang.mwms.monitorRobotMsg21037": "Communication link exception between power box and master control",
  "lang.mwms.monitorRobotMsg21036": "Obstacle avoidance box/upper computer received incorrect data from the obstacle avoidance sensor",
  "lang.mwms.monitorRobotMsg21035": "Communication link exception between obstacle avoidance box and master control",
  "lang.ark.fed.fiveLight": "Five-color indicator",
  "lang.ark.fed.successHandler": "Successfully workflowed",
  "lang.ark.fed.normal": "Normal",
  "lang.mwms.monitorRobotMsg21039": "CAN1 communication failure. For example: No data is received within 400ms.",
  "lang.mwms.monitorRobotMsg21052": '"Error occurred when saving image. For example, the calibration error of the ground QR code > 10°, and the side error > 4cm"',
  "lang.mwms.monitorRobotMsg21051": "The right wheel skids when moving in a straight line",
  "lang.mwms.monitorRobotMsg21050": "The left wheel skids when moving in a straight line",
  "lang.ark.fed.component.workflow.label.equipType": "Device type",
  "lang.ark.fed.serialNumber": "Serial number",
  "lang.mwms.monitorRobotMsg21056": '"Drive wheel encoder pulse number overflow, that is, the count exceeds the upper limit"',
  "lang.mwms.monitorRobotMsg21055": "Drive wheel encoder pulse count not updated. For example: encoder failure during linear or arc motion",
  "lang.mwms.monitorRobotMsg21054": "Lifting motor cannot lift. For example: An error is reported if no pallet is lifted within 20 seconds in the lifting mode",
  "lang.mwms.monitorRobotMsg21053": "Non-QR code area image feedback. For example: code loss error",
  "lang.ark.api.flowStrategyIsNull": "Process matching strategy is empty",
  "lang.ark.fed.operationalProcessConfiguration": "Operation workflow configuration",
  "lang.ark.apiCommonCode.locationToNotExists": "locationCode:{0} does not exist",
  "lang.ark.fed.noAvailableRobotsWereFound": "No available robots found",
  "lang.ark.fed.menu.interfaceLog": "Interface log",
  "lang.ark.fed.orderTaskHang": "Document suspended",
  "lang.ark.fed.trafficControl": "Traffic control",
  "lang.ark.fed.stationExistPassWorkflowInstance": "A process instance passing through this workstation exists. Please try again later",
  "lang.ark.waveStatus.distributed": "Distributed",
  "lang.mwms.monitorRobotMsg21049": '"Drive wheel deadlock failure. For example: In linear, arc, rotatory and other motion modes, it is considered a drive wheel deadlock if there is no motion for more than 2 seconds."',
  "lang.mwms.monitorRobotMsg21048": "No battery data update within 200 seconds",
  "lang.mwms.monitorRobotMsg21047": "No obstacle avoidance data update within 2 seconds",
  "lang.mwms.monitorRobotMsg21046": "The drive wheel skidded as it rotated",
  "lang.mwms.monitorRobotMsg.targetnotfree": "Destination location (target area) is not in the idle status",
  "lang.ark.illegal_containerType_code": "Incorrect container encoding format",
  "lang.ark.fed.menu.editWorkstationConfig": "Display control",
  "lang.mwms.monitorRobotMsg21023": "Timeout in getting dsp data ceil rect",
  "lang.mwms.monitorRobotMsg21022": "More than 2 ground QR codes lost",
  "lang.mwms.monitorRobotMsg21021": '"When placing the shelf, do not put it down if the shelf QR code is in a poor location, so as to prevent it from colliding with nearby shelves."',
  "lang.mwms.monitorRobotMsg21020": "The shelf position is poor when rotating to adjust the shelf",
  "lang.ark.container.entry.failed": "Container entry failed",
  "lang.ark.apiRobotTaskCode.robotIdNotEmpty": "Robot ID cannot be empty",
  "lang.ark.apiContainerCode.containerCategoryOnlyOneNotMatch": "The unique containerCategory not matched",
  "lang.ark.hitStrategy.shelfDenseStorage": "Dense storage of support corridor",
  "lang.ark.action.interface.containerCode": "containerCode",
  "lang.ark.fed.completed": "Completed",
  "lang.mwms.monitorRobotMsg21016": "The charge is going backwards in the wrong direction",
  "lang.ark.apiRobotTaskCode.robotTaskNotCreate": "No robot task generated",
  "lang.mwms.monitorRobotMsg21015": "The xy of the charging station deviates from the hourly coordinate by 20mm",
  "lang.mwms.monitorRobotMsg21014": "The robot moves to the charging station with a deviation of 20mm in xy coordinates",
  "lang.ark.fed.excel.deviceCodeNotExist": "Device ID in line {0} does not exist. Please make changes before uploading.",
  "lang.ark.fed.warehouse.goods": "Material",
  "lang.mwms.monitorRobotMsg21013": "Encoder angle change detection when the robot is in the stop mode. For example: An error may be reported if the robot is rotated by an external force in the stop status.",
  "lang.ark.fed.ruleExpression": "Expression",
  "lang.mwms.monitorRobotMsg21019": "The robots current point and path starting position exceed the limit",
  "lang.mwms.monitorRobotMsg21018": "The height parameter is not correct when lowering",
  "lang.mwms.monitorRobotMsg21017": "The height parameter is not correct when lifting",
  "lang.mwms.monitorRobotMsg21030": "The wheel slipped during movement.",
  "lang.ark.workflow.task.status.InterruptWaiting": "Interrupt waiting",
  "lang.mwms.monitorRobotMsg21034": "Obstacle avoidance box/upper computer did not receive data from the obstacle avoidance sensor",
  "lang.mwms.monitorRobotMsg21033": "Entered manual mode",
  "lang.mwms.monitorRobotMsg21032": "Emergency stop switch triggered",
  "lang.mwms.monitorRobotMsg21031": "Exception occurred when recognizing the shelf model.",
  "lang.ark.fed.addRack": "Add a shelf",
  "lang.ark.fed.areYouSureToDeleteTheListInformation": "Are you sure to delete the list information?",
  "lang.ark.fed.interfaceTaskCode": "External task number",
  "lang.ark.fed.severityLevel": "Severity",
  "lang.mwms.monitorRobotMsg21027": "Failed to charge",
  "lang.mwms.monitorRobotMsg21026": "Task execution error received in manual mode",
  "lang.mwms.monitorRobotMsg21025": "Wrong Angle when planning small path",
  "lang.ark.fed.nodeActionName": "Node interaction configuration name",
  "lang.mwms.monitorRobotMsg21024": "Timeout in getting dsp data ceil decode",
  "lang.mwms.monitorRobotMsg21029": '"After switching to QR code navigation, the first QR code on the ground cannot be reached"',
  "lang.ark.waveTaskStatus.distributing": "Distributing",
  "lang.mwms.monitorRobotMsg21028": '"During walking, the upwards QR codes on the shelves is out of field of view."',
  "lang.ark.fed.hybridRobot.hybridRobotType.singleLift": "Single lifting",
  "lang.ark.fed.receiveCallTask": "Receive material calling task",
  "lang.mwms.monitorRobotMsg21001": "The angle deviation of straight walking is more than 3°",
  "lang.ark.fed.waveOrders": "Number of documents",
  "lang.mwms.monitorRobotMsg21000": '"Lateral deviation of the end QR code positioning is > 20mm, and the angle is greater than 2°"',
  "lang.ark.fed.deliverOrder": "Distribution order",
  "lang.ark.fed.leftRotation": "Anticlockwise",
  "lang.ark.fed.waveTriggerCondition": "Triggering conditions",
  "lang.ark.fed.queueUp": "Queuing",
  "lang.ark.hitStrategy.cyclicSave": "Cyclic storage",
  "lang.ark.fed.menu.vens.equipmentAssociatedInfo": "Elevator Configuration",
  "lang.ark.fed.optionalDockPointPosition": "Optional point",
  "lang.ark.fed.second": "Second",
  "lang.ark.fed.interfaceInstructMsg1": "Please add at least one assignment value for sent parameter!",
  "lang.ark.workflow.area.noorder": "No queue",
  "lang.ark.fed.interfaceInstructMsg2": "Please add at least one processing of returned parameter!",
  "lang.ark.fed.menu.apiPlatform": "Interface platform",
  "lang.ark.fed.interfaceInstructMsg0": "There is unsaved data in assignment value for sent parameter or in processing of returned parameter!",
  "lang.mwms.monitorRobotMsg21012": "Image fusion angle change detection when the robot is in the stop mode. For example: An error may be reported if the robot is rotated by an external force in the stop status.",
  "lang.mwms.monitorRobotMsg21011": "Gyro integral detection when the robot is in stop mode. For example: An error may be reported if the robot is rotated by an external force in the stop status.",
  "lang.ark.fed.rackName": "Shelf name",
  "lang.mwms.monitorRobotMsg21010": "Lift the shelf askew",
  "lang.ark.fed.day": "Day",
  "lang.ark.workflow.task.status.assign": "Task issued",
  "lang.authManage.web.common.modifySuccess": "Successfully modified",
  "lang.ark.interface.interfaceDesc": "Interface description",
  "lang.ark.apiContainerCode.mapRemoveContainerFail": "Failed to remove the container from the map",
  "lang.ark.fed.confirmCallRobot": "Confirm to call robot",
  "lang.authManage.web.common.dataPermission": "Data permission",
  "lang.ark.fed.copySuccess": "Copied!",
  "lang.ark.fed.shelfEditor": "Shelf editing",
  "lang.ark.fed.chromeForbidScan": "2. The browser has enabled code scanning of the camera, while the camera is disabled. Click the safety warning sign in the browser address bar, and then follow the browser guide to enable the camera.",
  "lang.ark.workflow.goShift": "Offset",
  "lang.mwms.monitorRobotMsg21005": "The direction of control command is not consistent with the direction of actual angle rotation. The cause may be incorrect command or high load",
  "lang.mwms.monitorRobotMsg21004": "The drive wheel skidded as it rotated",
  "lang.mwms.monitorRobotMsg21003": "Robot center position offset exceeds the limit",
  "lang.mwms.monitorRobotMsg21002": "The difference between the Angle of rotation integral and the Angle of encoder exceeds the limit",
  "lang.mwms.monitorRobotMsg21009": "The relative error of the two-dimensional code of the shelf and the two-dimensional code of the ground is over limit",
  "lang.ark.workflow.existsAttachTaskNotComplete": "The current process has uncompleted sub-node processes and cannot be completed",
  "lang.ark.fed.unloading": "Not fed",
  "lang.mwms.monitorRobotMsg21008": "Rotate the shelf more than 180°",
  "lang.mwms.monitorRobotMsg21007": "The error of target attitude and stop attitude is over limit",
  "lang.mwms.monitorRobotMsg21006": '"When fitting the arc according to the point, the accumulated error of the track smoothness exceeds the limit"',
  "lang.ark.robot.robotExist": "The robot exists. Do not add it repeatedly.",
  "lang.ark.fed.personalizationOptions": "Personalization options",
  "lang.ark.fed.executionStatus": "Execution status",
  "lang.ark.fed.cancelWave": "Cancel wave picking",
  "lang.ark.fed.noWorkflowPleaseCreateANewWorkflow": "No workflows, please create a new workflow.",
  "lang.ark.fed.rackEntry": "Shelf type-in",
  "lang.ark.fed.workstation.noMoreWorkstations": "No workstation to switch to",
  "lang.ark.fed.slight": "Minor",
  "lang.ark.fed.childWorkflowUnpublished": "Sub-process {0} is not published. Please publish the sub-process first!",
  "lang.ark.fed.removeFromTheSystem": "Remove from system",
  "lang.authManage.web.others.importLicense": "Import certificate",
  "lang.ark.fed.theConnectionBetweenTheDockingPointAndWorkstationCannotBe": "Associated points/workstations cannot be connected",
  "lang.gles.strategy": "Strategy management",
  "lang.ark.fed.curvePathOfRobotWalkable": "Curve paths on which the robot can walk",
  "lang.ark.apiContainerCode.containerCategoryMustSelect": "Container type must be specified",
  "lang.ark.fed.menu.physicalButtonLog": "Caller log",
  "lang.mwms.fed.taskResolve": "Task split",
  "lang.ark.workflow.taskNodeWaiting": "Waiting at a node",
  "lang.ark.fed.roller": "Roller way",
  "lang.mwms.pickingException": "Inventory exception",
  "lang.ark.fed.screen.equipmentAssociatedInfo.rowDeleteConfirm": "Deletion cannot be undone. Are you sure you want to proceed?",
  "lang.ark.fed.pleaseSelectTheRobotBeforeDrawingTheMap": "Please select a robot before drawing a map",
  "lang.ark.record.robotCallback.fbShift": "Robot offset {0}",
  "lang.ark.apiCommonCode.robotTypeNotEmpty": "The robotType parameter cannot be empty",
  "lang.ark.waveStatus.disCanceled": "Wave picking canceled",
  "lang.ark.fed.theAudioCacheing": "Preview audio buffering...Try again later",
  "lang.ark.fed.baseWorkStation": "Standard workstation",
  "lang.ark.fed.thereIsNoWorkflowAtThisDockPoint": "No workflows for this docking point ",
  "lang.ark.countNum": "Total:",
  "lang.ark.fed.allowPutDown": "{0}A lowering command can be configured only once",
  "lang.ark.workflow.recycleTypeNoConfig": "No location configured for returning goods to their original position upon cancellation",
  "lang.ark.fed.containerType": "Container type",
  "lang.ark.fed.upload": "Upload",
  "lang.ark.fed.priority": "Priority ",
  "lang.mwms.monitorRobotMsg.sendtaskfail": "Exception in task sending RMS. Please check RMS running status",
  "lang.authManage.web.common.creatTime": "Creation time",
  "lang.ark.fed.unloadWholeOrder": "Material discharging for the whole order",
  "lang.ark.warehouse.hasSameProductionLineName": "The production line name already exists",
  "lang.ark.fed.cycleTimes": "Cycle number",
  "lang.ark.fed.cancel": "Cancel",
  "lang.ark.fed.screen.hybridRobot.robotBindDevice": "Bind robot with upper structure",
  "lang.ark.fed.startPointName": "Starting point name",
  "lang.ark.fed.materialNo": "Document line number",
  "lang.ark.fed.workstationOnlyOneUser": "（The multi-session is not available）",
  "lang.ark.fed.cellCodeType": "Code type",
  "lang.ark.workflow.template.type.nodeToNode": "Point-to-point task",
  "lang.ark.fed.goodsLocationLimtMax": "Upper limit of materials in the stock bin",
  "lang.ark.fed.screen.hybridRobot.robotBodyTip": "Data comes from RMS robot information",
  "lang.ark.interface.interfaceDesc.type": "Field type",
  "lang.ark.externalDevice.cautionContent": '"If the interaction actions of the equipment corresponding to the selected equipment type are not exactly the same, please modify them to the same!"',
  "lang.ark.apiStationCode.stationStopPointNotMatch": "Docking point in the workstation not matched",
  "lang.ark.workflow.arrive.action.goTurnOfAngle.neg360": "-360°",
  "lang.ark.fed.pleaseUploadImage": "Please upload the correct barcode picture first",
  "lang.ark.fed.fullBinsChange": "The stock bin is full. Please switch the stock bin",
  "lang.ark.fed.destGroup": "Node group",
  "lang.ark.fed.lower": "Down",
  "lang.ark.fed.operationMode": "Operating mode",
  "lang.ark.fed.dynamicNotAllowBranch": "Branch is not allowed for dynamic destination location",
  "lang.ark.fed.beforeExecute": '"Before each execution, keep the latest log"',
  "lang.ark.fed.automaticExecutionExpected": "Automatic execution, estimated",
  "lang.ark.fed.areUSureStop": "Confirm to execute emergency stop?",
  "lang.ark.fed.optionsSetting": "Option settings",
  "lang.ark.dynamicTemplate.nextPoint": "Next point",
  "lang.ark.fed.timeRange": "Time range",
  "lang.ark.fed.runMode.load": "Load",
  "lang.ark.fed.confirmShelfLeave": "Are you sure to remove?",
  "lang.ark.fed.creator": "Creator",
  "lang.ark.fed.containsIllegalCharacters": "Illegal characters included",
  "lang.ark.fed.menu.vens.dmpHeartbeat": "Device heartbeat",
  "lang.ark.workflow.notAllowCancelIfTaskPhase": "Cancellation is not supported at this stage!",
  "lang.ark.fed.notEnabled": "Not on",
  "lang.ark.fed.singleFactoryMultipleEntrances": "Single manufacturer with multiple entrances",
  "lang.authManage.web.permission.roleName": "Role name",
  "lang.ark.fed.productLineAutoSelect": "Automatic allocation by material",
  "lang.ark.fed.stationCode": "Workstation No.",
  "lang.ark.fed.actionsNotAllowAddContainer": "The {0} node has an interaction configuration error. The container cannot be added!",
  "lang.ark.warehouse.lineStationNotEmpty": "Production line station cannot be empty",
  "lang.ark.button.operation.command.send": "Deliver",
  "lang.ark.fed.abnormalInformation": "Exceptional information",
  "lang.mwms.fed.warehouseVolumeCharts": "Warehouse capacity report",
  "lang.ark.workflow.platformPriority": "Logistics container (shelf) is prior",
  "lang.ark.fed.shelfAttribute.RETURN": "RETURN",
  "lang.ark.goodsTask.export.title.fileName": "Material requisition sheet",
  "lang.ark.fed.systemVersion": "System version no.",
  "lang.ark.interface.apiNext": "Continue to execute the workflow",
  "lang.ark.fed.illegalProcess": "Illegal workflow",
  "lang.ark.workflow.buttonAlreadyExists": "This controller button already exists, and cannot be added repeatedly",
  "lang.ark.fed.theConnectionIsClosedAndReconnectedToTheServer": "Connection closed, reconnecting to server..",
  "lang.ark.robotDeviceComponent.robotType": "Robot model",
  "lang.ark.fed.amount": "Fetch/delivery QTY",
  "lang.mwms.fed.roleManage": "Role management",
  "lang.ark.workflow.arrive.action.goTurnOfSide.side": "Rotate to",
  "lang.ark.warehouse.zagvdbmNotexits": "Feeding point: {0} does not exist",
  "lang.ark.workflow.area.releaseStrategy": "Release strategy",
  "lang.ark.fed.remark": "Note",
  "lang.ark.fed.confirmMoving": "Are you sure to move?",
  "lang.ark.workflow.task.status.canceled": "Cancellation completed",
  "lang.ark.fed.workFlowNodeNotEdit": "The current node has no edit items",
  "lang.ark.workflowConfig.beginOrEndNodeCanNotEmpty": "Start node and end node need to be configured for the process",
  "lang.ark.fed.goodsLocationInfo": "Stock bin information",
  "lang.ark.fed.robotStrategy": "Robot strategy",
  "lang.ark.workflow.condition.in": "Contain",
  "lang.ark.fed.allType": "All types",
  "lang.ark.warehouse.materialPointOrderNotNull": "The feeding sequence cannot be empty",
  "lang.ark.api.goturn.turnangleError": "The value of the container rotation parameter turnAngle is not multiples of 90",
  "lang.ark.fed.pickUpTaskNumber": "Material requisition sheet number",
  "lang.ark.equipment.notSelectExistEquipment": "An already existing device cannot be added. The name of the existing device: {0}",
  "lang.ark.warehouse.hasSameSort": "The distribution order already exists",
  "lang.ark.interface.interfaceDesc.format": "Field length",
  "lang.ark.workflow.wareHouseAllType": "All",
  "lang.ark.fed.ruleName": "Rule name",
  "lang.ark.fed.screen.equipmentAssociatedInfo.columnEquipType": "Device type",
  "lang.ark.fed.retryDuration": "Retry time (in minutes)",
  "lang.mwms.fed.excelImport": "Excel import inquiry",
  "lang.ark.fed.describe": "Description",
  "lang.ark.fed.applicationEntryTime": "Application/Entry time",
  "lang.ark.fed.number": "Number",
  "lang.ark.workflow.putDownStuff": "Delivery of cargo",
  "lang.ark.fed.mapManagement": "Map management",
  "lang.ark.fed.screen.hybridRobot.binOrderTip": "The stock bin SN of the machine.",
  "lang.ark.action.interface.conditionValue": "Conditional value",
  "lang.ark.fed.interactionOfInternalInterface": "Internal interface interaction",
  "lang.ark.operatelog.operatetype.auto.group": "Automatic workflow (workflow group)",
  "lang.ark.fed.demandForMaterialsTitle": "Material calling on demand: the workstation can initiate all the processes that pass through this workstation",
  "lang.ark.fed.screen.equipmentAssociatedInfo.dialogDetail.btnAddEquip": "Add device",
  "lang.ark.fed.uploadImageMessage2": "The uploaded image file should preferably be visible on a white background",
  "lang.mwms.fed.putaway": "Putaway management",
  "lang.ark.fed.cancelMiddlePoint": "Cancel passing point",
  "lang.ark.fed.updateMap": "Update the map",
  "lang.ark.fed.screen.hybridRobot.robotPointOffset": "Robot point offset",
  "lang.ark.robot.go.receive": "Automatic receipt",
  "lang.gles.logisticsConfig.stationDefaultConfig": "Default setting",
  "lang.authManage.web.others.packup": "Pack up",
  "lang.ark.fed.uploadImageMessage1": "The uploaded image file should preferably be visible on a dark gray background",
  "lang.ark.fed.uploadImageMessage0": "The uploaded image file should preferably be visible against a light blue background",
  "lang.ark.fed.twoPointMode": "Double point mode",
  "lang.ark.fed.goodsPlaceType.put": "Placement gate",
  "lang.ark.fed.lowVolumeCharging": "Low battery charging",
  "lang.ark.fed.emptyContainerArea": "Empty box return area",
  "lang.ark.robot.go.return.box": "To return the box",
  "lang.ark.workflow.enterMapDestNotExistReturnFail": "There is no end point of entry for the shelf. Failed to return the empty box!",
  "lang.ark.robotDeviceComponent.recordAlreadyExist": "The record already exists and cannot be added repeatedly!",
  "lang.ark.workflow.sendOperationFailed": "Delivery operation failed",
  "lang.ark.fed.screen.hybridRobot.robotTypeNotAddedTip": "This model has not been added into the co-robot type. Please add it.",
  "lang.ark.workflow.function.type.recycleArea": "Recycling area",
  "lang.ark.workflowConfig.cellFunctions.skip": "Skip",
  "lang.ark.sys.config.group.other": "Other configuration",
  "lang.ark.fed.homePageBackgroundImageUploadViewSuccess": "The homepage background is uploaded successfully.",
  "lang.authManage.web.common.userName": "Username",
  "lang.ark.fed.byRackCode": "Encode the shelf",
  "lang.ark.fed.areUSureClearLock": "Confirm to unlock?",
  "lang.ark.workflowRule.referenced.cannot.be.delete": "The rule configuration is referenced by the process configuration and cannot be deleted",
  "lang.ark.fed.materialStation": "Station to which materials can be distributed",
  "lang.ark.workflow.cycleType.infiniteLoop": "Infinite cycle",
  "lang.ark.fed.rackEntrance": "Container Entry",
  "lang.ark.fed.closeAutoFlow": "Close automatic process",
  "lang.ark.workflow.areaInUsed": "The area code ({}) already exists. Please check!",
  "lang.ark.fed.robot": "Robot",
  "lang.ark.fed.sourceOrDestination": "Source/destination",
  "lang.ark.fed.externalDeviceType": "Device type",
  "lang.ark.fed.enabledShelfAttribute": "Do you want to select the type of material?",
  "lang.ark.fed.dataType.value": "Parameter type",
  "lang.ark.apiContainerCode.containerCodeNotBlank": "The container number must be specified",
  "lang.ark.fed.messagePush": "Message push",
  "lang.ark.fed.fullWorkStation": "FULL version of workstation",
  "lang.ark.fed.excel.deviceCodeRepeat": "Repeated data parameter {1} in line {0}. Please make changes before uploading.",
  "lang.ark.fed.issuedQuantity": "Actual quantity",
  "lang.ark.fed.flowTemplate": "Process template",
  "lang.gles.stock": "Inventory Management",
  "lang.ark.fed.taskStartTime": "Task start time",
  "lang.ark.workflow.shelfEntryNotSupported": "Container entry not supported",
  "lang.ark.fed.unsupportCamera": "Your browser has not enabled code scanning of the camera. Please use the uploaded picture to parse the barcode",
  "lang.ark.fed.serious": "Severe",
  "lang.ark.fed.piece": "items",
  "lang.ark.fed.dockPoint": "Docking point",
  "lang.mwms.fed.putawayRuleDesignatedShelf": "Specified shelf of putaway rules",
  "lang.mwms.fed.freeze": "Inventory freezing",
  "lang.ark.action.interface.locationTo": "locationTo",
  "lang.ark.fed.allValuesInTheCollectionAndRelationships": "All values in the collection have an AND relation",
  "lang.ark.fed.thereIsNoOptionalRobot": "No robots for selection!",
  "web.c.RoleAPI.item0090": "The role name already exists. Please use another name!",
  "lang.ark.fed.previousStation": "Start station",
  "lang.ark.fed.pleaseEnterANonzeroPositiveInteger": "Please enter a non-zero positive integer",
  "web.c.RoleAPI.item0092": "The role keyword cannot be null!",
  "web.c.RoleAPI.item0091": "Failed to add the role!",
  "web.c.RoleAPI.item0094": "Failed to modify!",
  "lang.ark.fed.currentPoint": "Current point",
  "lang.ark.fed.authWorkStation": "Workstation authority",
  "web.c.RoleAPI.item0093": "The role name cannot be null!",
  "web.c.RoleAPI.item0096": "Please select a role!",
  "lang.ark.fed.taskDashboard": "Task display board",
  "web.c.RoleAPI.item0095": "Failed to delete!",
  "lang.ark.fed.pleaseSelectWorkStation": "Please select at least one workstation!",
  "web.c.RoleAPI.item0089": "No role data",
  "lang.ark.fed.import": "Import ",
  "lang.ark.fed.workOrientation": "Work orientation:",
  "lang.ark.fed.floorNo": "Layer {0}",
  "lang.ark.fed.destPoint": "Destination location",
  "lang.ark.workflow.actionNameExists": "The interaction name already exists!",
  "lang.ark.targetNotFree": "The destination location is not in the idle status!",
  "lang.ark.robot.robotModelBindTopModulesNotAllowDelete": "The composite robot type that has been bound to the upper deck cannot be deleted",
  "lang.ark.fed.autoRefreshEvery20Seconds": "Monitoring records are automatically refreshed every 20 seconds",
  "lang.ark.fed.rackStatusRecovery": "Shelf condition recovery",
  "lang.ark.fed.email": "Email",
  "lang.ark.fed.SMPAlter": "Alarm notification",
  "lang.ark.fed.triggerContent": "Trigger content",
  "lang.ark.fed.chargingStation": "Charging station",
  "lang.ark.workflow.noContainersWereFoundThatCouldBeMoved": "No container can be moved!",
  "lang.ark.fed.selectMaterial": "Select materials",
  "lang.ark.fed.boxMoving": "Container handling",
  "lang.ark.fed.monitor.taskDashboard": "Task display board",
  "lang.ark.singleCellStation.canNotEdit": "Single point station cannot be edited",
  "lang.ark.fed.required": "Required",
  "lang.ark.fed.wareHouseSetting": "Stock in/out settings",
  "lang.ark.warehouse.configEnableDescription": "Disable it before modifying the configuration. Please modify during non-working hours",
  "lang.ark.fed.goodsNonExistent": "The current material does not exist. Please switch the material",
  "lang.ark.fed.exceptionDescription": "Exception description",
  "lang.ark.fed.deliveryCompleteTime": "Completion time of distribution",
  "lang.ark.workflow.archiveType": "Archive type",
  "lang.ark.interface.apiWorkflowList": "Workflow query",
  "lang.ark.fed.failedRetryTime": "Polling time",
  "lang.ark.fed.shelfAttribute.P1": "P1",
  "lang.ark.fed.shelfAttribute.P3": "P3",
  "lang.ark.fed.soon": "Coming soon",
  "lang.ark.fed.pleaseEnterContent": "Please enter the content",
  "lang.ark.button.operation.command.systemControl": "System control",
  "lang.ark.fed.menu.editingWorkflow": "Workflow",
  "lang.ark.fed.paramConfig": "Parameter configuration",
  "lang.gles.baseData.warehouseArea": "Warehouse area",
  "lang.ark.fed.robotMonitoring": "Robot monitoring",
  "lang.ark.fed.warnType": "Alarm mode",
  "lang.ark.interface.apiCancel": "Task revoked",
  "lang.mwms.fed.index": "Home page",
  "lang.ark.shelf.status.noValiable": "No shelf available ",
  "lang.ark.fed.interface": "Interface command",
  "lang.ark.workflow.task.status.cancelToNewAssignation": "Deliver to the designated point",
  "lang.ark.fed.waitingToSend": "Wait to send",
  "lang.ark.fed.moveTo": "Mobile",
  "lang.ark.fed.forceDeleteConfirmMsg1": "Failed to cancel the process. Do you want to force delete the process?",
  "lang.ark.workflow.shelfExchange": "Shelf exchange",
  "lang.ark.fed.id": "Encoding:",
  "lang.mwms.fed.receive": "Inspection of incoming cargo",
  "lang.ark.fed.onePicking": "Received by one AGV",
  "lang.ark.fed.palletPoint": "Pallet position",
  "lang.ark.fed.batchCancellation": "Batch cancellation",
  "lang.ark.interface.businessStatus": "Task status",
  "lang.ark.fed.value": "Value",
  "lang.ark.robot.invalid": "Illegal mode",
  "lang.ark.fed.reqNum": "Original order number",
  "lang.ark.fed.startScanningTheMap": "Start scanning the map",
  "lang.ark.fed.file": "File",
  "lang.ark.workflowOuterCode.exists": "Process external code cannot be repeated",
  "lang.ark.fed.dayLogo": "day(s) log",
  "lang.ark.fed.menu.operationLog": "Operation log",
  "lang.ark.fed.task": "Task",
  "lang.ark.loadCarrier.inUsing": "loadcarrier in using",
  "lang.ark.fed.normalStation": "Common workstation",
  "lang.ark.fed.operationInformation": "Operating information",
  "lang.ark.interface.response": "Response",
  "lang.ark.record.interface.sendTask": "Send interface instruction task",
  "lang.ark.fed.allOptions": "All",
  "lang.ark.fed.hostCellCode": "External No.",
  "lang.ark.fed.lowerSpeed": "Low speed",
  "lang.ark.record.robotCallback.arrivePassWaitPoint": "The robot has arrived waiting point",
  "lang.mwms.monitorRobotMsg10069": "Driver command error",
  "lang.ark.fed.menu.mapController": "Map",
  "lang.mwms.monitorRobotMsg10068": "Task step stage error",
  "lang.ark.equipment.notFoundEquipment": "Failed to find corresponding device",
  "lang.mwms.monitorRobotMsg10076": "Driver current short circuit",
  "lang.mwms.monitorRobotMsg10075": "Driver over current",
  "lang.ark.workflow.workflowTaskStatusError": "Incorrect state of the workflow task",
  "lang.mwms.monitorRobotMsg10078": "Driver motor temperature is too low",
  "lang.ark.base.license.sysParamForInstanceIdIsNull": "Instance ID verified by the certificate is null",
  "lang.mwms.monitorRobotMsg10077": "Driver motor temperature is too high",
  "lang.mwms.monitorRobotMsg10072": "Driver feedback error",
  "lang.mwms.monitorRobotMsg10071": "Driver trace error",
  "lang.mwms.monitorRobotMsg10074": "Driver over voltage",
  "lang.mwms.monitorRobotMsg10073": "Driver under voltage",
  "lang.mwms.monitorRobotMsg10070": "Driver motor phase error",
  "lang.ark.fed.segmentName": "Link line name",
  "lang.ark.fed.menu.config": "Config",
  "lang.ark.workflow.condition.equal": "Equal to",
  "lang.ark.fed.funcComponent": "Functional component",
  "lang.ark.fed.addInterface": "Add interface command",
  "lang.ark.fed.component.workflow.tooltip.specifiedEquip": '"When no specified device is selected, all devices of the selected feed gate type are searched for by default."',
  "lang.mwms.monitorRobotMsg10079": "Driver temperature is too high",
  "lang.ark.record.upstream.callback": "Notification upstream",
  "lang.ark.workflow.area.range": "Area scope",
  "lang.ark.fed.delete": "Delete",
  "lang.ark.api.workflow.break.failure": "Abort failed, robot task cancellation failed",
  "lang.mwms.monitorRobotMsg10082": "Driver address error",
  "lang.ark.fed.containerPoint": "Container point",
  "lang.mwms.monitorRobotMsg10081": "Driver overspeed alarm",
  "lang.mwms.monitorRobotMsg10080": "Driver temperature is too low",
  "lang.authManage.web.common.status": "Status",
  "lang.authManage.web.others.customer": "Client ID",
  "lang.ark.fed.common.btn.edit": "Edit",
  "lang.ark.interface.shelfMovingCallbackMsg": "Container position change callback",
  "lang.ark.fed.pleaseSelectTheConsole": "Please select the operation end",
  "lang.ark.fed.siteMainInterface": "Field main interface",
  "lang.mwms.monitorRobotMsg10047": "The error of target attitude and stop attitude is over limit",
  "lang.mwms.monitorRobotMsg10046": '"When fitting the arc according to the point, the accumulated error of the track smoothness exceeds the limit"',
  "lang.ark.fed.pinkingFinish": "Material fed",
  "lang.mwms.monitorRobotMsg10049": "The relative error of the two-dimensional code of the shelf and the two-dimensional code of the ground is over limit",
  "lang.ark.workflowConfig.configInUse": "There are ongoing process instances. Please try again later",
  "lang.ark.fed.hour": "hour",
  "lang.mwms.monitorRobotMsg10048": "Rotate the shelf more than 180°",
  "lang.mwms.monitorRobotMsg10054": "The robot moves to the charging station with a deviation of 20mm in xy coordinates",
  "lang.ark.fed.no": "No",
  "lang.ark.fed.goodsPlaceType": "Feed gate type",
  "lang.mwms.monitorRobotMsg10053": "Encoder angle change detection in stop mode. For example: An error may be reported if rotated by an external forced in the stop status.",
  "lang.mwms.monitorRobotMsg10056": "The charge is going backwards in the wrong direction",
  "lang.mwms.monitorRobotMsg10055": "The xy of the charging station deviates from the hourly coordinate by 20mm",
  "lang.ark.fed.timeoutInfoTip": "Document display board in the task display board shows the timeout information",
  "lang.mwms.monitorRobotMsg10050": "Lift the shelf askew",
  "lang.ark.workflow.mandatoryAllocation": "Force",
  "lang.ark.workflow.nodeClassification": "Node category",
  "lang.mwms.monitorRobotMsg10052": "Image fusion angle change detection in stop mode",
  "lang.ark.fed.billCreateFail": "Failed to create document",
  "lang.mwms.fed.strategyOrderTime": "Closing time calculation strategy",
  "lang.mwms.monitorRobotMsg10051": "Gyro integral detection in stop mode. For example: An error may be reported if rotated by an external forced in the stop status.",
  "lang.mwms.fed.sysconfig": "System configuration",
  "lang.ark.warehouse.materialPointNameNotNull": "Name cannot be empty",
  "lang.ark.agv.instructionRule.executeByNormal": "normal execution",
  "lang.ark.fed.goodsPlaceType.fetch": "Fetch gate",
  "lang.ark.fed.closeMoreGoodsInfo": "Collapse more material information",
  "lang.mwms.fed.dictSet": "Data dictionary",
  "lang.ark.fed.allDay": "Whole day",
  "lang.ark.fed.newAddedSystem": "Newly added system",
  "lang.ark.fed.servicePoints": "Service point",
  "lang.mwms.monitorRobotMsg10058": "The height parameter is not correct when lowering",
  "lang.ark.fed.externalDevice": "Device name",
  "lang.mwms.monitorRobotMsg10057": "The height parameter is not correct when lifting",
  "lang.ark.fed.screen.area.groupName": "Grouping name",
  "lang.mwms.monitorRobotMsg10059": "The current point and the path start location exceed the limit.",
  "lang.mwms.monitorRobotMsg10065": "Wrong Angle when planning small path",
  "lang.mwms.monitorRobotMsg10064": "Timeout in getting dsp data ceildecode",
  "lang.ark.action.interface.robotType": "robotType",
  "lang.mwms.monitorRobotMsg10067": "An incorrect command is given at the end of the task",
  "lang.mwms.monitorRobotMsg10066": "Task status machine switching error",
  "lang.ark.fed.menu.destPoint": "PDA settings",
  "lang.mwms.monitorRobotMsg10061": '"When placing the shelf, do not put it down if the shelf QR code is in a poor location, so as to prevent it from colliding with nearby shelves."',
  "lang.mwms.monitorRobotMsg10060": "The shelf position is poor when rotating to adjust the shelf",
  "lang.mwms.monitorRobotMsg10063": "Timeout in getting dsp data ceilrect",
  "lang.mwms.monitorRobotMsg10062": "More than 2 ground QR codes lost",
  "lang.ark.fed.modeTitle": '"Management mode: enabled. When the management mode is enabled for material calling, delivery of materials and moving, the workstation can call and deliver materials on behalf of the user, and specify any point to initiate a moving task."',
  "lang.ark.fed.oneWayEntrance": "Single entry",
  "lang.ark.workflow.cycleType.timeLoop": "Cycle by times",
  "lang.mwms.monitorRobotMsg22010": "Charging module error status",
  "lang.mwms.monitorRobotMsg22011": "The charging station reports that this station is unavailable",
  "lang.ark.fed.alarmLevel": "Alarm level",
  "lang.ark.fed.excel.data.isBlank": "Incomplete data in line {0}. Please complete the data and upload it again.",
  "lang.ark.loadCarrier.loadCarrierIsInUse": "The container is being used.",
  "lang.ark.fed.drawTheMap": "Drawn a map",
  "lang.ark.fed.shelfHeat": "Shelf popularity",
  "lang.mwms.monitorRobotMsg10029": "Driver over temperature",
  "lang.mwms.monitorRobotMsg10028": "Shelf QR code decoding timeout",
  "lang.mwms.monitorRobotMsg10025": "DSP lost heartbeat. No feedback at the correct QR code position",
  "lang.mwms.monitorRobotMsg10024": "Lifting motor over-current",
  "lang.ark.fed.classCode": "Category code",
  "lang.mwms.monitorRobotMsg10027": "Shelf QR code decoding failed. For example: Even the black box has not been parsed.",
  "lang.mwms.monitorRobotMsg10026": "DSP data feedback error",
  "lang.mwms.monitorRobotMsg10032": "Obstacle avoidance box/upper computer received incorrect data from the obstacle avoidance sensor",
  "lang.ark.fed.deleteSuccessfully": "Deleted successfully",
  "lang.ark.fed.pleaseEnterAWorkflowName": "Please enter a workflow name",
  "lang.mwms.monitorRobotMsg10031": "Communication link exception between obstacle avoidance box and master control",
  "lang.mwms.monitorRobotMsg10034": "The power box received incorrect battery data",
  "lang.mwms.monitorRobotMsg10033": "Communication link exception between power box and master control",
  "lang.ark.workflow.lastNodeMustConnectionEndNode": "The last non-sub-process node is not connected to the end node!",
  "lang.mwms.monitorRobotMsg10030": "Obstacle avoidance box/upper computer did not receive data from the obstacle avoidance sensor",
  "lang.ark.interface.id": "Serial number",
  "lang.ark.workflow.template.type": "Process template type",
  "lang.mwms.monitorRobotMsg22007": "Charging module overtemperature",
  "lang.mwms.monitorRobotMsg22008": "Charge current in automatic mode is 0",
  "lang.mwms.monitorRobotMsg22009": "Charging module warning status",
  "lang.mwms.monitorRobotMsg22003": "RMS data exception",
  "lang.ark.manual.place": "Manual placement",
  "biz.UserServiceImpl.updateUserAndRoleRelation.msg1": "The admin user is not editable",
  "lang.mwms.monitorRobotMsg22004": "RMS command exception",
  "lang.mwms.monitorRobotMsg22005": "Emergency stop button is pressed at forklift charging station",
  "lang.ark.fed.enterCode": "Enter the station code or production line code",
  "lang.mwms.monitorRobotMsg22006": "No sensor is detected at forklift charging station",
  "lang.ark.fed.singlePointMode": "Single point mode",
  "lang.ark.waveTaskStatus.distributed": "Distributed",
  "lang.mwms.monitorRobotMsg10039": "DSP loss of connection. For example: No DSP data received within 40s",
  "lang.ark.fed.workflowStatus": "Workflow state",
  "lang.mwms.monitorRobotMsg10036": "CAN2 communication failure. For example: No data is received within 400ms.",
  "lang.mwms.monitorRobotMsg10035": "CAN1 communication failure. For example: No data is received within 400ms.",
  "lang.gles.receipt.warehousingOrder": "Putaway order",
  "lang.ark.workflow.action.command.paramSourceType": "Value source",
  "lang.mwms.monitorRobotMsg10038": "Encoder loss of connection",
  "lang.mwms.monitorRobotMsg10037": "Driver loss of connection",
  "lang.mwms.monitorRobotMsg10043": "Center location deviation exceeds the limit.",
  "lang.mwms.monitorRobotMsg10042": "The difference between the Angle of rotation integral and the Angle of encoder exceeds the limit",
  "lang.ark.fed.shelfAttribute.IA": "IA",
  "lang.mwms.monitorRobotMsg10045": "The direction of control command is not consistent with the direction of actual angle rotation. The cause may be incorrect command or high load",
  "lang.ark.workflow.robotLineUp": "Robot queue",
  "lang.mwms.monitorRobotMsg10044": "The drive wheel skidded as it rotated",
  "lang.ark.fed.noOperationAvailable": "No operation available!",
  "lang.mwms.monitorRobotMsg10041": "The angle deviation of straight walking is more than 3°",
  "lang.mwms.monitorRobotMsg10040": "Lateral deviation exceeds 20mm during straight walking",
  "lang.ark.fed.menu.logManagement": "Log",
  "lang.ark.apiCommonCode.robotStopFailed": "Robot emergency stop failed",
  "lang.ark.workflow.workflowHaveFinished": "Cancellation failed. The process has been completed",
  "lang.ark.fed.flowClass": "Process category",
  "lang.ark.fed.goodsExistent": "The current material already exists. Please switch the material",
  "lang.ark.fed.doYouConfirmTheGeneration": "Are you sure to generate?",
  "lang.ark.workflow.wareHouseUnifiedConfig": "Unified configuration",
  "lang.mwms.monitorRobotMsg10": "Task exception",
  "lang.mwms.monitorRobotMsg10007": "The right wheel skids when moving in a straight line",
  "lang.mwms.monitorRobotMsg10006": "The left wheel skids when moving in a straight line",
  "lang.mwms.monitorRobotMsg10009": "Non-QR code area image feedback. For example: code loss error",
  "lang.mwms.monitorRobotMsg10008": '"Error occurred when saving image. For example, the calibration error of the ground QR code > 10°, and the side error > 4cm (not applied)"',
  "lang.mwms.monitorRobotMsg10003": "No obstacle avoidance data update within 2 seconds",
  "lang.mwms.monitorRobotMsg10002": "The drive wheel skidded as it rotated",
  "lang.gles.baseData.baseFactoryPosition": "Storage position",
  "lang.mwms.monitorRobotMsg10005": '"Drive wheel deadlock failure. For example: In linear, arc, rotatory and other motion modes, it is considered a drive wheel deadlock if there is no motion for more than 2 seconds."',
  "lang.mwms.monitorRobotMsg10004": "No battery data update within 200 seconds",
  "lang.mwms.monitorRobotMsg10010": "Lifting motor cannot lift. For example: An error is reported if no pallet is lifted within 20 seconds in the lifting mode",
  "lang.ark.fed.notification": "Notification object",
  "lang.mwms.monitorRobotMsg10012": '"Drive wheel encoder pulse number overflow, that is, the count exceeds the upper limit"',
  "lang.mwms.monitorRobotMsg10011": "Drive wheel encoder pulse count not updated. For example: encoder failure during linear or arc motion",
  "lang.gles.receipt.outWarehouseExternalOrder": "External outbound order",
  "lang.ark.fed.designatedRobotNew": "Designate a robot",
  "lang.ark.fed.systemEmergencyStop": "System emergency stop",
  "lang.mwms.fed.base": "Basic settings",
  "lang.mwms.monitorRobotMsg22000": "RMS communication interrupted",
  "lang.mwms.monitorRobotMsg22001": "CAN communication interrupted (charging module)",
  "lang.mwms.monitorRobotMsg22002": "Screen communication interrupted",
  "lang.ark.warehouse.materialPointOrderNoLessZero": "The feeding sequence must be greater than 0",
  "lang.ark.robot.robotNotExist": "The robot does not exist.",
  "lang.ark.fed.menu.taskLog": "Task log",
  "lang.mwms.monitorRobotMsg10018": "The image data uploaded by the camera failed to parse header and tail checksum",
  "lang.mwms.monitorRobotMsg10017": "Lifting motor cannot lower. For example: the shelf cannot be put down within 40 seconds",
  "lang.ark.fed.childWorkflowInUse": "Process {0} is in execution. Unload failed!",
  "lang.ark.fed.noMatchCellCode": "Target station",
  "lang.mwms.monitorRobotMsg10019": "Charging without current",
  "lang.ark.fed.saturday": "Saturday",
  "lang.mwms.monitorRobotMsg10014": "Shelf QR code decoding error",
  "lang.mwms.monitorRobotMsg10013": "Triggered by front anti-collision strip",
  "lang.ark.fed.endTime": "End time",
  "lang.mwms.monitorRobotMsg10016": "Obstacle avoidance triggered",
  "lang.mwms.monitorRobotMsg10015": "Triggered by rear anti-collision strip",
  "lang.mwms.monitorRobotMsg10021": "The original data of obstacle avoidance not updated within 2 seconds",
  "lang.mwms.monitorRobotMsg10020": "Charging sensor failure",
  "lang.mwms.monitorRobotMsg10023": "Drive wheel motor over-current",
  "lang.ark.workflow.action.command.robot.MediaStop": "Stop voice play",
  "lang.mwms.monitorRobotMsg10022": '"The drive wheel is overcharged, not a failure"',
  "lang.authManage.fed.screen.creditCardLogin.pleaseBrushCard": "Please swipe your work card",
  "lang.ark.fed.areaGroupCode": "Grouping code",
  "lang.ark.fed.take": "Fetch",
  "lang.ark.fed.pleaseSelectATarget": "Please select a destination",
  "lang.ark.interface.responseStatus": "Communication status",
  "lang.ark.interface.startPoint": "Start point code",
  "lang.ark.fed.materialPreparationPoint": "Feeding point",
  "lang.ark.warehouse.stationOtherCellCodeNotFree": '"There are tasks at other points in the workstation, and the material calling task cannot be initiated"',
  "lang.ark.fed.agvIsMovingWaitLock": "An AGV is moving and waiting to be locked",
  "lang.ark.interface.responseDate": "Response time",
  "lang.ark.fed.offShelves": "Go to the shelf",
  "lang.ark.logType.rmsCallbackInfo": "RMS callback information",
  "lang.ark.fed.to": "To",
  "lang.ark.fed.dmpTaskUnComplete": "There are unfinished device interaction tasks",
  "lang.ark.mechanical.arm.pick.up": "Removed by mechanical arms",
  "lang.authManage.web.common.realName": "Name",
  "lang.ark.fed.enabledScanValidate": "Scan the code to verify the container",
  "lang.ark.robotDeviceComponent.robotId": "Robot ID",
  "robot.task.already.send": "Already issued to the robot and cannot be done manually!",
  "lang.ark.fed.specifications": "Specification",
  "lang.ark.fed.throughEscSwitchLineDrawingModeDoublePointSwitchSinglePoint": "Switch the linedraw mode through ESC. If you want to switch from double points to single points, you need to click twice 1. Single point mode (A-B-C sequential click) 2. Double point mode (A-B B-C mode)",
  "lang.ark.fed.triggerWorkflow": "Trigger workflow",
  "lang.ark.fed.screen.equipmentAssociatedInfo.columnEquipName": "Device name",
  "lang.ark.fed.robotWillArriveText": "Robot: {0}shortest distance",
  "lang.ark.deliverOrder.defaultOrder": "Distribution order of production line",
  "lang.ark.workflowgroup.triggerpoint.begin": "Start",
  "lang.mwms.homePage": "Home page",
  "lang.ark.fed.trafficLockMsg": '"After the area is manually locked, it needs to be manually released again before a robot applies for entry. Continue to lock it?"',
  "lang.ark.fed.taskType": "Task type",
  "lang.mwms.monitorRobotMsg10001": "The base values of the two calibrations of gyro are very different",
  "lang.mwms.monitorRobotMsg10000": "Gyro temperature changes too much.",
  "lang.ark.warehouseTask.cuttingTaskOverTime": "Warning of station discharging duration given for more than {0} minutes",
  "lang.ark.interface.interfaceDesc.numbers": "Number",
  "lang.ark.apiCommonCode.locationToNotEmpty": "Destination location cannot be empty",
  "lang.ark.fed.recoverRefresh": "Resume refresh",
  "lang.ark.waitForAssign": "Waiting for allocation",
  "lang.ark.fed.revoke": "Withdraw",
  "lang.ark.fed.arrivedTime": "Duration of stay at target station",
  "lang.ark.warehouse.manualOperateTypeIn": "Manual stock-in",
  "lang.ark.workflow.chooseStrategy.exception": "Completed abnormally",
  "lang.ark.fed.scrollNumber": "Reel number",
  "lang.ark.fed.sureClickConfirm": "Confirm to save?",
  "lang.ark.fed.uploadImage": "Upload picture",
  "lang.ark.fed.defaultName": "Default name",
  "lang.ark.fed.liveNotSaveParamConfig": "Unsaved parameter configuration exists, please save first!",
  "lang.mwms.fed.wareMutex": "Mutually exclusive configuration of materials",
  "lang.ark.fed.prepareToExecuteTheWorkflowAutomatically": "Prepare the automatic execution workflow",
  "lang.ark.fed.operationSuccessfully": "Successful operation",
  "lang.ark.fed.noSelectGoods": "No selected material",
  "lang.ark.fed.screen.flowNodeConfig.pleaseInputCellCode": "Enter point ID",
  "lang.ark.warehouse.goodsManagement": "Stock bin management",
  "lang.ark.fed.contents.flowConfig.recycleType.autoRecoveryRule.columns.priority": "priority",
  "lang.mwms.fed.outWarehouseCollect": "Stock out summary",
  "lang.ark.fed.theSpaceIsTaken": "The stock bin has been occupied",
  "lang.ark.fed.stationNumber": "Number of stations",
  "lang.ark.fed.notMergeNode": "No nodes to connect!",
  "lang.authManage.web.common.delete": "Delete",
  "lang.ark.fed.reasonsForFailure": "Reason for failure",
  "lang.ark.fed.status": "Status",
  "lang.ark.fed.arriveRobotActions": "The robot moves upon arrival",
  "lang.ark.workflow.arrive.action.goTurnOfAngle.pos360": "360°",
  "lang.ark.fed.leavingSoon": "About to leave",
  "lang.ark.fed.tray": "Pallet ",
  "lang.ark.workflowConfig.cellFunctions.notAllowTurn": "No turn",
  "lang.ark.apiContainerCode.containerAlreadyRemoved": "The container {} has been removed",
  "lang.ark.fed.config": "Configuration",
  "lang.ark.warehouse.goodsTaskHasHang": '"The process is canceled due to exception, and the document is suspended and to be processed"',
  "lang.gles.workflow.abuttingJoint": "External docking position",
  "lang.auth.UserAPI.item0306": "Enable",
  "lang.ark.fed.workflowGroupConfiguration": "Workflow group editing",
  "lang.auth.UserAPI.item0305": "Disable",
  "lang.ark.workflow.initiateNextTaskSimple": "Next step",
  "lang.ark.button.node.type.startPoint": "Actuation point",
  "lang.ark.workflow.wareHouseAutoCreate": "Automatic creation",
  "lang.ark.workflow.workflowTaskHasCompleted": "The workflow task has been completed",
  "lang.ark.apiContainerCode.angleValueIllegal": "The value range of container angle is -180.0 to 180.0",
  "lang.ark.trafficControl.artificialControlFunction": "Manual control area function",
  "lang.ark.action.interface.conditionLocationTo": "locationTo",
  "lang.ark.fed.robotInstruct": "Robot command",
  "lang.ark.warehouse.setPointNumber": "Node number",
  "lang.ark.workflow.wareHouseConfigMethod": "Configuration method",
  "lang.ark.fed.addParam": "Add parameters",
  "lang.ark.fed.yes.generateCode": "Yes-the system generated container numbers",
  "lang.ark.fed.component.workflow.label.hoisterFlow": "Lifter workflow",
  "lang.ark.apiStationCode.stationCodeNotBlank": "stationCode cannot be empty",
  "lang.ark.fed.targetProductionLine": "Demand production line",
  "lang.ark.action.interface.extraParam": "extraParam",
  "lang.ark.fed.endDate": "End date",
  "lang.ark.fed.autoDeliver": "Active distribution",
  "lang.authManage.web.others.project": "Item ID",
  "lang.ark.workflowConfig.status.error": "Abnormal",
  "lang.ark.fed.deleteAll": "Delete all",
  "lang.ark.record.dmp.sendTask": "Send dmp task",
  "lang.ark.workflow.arrive.action.goTurnOfAngle.angle": "Rotation angle",
  "lang.ark.robot.robotModelNotExist": "The corresponding co-robot type does not exist.",
  "lang.ark.fed.menu.robotParamConfig": "Robot parameters",
  "lang.ark.fed.pleaseSelectCycle": "Please select a frequency",
  "lang.ark.fed.pleaseAtLeastOneCellInfo": "Please add at least one point information",
  "lang.ark.fed.areaCode": "Area code",
  "lang.ark.workflow.arrive.action.goTurnOfAngle.neg270": "-270°",
  "lang.ark.fed.menu.applyMaintenance": "Application maintenance",
  "lang.ark.fed.senior": "Advanced",
  "lang.ark.trafficControl.enterPattern.serialPass": "Continuous pass",
  "lang.mwms.fed.asn": "Warehousing entry",
  "lang.ark.warehouse.theMatraialHaveAnyWave": "There are multiple wave pickings to be distributed at the material point",
  "lang.ark.fed.selectRobotsThatNeedToBeDisplayedAndManipulated": "Select the robot you want to display and operate",
  "lang.auth.UserAPI.item0302": "This user does not exist, please contact the administrator!",
  "lang.auth.UserAPI.item0301": "The work card number has been bound to user {0}",
  "lang.ark.fed.createNewExcute": "Add conditions",
  "lang.ark.fed.workstation": "Workstation ",
  "lang.ark.workflow.arrive.action.goTurnOfSide": "Component rotation by side",
  "lang.ark.fed.screen.area.grouping": "Grouping",
  "lang.ark.fed.beforeExecuteSaveListLog": '"Before each execution, keep the last {0} logs"',
  "lang.ark.fed.dealSuccessCode": "Workflow the logic after success",
  "lang.ark.fed.systemControl": "System control",
  "lang.ark.fed.pleaseSelectTheLastNode": "Please select the last node",
  "lang.authManage.web.common.noPermission": "Sorry, you do not have auth of {0} system!",
  "lang.ark.fed.screen.LoginLog.userName": "username",
  "lang.ark.fed.padFlowStartAgain": "PAD-Process restart",
  "lang.ark.api.goturn.isForbidden": "Container rotation cannot be executed for the task in the current status.",
  "lang.ark.fed.childFlowOnlySaveType": "Sub-processes are only allowed to connect to sub-processes!",
  "lang.ark.fed.screen.hybridRobot.bindReason": "Binding reason: The stock bin must be bound with the docking points because the upstream can only return the stock bin information instead of the information of points that we know. When the upstream returns the stock bin information, GMS can trace the corresponding point to ensure normal docking with the machine.",
  "lang.ark.fed.currentNode": "Current node",
  "lang.ark.fed.monitoringObjects": "Monitoring object",
  "lang.ark.fed.theTotalNumberOfTrays": "Total number of stock bins",
  "lang.ark.fed.isOpenAllQueue": "Do you want to enable queuing for all",
  "lang.ark.base.license.licenseExpiredWarningMsg": "Certificate expired. You can continue to use the system",
  "lang.ark.fed.pullLoadMore": "Pull up to load more",
  "lang.ark.fed.locked": "Locked",
  "lang.ark.fed.menu.workflowTrigger": "Trigger Configuration",
  "lang.ark.button.node.type.middlePoint": "Intermediate point",
  "lang.ark.fed.switchingStandPoint": "Switching to another point...",
  "lang.ark.paramNameExist": "Names cannot repeat",
  "lang.ark.trafficControl.areaLockStatus": "Area status",
  "lang.ark.fed.screen.flowNodeConfig.offsetParam": "Parameter value",
  "lang.ark.fed.nodePoint": "Node point",
  "lang.ark.fed.scrollCode": "Reel number",
  "lang.ark.apiRobotTaskCode.waitPointTaskContinueFailed": "Failed to continue to execute the task at the waiting point",
  "lang.authManage.fed.instanceId": "Item instance",
  "lang.authManage.fed.remainingDays": "Remaining days",
  "lang.ark.agv.instructionRule1": "Machine offset beyond ±50 mm (exclusive)",
  "lang.authManage.web.others.license": "Certificate",
  "lang.ark.fed.batchNumber": "Batch number",
  "lang.ark.fed.CuttingFinish": "Material discharged",
  "lang.ark.workflow.arriveOperation": "The robot moves upon arrival",
  "lang.ark.fed.resetAll": "Reset all",
  "lang.ark.workflowConfig.status.designing": "In design",
  "lang.ark.fed.screen.equipmentAssociatedInfo.columnUpdater": "Edited by",
  "lang.ark.fed.pleaseEnterAPositiveNumber": "Please enter a non-negative number",
  "lang.ark.fed.menu.flowTemplate": "Workflow template",
  "lang.gles.receipt.upAndDownMaterialExternalOrder": "External feeding and discharging order",
  "lang.ark.fed.orderWaveSucess": "Successful wave grouping",
  "lang.ark.fed.contents.flowConfig.recycleType.auto": "Automatically return to the preset location.",
  "lang.ark.fed.material": "Feeding point",
  "lang.gles.material": "Material management",
  "lang.ark.fed.waitSend": "To be distributed",
  "lang.ark.fed.batteryTemperature": "Battery temperature",
  "lang.ark.fed.orderCollection": "Distribution by receiving order",
  "lang.ark.plugin.pluginType.fetchContainer.way.full": "Fetch full container",
  "lang.ark.workflowConfig.status.released": "Published",
  "lang.ark.fed.screen.hybridRobot.pleaseInputIntOffset": "Please enter an offset value.",
  "lang.ark.workflowTriggerType.workflow": "Trigger workflow",
  "lang.ark.fed.screen.equipmentAssociatedInfo.labelEquipName": "Device name",
  "lang.ark.fed.everyOnceHappen": "every? days",
  "lang.ark.fed.sendGoodsTitle": "Delivery of materials: enabled. The workstation supports manually selecting a material to initiate a delivery task or receiving a material calling task for distribution.",
  "UserAPI.item0100": "The user is disabled. Please contact the administrator to enable the user again, or log in with another user",
  "lang.ark.action.interface.instanceId": "instanceId",
  "lang.ark.workflow.action.commandExecutePhase.nextStart": "Initiate the next task",
  "lang.ark.fed.secondClassification": "Second class classification",
  "lang.ark.element.shelf.point.belong.to.area": "This shelf point belongs to an area",
  "lang.ark.fed.enable": "Enable",
  "lang.ark.workflowTriggerStatus.create": "Create",
  "lang.ark.notManualTrigger": "The destination location is not triggered manually and this operation is not supported!",
  "lang.ark.fed.theSameLevelAlterMessageCanNotBeRepeat": "Early warning notifications of the same level and type cannot be repeated",
  "lang.ark.fed.cellIdleTrigger": "Node idle triggering",
  "lang.ark.fed.addGoods": "Add material",
  "lang.ark.fed.lineDrawingMode": "Line drawing mode",
  "lang.ark.warehouse.goodsEditError": "Material editing error",
  "lang.ark.fed.goodsComplete": "Distributed",
  "lang.ark.interface.requestDate": "Request time",
  "lang.ark.fed.robotConfigurationEditingPage": "Robot configuration editing page",
  "lang.ark.workflow.waitRelease": "Waiting for release",
  "lang.ark.workflowTrigger.logType.all": "All",
  "lang.ark.fed.taskList": "Task list",
  "lang.gles.logisticsConfig.workPosition": "Station configuration",
  "lang.ark.fed.uploadFailed": "Upload failed",
  "lang.ark.workflowConfig.cellFunctions.blockedCell": "BLOCKED_CELL",
  "lang.ark.fed.screen.workflowInfo.requestParam": "Request",
  "lang.ark.fed.uninstall": "Uninstall",
  "lang.ark.fed.signOut": "Exit",
  "lang.ark.fed.common.validator.required": "Required",
  "lang.ark.interface.containerAmountNumberTip": '"Add up to 5,000 each time, and only numbers are supported."',
  "lang.ark.fed.isForceDelete": "Risky! Do you want to force delete? Please be careful!",
  "lang.mwms.fed.reportManagement": "Statement management",
  "lang.ark.fed.sendGoods": "Delivery of materials",
  "lang.ark.fed.warehouse": "Warehouse Management",
  "lang.ark.fed.calledGoods": "Material called",
  "lang.ark.workflow.positionIsOccupied": "The location is occupied",
  "lang.ark.workflow.rollOver": "Flip",
  "lang.ark.fed.theRobotIsNotHere": "The robot is not here",
  "lang.ark.workflowConfig.cellFunctions.omniDirCell": "OMNI_DIR_CELL",
  "lang.ark.fed.liveNotSaveExternalInteraction": "Unsaved external interaction configuration exists, please save first!",
  "lang.ark.record.nextTask": "Next task",
  "lang.ark.fed.addNewRobotInstruct": "Add robot command",
  "lang.ark.fed.extraParam1": "extraParam1",
  "lang.ark.workflowConfig.cellFunctions.elevatorCell": "ELEVATOR_CELL",
  "lang.ark.fed.extraParam9": "extraParam9",
  "lang.ark.fed.extraParam8": "extraParam8",
  "lang.ark.fed.extraParam7": "extraParam7",
  "lang.ark.workflow.workflowTaskSendRecover": "Workflow is operating and cannot be recovered",
  "lang.ark.fed.extraParam6": "extraParam6",
  "lang.ark.fed.extraParam5": "extraParam5",
  "lang.ark.fed.extraParam4": "extraParam4",
  "lang.ark.fed.extraParam3": "extraParam3",
  "lang.ark.fed.extraParam2": "extraParam2",
  "lang.authManage.web.common.logout": "Log out",
  "lang.ark.fed.productLineDetail": "Production line details",
  "lang.ark.fed.containerInfo": "Container information",
  "lang.ark.fed.screen.equipmentAssociatedInfo.dialogDetail.titleNew": "Add device association information",
  "lang.ark.warehouse.noMatchCellCode": "No target station of material matched",
  "lang.ark.workflow.template.type.dynamiNodeUnit": "Dynamic cell task",
  "lang.gles.workflow.MonitorManagement": "Monitoring management",
  "lang.ark.base.license.sysParamForCustomerIdIsNull": "Client ID verified by the certificate is null",
  "lang.ark.fed.executeSuccessfully": "Successful execution of delivery",
  "lang.slam.api.menu.item0001": "PC",
  "lang.ark.fed.containerTypeName": "Container type name",
  "lang.ark.fed.activeDistributionTitle": "Active distribution: the workstation can only initiate the processes that start from this workstation",
  "lang.ark.fed.notParams": "Parameters not configured yet!",
  "lang.mwms.fed.qrCodeAnalysis": "QR code parsing strategy",
  "lang.ark.fed.time": "Time",
  "lang.ark.base.license.licensePreAlertMsg": "The certificate will expire after {0} days!",
  "lang.ark.fed.setStartPoint": "Set as start point",
  "lang.ark.fed.wave": "Wave picking",
  "lang.ark.workflow.task.status.deviceExecuting": "External device in transportation",
  "lang.mwms.fed.category": "Cargo category",
  "lang.ark.workflow.containerEnterMapDest": "End point of container entry",
  "lang.ark.fed.processOperation": "Workflow operation",
  "lang.ark.fed.screen.flowNodeConfig.judgingByPath": "Path",
  "lang.ark.fed.orderDetail": "Document details",
  "lang.ark.fed.login": "Log in",
  "lang.ark.fed.screen.equipmentAssociatedInfo.dialogDetail.titleEdit": "Edit device association information",
  "lang.ark.fed.timeOfReceipt": "Material requisition duration",
  "geekplus.moving.uic.elTableWrapperVue2.column.action": "Operation",
  "lang.ark.fed.device.ruleMatch": "Match according to point device information",
  "lang.ark.fed.instructList": "Command list",
  "lang.ark.waveGenerateScope.materialDocument": "By preparation point",
  "lang.ark.workflowConfig.cellFunctions.stayBlocking": "Stay blocking",
  "lang.ark.interface.apiStationQueueStop": "Disable queuing for fetching",
  "lang.ark.fed.startOrEndNodeNotExist": "Operation failed. No start node or end node!",
  "lang.ark.fed.containerEntryWay": "Entry method",
  "lang.ark.auth.otherUserHaveLoginThisStation": "A user {0} has logged in at No. {0} workstation!",
  "lang.ark.fed.cancelEndPoint": "Cancel end point",
  "lang.gles.workflow.workReceipt": "Operation order",
  "lang.ark.fed.screen.flowNodeConfig.assignOffset": "Send a robot point offset value",
  "lang.gles.strategy.shelf": "Putaway strategy",
  "lang.ark.fed.release": "Release",
  "lang.ark.fed.menu.nodeMapRelation": "Sensor triggering task",
  "lang.ark.fed.whatAreYouGoingToDoWithTheCurrentAreaOrShelf": "For the current area or shelf, what workflowing are you going to perform?",
  "lang.ark.fed.lengthLang": "Length",
  "lang.ark.fed.putDownOrTurnSide": '"The component action {0} already includes?? lowering? , and it is not allowed to configure the rotation side!"',
  "lang.ark.workflow.condition.notIn": "Not contain",
  "lang.ark.fed.twoDimensionalCodeMode": "QR code mode",
  "lang.ark.fed.sourceProductionLineOrWorkshop": "Source production line/workshop",
  "lang.ark.fed.displayOrder": "Display sequence of points in the group",
  "lang.ark.waveTriggerCondition.wireless": "Physical button",
  "lang.ark.fed.notSameStartAndEnd": "The position of start point and end point cannot be the same",
  "lang.ark.workflow.enterMapDest": "End point of entry",
  "lang.ark.fed.buttonCommand": "Button command",
  "lang.ark.fed.dataTimeRange": "Date range",
  "lang.ark.fed.areYouSureYouWantToDeleteThisWorkflow": "Are you sure to delete this workflow?",
  "lang.ark.workflow.workflowTaskNotExists": "The workflow task does not exist",
  "lang.ark.workflow.noPause":"The last subtask cannot be paused",
  "lang.mwms.rf.outboundWithoutTask": "Stock out without outbound delivery order",
  "lang.ark.workflow.paramValueCode.taskId": "taskId",
  "lang.ark.workflow.fetchStuff": "Fetch of cargo",
  "lang.ark.fed.hostSeriNum": "External code",
  "lang.ark.fed.screen.hybridRobot.hybridRobotType": "Co-robot type",
  "lang.ark.fed.StationNum": "Material requisition sheet number",
  "lang.ark.action.interface.exceptionResponse": "Interval (in seconds) between two adjacent re-try attempts",
  "lang.ark.fed.goodsCoding": "Stock bin code",
  "lang.mwms.fed.workStationEfficiencyCharts": "Station efficiency report",
  "lang.ark.fed.light": "Lamplight",
  "lang.ark.fed.operatingTime": "Operation time",
  "lang.mwms.fed.shelfRuleManagement": "Putaway rule management",
  "lang.ark.dynamicTemplate.dynamicControlLogic": "Control logic",
  "lang.ark.fed.taskDashboardColumn": "Display column of task display board",
  "lang.ark.robotUsage.sorting": "Sorting",
  "lang.ark.fed.disCharingMount": "Material discharging quantity",
  "lang.ark.workflow.autoOperationFailed": "Automatic execution failed",
  "lang.ark.fed.areasThatCanBeSpeciallyControlledForRobotMovements": "An area that allows special control of a robot  movements",
  "lang.ark.fed.floors": "Number of layers",
  "lang.ark.task.log.export.title.workflow.name": "Workflow name",
  "lang.ark.workflowAction.noDefault": "Non-default",
  "lang.ark.fed.redraw": "Repaint",
  "lang.ark.fed.recoverSuccess": "Successfully resumed",
  "lang.ark.fed.triggerName": "Trigger name",
  "lang.ark.fed.dragPictureFileHereOr": "Drag the image file here, or",
  "lang.ark.fed.menu.nodeConfig": "Interaction waiting time",
  "lang.ark.apiContainerCode.containerCodeGenerateFail": "Failed to generate container number",
  "lang.ark.workflow.lackRobotQueue": "There is a task queue when the robot is insufficient",
  "lang.ark.fed.selectedGoods": "Selected material",
  "lang.ark.apiStationCode.stationQueueAlreadyEnable": '"Queuing control at workstation is already enabled, so it cannot be enabled again"',
  "lang.ark.workflow.workflowTask": "Workflow task",
  "lang.ark.fed.copyError": "Copy failed!",
  "lang.ark.fed.logicalNodeInfo": "Logic point information",
  "lang.ark.fed.grabShelvesFromWorkstations": "Grab the shelf from the workstation",
  "lang.ark.workflow.workflowConfigNotExists": "The workflow configuration does not exist",
  "lang.ark.warehouse.hasSameStationNumber": "The process number already exists",
  "lang.ark.fed.backPre": "Return",
  "lang.ark.fed.deleteNode": "Delete node",
  "lang.ark.fed.receivingWorkstation": "Material requisition workstation",
  "lang.ark.fed.creationTime": "Creation time",
  "lang.ark.fed.goodsIsNotExists": "The material information does not exist",
  "lang.ark.fed.pleaseSelectAtLeastOneProcess": "Please select at least one workflow",
  "lang.ark.fed.pleaseSelectGoods": "Please select the material.",
  "lang.ark.common.invalidParameter": "Illegal parameter",
  "lang.mwms.fed.pickWorkCreate": "Picking is generated",
  "lang.ark.robot.go.rest": "To a fixed position",
  "lang.mwms.fed.codeRule": "Bar code rules",
  "lang.ark.fed.username": "Username",
  "lang.ark.workflowConfig.cellFunctions.recycleid": "Recycling point",
  "lang.ark.fed.menu.siteMonitoring": "Site monitoring",
  "lang.ark.fed.areaStop": "Area emergency stop",
  "lang.ark.workflowgroup.triggerpoint.end": "End",
  "lang.ark.warehouse.noMatchToloc": "No production line matched",
  "lang.ark.interface.apiCallback": "Task callback",
  "lang.ark.fed.cancelStartPoint": "Cancel start point",
  "lang.ark.fed.flowBelongClass": "Process category",
  "lang.ark.hitStrategy.queue": "First-in First-out",
  "lang.ark.existsDefaultNodeAction": "The default configuration already exists!",
  "lang.ark.fed.wall": "Wall",
  "lang.ark.fed.systemCustomization": "System customization",
  "lang.ark.fed.originalLocation": "Original location",
  "lang.ark.workflow.workflowTaskSendDuplicate": "Delivery operation repeated",
  "lang.mwms.fed.internalManager": "In-warehouse management",
  "lang.ark.fed.areaTypeExistence": '"The current area type already exists, and only one item of the same type can be added!"',
  "lang.ark.fed.priorityGoDown": "Priority decline",
  "lang.ark.workflow.noAvailableEndNode": "The available target node cannot be selected",
  "lang.ark.fed.pleaseSelectAConditionValueCollectionFile": "Please select condition value collection file",
  "lang.ark.task.log.export.title.startNode.name": "Starting point name",
  "lang.ark.fed.demandFactory": "Demand factory",
  "lang.ark.lift.up": "Jacking up",
  "lang.ark.fed.sendMaterialForStations": "For which stations the feeding point can prepare materials",
  "lang.ark.trafficControl.enterStrategy.byOccupancy": "First occupy first pass",
  "lang.mwms.fed.mergeInventory": "Aggregate inventory",
  "lang.ark.fed.middlePoint": "Intermediate point",
  "lang.ark.fed.workstationManage": "Workstation management ",
  "lang.ark.fed.demandStation": "Demand station",
  "lang.ark.fed.collectionAnd": "Collection (AND)",
  "lang.ark.fed.execution": "Reach operation",
  "lang.ark.loadCarrier.loadCarrierReqParamsErr": "Container request parameter encountered error.",
  "lang.gles.workPositionMaterialConfig": "Material configuration at station",
  "lang.ark.fed.destCode": "Node code",
  "lang.ark.fed.lastDay": "The last day",
  "lang.mwms.fed.inManage": "Stock in management",
  "lang.ark.fed.menu.parameterConfigOuter": "Scheduling setting",
  "lang.ark.fed.isSureDelFlowList": "Are you sure to delete the workflow?",
  "lang.ark.fed.batchSave": "Batch save",
  "lang.ark.containerType.exists": "The container classification name already exists",
  "lang.ark.fed.deleteTaskProcessConfirmText": "Confirm to delete all tasks under this process instance?",
  "lang.ark.apiNodeActionCode.commonNodeActionNotExists": "Universal node interaction configuration does not exist",
  "lang.ark.effectiveTimeCannotLessThanEffectiveTime": "Expiration date cannot be earlier than effective date",
  "lang.ark.fed.moveBins": "The shelf is removed and the feeding is canceled. Please receive again",
  "lang.ark.interface.moving": "Point-to-point moving",
  "lang.ark.fed.belongNodeAlreadyExistInOtherAreaConfig": "The selected node list contains nodes that exist in other area config.",
  "lang.mwms.fed.robotCharts": "Robot report",
  "lang.ark.workflow.authUserHasUsed": "The following users have set workstation permissions",
  "lang.ark.api.template.startNodeNotMatch": "The specified start point [{}] does not match the template start point [{}]",
  "lang.ark.workflow.queue.noAvailableRobot": "No robots available",
  "lang.ark.interface.resentFail": "Invocation failed",
  "lang.ark.fed.singleGeneration": "Single generation",
  "lang.ark.fed.screen.equipmentAssociatedInfo.dialogDetail.labelMaxTaskCont.tooltip": "Tasks exceeding the maximum number of tasks that a lifter can handle in the same period still can be sent in the sequence of a queue. The lifer will be given a new task after it complete the previous one, until it completes all tasks.",
  "lang.ark.fed.emptyShelves": "Empty shelf",
  "lang.ark.fed.isOpenCamera": "Detect whether the camera is enabled...",
  "lang.ark.fed.containerPosition": "Container location",
  "lang.ark.workflow.noLongerPause": "The current task is no longer in the?? pause?? status and cannot be resumed. Please refresh",
  "lang.ark.container,syncContainerErr": "Failed to synchronize container information",
  "lang.ark.logType.trafficControlTask": "Traffic control task",
  "lang.ark.fed.triggerMode": "Triggering mode",
  "lang.ark.fed.interactiveActionName": "Interaction action",
  "lang.ark.fed.leftAndRightObstacleAvoidance": "Left and right obstacle avoidance",
  "lang.ark.fed.pleaseEnterARuleName": "Please enter the rule name",
  "lang.ark.fed.workflowTrigger": "Task trigger",
  "lang.ark.fed.obstacleAvoidanceRange": "Scope of obstacle avoidance",
  "lang.ark.fed.area": "Area",
  "lang.ark.fed.oneByOne": "One-by-one pass",
  "lang.ark.warehouse.getTaskFailed": "The material calling task has been received by another preparation point",
  "lang.ark.fed.cancelCall": "Cancel material calling",
  "lang.ark.fed.workFlowType": "Process category",
  "lang.ark.workflow.area.releaseTime": "Automatic release time",
  "lang.ark.fed.menu.areaManage": "Area",
  "lang.ark.warehouse.Feeding": "Feeding",
  "lang.ark.fed.trafficArea": "Control area",
  "lang.ark.fed.interactiveMode": "Interaction mode",
  "lang.ark.fed.executing": "Executing",
  "lang.ark.fed.received": "Received",
  "lang.ark.fed.siteName": "Site name",
  "lang.ark.fed.menu.workflowConfiguration": "Workflow",
  "lang.ark.fed.pleaseAddAreaFun": "Please add at least one function type",
  "lang.ark.workflow.containerNotExists": "Container does not exist",
  "lang.ark.fed.passbyPoint": "Thru point",
  "lang.ark.fed.theFirstNodeCanNotBeDeleted": "The first node cannot be deleted!",
  "lang.ark.apiNodeActionCode.nodeActionNotExists": "Node interaction configuration does not exist",
  "lang.ark.workflow.arrive.action.goTurnOfAngle.pos90": "90°",
  "lang.mwms.fed.kpi": "Staff efficiency management",
  "lang.ark.fed.nodeConfig": "Node configuration",
  "lang.mwms.fed.outWarehouse": "Stock out daily report",
  "lang.ark.action.interface.conditionExtraParam": "extraParam",
  "lang.ark.fed.endType": "End point type",
  "lang.ark.fed.shelfSwap": "Shelf exchange",
  "lang.ark.fed.selectProductLineOrCellcode": "Select production line or station",
  "lang.ark.fed.waveStatus": "Wave picking status",
  "lang.ark.fed.menu.callbackAddressConfig": "Callback setting",
  "lang.ark.fed.drawANewMapToCoverTheCurrentMap": "Draw a new map that covers the current map",
  "lang.ark.workflow.recycleAreaNoConfigAction": "Cancellation failed. No recycling area interaction configured for the process!",
  "lang.ark.workflow.exceptionHandler.cache": "Temporary storage area is queuing",
  "lang.ark.fed.batchNo": "Batch number",
  "lang.ark.warehouse.binNoShelf": '"The shelf of the material does not exist, and the operation cannot be executed"',
  "lang.ark.api.workflowTask.notExistOrCompleted": "The task does not exist or has ended and the operation cannot be continued",
  "lang.ark.container.containerCodeTooLong": "Length of type code cannot exceed 64",
  "lang.ark.fed.pleaseSelectALanguage": "Please select languages ",
  "lang.ark.fed.cageTrolley": "Cage trolley",
  "lang.ark.fed.common.btn.cancel": "Cancel",
  "lang.ark.api.workflow.locationToIsNull": "The target point code cannot be empty",
  "lang.auth.UserAPI.item2001": "Incorrect expiry date",
  "lang.ark.common.exportExcelFile": "Export template",
  "lang.auth.UserAPI.item2000": "Account will expire in {0} days",
  "lang.ark.workflowTriggerType.clear.log": "Clear log",
  "lang.ark.fed.unknowError": "Unknown error",
  "lang.ark.fed.workflowName": "Workflow name",
  "lang.ark.fed.wednesday": "Wednesday",
  "lang.ark.fed.restartAngle": "Restart angle",
  "lang.ark.fed.mediumSpeed": "Intermediate speed",
  "lang.ark.workflow.arrive.action.goTurnOfAngle.neg180": "-180°",
  "lang.mwms.fed.inWarehouseCollect": "Stock in summary",
  "lang.ark.fed.modifier": "Modified by:",
  "lang.ark.fed.layer": "Layer",
  "lang.ark.apiCommonCode.locationToNotMatchDest": '"locationTo:{0} failed to match the corresponding position by any point, workstation or area in the system"',
  "lang.ark.fed.mainProcessInstance": "Main process instance",
  "lang.mwms.fed.containerInfo": "Container management",
  "lang.ark.interface.interfaceDesc.required": "Required or not",
  "lang.ark.fed.taskCycle": "Task cycle",
  "lang.ark.fed.noWorkFlowData": "No process information",
  "lang.ark.fed.help": "Help",
  "lang.ark.fed.screen.workflowInfo.workflowTaskId": "First Task Id",
  "lang.mwms.fed.kpiCharts": "Staff efficiency report",
  "lang.ark.fed.taskDetail": "Task details",
  "lang.ark.shelfTypeNotExist": "Container type does not exist",
  "lang.ark.apiStationCode.stationNotSupportLogin": "The workstation cannot be logged in to",
  "lang.ark.fed.manualTrigger": "Manual trigger",
  "lang.ark.trafficControl.trafficLightRange": "Traffic control area",
  "lang.ark.fed.dataChangeRefreshPage": "Data changes. Please refresh the page~",
  "lang.ark.fed.logining": "Logging in...",
  "lang.ark.fed.common.btn.confirm": "OK",
  "lang.auth.UserAPI.item0203": "Exception in {0} user",
  "lang.ark.exceptionHandle": "No operation allowed. The destination location has DMP configurations.",
  "lang.auth.UserAPI.item0202": "No user selected",
  "lang.ark.fed.materialName": "Material name",
  "lang.ark.fed.menu.configManage": "Configuration management",
  "lang.auth.UserAPI.item0201": "User status is not modified",
  "lang.ark.fed.screen.flowNodeConfig.checkMsg.robot": "Saving failed. Please select robot state.",
  "lang.auth.UserAPI.item0200": "Exception in modifying the user",
  "lang.ark.fed.actionsErrorCommandDetailError": "Failed to save the interaction configuration and command list",
  "lang.authManage.web.others.noAllow": "Sorry, you have no access to this page",
  "lang.ark.fed.groupDisplayOrder": "Group sequence",
  "lang.ark.workflow.putDown": "Lay down",
  "lang.ark.workflowConfig.segmentWithoutRule": "No rule is configured for the connecting line. Please check",
  "lang.ark.fed.taskIsSavedSuccessfully": "Task saved successfully",
  "lang.ark.fed.repeatWeek": "Week",
  "lang.ark.fed.shelfType": "Shelf type",
  "lang.ark.fed.pickUpARack": "Shelf",
  "lang.ark.apiContainerCode.codeAndNumberAreNull": "containerCodes and numberOfContainer cannot be empty at the same time",
  "lang.ark.fed.menu.robotSoftwareManagement": "Software Version",
  "lang.mwms.fed.systemLack": "Handling of system shortage report exception",
  "lang.ark.warehouse.uploadFileFormatError": "Uploaded file format error",
  "lang.ark.fed.copy": "Copy",
  "lang.ark.apiContainerCode.containerCategoryMustUnique": '"Container type in the system is not unique, and containerCategory must be specified"',
  "lang.ark.button.operation.command.end": "End",
  "lang.ark.fed.binFree": "Available",
  "lang.ark.fed.operationFailed": "Operation failed",
  "lang.ark.fed.closeotherTabs": "Close others",
  "lang.ark.fed.batchEditing": "Bulk edition",
  "lang.ark.fed.shelfAttribute.BALANCE": "BALANCE",
  "lang.ark.fed.wfTaskInfo": "Material requisition sheet",
  "lang.ark.fed.modifier2": "Modified by",
  "lang.ark.loadCarrier.loadCarrierModelCodeIsEmpty": "Container model code cannot be empty.",
  "lang.ark.operation.workflow.adjustPriority": "Adjust the task priority to {0}",
  "lang.ark.fed.forceDeleteSuccess": "Force deleted successfully!",
  "lang.ark.fed.listLogo": "log(s)",
  "lang.ark.fed.send": "Deliver",
  "lang.ark.fed.menu.containerManagement": "Container",
  "lang.ark.fed.screen.equipmentAssociatedInfo.btnAddInfo": "Add device association information",
  "lang.ark.fed.disable": "Disabled",
  "lang.ark.fed.container.confirmLeave": "Confirm container exit",
  "lang.ark.fed.download": "Download",
  "lang.ark.fed.idleCharging": "Idle charging",
  "lang.ark.fed.startType": "Start point type",
  "lang.ark.fed.date": "Date",
  "lang.ark.fed.autoUpdatePage": "Automatic update and page turning",
  "lang.ark.groupStrategy.closestDistance": "Closest distance",
  "lang.ark.groupStrategy.sequentialSelection": "Sequential selection",
  "lang.ark.workflow.removeShelfFailed": "Failed to delete container",
  "lang.ark.fed.waveTasks": "Number of wave picking tasks",
  "lang.ark.fed.queuingAtTheWorkstation": "Queue up at the workstation",
  "lang.ark.workflow.nodeStatus": "Current point state",
  "lang.ark.pda.function.container.entry": "Container entry",
  "lang.ark.workflow.template.validate.templateError": "Error in dynamic template configuration",
  "lang.ark.fed.menu.containerType": "Container type",
  "lang.ark.fed.screen.flowNodeConfig.judgingByRobotStatus": "Robot state",
  "lang.ark.fed.drivingRestrictions": "Driving restrictions:",
  "lang.ark.fed.getGoodsTitle": "Material calling: enabled. The workstation supports selecting a material to initiate a material calling task.",
  "lang.authManage.web.auth.roler": "Role",
  "lang.ark.fed.multiNodeCustom": "Custom point-to-multipoint task",
  "lang.ark.fed.suggestedTreatment": "Recommended treatment method",
  "lang.ark.record.robotCallback.move": "The robot starts to move",
  "lang.ark.fed.normalWork": "Work normally",
  "lang.ark.shelfNameExist": "Container type name already exists",
  "lang.ark.fed.robotTaskFlow": "Robot task flow",
  "lang.ark.fed.editor": "Editor",
  "lang.ark.workflow.paramValueCode.locationFromFloor": "locationFromFloor",
  "lang.mwms.fed.sysMonitor": "System monitoring",
  "lang.ark.fed.component": "Component",
  "lang.mwms.monitorRobotMsg.scanabnormal.notMatch": "The recognized container No. is inconsistent with the issued container No.",
  "lang.ark.workflow.autoSkip": "Automatically skip after completion",
  "lang.ark.workflow.exceptionHandler.taskQueue": "Task queue",
  "lang.ark.fed.rackManagement": "Container management",
  "lang.ark.workflow.cycleType": "Cycle type",
  "lang.ark.loadCarrier.loadCarrierModelTypeIsEmpty": "Container model form cannot be empty.",
  "lang.ark.fed.screen.workflowInfo.commandTaskId": "Interface Task Id",
  "lang.ark.warehouse.deliveryMaterial": "Material distribution",
  "lang.ark.element.stop.point.belong.to.workstation": "The point belongs to workstation",
  "lang.auth.PwdMgrAPI.item0009": "Password will expire in {0} days.",
  "lang.auth.PwdMgrAPI.item0008": "New password must differ from old password.",
  "lang.ark.apiStationCode.stationQueueNotExists": "Queuing control in workstation does not exist",
  "lang.auth.PwdMgrAPI.item0005": "Adding password management encountered error.",
  "lang.auth.PwdMgrAPI.item0004": "If existing, the password warning days must be set to a positive integer that is less than the validity period of password.",
  "lang.auth.PwdMgrAPI.item0007": "Deleting password management encountered error.",
  "lang.ark.fed.flowTemplateSelTypeOrRobot": "either robot model or specified robot ID is required",
  "lang.auth.PwdMgrAPI.item0006": "Password management ID was not found.",
  "lang.auth.PwdMgrAPI.item0001": "The password length is shorter than length {0} specified in rules.",
  "lang.auth.PwdMgrAPI.item0003": "If existing, the validity period of password must be set to a positive integer.",
  "lang.auth.PwdMgrAPI.item0002": "The password does not comply with rule: {0}.",
  "lang.ark.fed.waitExecute": "Not executed",
  "lang.ark.workflow.TimeTriggerSimple": "Automation",
  "lang.ark.fed.shelfAttribute.name": "Type of material",
  "lang.ark.fed.orderComplete": "Distributed",
  "geekplus.moving.uic.elTableWrapperVue2.column.index": "No.",
  "lang.ark.interface.apiWorkflowInstanceList": "Workflow instance query",
  "lang.ark.workflow.recoveryAreaType": "Recycling area type",
  "lang.ark.warehouse.manualOperateTypeOut": "Manual stock-out",
  "lang.ark.fed.menu.workstationManage": "Workstation",
  "lang.auth.PwdMgrAPI.item0012": "Login to tour account is banned for {0} minutes.",
  "lang.ark.action.interface.compareValue": "Compare with conditional value",
  "lang.auth.PwdMgrAPI.item0011": "Login to account {0} has been banned because of too many login failures.",
  "lang.ark.warehouse.goodsNumberExists": "Material codes have already existed",
  "lang.auth.PwdMgrAPI.item0013": "You have {0} tries left.",
  "lang.ark.fed.screen.equipmentAssociatedInfo.dialogDetail.labelEquipName": "Device name",
  "lang.authManage.web.others.login": "Log in",
  "lang.auth.PwdMgrAPI.item0010": "Current password has expired. Please log in again.",
  "lang.ark.fed.containerEntryAndLeave": "Container entry and remove",
  "lang.ark.fed.machineUse": "Machine use",
  "lang.ark.fed.description": "Description",
  "lang.mwms.fed.sku": "Product file",
  "lang.ark.fed.waveTask": "Wave picking task",
  "lang.ark.fed.startDate": "Start date",
  "lang.ark.workflow.exceptionHandler.robotQueue": "Robot queue",
  "lang.ark.archiveType.workStationOperateLog": "Workstation operation log",
  "lang.mwms.fed.customer": "Client file",
  "lang.ark.workflow.area.factory": "Manufacturer",
  "lang.ark.warehouse.theMatrialPointIsNotUni": "The feeding point is not unique. Wave grouping failed",
  "lang.gles.baseData": "Basic Data",
  "lang.ark.warehouse.thePointExistsAnyOrderPleaseCancel": "Wave grouping for multiple material requisition sheets of the production line failed. Please cancel redundant documents.",
  "lang.ark.fed.workflowNumber": "Workflow number",
  "lang.ark.trafficControl.blockRange": "Traffic control area",
  "lang.ark.fed.demandProductionLine": "Demand production line",
  "lang.ark.trafficControl.enterStrategy.byOccupancyPriority": "First occupy first pass (priority first)",
  "lang.ark.fed.goodsInfo": "Material information",
  "lang.ark.fed.deliveryTimeV2": "Distribution duration",
  "lang.ark.fed.switchingWorkstation": "Switch workstations",
  "lang.ark.fed.defaultSet": "Default layout",
  "lang.ark.workflow.condition.lessThanOrEqual": "Less than or equal to",
  "lang.ark.workflow.sendTask": "Send instructions",
  "lang.ark.fed.partition": "Partition plate",
  "lang.common.invalidParameters": "Incorrect input parameters",
  "lang.ark.fed.notNow": "Not for the time being",
  "lang.ark.fed.component.workflow.msg.inconsistentEquipIds": "Some components/devices do not belong to the same device. Please examine relevant device configurations.",
  "lang.ark.interface.disabledSuccess": "Successfully disabled",
  "lang.ark.fed.reeditTheCurrentMapBackground": "Reedit the current map background",
  "lang.ark.fed.robotModel": "Robot model",
  "lang.ark.fed.adjustment": "Adjust",
  "lang.ark.fed.editingMapBackground": "Edit map background",
  "lang.ark.interface.interfaceDesc.phase": "Callback timing",
  "lang.ark.removeContainerFail": "Failed to delete container!",
  "lang.ark.fed.strategyManagement": "Strategic management",
  "lang.ark.fed.modificationTime": "Modification time:",
  "lang.ark.fed.collectionOr": "Collection (OR)",
  "lang.ark.trafficControl.trafficFunctionType": "Function type",
  "lang.ark.areaCodeExist": "Regional code can’t be repeated",
  "lang.ark.fed.screen.flowNodeConfig.executeDeviceInstruct": "Execute this device instruction",
  "lang.ark.externalDevice.device_own_type.externalDevice": "External equipment",
  "lang.ark.workflow.area.increaseStrategy": "Progressive strategy",
  "lang.ark.action.interface.applicationType": "Application of returned parameter",
  "lang.ark.operatelog.operatetype.auto": "Automatic workflow",
  "lang.ark.warehouse.quadrupleContainerSide": "Four sides",
  "lang.ark.fed.RightBracket": "+ right parenthesis",
  "lang.ark.workflow.task.status.wait.queue.robot": "Waiting for robots in queue",
  "lang.ark.fed.goodsManagement": "Stock bin management",
  "lang.ark.robotDeviceComponent.deviceName": "Device name",
  "lang.ark.fed.expressionError": "Wrong expression format",
  "lang.ark.fed.waitingSeconds": "Seconds to wait",
  "lang.mwms.fed.arrangePlanDetails": "Tallying schedule detail inquiry",
  "lang.ark.equipment.equipmentTypeCannotNull": "Device type cannot be empty",
  "lang.ark.fed.configurationParameter": "Configuration parameter",
  "lang.ark.fed.editBackgroundMap": "Edit background images",
  "lang.ark.workflow.paramValueCode.constant": "constant",
  "lang.ark.fed.containerOrientation": "Container orientation",
  "lang.ark.fed.screen.hybridRobot.binBindStopPoint": "Bind stock bin with map point",
  "lang.ark.fed.pauseSuccess": "Successfully suspended",
  "lang.ark.interface.endpoint": "End point code",
  "lang.ark.workflow.notAllowFinishIfUnArrivedEnd": "The task cannot be completed until it reaches the finish line.",
  "lang.ark.fed.batchGeneration": "Batch generation",
  "lang.ark.fed.inAutomaticExecution": "In automatic execution...",
  "lang.ark.fed.chinese": "Chinese",
  "lang.ark.workflowTriggerMonitorStatus.cancel": "Cancel",
  "lang.ark.workStatus.execute": "Executing",
  "lang.ark.waveTaskStatus.executing": "Executing",
  "lang.ark.fed.taskQueue": "Task queue",
  "lang.ark.fed.triggerTimer": "Triggering duration",
  "lang.ark.workflow.area.artificialControl": "Manual control area",
  "lang.ark.workflow.task.status.node.pause": "Task suspended",
  "lang.ark.fed.unknownArea": "Unknown area",
  "lang.ark.fed.workflowConfiguration": "Workflow management",
  "lang.ark.robot.classfy.lift": "Lift",
  "lang.ark.workflow.task.status.exception.completed": "Completed abnormally",
  "lang.ark.fed.nonumberWorkstation": "No. {number} workstation",
  "lang.ark.fed.inventoryAdjustment": "Invitatory adjustment ",
  "lang.ark.fed.robotWaitFlagnew": "In-situ wait of robot",
  "lang.ark.interface.apiRemoveContainer": "Delete container",
  "lang.ark.fed.noRmsTask": "Moving task is not generated",
  "lang.auth.DataAPI.item0001": "Exception in obtaining user data auth. Exceptional information: {0}",
  "lang.ark.fed.screen.flowNodeConfig.conditionTips": "Note: If multiple conditions are selected, the instruction can only be executed only if all selected conditions are met.",
  "lang.ark.fed.managementMode": "Management mode",
  "lang.ark.fed.distributionMode": "Distribution mode",
  "lang.ark.workflow.arrive.action.goTurnOfAngle": "Component rotation by angle",
  "lang.ark.fed.screen.flowNodeConfig.ifRobotStatus": "If robot state is",
  "lang.ark.fed.taskQuery": "Task query",
  "lang.ark.fed.lift": "Lift",
  "lang.ark.fed.buttonFunctionConfiguration": "Button function configuration",
  "lang.ark.fed.pleaseSelectTheProcessNodeYouWantToEdit": "Please select the workflow node you want to edit!",
  "lang.ark.fed.uploadCutImage": "Upload and crop the barcode",
  "lang.ark.fed.floorStage.differentFloor": "Different floors",
  "lang.ark.fed.editing": "Edit",
  "lang.ark.fed.getGoodsMESLocation": "Get the material MES request address",
  "lang.ark.fed.systemConfiguration": "System Configuration",
  "lang.ark.element.workstation.atLeast.one": "Workstation must have at least one point",
  "lang.ark.bussinessModel.workflow": "Process",
  "lang.mwms.fed.arrangeTaskSplit": "Tallying task split strategy",
  "lang.ark.workflow.canNotContinue": "Workflow status exception!",
  "lang.ark.container.containerAmountNumberCurrentScope": '"For the specified container code {}, up to {} containers can be added. Confirm to add?"',
  "lang.ark.fed.exportSuccessFailNum": "Failed to import data{0}",
  "lang.ark.apiCommonCode.flowStrategyNotSupported": "flowStrategy:{0} not supported",
  "lang.ark.fed.productionLineNoGoodsInfo": "Material information is not bound to the production line station",
  "workflow.task.cancel.robot.task.failure": "Failed to delete! Reason: rms feedback message",
  "lang.ark.fed.callMaterialTask": "Initiate a material calling task",
  "lang.ark.auth.userLoginNotSessionOtherStation": "The current user {0} is already logged in at the {1} workstation. After logging out, you can log in to a different workstation!",
  "lang.mwms.rf.multiSkuBoard": "Pull type display board",
  "lang.ark.warehouse.materialPointCellCodeRepeat": "{0} used",
  "lang.ark.fed.pleaseGoToTheLoginPageAndSelectTheSerialPassword": "Please go to the login page to select serial port No.",
  "lang.ark.fed.goToCharge": "Charging",
  "lang.ark.apiCommonCode.binCodeAndOrderExist": "The same stock bin point and stock bin SN already exist. Do not add them repeatedly.",
  "lang.ark.fed.startPoint": "Start point",
  "lang.ark.fed.everyOnce": "Once",
  "lang.gles.interface.interfaceConfig": "Interface Configuration",
  "lang.ark.robotDeviceComponent.deviceType": "Device type",
  "lang.ark.fed.startFlowSuccess": "Process initiated successfully",
  "lang.ark.button.operation.command.cleanWaitPoint": "Clear waiting points",
  "lang.ark.fed.pleaseChooseAreaCode": "Please select the corresponding nodes of the area.",
  "lang.ark.workflowTriggerType.clear.workflow": "Clear specified process",
  "lang.ark.fed.taskCancelTime": "Task cancellation time",
  "lang.ark.fed.rackCode": "Container code",
  "lang.auth.fed.password.ruleDesc": "The password must contain at least nine characters containing numbers, upper case and lower case letters.",
  "lang.ark.warehouse.noMatchMatnr": "No material matched",
  "lang.ark.fed.noWorkflowNodePleaseClickToSelect": "No workflow nodes for the time being, please click to select!",
  "lang.ark.action.interface.saveValue": "Store conditional value",
  "lang.ark.fed.screen.area.addGroup": "Add grouping",
  "lang.auth.role.edit.sysName.gms": "GMS",
  "lang.common.success": "Successful operation",
  "lang.ark.deliverOrder.invertedSequence": "Reverse order of production line processes",
  "lang.ark.fed.wirelessSignal": "Wireless signal",
  "lang.ark.logType.warehouseInterfaceLog": "Warehouse interface log",
  "lang.ark.fed.handleRefresh": "Manual refresh",
  "lang.ark.warehouse.cellCodeHasTask": "There are unfinished tasks in the station. Please complete them before the operation",
  "lang.ark.fed.playVoice": "Play voice",
  "lang.ark.fed.entryStartPoint": "Start point of entry",
  "lang.ark.fed.xialiao": "Discharging timeout",
  "lang.ark.fed.cycleNum": "Cycle number",
  "lang.ark.apiCallbackReg.sendInterval": "One by one/all",
  "lang.ark.fed.existBinCode": "The serial number of the stock bin already exists",
  "lang.ark.loadCarrier.loadCarrierModelUsed": "Deletion failed because the container model is already in use.",
  "lang.ark.workflow.area.releaseOrder": "Release order",
  "lang.ark.fed.menu.workstationAndqueueController": "Queuing control",
  "lang.ark.fed.noWorkflowConfiguration": "No workflow configuration available",
  "lang.ark.fed.startFlow": "Start",
  "lang.ark.fed.shelfAttribute.msgType": "Message type",
  "lang.common.cancel": "Cancel",
  "lang.ark.fed.containerChangeLogType": "Container operation log task type",
  "lang.mwms.fed.innerException": "In-warehouse exception",
  "lang.ark.fed.add": "Add",
  "lang.mwms.exceptionHandling": "Exception handling",
  "lang.ark.fed.firstDrawWorkStop1": "Please draw the first node of the process first, and the type of the first node is: workstation, point",
  "lang.ark.workflow.brotherNodeNotWorkflowNode": "The node that inherits the robot and its brother nodes must be sub-process nodes!",
  "lang.ark.fed.bindWorkstation": "Bound workstation",
  "lang.ark.fed.nodeNumber": "Node number",
  "lang.ark.fed.japanese": "Japanese",
  "lang.ark.fed.screen.LoginLog.loginTime": "login time",
  "lang.ark.fed.screen.systemConfig.businessGroup": "Business function",
  "lang.ark.fed.savedSuccessfully": "Save successfully",
  "lang.ark.fed.trafficRangeRun": "Control area operation",
  "lang.ark.hitStrategy.shortestDistance": "Shortest distance",
  "lang.ark.fed.sureCutting": "The following materials are not discharged fully. Confirm to complete the material discharging?",
  "lang.ark.workflow.task.status.fetched": "Container fetched",
  "lang.ark.warehouse.policyHaveSameTriggerCondition": "The triggering condition strategy already exists",
  "lang.mwms.fed.batchAdjustmentManager": "Batch adjustment sheet management",
  "lang.ark.fed.angle": "Angle",
  "lang.ark.fed.sureFeeding": "The following materials are not fed fully. Confirm to complete the feeding?",
  "lang.ark.fed.workstationType": "Type",
  "lang.ark.fed.waveGeneratePattern": "Wave picking generation mode",
  "lang.ark.fed.isExcetuTrigger": "Do you want to execute the current trigger?",
  "lang.ark.fed.node": "Node",
  "lang.ark.fed.successfulLogin": "Login succeeded",
  "lang.ark.base.license.exceptionForCannotFindServerUUID": "Hardware information does not match!",
  "lang.ark.operation.workflow.deleteExecution": "Delete task {0}",
  "lang.ark.fed.inventoryStatus": "Inventory status",
  "lang.ark.loadCarrier.batchAddAmountTransfinite": "The data operated in batch are out of limit. Up to 5000 pieces of data can be operated at a time.",
  "lang.ark.fed.options": "Options",
  "lang.ark.fed.homepage": "Home page",
  "lang.ark.fed.taskSource.station": "Workstation ",
  "lang.ark.fed.targetPointCode": "Target point code",
  "lang.ark.fed.cycle": "Circulation",
  "lang.ark.fed.workflowGroupName": "Workflow group name",
  "lang.ark.fed.edit": "Edit",
  "lang.ark.fed.circle": "Circle",
  "lang.ark.ruleStage.sameFloor": "Same floor",
  "lang.ark.fed.working": "Working ",
  "lang.ark.auth.otherUserHaveLoginOtherStation": "User {0} has logged in to the workstation. Please contact the user to log out and log in again",
  "lang.ark.fed.waveStrategy": "Wave picking stagey ",
  "lang.ark.fed.appointmentTip": "The reservation start time must be later than the current time!",
  "lang.ark.fed.bindTemplate": "Bound template",
  "lang.ark.bin.binNotExist": "The stock bin point does not exist.",
  "lang.ark.fed.menu.vens.dmpTaskManage": "Device task",
  "lang.authManage.fed.expiryDate": "Expiring date",
  "lang.ark.fed.morePickingTitle": '"Received by multiple AGVs: When the material calling task requires multiple feeding points for preparation, after confirming the combination of feeding points, multiple material requisition sheets are generated according to the materials of different feeding points. The AGVs are allocated to different feeding points to receive the task and prepare materials, and then deliver the materials to the demand station."',
  "lang.ark.workflow.area.factoryFollowControl": "Manufacturer that is not allowed to enter by following other manufacturers",
  "lang.ark.workStatus.exception": "Completed abnormally",
  "lang.ark.fed.fullBins": "The stock bin is full with up to 7 materials",
  "lang.ark.fed.upper": "Up",
  "lang.ark.fed.screen.hybridRobot.binCellCodeTip": "The stock bin code of the machine.",
  "lang.ark.fed.syncProductGoodsInfo": "Synchronize the material information of the production line station",
  "lang.ark.workflow.template.validate.templateMidNodeMustUnique": "Template middle must be unique.",
  "lang.ark.workflow.action.commandExecutePhase.undoBackArrived": "Revoke/Return to arrival",
  "lang.ark.fed.menu.strategyCenter": "System configuration",
  "lang.ark.fed.startDrawing": "Start drawing",
  "lang.ark.warehouse.triggerPoint": "Start station",
  "lang.ark.fed.offRefresh": "Disable refresh",
  "lang.ark.interface.resent": "Resend",
  "lang.ark.fed.areaAreaOrAreaShelfOrShelfShelfIllegalProcess": "Area-area or area-shelf or shelf-shelf, illegal workflow",
  "lang.ark.operation.workflow.pauseExecution": "Recovery task",
  "lang.ark.workflow.condition.greaterThan": "Greater than",
  "lang.ark.robot.firmware.update": "Firmware upgrade",
  "lang.ark.fed.goodsName": "Material name",
  "lang.ark.loadCarrier.alreadyRemoved": "The container has already been removed.",
  "lang.ark.workflow.existMultipleNodeExtendRobot": "Multiple nodes inherit the robot!",
  "lang.mwms.rf.receive": "Stock in",
  "lang.ark.fed.eitherOrRobotAndTypeNew": "Select either robot model or robot",
  "lang.ark.taskCannotOperate": "The operation cannot be executed!",
  "lang.ark.workflow.rollOverBack": "Flip-Reset",
  "lang.ark.equipment.equipmentCellCodeExists": "Point position {0} has been occupied by a device",
  "lang.ark.fed.waveType": "Wave grouping conditions",
  "lang.ark.fed.alarmType": "Report type",
  "lang.ark.fed.pointsList": "Point list",
  "lang.ark.workflow.area.vertexInfo": "Vertex information",
  "lang.ark.fed.addContainerAmount": '"Batch added container quantity (1 by default, up to 5,000)"',
  "lang.ark.fed.types": "Type",
  "lang.ark.apiCommonCode.instructionNotSupported": "The execution command type {0} is not supported",
  "lang.mwms.fed.businessRule": "Business rules",
  "lang.ark.fed.abnormal": "Abnormal",
  "lang.ark.fed.orderAbnormalFailed": "Operation failed. Only documents whose task is in the abnormal suspended status can be operated",
  "lang.ark.fed.autoCancleTask": "Auto cancel task",
  "lang.ark.fed.menu.palletPositionManage": "Pallet position",
  "lang.ark.workflow.controllerOutOfBounds": '"Controller quantity must be between 1 and 65,536."',
  "lang.ark.fed.emptyIt": "Clear",
  "lang.ark.fed.theMaterialsOfTime": "Material use time",
  "lang.ark.fed.numberLang": "QTY",
  "lang.ark.fed.robotID": "Robot ID",
  "lang.ark.fed.nodeConfirmedLeave.tip": "Used together with path points in the map",
  "lang.ark.fed.distributionMaterials": "Material distribution",
  "lang.auth.UserAPI.item0195": "The role does not exist",
  "lang.auth.UserAPI.item0194": "Failed to modify the role",
  "lang.auth.UserAPI.item0197": "The real name cannot be null",
  "lang.ark.fed.taskStartingPoint": "Task start point",
  "lang.auth.UserAPI.item0196": "Failed to modify the user basic information",
  "lang.authManage.web.existLoginUser": "Logged in elsewhere, please log in after normal log out!",
  "lang.auth.UserAPI.item0191": "Failed to add the user",
  "lang.auth.UserAPI.item0190": "Old password error",
  "lang.auth.UserAPI.item0193": "The username already exists",
  "lang.ark.apiStationCode.stationQueueUnDefinite": "Workstation queuing control status is not defined",
  "lang.auth.UserAPI.item0192": "Exception in adding the user",
  "lang.ark.fed.waveConfig": "Wave picking configuration",
  "lang.ark.addContainerFail": "Failed to add container!",
  "lang.ark.fed.distribution": "Allocate",
  "lang.ark.fed.showByFlowClass": "Display according to the process category",
  "lang.auth.UserAPI.item0199": "No user to be modified",
  "lang.mwms.monitorRobotMsg.notfree": "The shelf is not in the idle status.",
  "lang.auth.UserAPI.item0198": "Username cannot be empty",
  "lang.ark.fed.containerTypeInfo": "Container type information",
  "lang.ark.fed.scanExceptionProcess": "Code scanning exception processing",
  "lang.ark.fed.shelfAttribute.REPLEN": "REPLEN",
  "lang.ark.fed.onTheWay": "On the way",
  "lang.ark.api.moving.startIsEmpty": "Task start point is empty",
  "lang.ark.fed.ContainerIsWorkingCanNotEmptyInventory": "Container in operation. The inventory cannot be cleared",
  "lang.ark.workflow.area.append": "Fill the vacancy by rear",
  "lang.ark.fed.amounts": "Demand quantity",
  "lang.mwms.fed.taskMonitor": "Task monitoring",
  "lang.ark.fed.SIMPPushTip": "Select SIMP configuration rules; support email, enterprise WeChat, DingTalk and other push modes",
  "lang.ark.externalDevice.caution": "Attention please",
  "lang.ark.fed.flowCreateTitle": "Process creation: the process creation page shows the process display style",
  "lang.ark.fed.pleaseAtLeastOneGoodsInfo": "Please add at least one material information",
  "lang.ark.fed.query": "Inquiry",
  "lang.ark.api.none": "Unknown meaning",
  "lang.ark.fed.pleaseEnterANonnegativeNumber": "Please enter a non-negative number!",
  "lang.ark.fed.intelligentMovingSystem": "Intelligent handling system!",
  "lang.ark.fed.chooseGoods": "Material for distribution",
  "lang.auth.UserAPI.item0175": "Failed to obtain current login user",
  "lang.auth.UserAPI.item0174": "No auth",
  "lang.ark.fed.deviceNotExists": "Device does not exist.",
  "lang.ark.fed.sourcesOfTheFactory": "Source factory",
  "lang.mwms.fed.stocktake": "Inventory checking",
  "lang.auth.UserAPI.item0177": "The user is disabled. Please contact the administrator to enable the user again, or log in with another user",
  "lang.auth.UserAPI.item0176": "Data error",
  "lang.auth.UserAPI.item0179": "Password error",
  "lang.auth.UserAPI.item0178": "The account does not exist. Please enter again.",
  "lang.ark.record.interface.createTask": "Create an interface instruction task",
  "lang.ark.fed.executeNotAllowed": "Executing. No operation allowed",
  "lang.common.failed": "Operation failed",
  "lang.ark.fed.flowRule": "Circulation rules",
  "lang.ark.api.cellNotExists": "Cell does not exist",
  "lang.ark.workflow.includesSubflowCancellationNotAllowed": "Cancellation is not allowed for the current process that contains sub-processes.",
  "lang.ark.workflow.pathDecision": "Path judgment",
  "lang.authManage.web.others.relUser": "Associated user",
  "lang.ark.fed.materialConsumptionTime": "Material use time",
  "lang.auth.UserAPI.item0184": "No user data",
  "lang.auth.UserAPI.item0183": "Exit exception",
  "lang.auth.UserAPI.item0186": "The password you entered does not match the previous password",
  "lang.ark.fed.notAllowOperation": "The end point code already exists. No operation allowed",
  "lang.auth.UserAPI.item0185": "User inquiry exception",
  "lang.auth.UserAPI.item0180": "Data error",
  "lang.auth.UserAPI.item0182": "Please enter username",
  "lang.ark.workflow.canNotPublish": "Workflow exception and cannot be published",
  "lang.auth.UserAPI.item0181": "Please enter password",
  "lang.ark.trafficControl.enterStrategy": "Release order",
  "lang.auth.UserAPI.item0188": "Failed to change the password",
  "lang.auth.UserAPI.item0187": "User old password verification exception",
  "lang.ark.systemParamCannotEdit": "System default, uneditable!",
  "lang.ark.fed.queue": "Queuing logic",
  "lang.auth.UserAPI.item0189": "Exception in changing password",
  "lang.ark.fed.screen.equipmentAssociatedInfo.columnUpdateTime": "Edited on",
  "lang.ark.backTaskNotAllowedUndo": "The back task cannot be revoked!",
  "lang.ark.fed.processDescription": "Workflow description",
  "lang.ark.fed.goSomewhere": "Go somewhere",
  "lang.ark.fed.trafficControlManage": "Traffic control management",
  "lang.ark.fed.citeNode": "Reference node",
  "lang.ark.fed.waveGenerateScope": "Wave grouping range",
  "lang.authManage.web.others.number": "Serial number",
  "lang.ark.fed.targetStation": "Demand station",
  "lang.ark.waveStatus.distributing": "Distributing",
  "lang.gles.strategy.tallyStrategy": "Tally strategy",
  "lang.ark.fed.createAMap": "Create a map",
  "lang.ark.fed.delNodeConfig": "Are you sure to delete the current node interaction configuration?",
  "lang.ark.fed.multipleChoice": "Multiple choices",
  "lang.ark.apiContainerCode.locationCodeExistsUsingContainer": "locationCode:{0} container in use",
  "lang.ark.apiCallbackReg.single": "One by one",
  "lang.ark.getDMPErr": "Error device information acquired",
  "lang.ark.fed.workstationUrl": "Workstation URL",
  "lang.ark.fed.taskFrom": "Pick-and-place type",
  "lang.ark.fed.menu.chargeInfo": "Charger monitoring",
  "lang.gles.interface.interfaceLog": "Interface Log",
  "lang.ark.workflow.template.type.dynamiNode": "Dynamic point task",
  "lang.ark.fed.chooseFreeMaterial": "Select a material for distribution",
  "lang.ark.fed.GENERAL": "Normal",
  "lang.ark.workflowTriggerStatus.unEnable": "Disable",
  "lang.ark.workflow.Full": "Full/occupied",
  "lang.ark.fed.playVoiceTime": "Play time",
  "lang.ark.fed.shelfLeaveSuccess": "Container removed successfully",
  "lang.ark.fed.common.btn.delete": "Delete",
  "lang.ark.fed.deliverType": "Distribution method",
  "lang.ark.workflow.canDeleteContainerFlag": "Remove the container",
  "lang.ark.fed.screen.workflowInfo.workflowExeTaskId": "Second Task Id",
  "lang.ark.noOperation": "No action",
  "lang.ark.workflowTrigger.logType.interface": "Interface Log",
  "lang.ark.base.license.errorLicenseCustomerIdOrInstanceId": "Certificate client ID or instance ID is inconsistent with the certificate",
  "lang.ark.fed.firstNode": "First node",
  "lang.ark.fed.cleanAll": "Clear all",
  "lang.ark.fed.feedingNode": "Feeding node",
  "lang.ark.fed.ordinary": "Normal",
  "lang.ark.api.goturn.neededsidesError": '"The value of the container rotation parameter neededSides is not F, B, L or R"',
  "lang.mwms.monitorRobotMsg.scanabnormal": "Code scanning exception",
  "lang.ark.fed.forklift": "Forklift",
  "lang.ark.fed.theCargoSpaceIsLocked": "The stock bin has been locked",
  "lang.auth.UserAPI.item0115": "The username already exists",
  "lang.auth.UserAPI.item0114": "Exception in adding the user",
  "lang.ark.workflow.template.validate.templateFinishNodeMustUnique": "Template end must be unique.",
  "lang.auth.UserAPI.item0117": "The role does not exist",
  "lang.auth.UserAPI.item0116": "Failed to modify the role",
  "lang.auth.UserAPI.item0111": "Old password error",
  "lang.auth.UserAPI.item0110": "Failed to change the password",
  "lang.auth.UserAPI.item0113": "Failed to add the user",
  "lang.auth.UserAPI.item0112": "Exception in changing password",
  "lang.ark.workflow.arrive.action.command.executeFailed": "Command execution failed",
  "lang.auth.UserAPI.item0108": "The password you entered does not match the previous password",
  "lang.auth.UserAPI.item0107": "User inquiry exception",
  "lang.ark.interface.oneToSixTeenLettersAndNumbers": "1-16 digits of letters and numbers are supported!",
  "lang.ark.workflow.cellCode": "Point location number",
  "lang.auth.UserAPI.item0109": "User old password verification exception",
  "lang.ark.workflow.extendRobotFalse": "No",
  "lang.ark.interface.checkout": "View",
  "lang.auth.UserAPI.item0120": "Username cannot be empty",
  "lang.gles.systemManage.baseDict": "Data dictionary",
  "lang.ark.fed.interfaceSetNodeValue": "Interface value assignment point",
  "lang.ark.fed.outWarehouse": "Manual stock-out",
  "lang.ark.fed.conButtonLogResult": "Execution result",
  "lang.auth.UserAPI.item0126": "No user data",
  "lang.ark.fed.linkName": "Link name",
  "lang.auth.UserAPI.item0125": "Exception in {0} user",
  "lang.auth.UserAPI.item0122": "Exception in modifying the user",
  "lang.ark.fed.rotateMap": "Rotate",
  "lang.ark.fed.theFirstNodeMustBeWorkstationOrDockPoint": "The first node must be a workstation or a point",
  "lang.ark.fed.screen.hybridRobot.stopPointCode": "Map point code",
  "lang.auth.UserAPI.item0121": "No user to be modified",
  "lang.auth.UserAPI.item0124": "No user selected",
  "lang.auth.UserAPI.item0123": "User status is not modified",
  "lang.ark.fed.suspend": "Pause",
  "lang.ark.fed.processInstance": "Workflow instance",
  "lang.ark.fed.screen.area.ynGroup": "Whether to do grouping",
  "lang.auth.UserAPI.item0119": "The real name cannot be null",
  "lang.ark.fed.entryPoint": "Entry point",
  "lang.wms.biz.UserServiceImpl.deleteAdminAlert": "An administrator cannot be deleted.",
  "lang.auth.UserAPI.item0118": "Failed to modify the user basic information",
  "lang.ark.controlNodeType.station": "Workstation ",
  "lang.ark.workflow.completeBizAutoTrigger": "Automatically select branch after business completion",
  "lang.ark.apiNodeActionCode.componentCommandIsEmpty": "The component command of node interaction configuration is empty",
  "lang.ark.warehouse.goodsTaskCantNotExecute": "The task has been received by another feeding point and cannot be executed at this point",
  "lang.ark.fed.common.checkNumberFormatMsg0": "Please enter a number whose length is within {0}.",
  "lang.ark.workflow.area.stragingRange": "Storage area",
  "lang.ark.fed.common.checkNumberFormatMsg1": "Please enter a number whose length is from {0} to {1}.",
  "lang.ark.fed.orderAbnormalSure": "Confirm to manually complete the document in the abnormal status?",
  "lang.ark.fed.menu.flowNodeConfig": "Interaction setting",
  "lang.ark.fed.factory": "Factory",
  "lang.authManage.web.auth.roleList": "Role profile",
  "lang.ark.recycleAreaTaskNotAllowedOperate": "This operation is not allowed in the recycle area task",
  "lang.ark.fed.location": "Stock bin",
  "lang.ark.fed.defaultType": "Default type",
  "lang.mwms.fed.inventoryNum": "Inventory balance",
  "lang.ark.shelfCodeErr": "Container type number format is incorrect, please enter 6-digit figure",
  "lang.ark.fed.sure": "OK",
  "lang.ark.fed.reflectCell": "Corresponding nodes of the area",
  "lang.ark.workflow.paramValueCode.count": "count",
  "lang.ark.fed.friday": "Friday",
  "lang.ark.task.log.export.title.workflow.instance": "Workflow instance",
  "lang.ark.fed.descriptionMessage": "Descriptive information",
  "lang.auth.UserAPI.item0104": "Please enter username",
  "lang.ark.fed.station": "Workstation",
  "lang.auth.UserAPI.item0103": "Please enter password",
  "lang.auth.UserAPI.item0106": "No user data",
  "lang.auth.UserAPI.item0105": "Exit exception",
  "lang.auth.UserAPI.item0100": "The user is disabled. Please contact the administrator to enable the user again, or log in with another user",
  "lang.ark.fed.thisWorkflowHasBeenSelected": "{str} selected, this workflow",
  "lang.auth.UserAPI.item0102": "Password error",
  "lang.auth.UserAPI.item0101": "The account does not exist. Please enter again.",
  "lang.ark.fed.rackType": "Shelf type",
  "lang.authManage.web.permission.permissiontype": "Permission type",
  "lang.ark.workflow.recoveryAreaType.customCell": "Custom recycling area",
  "lang.ark.fed.arrivalOrientation": "Reach orientation",
  "lang.ark.loadCarrier.loadCarrierModelFormErr": "The container model data fail to meet the requirement.",
  "lang.ark.fed.whereRobotsCanWalk": "Where the robot can arrive",
  "lang.ark.fed.thursday": "Thursday",
  "lang.mwms.fed.charts": "Statistical report",
  "lang.ark.workflow.template.validate.templateTypeNotBlank": "Template type cannot be empty.",
  "lang.ark.fed.oneWayExit": "Single export",
  "lang.authManage.web.common.modifyPw": "Change Password",
  "lang.ark.action.interface.responseParamType": "Type of returned parameter",
  "lang.ark.robot.classfy.mix": "Recombination",
  "lang.ark.fed.cancelledSuccessfully": "Canceled successfully",
  "lang.authManage.web.others.toPrePage": "Return to previous page",
  "lang.ark.firstSendNodeUnsupportedOperation": "The first node does not support this operation!",
  "lang.ark.fed.expiringDate": "Expiration date",
  "lang.ark.fed.pleaseSelectARobot": "Please select a robot",
  "lang.ark.fed.goToWork": "Go to work",
  "lang.ark.workflow.function.type.functionArea": "Function area",
  "lang.ark.fed.closeRightTabs": "Close the right",
  "lang.ark.fed.component.workflow.label.nonSpecified": "Not specify",
  "lang.ark.fed.firstClassification": "First class classification",
  "lang.ark.workstationNotExists": "Workstation does not exist!",
  "lang.ark.fed.screen.equipmentAssociatedInfo.dialogDetail.labelFloor": "Floor",
  "lang.ark.fed.basicData": "Fundamental data",
  "lang.ark.fed.pleaseSelectAtLeastOneAction": "Please select at least one operation",
  "lang.ark.fed.waiting": "Waiting",
  "lang.ark.fed.orientation": "Orientation",
  "lang.ark.fed.emptyMoving": "Robot with no load",
  "lang.ark.container.containerEntryWay.manual": "Manual entry",
  "lang.auth.PermissionAPI.item0009": "The mutual exclusive verification between the subsystems of save page auth and page auth failed",
  "lang.ark.workflowConfig.configErr": "Process configuration error. Please check",
  "lang.auth.PermissionAPI.item0008": "The subsystem ID of save page auth is inconsistent with that of page auth",
  "lang.gles.receipt.receiptUpAndDownMaterialOrder": "Feeding and discharging order",
  "lang.auth.PermissionAPI.item0005": "Exception in inquiring all auth: {0}",
  "lang.auth.PermissionAPI.item0004": "Exception in saving page auth: {0}",
  "lang.auth.PermissionAPI.item0007": "Failed to verify the number of save page auth subsystems",
  "lang.auth.PermissionAPI.item0006": "Role name repetition",
  "lang.authManage.web.common.newPassword": "New password",
  "lang.ark.fed.flowStartAgain": "Process restart",
  "lang.ark.fed.arrived": "Arrived",
  "lang.ark.fed.cellNodeDeviceExists": "Point device information already exists.",
  "lang.gles.containerManage": "Container Management",
  "lang.ark.fed.screen.equipmentAssociatedInfo.dialogDetail.labelEquipType": "Device type",
  "lang.ark.fed.containerDeal": "Container handling",
  "lang.ark.fed.monday": "Monday",
  "lang.ark.hitStrategy.denseStorage": "Dense storage of pallets",
  "lang.ark.fed.conditionalValue": "Condition value",
  "lang.ark.workflow.palletLatticeNotExist": "No pallet position at the destination location",
  "lang.ark.fed.shelfLocation": "Shelf position",
  "lang.ark.fed.greaterThan": ">",
  "lang.ark.fed.please": "Please",
  "lang.ark.workflow.rollBack": "Reset",
  "lang.mwms.monitorRobotMsg81000": "Low battery of all robots in the site",
  "lang.ark.fed.rackPoint": "Shelf point",
  "lang.ark.fed.delRowData": "Are you sure to delete the data in this line?",
  "lang.ark.workflowTrigger.logType.task": "Log",
  "lang.ark.robot.manual": "Remote control mode",
  "lang.ark.workflowConfig.cellFunctions.wait": "Wait",
  "lang.mwms.fed.kanban": "Electronic display board",
  "lang.ark.fed.modificationRecord": "Modification records",
  "lang.ark.fed.recommendedSize": "Recommended size",
  "lang.mwms.monitorRobotMsg16000": "Robot exception",
  "lang.ark.fed.sacnFailAndCancelTaskV2": "Failed to recognize the container code. Exception code {0}. Waiting for the upstream business system to process",
  "lang.ark.workflow.executingCancelOperation": "Cancellation operation in progress.",
  "lang.ark.workflow.controllerCodeOutOfBounds": "Controller number shall be between 0 and 65535 ",
  "lang.ark.fed.triggerHandler": "Trigger timing",
  "lang.ark.interface.interfaceDesc.name": "Field name",
  "lang.ark.equipment.equipmentAlreadyRelevanceWorkflow": "The device has been associated with a workflow. Please uninstall the workflow first.",
  "lang.ark.workflow.paramValueCode.locationToFloor": "locationToFloor",
  "lang.ark.fed.receiptTimeout": "Material requisition timeout",
  "lang.ark.fed.licenseEditController": "License info",
  "lang.ark.element.area.atLeast.one": "The area must have at least one shelf point",
  "lang.ark.cellCodeConfig.exists": "Save failed, the node configuration already exists!",
  "lang.ark.unsupportedRunMode": "Unsupported operating mode!",
  "lang.ark.fed.appointMentStatus": "Reservation status",
  "lang.gles.baseData.baseContainerType": "Container type",
  "lang.ark.fed.stopPointIdNonExistent": "The current point does not exist. Please switch the point",
  "lang.ark.fed.serialNumPlaceholder": '"When the material is selected, the camera automatically focuses on the scan box to scan the serial number on the material rod"',
  "lang.ark.fed.distributionRobot": "Robots distribution",
  "lang.ark.fed.setEndPoint": "Set as end point",
  "lang.ark.fed.standardStation": "Standard workstation",
  "lang.ark.fed.triggerTime": "Trigger time",
  "lang.ark.fed.noAvailableRobots": "No robot available yet",
  "lang.ark.fed.ruleCode": "Condition code",
  "lang.ark.fed.cellName": "Point name",
  "lang.ark.fed.orderExComplete": "Completed abnormally",
  "lang.ark.fed.rack": "Shelf",
  "lang.ark.fed.enlarge": "Zoom in",
  "lang.ark.ruleStage.differentFloor": "Different floors",
  "lang.ark.fed.fixedRollerTrackOnTheGroundToConnectWithRobot": "A roller table fixed on the ground for docking with the robot",
  "lang.ark.workflow.cycleType.noLoop": "No cycle",
  "lang.ark.workflow.denyFollowFactoryNullError": "Manufacturer that is not allowed to enter by following others is empty. Please fill in the manufacturer",
  "lang.ark.fed.waveStrategyCode": "Strategy code",
  "lang.ark.workflow.paramValueCode.skuCode": "skuCode",
  "lang.ark.fed.workstation.msg.logout": "log out",
  "lang.ark.fed.autoRefresh": "Automatic refresh",
  "lang.auth.PermissionAPI.item0001": "Exception in inquiring page auth: {0}",
  "lang.auth.PermissionAPI.item0003": "Failed to edit the page auth. Other auth cannot be saved as page auth.",
  "lang.ark.fed.contents.flowConfig.recycleType.api": "upstream designated location",
  "lang.auth.PermissionAPI.item0002": "Failed to edit the page auth. The {0} role does not exist.",
  "lang.ark.syncRobotInfoError": "Acquisition of robot information failed",
  "lang.ark.fed.speedLimitZone": "Speed limit area",
  "lang.ark.interface.clientCode": "Client code",
  "lang.ark.workflow.canAddshelfFlag": "Add a shelf",
  "lang.ark.fed.AutoIn": "Automatic processing...",
  "lang.ark.robot.map.init": "Map initialization",
  "lang.ark.fed.remainingDistance": "Reminder: the remaining distance from the nearest AGV to you is:",
  "lang.ark.fed.targetFactory": "Demand factory",
  "lang.ark.fed.collectionTime": "Material requisition time",
  "lang.ark.fed.buttonType": "Button type",
  "lang.ark.workflow.arrive.action.component.robotComponentExecuteFailed": "The robot failed to execute the component command of the node interactive configuration ({0})",
  "lang.ark.fed.waveRanage": "Wave grouping range",
  "lang.ark.fed.screen.flowTemplate.specialNodeRepetitionTip": "The same node type and node code data already exists, please modify it before submitting!",
  "lang.ark.fed.orderCreateFail": "Creation failed",
  "lang.ark.workflowTriggerMonitorStatus.success": "Success",
  "lang.ark.fed.logType": "Log type",
  "lang.ark.fed.robotLog": "Robot log",
  "lang.ark.fed.pickUpTaskDetail": "Material requisition sheet details",
  "lang.ark.fed.getGoods": "Material calling",
  "lang.authManage.web.common.requiredInput": "Please enter required fields",
  "lang.ark.workflowTriggerMonitorStatus.failed": "Failed",
  "lang.ark.workflow.denseStorageTemplateAreaEmpty": "The start point is in a dense storage area where no available containers exist.",
  "lang.ark.workflow.wareHouseStationBusinessConfig": "Workstation business",
  "lang.ark.loadCarrier.loadCarrierModelRequired": "Container model cannot be empty. Please select the container model.",
  "lang.ark.fed.screen.flowTemplate.LogicNodeRepetitionTip": "Logic point: the same node type and node code data already exists, please change it before submitting!",
  "lang.ark.workflow.invalidStopPointStatus": "Point status exception",
  "lang.mwms.fed.stockRotation": "Inventory turnover rules",
  "lang.ark.fed.deleteTaskConfirmText": '"If deleted, the task will be restored to the last interruption and suspension of the process and re-issued after restoration. Confirm to delete?"',
  "lang.ark.workflowConfig.cellCodeDoNotExists": "Node code not configured",
  "lang.ark.fed.containerSide": "Container side",
  "lang.ark.fed.screen.hybridRobot.stopPointTip": "It is the physical point code before the robot reaches the machine.",
  "lang.ark.fed.cuttingGoods": "Material discharging",
  "lang.ark.fed.component.workflow.label.nodeComponent": "Node component",
  "lang.ark.fed.rotateLeft": "Anticlockwise",
  "lang.ark.fed.nonEmptyShelf": "Non-empty shelf",
  "lang.ark.workflow.end": "End",
  "lang.ark.workflow.template.validate.templateCodeNotBlank": "Template code cannot be empty.",
  "lang.ark.fed.component.workflow.label.specified": "Specify",
  "lang.ark.fed.unlockSure": "Confirm to unlock the stock bin",
  "lang.ark.fed.liveAllNodeUnallowAdd": "All nodes have been added and cannot be added repeatedly!",
  "lang.gles.receipt.stockAdjustOrder": "Inventory adjustment order",
  "lang.ark.fed.onEnter": "Entering",
  "lang.ark.fed.floorStage.sameFloor": "Same floor",
  "lang.auth.UserAPI.item0098": "Failed to obtain current login user",
  "lang.auth.UserAPI.item0097": "No auth",
  "lang.ark.apiContainerCode.codeRightNotNumber": "The rightmost digit or digits of startContainerCode shall be numbers",
  "lang.ark.fed.screen.area.maxTaskSize": "Hit quantity of concurrent tasks",
  "lang.ark.taskStatusCannotCancel": "The current task is in {0} status and cannot be deleted!",
  "lang.ark.fed.createNew": "Create ",
  "lang.ark.workflow.task.status.node.wait": "Node waiting",
  "lang.auth.UserAPI.item0099": "Data error",
  "lang.mwms.fed.classifyMutex": "Mutually exclusive configuration of category",
  "lang.ark.fed.triggerNameRepeat": "Duplicate trigger name",
  "lang.authManage.web.common.item0027": "Edit",
  "lang.mwms.monitorRobotMsg13003": "End of charging station command sending timeout",
  "lang.authManage.web.common.item0028": "New",
  "lang.mwms.monitorRobotMsg13001": "Charging station offline (long-term loss of connection)",
  "lang.mwms.monitorRobotMsg13002": "Start sending charging station command timeout",
  "lang.ark.fed.reset": "Reset",
  "lang.mwms.fed.move": "Inventory movement",
  "lang.ark.area.lockJobInsertError": "The reserved area locking time overlaps. Please check!",
  "lang.mwms.monitorRobotMsg13000": "Charging station disconnected (temporary network disconnection)",
  "lang.ark.button.command.reset": "Upspring",
  "lang.ark.waveTaskStatus.create": "Create",
  "lang.ark.workflow.workflowRuleExpressionErr": "Failed to save. Please configure complete expression!",
  "lang.ark.fed.create": "Create",
  "lang.ark.sendRmsTaskError": "RMS recovery exception. Please try again later",
  "lang.authManage.web.common.item0023": "Inquiry",
  "lang.ark.api.globalConfigCancelOff": "Delete configuration not enabled",
  "lang.ark.workflow.endSimple": "End",
  "lang.ark.pda.function.container.leave": "Remove container",
  "lang.ark.fed.robotLoadStatus": "Robot state",
  "lang.ark.warehouse.materialPointNameRepeat": "The name already exists",
  "lang.mwms.fed.ownerMutex": "Mutually exclusive configuration of cargo owner",
  "lang.ark.fed.pleaseSelectADestinationOnTheMapElementsOnly": "Please select a destination on the map (map elements only)",
  "lang.ark.fed.welcomePage": "Welcome page",
  "lang.ark.fed.screen.hybridRobot.setPointOffset": "Set a point offset value",
  "lang.ark.recycleFunctionSwitchIsClosed": "The function is not enabled, please contact the developer to enable this function",
  "lang.ark.fed.menu.dockModel": "Shelf model",
  "lang.common.ok": "OK",
  "lang.ark.fed.alarmTypeDetail": "Material requisition timeout, feeding timeout and material discharging timeout",
  "lang.ark.fed.dispatchQueue": "Distribution order",
  "lang.ark.fed.cancelButton": "Cancel button",
  "lang.ark.fed.shelfAttribute.RTV": "RTV",
  "lang.mwms.fed.batchProperty": "Batch attribute",
  "lang.ark.interface.apiStationQueueStart": "Enable queuing for fetching",
  "lang.ark.fed.selectTaskType": "Please select task type",
  "lang.ark.loadCarrier.loadCarrierModelNameIsEmpty": "Container model name cannot be empty.",
  "lang.authManage.web.others.deviceInfo": "Hardware information",
  "lang.ark.button.type.selfLocking": "Self locking",
  "lang.ark.fed.strategyCenter": "Strategy center",
  "lang.authManage.api.menu.userList": "User profile",
  "lang.authManage.web.auth.passwordPolicy": "Password Policy",
  "lang.ark.fed.theTotalNumberOfContainers": "Total number of containers",
  "lang.ark.fed.buttonNumber": "Button No.",
  "lang.ark.fed.unMatchGoodsLocation": "The shelf does not match the delivery shelf position {0}. Please reply the shelf before the operation",
  "lang.ark.record.dmp.createTask": "Create dmp task",
  "lang.ark.fed.continuityPassage": "Continuous pass",
  "lang.ark.fed.stationLineUpControl": "Workstation queuing control",
  "lang.ark.fed.rackTypeManagement": "Shelf type management",
  "lang.ark.fed.containBinIsNotEmpty": "The stock bin is not empty",
  "lang.ark.fed.baseInfoV2": "Basic information",
  "lang.ark.api.template.unclearTargetBusinessType": "Undefined destination location business type.",
  "lang.ark.fed.rmsTaskPhase": "Execution phase",
  "lang.mwms.fed.adjustments": "Inventory adjustment",
  "lang.ark.task.log.export.title.task.status": "Task status",
  "lang.ark.workflowConfig.cellFunctions.beep": "Horn",
  "lang.ark.fed.notAllowedEditContainer": "Container has already been entered and cannot be edited",
  "lang.ark.trafficControl.manageArea": "Management area",
  "lang.ark.fed.bezier": "Curve",
  "lang.ark.fed.onePickingTitle": '"Received by one AGV: When the material calling task requires multiple feeding points for preparation, the AGV receives and completes the task at the first feeding point before going to the second feeding point, and so on. After the AGV passes all the feeding points, it will deliver the materials to the demand station for material discharging."',
  "lang.ark.fed.excel.data.repeat": "Repeated point in line {0} and line {1}. Please make changes before uploading.",
  "lang.ark.apiNodeActionCode.componentCommandNotSupported": "The component command of node interaction configuration is not supported",
  "lang.ark.operation.workflow.recoveryExecution": "Recovery task",
  "lang.authManage.web.common.editTime": "Editing time",
  "lang.ark.fed.password": "Password",
  "lang.ark.fed.component.workflow.msg.duplicatedMaterialEntryType": "The feed gate type is the same. Please examine relevant device configurations.",
  "lang.authManage.web.common.cancel": "Cancel",
  "lang.mwms.fed.seedManager": "Put wall management",
  "lang.ark.interface.messageName": "Execute interface",
  "lang.ark.workflow.template.validate.templateStartNodeMustUnique": "Template start point must be unique.",
  "lang.ark.fed.changePassword": "Change Password",
  "lang.ark.fed.releaseSuccess": "Published successfully",
  "lang.ark.workflow.ruleConfig": "Rule configuration",
  "lang.ark.fed.taskName": "Task name",
  "lang.ark.base.license.errorLicenseInfoStr": "Failed to pass certificate verification!",
  "lang.ark.fed.theBranchMustStartWithTheMainLineNode": "Branches must start with the main node",
  "lang.ark.fed.select": "Select",
  "lang.wms.station.web.UserAPI.item0305": "Disable",
  "lang.ark.workflow.lastTaskArrived": "Arrival of previous task",
  "lang.ark.warehouse.productLineInfoException": "Production line information does not exist. Please confirm",
  "lang.ark.warehouse.stationBusinessConfigDescription": '"Only valid for the FULL version of workstation, which has a separate entrance. If you need the access address, please contact us"',
  "lang.ark.fed.areanodeidDidNotSelectShelfPoint": "The area {nodeId} did not select a shelf point;",
  "lang.ark.fed.orderStatus": "Document status",
  "lang.ark.fed.menu.moduleInformationConfiguration": "Button model setting",
  "lang.mwms.fed.logistics": "Logistics management",
  "lang.ark.interface.messageBody": "Message",
  "lang.authManage.fed.import": "Import",
  "lang.ark.fed.operateSwitch": "Operation switch",
  "lang.ark.workflow.priorityAllocation": "Priority",
  "lang.ark.workflow.arrive.action.goTurnOfAngle.neg90": "-90°",
  "lang.ark.fed.orderHang": "Suspend",
  "lang.ark.shelfFirCodeErr": "Container type number format is incorrect, initial letter of pallet is P and initial letter of shelf is S",
  "lang.ark.record.rms.sendCommandTask": "Send to rms",
  "lang.ark.fed.period": "Frequency",
  "lang.ark.workflow.action.commandExecutePhase.previousArrived": "Arrival of previous task",
  "lang.common.internalException": "Inner exception of server",
  "lang.ark.record.breakTask": "The original task is interrupted to create a new task",
  "lang.ark.fed.containBinIsNotExists": "The stock bin does not exist",
  "lang.ark.fed.workTriggerManage": "Trigger management",
  "lang.ark.workflow.exceptionHandler.queueUp": "Queue up",
  "lang.ark.workflow.workflowTaskHasArrivedTargetLocation": "The robot has reached the target location and cannot be paused",
  "lang.ark.fed.flowName": "Process name",
  "lang.ark.workflowConfig.cellFunctions.backup": "Back",
  "lang.ark.workflow.task.status.manual.completed": "Done manually",
  "lang.ark.fed.saveFailed": "Save failed",
  "lang.ark.workflowgroup.selector.ok": "Yes",
  "lang.ark.interface.interfaceDesc.english": "English",
  "lang.ark.fed.nowYouCanDrawAMap": "Now you can draw a map",
  "lang.ark.fed.listInformation": "List information:",
  "lang.ark.fed.editingTime": "Editing time",
  "lang.gles.stockInStore.fixedGoodsPositionStock": "Fixed shelf bin inventory",
  "lang.ark.fed.returnShelf": "Return shelves",
  "lang.ark.workflow.notAllowCancel": "Cancellation is not allowed during robot picking and placing operation",
  "lang.gles.baseData.baseDevice": "Device",
  "lang.mwms.monitorRobotMsg3": "The start point or end point of the route planned is obstacle",
  "lang.mwms.monitorRobotMsg4": "Robot or shelf getting in the way",
  "lang.ark.warehouse.singleContainerSide": "One side",
  "lang.mwms.monitorRobotMsg5": "The route is being occupied by other robots",
  "lang.ark.workflow.task.status.executing": "Executing",
  "lang.ark.fed.locationOfShelvesWithXyCoordinateAttributes": "A position on which shelves can be placed, with xy coordinate properties",
  "lang.ark.fed.configEnableTip": "Disable it before modifying the configuration. Please modify during non-working hours",
  "lang.mwms.monitorRobotMsg6": "No path for planning",
  "lang.mwms.monitorRobotMsg7": "Robot unable to be connected",
  "lang.mwms.monitorRobotMsg8": "Timeout for subtask sending",
  "lang.ark.record.clearWaitPoint": "Clear wait points",
  "lang.mwms.monitorRobotMsg9": "Timeout for command sending",
  "lang.ark.fed.confirm": "Enter",
  "lang.ark.robot.Task.pausing": "The process has been paused. Please try again after recovery!",
  "lang.ark.fed.cancelFinish": "Cancellation completed",
  "geekplus.moving.uic.elTableWrapperVue2.column.datetime": "Date and time",
  "lang.ark.workflowgroup.selector.no": "No",
  "lang.ark.workflow.areaLockedEdit": "The traffic control area is locked and cannot be edited",
  "lang.ark.warehouse.importFileFormatError": "File format error. Please download a template to import",
  "lang.ark.fed.front": "Front",
  "lang.ark.fed.rackPositionRecovery": "Shelf position recovery",
  "lang.ark.workflow.area.trafficRange": "Traffic control area",
  "lang.ark.fed.updateAngle": "Update angle",
  "lang.ark.fed.addTaskTrigger": "Add task trigger",
  "lang.ark.fed.manuallyControlArea": "Manual control area",
  "lang.ark.fed.screen.equipmentAssociatedInfo.dialogDetail.labelMaxTaskCont": "Maximum number of physical tasks",
  "lang.ark.api.moving.destIsEmpty": "Task destination location is empty",
  "lang.ark.workflowPeriod.oneDay": "Once every day",
  "lang.ark.fed.cannotDeleteFetchNodesAlone": "Taking a node cannot be deleted separately",
  "lang.ark.interface.apiStationList": "Workstation query",
  "lang.ark.fed.details": "Details",
  "lang.ark.fed.trafficDeleteMsg": '"The operation of the robot in the control area this time is no longer tracked after the removal, and other robots may enter the control area. Confirm to remove it?"',
  "lang.ark.fed.welcomeToUse": "Welcome to use",
  "lang.ark.fed.robotType": "Robot type",
  "lang.ark.interface.apiLastStep": "Task returned",
  "lang.ark.fed.workFlowConfigIsNotModel": "The workflow is not a template workflow",
  "lang.ark.fed.conButtonLogFailed": "Fail ",
  "lang.mwms.monitorRobotMsg1": "Failed to plan the route",
  "lang.mwms.monitorRobotMsg2": "Robot not in map",
  "lang.ark.fed.taskInterVal": "Task interval",
  "lang.mwms.fed.priorityManager": "Priority management",
  "lang.ark.workflow.autoSkipSimple": "Automatically skip",
  "lang.ark.fed.isForbidTrigger": "Do you want to disable this trigger?",
  "lang.ark.fed.rice": "m",
  "lang.ark.fed.workFlowRule": "Circulation rules",
  "lang.ark.warehouse.hasSameStationGoods": "A material cannot be repeatedly added to the same station of the production line",
  "lang.ark.workflow.TimeTrigger": "Automatically triggered after completion",
  "lang.ark.fed.automaticTrigger": "Time trigger",
  "lang.ark.workflowTrigger.logType.controllerButtonLog": "Physical button log",
  "lang.ark.fed.productionLine": "Production line management",
  "lang.auth.RoleAPI.item0007": "Exception in saving data auth: {0}",
  "lang.ark.fed.abnormalCancelTime": "Abnormal cancellation time",
  "lang.ark.fed.production": "Production line",
  "lang.ark.fed.Entrance": "Entrance",
  "lang.auth.RoleAPI.item0008": "roleId parameter cannot be null",
  "lang.auth.RoleAPI.item0005": "Failed to edit the data auth. The role does not exist.",
  "lang.ark.workflowConfig.cellFunctions.palletPackCell": "PALLET_PACK_CELL",
  "lang.auth.RoleAPI.item0006": "Failed to edit the data auth. Other auth cannot be saved as data auth.",
  "lang.ark.fed.interactiveActionNameConfig": "Interaction action configuration",
  "lang.ark.api.workflowWorkIdIsNull": "workflowWorkId cannot be empty",
  "lang.auth.RoleAPI.item0009": "Failed to delete the role. The role does not exist.",
  "lang.auth.RoleAPI.item0003": "Exception in enabling or disabling the role auth: {0}",
  "lang.auth.RoleAPI.item0004": "Exception in enabling the role auth: {0}",
  "lang.auth.RoleAPI.item0001": "Failed to enable or disable the role auth! The parameter is null",
  "lang.ark.fed.common.btn.save": "Save",
  "lang.auth.RoleAPI.item0002": "Exception in enabling or disabling the role auth! The parameter does not conform to standard: status {0}",
  "lang.ark.fed.workstationAndqueueController": "Queuing control",
  "lang.ark.fed.workTaskDetails": "Process task details",
  "lang.mwms.fed.strategyWave": "Wave strategy",
  "lang.ark.record.robotCallback.arrived": "The robot has arrived",
  "lang.authManage.web.others.operation": "Operation",
  "lang.auth.RoleAPI.item0016": "The role in use cannot be deleted",
  "lang.auth.RoleAPI.item0017": "The role (roleId = 1) cannot be edited",
  "lang.auth.RoleAPI.item0010": "Exception in deleting the role: {0}",
  "lang.ark.fed.nodeInteractiveMode": "Device interaction mode",
  "lang.auth.RoleAPI.item0011": "{0} role auth exception. Parameter is null: roleId {1} roleName {2}",
  "lang.auth.RoleAPI.item0014": "{0} role auth exception: Failed to update roleId{1}",
  "lang.auth.RoleAPI.item0015": "Exception in inquiring role data auth: {0}",
  "lang.auth.RoleAPI.item0012": "{0} role auth exception. Multiple records are found: roleId {1} roleName {2}",
  "lang.auth.RoleAPI.item0013": "{0} role auth exception. Parameter id and name do not match with db records",
  "lang.gles.workflow.abnormalTask": "Abnormal task clearing",
  "lang.mwms.fed.strategicCenter": "Strategy center",
  "lang.ark.fed.productionDate": "Production date",
  "lang.ark.fed.ensure": "Enter",
  "lang.ark.fed.rackAngle": "Container angle",
  "lang.gles.strategy.hit": "Allocation Strategy",
  "lang.ark.apiContainerCode.containerCategoryNoMatch": "containerCategory:{0} not matched",
  "lang.ark.fed.templateName": "Template name",
  "lang.wms.station.web.UserAPI.item0306": "Enable",
  "lang.ark.container.containerEntryWay.moving": "Entry by robot",
  "lang.ark.warehouse.noShelf": "No shelf available at the current point",
  "lang.ark.workstationDoesNotExistNode": "{1} docking point does not exist in No. {0} workstation!",
  "lang.ark.fed.alreadyIssued": "Issued",
  "lang.ark.action.interface.referenceValue": "Cite value",
  "lang.ark.fed.taskAbnormal": "Task exception",
  "lang.ark.fed.publish": "Publish",
  "lang.ark.action.interface.internal": "response exceptions",
  "lang.ark.warehouse.supportBusinessDescription": '"Material calling: enabled. The workstation supports selecting a material to initiate a material calling task. Delivery of materials: enabled. The workstation supports manually selecting a material to initiate a delivery task or receiving a material calling task for distribution. Moving: enabled. The workstation supports selecting a process to initiate a moving task. Management mode: enabled. When the management mode is enabled for material calling, delivery of materials and moving, the workstation can call and deliver materials on behalf of the user, and specify any point to initiate a moving task."',
  "lang.ark.workflow.manulSimple": "Manual triggering",
  "lang.ark.fed.conButtonLogDayData": "Day data",
  "lang.ark.fed.manualProcess": "Manual processing",
  "lang.ark.fed.successfulConnectionToTheServer": "Successful connection with the server ",
  "lang.ark.fed.notAllowMergeGet": "The fetch node does not support to be connected to other nodes!",
  "lang.ark.fed.menu.scaffold": "Scaffold",
  "lang.ark.fed.model": "Type",
  "lang.ark.fed.zoneLockReleaseTime": "Area locking/release time",
  "lang.ark.fed.createFlow": "Create workflows",
  "lang.ark.fed.selectPalletPosition": "Select pallet position",
  "lang.ark.fed.waitTime": "Waiting time",
  "lang.ark.fed.firstAddSendPoint": "Please add a delivery node first!",
  "lang.ark.workflow.staging": "Deliver to a temporary storage area",
  "lang.ark.fed.enterCodeOrName": "Enter the material code or material name",
  "lang.ark.button.operation.command.systemEmergencyStop": "System emergency stop",
  "lang.ark.fed.loadingCell": "Feeding station",
  "lang.ark.fed.actionTriggerHandlerError": "Please fill in the trigger timing",
  "lang.ark.fed.pointContainNoStation": "Trigger point/container number/workstation",
  "lang.ark.fed.demandProductionLineOrWorkshop": "Demand production line/workshop",
  "lang.ark.waveStatus.preDistribute": "To be distributed",
  "lang.ark.interface.errorMessage": "Cause of the exception",
  "lang.ark.fed.screen.systemConfig.sysGroupName": "Category marking",
  "lang.ark.fed.materialQuantity": "Number of categories",
  "lang.ark.workflowTriggerType.workflowGroup": "Process group",
  "lang.ark.fed.defaultCacheArea": "Default buffer area",
  "lang.ark.fed.enabled": "Enabled",
  "lang.ark.workflow.recycleAreaIsFull": "Cancellation failed. The recycling area is full. Please try again later!",
  "lang.ark.fed.leaving": "Leaving",
  "lang.ark.fed.screen.workflowInfo.executeAction": "Action",
  "lang.ark.fed.endPoint": "End point",
  "lang.ark.apiContainerCode.locationCodeExistsContainer": "Container already exists for locationCode:{0}",
  "lang.ark.fed.operationDuration": "Operation duration",
  "lang.ark.fed.deliveryInfo": "Distribution information",
  "lang.ark.fed.closeLoop": "Closed-loop process",
  "lang.ark.fed.cancelConfirmMsg3": '"After canceling a process, the process will end and the container will be delivered to the designated recycling area. Confirm to cancel it?"',
  "lang.ark.fed.cancelConfirmMsg2": "Confirm to cancel all tasks under the process instance?",
  "lang.ark.fed.cancelConfirmMsg1": "Please specify the area to which the container is sent after the process is canceled",
  "lang.ark.fed.total": "Total",
  "lang.ark.fed.goodsWaitSend": "To be distributed",
  "lang.ark.workflow.acceptTask": "Receive instructions",
  "lang.mwms.monitorTaskPhaseMsg21": "Arrived at the destination",
  "lang.ark.theRunningTaskIsUsingThisArea": "Edition not allowed. The running task is using this area!",
  "lang.mwms.monitorTaskPhaseMsg22": "The task has been completed",
  "lang.ark.fed.menu.vensManagement": "Device",
  "lang.mwms.monitorTaskPhaseMsg20": "Leave the elevator",
  "lang.ark.workflowTrigger.logType": "Log type",
  "lang.ark.api.requestExists": "Duplicate requestId",
  "lang.ark.workflow.shelfAlreadyExists": "Container already exists",
  "lang.ark.fed.timingTrigger": "Timed triggering",
  "lang.ark.fed.areUSureLocked": "Confirm to lock?",
  "lang.ark.base.license.licenseInfoStrIsNull": "Certificate secret key is null!",
  "lang.ark.task.exception.cellCode.not.null": "Destination location: {} already has the container numbered {}",
  "lang.ark.fed.conButtonLogALLData": "All data",
  "lang.ark.fed.stationPosition": "Station point",
  "lang.ark.fed.containerLevelOne": "First class classification of containers",
  "lang.mwms.monitorTaskPhaseMsg10": "Roller robot arrived at fetching point and got ready for fetching",
  "lang.mwms.monitorTaskPhaseMsg11": "Roller robot arrived at dropping point and got ready for dropping",
  "lang.ark.fed.initiationTime": "Starting time",
  "lang.ark.fed.containerTypeNo": "Container type number",
  "lang.ark.workStatus.complete": "Finished",
  "lang.mwms.monitorTaskPhaseMsg18": "Arrived at the waiting point (outside the elevator). Waiting for entering the elevator",
  "lang.mwms.monitorTaskPhaseMsg19": "Entered the elevator",
  "lang.mwms.monitorTaskPhaseMsg16": "Completed fetching the box",
  "lang.mwms.monitorTaskPhaseMsg17": "The box arrived",
  "lang.ark.fed.executeWorkflowFailed": "No container available. Process initiation failed.",
  "lang.mwms.monitorTaskPhaseMsg14": "Roller robot completed fetching",
  "lang.ark.api.goturn.noTurnParameter": "Fill in either the container rotation parameter neededSides or turnAngle",
  "lang.mwms.monitorTaskPhaseMsg15": "Roller robot completed dropping",
  "lang.ark.fed.excel.deviceMode": "Device interaction mode information error in line {0}. Please make changes before uploading.",
  "lang.ark.fed.inputParameterAssignment": "Value assignment for sent parameter",
  "lang.mwms.monitorTaskPhaseMsg12": "Arrived at the waiting point",
  "lang.mwms.monitorTaskPhaseMsg13": "Left the waiting point",
  "lang.ark.workflow.occupiedByWorkflowGroup": "This workflow has been associated with the workflow group, you cannot delete this workflow or set the first node to manual",
  "lang.ark.workflow.extendRobot": "Forced inheritance of robot",
  "lang.ark.fed.waveTaskStatus": "Wave picking task status",
  "lang.auth.UserAPI.item0001": "For users {0} having logged in, please log in after log out",
  "lang.ark.fed.processDemonstration": "Workflow display",
  "lang.ark.workflow.workflowEditFailed": "The workflow or workflow group in use cannot be edited",
  "lang.ark.fed.pointPositionName": "Point",
  "lang.ark.fed.component.workflow.label.materialEntryType": "Feed gate type",
  "lang.ark.fed.floorStage": "Floor flow strategy",
  "lang.ark.fed.scrollIsNotMatchWithGoods": "Reel number and material information do not match",
  "lang.authManage.web.common.pagePermission": "Page permissions",
  "lang.ark.no.operation": "No-operation",
  "lang.ark.fed.autoClean": "Automatically clear",
  "lang.ark.warehouse.columnCountLimit26": "Each layer has a maximum of 26 columns. Please re-enter the number of columns",
  "lang.ark.fed.component.workflow.edgeName.equipmentTask": "Device flow",
  "lang.ark.fed.stop": "Pause",
  "lang.ark.workflow.area.releaseCountDown": "Automatic release countdown",
  "lang.authManage.fed.screen.login.toModify": "Go to change",
  "lang.ark.trafficControl.taskControlRange": "Traffic control area",
  "lang.ark.workflow.task.status.commandExecuting": "Command in execution",
  "lang.ark.fed.yes": "Yes",
  "lang.ark.fed.language": "Language",
  "lang.ark.fed.screen.hybridRobot.robotOffsetConfig": "Robot point offset configuration",
  "lang.ark.workflowConfig.cellFunctions.transCell": "TRANS_CELL",
  "lang.ark.warehouse.cellCodeNotexits": "Material use point: {0} does not exist",
  "lang.ark.fed.eachRow": "Each line",
  "lang.ark.trafficControl.containerFunction": "Store area function",
  "lang.ark.fed.lagTime": "Retardation time",
  "lang.ark.alreadyExpired": "Current time exceeded the set valid period",
  "lang.ark.workflow.userNoAuthToLogin": "The current user does not have permission to log in to the workstation",
  "lang.ark.fed.createFlowSuccess": "Workflow initiated successfully",
  "lang.ark.workflow.paramValueCode.extraParam15": "extraParam15",
  "lang.ark.action.interface.conditionExtraParam18": "extraParam18",
  "lang.ark.fed.uploadOnly": "upload only",
  "lang.ark.trigger.missedEffectiveTime": "Missed the effective date",
  "lang.ark.workflow.paramValueCode.extraParam14": "extraParam14",
  "lang.ark.fed.noPermissionPage": "This page has no permission",
  "lang.ark.action.interface.conditionExtraParam19": "extraParam19",
  "lang.ark.workflow.paramValueCode.extraParam13": "extraParam13",
  "lang.ark.action.interface.conditionExtraParam16": "extraParam16",
  "lang.ark.workflow.paramValueCode.extraParam12": "extraParam12",
  "lang.ark.action.interface.conditionExtraParam17": "extraParam17",
  "lang.ark.workflow.paramValueCode.extraParam19": "extraParam19",
  "lang.ark.action.interface.conditionExtraParam14": "extraParam14",
  "lang.ark.fed.waitingPoint": "Waiting point",
  "lang.ark.workflow.paramValueCode.extraParam18": "extraParam18",
  "lang.ark.action.interface.conditionExtraParam15": "extraParam15",
  "lang.ark.workflow.paramValueCode.extraParam17": "extraParam17",
  "lang.ark.action.interface.conditionExtraParam12": "extraParam12",
  "lang.ark.workflow.paramValueCode.extraParam16": "extraParam16",
  "lang.ark.action.interface.conditionExtraParam13": "extraParam13",
  "lang.ark.workflowConfig.cellFunctions.chargerCell": "CHARGER_CELL",
  "lang.ark.fed.containerLeave": "Remove",
  "lang.ark.fed.enableSettings": "Enable the setting",
  "lang.ark.interface.interfaceDesc.content": "Field return value",
  "lang.ark.interface.apiRecover": "Workflow recovered",
  "lang.ark.fed.menu.operator": "Operation",
  "lang.ark.fed.sunday": "Sunday",
  "lang.ark.workflow.taskType": "Task type",
  "lang.ark.action.interface.conditionExtraParam20": "extraParam20",
  "lang.ark.workflow.paramValueCode.extraParam20": "extraParam20",
  "lang.ark.fed.triggerType": "Trigger type",
  "lang.common.retry": "Retry",
  "lang.gles.materialArchives": "Material profile",
  "lang.ark.warehouse.binShelfNotFree": '"The shelf of the material is full, and the operation cannot be executed"',
  "lang.ark.fed.sendComplete": "Distributed",
  "lang.ark.fed.siteMonitoring": "Site monitoring",
  "lang.ark.interface.movingMulti": "Point-to-multipoint moving",
  "lang.ark.fed.pleaseSelectAvailableRobot": "Select a robot:",
  "lang.ark.fed.deviceInfo": "Device information",
  "lang.ark.warehouseTask.pickTaskOverTime": "Material requisition sheet not received for {0} minutes",
  "lang.ark.fed.robotNumber": "Robot number",
  "lang.ark.fed.getGoodsTimeout": "Feeding timeout",
  "lang.ark.action.interface.conditionExtraParam10": "extraParam10",
  "lang.ark.fed.moveTitle": "Moving: enabled. The workstation supports selecting a process to initiate a moving task.",
  "lang.ark.action.interface.conditionExtraParam11": "extraParam11",
  "lang.ark.workflow.notAllowFinishIfEmptyLoad": "Direct completion is not allowed in an unloaded state.",
  "lang.authManage.web.common.search": "Query",
  "lang.ark.button.operation.command.addContainer": "Container entry",
  "lang.mwms.fed.putawayRuleDesignatedBin": "Specified fetching point of putaway rules",
  "lang.ark.fed.processProcessGroup": "Workflow/workflow group",
  "lang.ark.fed.insert": "Insert",
  "lang.ark.workflow.acceptUpstreamParam": "Receive upstream parameters",
  "lang.ark.fed.turningSurface": "Turn surface",
  "lang.fed.ark.sure": "Enter",
  "lang.ark.fed.taskOver": "Withdraw",
  "lang.mwms.fed.queryTask": "Task query",
  "lang.ark.fed.pleaseSelectShelf": "Please select a shelf!",
  "lang.ark.fed.shelfAttribute.GMSInterfaceField": "GMS interface field",
  "lang.ark.fed.editInterface": "Edit interface command",
  "lang.ark.fed.completionOfDrawing": "Complete the drawing",
  "lang.ark.apiCommonCode.systemRecoverFailed": "System recovery failed",
  "lang.mwms.monitorTaskPhaseMsg9": "Charging",
  "lang.mwms.monitorTaskPhaseMsg8": "Moving",
  "lang.mwms.monitorTaskPhaseMsg7": "Rotate shelf",
  "lang.authManage.web.common.abled": "Enable",
  "lang.mwms.monitorTaskPhaseMsg2": "Shelf is obtained",
  "lang.mwms.monitorTaskPhaseMsg1": "Fetching shelves",
  "lang.mwms.fed.putawayRuleDesignatedArea": "Specified area of putaway rules",
  "lang.mwms.monitorTaskPhaseMsg6": "Returning shelf",
  "lang.mwms.monitorRobotMsg.noshelf": "No shelf available ",
  "lang.mwms.monitorTaskPhaseMsg5": "Shelf arrived at destination",
  "lang.mwms.monitorTaskPhaseMsg4": "Queuing",
  "lang.mwms.monitorTaskPhaseMsg3": "Moving shelf",
  "lang.ark.interface.interfaceDesc.desc": "Field description",
  "lang.ark.workflow.template.type.nodeToMultiNode": "Point-to-multipoint task",
  "lang.ark.sys.config.group.common": "Basic configuration",
  "lang.gles.StockInTransit": "Inventory in transit",
  "lang.ark.fed.publishAndSave": "Publish and save",
  "lang.ark.fed.refreshCycle": "Refresh frequency",
  "lang.ark.fed.operationSymbol": "Operator",
  "lang.mwms.fed.stationManager": "Workstation management",
  "lang.ark.fed.targetPointType": "Target point type",
  "lang.ark.workflow.canAddContainerFlag": "Add a container",
  "lang.ark.fed.departure": "Remove",
  "lang.ark.pda.function.area.lock": "Area locking",
  "lang.ark.apiStationCode.stationQueueAlreadyDisable": '"Queuing control at workstation is already disabled, so it cannot be disabled again"',
  "lang.ark.fed.paramCode": "Parameter coding",
  "lang.ark.interface.apiContainerList": "Container query",
  "lang.ark.fed.moduleInformationConfiguration": "Module information configuration",
  "lang.ark.fed.singleFactorySingleEntrances": "Single manufacturer with a single entrance",
  "lang.ark.fed.shelfCondition": "Shelf condition",
  "lang.ark.fed.basicInformation": "Basic information",
  "lang.ark.workflow.ruleDecision": "Rule judgment",
  "lang.ark.fed.unlock": "Unlock",
  "lang.ark.fed.cloaseAll": "Disable all",
  "lang.ark.fed.containerCodeAutoIncrement": "Auto increment of multiple container codes",
  "lang.ark.containerNotAtTheTriggerPoint": '"Current position of container {0} is {1}, not actuation point {2}!"',
  "lang.ark.fed.palletBitNode": "Pallet position node",
  "lang.ark.fed.priorityGoUp": "Priority rise",
  "lang.ark.fed.flowNodeConfig": "Interaction configuration",
  "lang.ark.systemParamCannotDelete": "System default, and cannot be deleted",
  "lang.ark.fed.rotateRight": "Clockwise",
  "lang.ark.container.shelfTypeNotExit": "Secondary classification of container does not exist",
  "lang.gles.logisticsConfig": "Logistics configuration",
  "lang.ark.fed.dischargeLoadMore": "Load after release",
  "lang.gles.baseData.area": "Area",
  "lang.common.abort": "Suspend",
  "lang.ark.fed.workstationManagement": "Workstation management ",
  "lang.ark.fed.entryEndPoint": "End point of entry",
  "lang.ark.interface.messageInstruction": "Execute command",
  "lang.ark.fed.specialAreaSavedSuccessfully": "Special area",
  "lang.ark.fed.orderCancel": "Cancel",
  "lang.ark.fed.menu.replacementMaterial": "replacementMaterial",
  "lang.ark.fed.userHelpDocumentDownload": "Users help document download",
  "lang.ark.record.robotCallback.leaveStart": "The robot leaves",
  "lang.ark.fed.conButtonLogSuccess": "Success",
  "lang.ark.fed.currentContainer": "Current container",
  "lang.mwms.fed.inventoryCharts": "Warehouse report",
  "lang.ark.fed.GoodsTask": "Material receiving task",
  "lang.ark.fed.actionsNotAllowToUp": "The {0} node has an interaction configuration error. Jacking up is not allowed!",
  "lang.ark.fed.changeStopPoint": "Switch to another docking point",
  "lang.ark.fed.theDeliveryTime": "Distribution time",
  "lang.ark.fed.orderOccupy": "First occupy first pass",
  "lang.ark.containerAlreadyExists": "Container already exists!",
  "lang.ark.fed.workflowList": "Workflow list",
  "lang.ark.workflow.paramValueCode.extraParam11": "extraParam11",
  "lang.ark.workflow.paramValueCode.extraParam10": "extraParam10",
  "lang.ark.record.robotCallback.arriveWaitPoint": "The robot has arrived waiting point",
  "lang.auth.Audit.item0010": "Disable role: {0}",
  "lang.auth.Audit.item0012": "Set role permissions: {0}",
  "lang.auth.Audit.item0011": "Enable role: {0}",
  "lang.ark.fed.shelfAttribute.upstreamInterfaceField": "Upstream interface field",
  "lang.ark.fed.taskId": "Task number",
  "lang.auth.Audit.item0013": "Delete role: {0}",
  "lang.ark.workflowTrigger.logType.triggerExeLog": "Trigger log",
  "lang.ark.fed.pleaseChooseShelfTypeOrShelfCode": "Please select shelf type or shelf code",
  "lang.ark.workflow.shelfNotExists": "Container does not exist",
  "lang.ark.interface.request": "Request",
  "lang.ark.fed.robotsCanBeScheduledByTheSystemDuringChargingElectricityMust": "The robot in the workflow of charging (the power must be greater than 5%) can be scheduled by the system",
  "lang.ark.fed.breakAndWait": "Interrupt waiting",
  "lang.ark.task.exception.work.not.null": "Destination location: {} already has the occupied task instance {}",
  "lang.ark.fed.areaMustContainNodes": "Failed to save! The area must contain at least one point!",
  "lang.ark.workflow.workflowUndoing": "Cancellation failed. The process is being canceled",
  "lang.ark.fed.raceway": "Roller table",
  "lang.ark.fed.selectionRobot": "Select a robot",
  "lang.ark.fed.specialArea": "Special area",
  "lang.mwms.monitorRobotMsg14004": "Driver under voltage",
  "lang.ark.workflow.noAvailableStopPointBeginNode": "No available starting points were found",
  "lang.mwms.monitorRobotMsg14000": "Shelf location to be confirmed",
  "lang.ark.workflow.condition.greaterThanOrEqual": "Greater than or equal to",
  "lang.gles.receipt.receiptWarehousingOrder": "Putaway order",
  "lang.ark.fed.target": "Destination",
  "lang.ark.fed.minute": "Minute",
  "lang.ark.fed.menu.containerManage": "Container management",
  "lang.ark.fed.addComponentInterface": "Add component command",
  "lang.ark.api.template.startNodeNotMatchForWave": "The specified start point {0} does not match the template start point {1}",
  "lang.ark.element.no.element.selected": "No selected elements",
  "lang.gles.receipt.outWarehouseOrder": "Outbound order",
  "lang.ark.trafficControl.enterStrategy.byTime": "First come first pass",
  "lang.ark.workflowTrigger.logType.containerChangeLog": "Container log",
  "lang.ark.fed.canDeleteshelfFlag": "Remove the shelves",
  "lang.ark.fed.saveCurCustomConfig": "Save the current customization configuration",
  "lang.ark.loadCarrier.loadCarrierModelNotExist": "The container model does not exist.",
  "lang.ark.fed.tow": "Traction",
  "lang.authManage.web.common.input": "Please enter",
  "lang.ark.api.nodesNeedConfigureAction": "Interactive configuration matching the node not found",
  "lang.ark.fed.wholeTriggeWorkflow": "Overall trigger workflow",
  "lang.ark.plugin.pluginType.returnContainer.way.empty": "Deliver empty container",
  "lang.ark.fed.addExtendDevice": "Add external equipment",
  "lang.ark.fed.viewMap": "View map",
  "lang.ark.fed.shelfArriveOrientation": "Shelves demand orientation",
  "lang.ark.sys.config.group.switch": "System switch",
  "lang.ark.button.operation.command.systemRecover": "System recovery",
  "lang.ark.fed.implement": "Execute",
  "lang.ark.workStatus.create": "Create",
  "lang.ark.fed.screen.hybridRobot.installEquipment": "Upper structure device",
  "lang.ark.fed.hybridRobot.hybridRobotType.doubleLift": "Double lifting",
  "lang.ark.fed.carryOutTheTask": "Execute task",
  "lang.ark.auth.userHaveLoginOtherPlace": "The current user has logged in elsewhere!",
  "lang.ark.fed.waitLockSureCancel": "Waiting to be locked. Confirm to cancel?",
  "lang.ark.fed.right": "Right",
  "lang.ark.fed.processControl": "Workflow control",
  "lang.ark.fed.driving": "Active distribution",
  "lang.ark.fed.goodsNum": "Material code",
  "lang.ark.fed.twoDimensionalCodeFlowManagement": "QR code workflow management",
  "lang.ark.fed.waitCodeType": "Waiting for command",
  "lang.ark.workflow.currentOperateIsHappening": "Do not operate repeatedly!",
  "lang.ark.warehouse.workstationPointIsMuchForWave": "A workstation cannot have multiple points in the wave picking business mode",
  "lang.ark.fed.renderingFlowChart": "Rendering flowchart",
  "lang.ark.fed.collect": "Collection",
  "lang.ark.trafficControl.enterPattern": "One-by-one pass/continuous pass",
  "lang.ark.fed.currentStopPointStatus": "Current node state",
  "lang.ark.fed.screen.workflowInfo.robotTaskId": "Robot Task Id",
  "lang.ark.fed.moreOperations": "More operations",
  "lang.ark.fed.sureWantExecute": "Confirm to execute?",
  "lang.ark.fed.wfTaskNum": "Material requisition sheet number",
  "lang.ark.fed.taskManagementMsg0": "Task monitoring: Whether the following buttons are displayed on the task monitoring page; whether the deletion button initialization is selected;? whether the cancellation and return button initialization is selected",
  "lang.ark.area.areaAlreadyLock": "The area has been locked!",
  "lang.ark.interface.interfaceDesc.targetName": "Upstream field name",
  "lang.ark.action.interface.locationFrom": "locationFrom",
  "lang.ark.fed.arriveOrientation": "Shelf orientation",
  "lang.ark.fed.fieldInform": "Site information",
  "lang.ark.fed.eraseNoise": "Erase noise",
  "lang.ark.fed.recoveryToTargetSucceedStatus": "Return to the target success state",
  "lang.ark.action.interface.extraParam1": "extraParam1",
  "lang.ark.fed.orderPass": "First come first pass",
  "lang.ark.action.interface.extraParam2": "extraParam2",
  "lang.ark.action.interface.extraParam3": "extraParam3",
  "lang.ark.action.interface.extraParam4": "extraParam4",
  "lang.ark.action.interface.extraParam5": "extraParam5",
  "lang.ark.fed.robotWillArrive": "Shortest distance: {0}",
  "lang.ark.fed.demandForMaterials": "Material calling on demand",
  "lang.ark.workflowTriggerStatus.enable": "Enable",
  "lang.ark.auth.userHaveLoginCurrentPlace": "The current user has logged in here!",
  "lang.ark.fed.workflowEncoding": "Workflow coding",
  "lang.ark.apiRobotTaskCode.waitPointExecutingTaskNotOnlyOne": "More than one task being executed at the waiting point",
  "lang.ark.workflow.criteria": "Conditions",
  "lang.authManage.web.common.makeSure": "OK",
  "lang.ark.trafficControl.enterPattern.singlePass": "One-by-one pass",
  "lang.ark.fed.menu.taskExeRecord": "Task execution log",
  "lang.ark.workflow.allType": "All",
  "lang.ark.fed.orderWaitSend": "To be distributed",
  "lang.ark.fed.timingCharging": "Timed charging",
  "lang.ark.fed.uploadFileLimit3M": '"Only files in mp3, wma, wav and amr formats can be uploaded, and the size of a single file cannot exceed"',
  "lang.ark.workflow.chooseStrategy.cancel": "Cancel operation ",
  "lang.ark.fed.technicalSupport": "Technical Support",
  "lang.authManage.web.common.differentPassword": "The passwords entered twice are inconsistent!",
  "lang.ark.trafficControl.enterType.singleFactoryMultiEnter": "Single manufacturer with multiple entrances",
  "lang.ark.fed.loopSetup": "Cycle setting",
  "lang.auth.Audit.item0001": "User: {0} logs into the system.",
  "lang.auth.Audit.item0003": "Add user: {0}",
  "lang.ark.fed.isSureStartFlow": "Confirm to initiate the workflow?",
  "lang.auth.Audit.item0002": "User: {0} logs out of the system.",
  "lang.auth.Audit.item0005": "Enable user: {0}",
  "lang.ark.action.interface.extraParam6": "extraParam6",
  "lang.auth.Audit.item0004": "Disable user: {0}",
  "lang.ark.action.interface.extraParam7": "extraParam7",
  "lang.ark.fed.pickingMethod": "Receiving method",
  "lang.auth.Audit.item0007": "Modify user: {0}",
  "lang.ark.action.interface.extraParam8": "extraParam8",
  "lang.auth.Audit.item0006": "Delete user: {0}",
  "lang.ark.action.interface.extraParam9": "extraParam9",
  "lang.auth.Audit.item0009": "Bulk alter roles: {0} to be associated with user permissions.",
  "lang.auth.Audit.item0008": "Change user password: {0}",
  "lang.ark.fed.createType": "Generation mode",
  "lang.ark.workflowConfig.cellFunctions.stationCell": "STATION_CELL",
  "lang.ark.fed.componentInterface": "Component command",
  "lang.ark.noContainerAvailableAtCurrentPoint": "No available container at current point!",
  "lang.ark.fed.pleaseSelect": "Please select",
  "lang.ark.fed.outerData": "External data",
  "lang.ark.fed.afterArrivingHere": "Arrive here",
  "lang.ark.fed.shelfAttribute.SSR": "SSR",
  "lang.mwms.fed.innerFreezes": "In-warehouse freezing",
  "lang.ark.warehouse.containerWorkingEdit": '"There is a task or available inventory, and the stock bin cannot be edited"',
  "lang.ark.apiCommonCode.notMasterServer!": "The server currently being requested is not the main server!",
  "lang.ark.fed.currentRackInformation": "Current shelf information",
  "lang.ark.fed.changePointPosition": "Switch point",
  "lang.ark.fed.conButtonLogExecuteTime": "Execution time",
  "lang.ark.externalDevice.device_own_type_desc": '"When selecting robot components, the robot components need to be maintained on the robot components management page!"',
  "lang.ark.fed.binUsed": "In transit (container in operation)",
  "lang.ark.warehouse.theMatrialPointExistsAny": "There are multiple wave pickings for production line distribution. Please cancel the excess wave pickings",
  "lang.ark.fed.currentStatus": "Current status",
  "lang.ark.fed.workflowNode": "Workflow nodes",
  "lang.ark.fed.screen.workflowInfo.responseParamDetail": "Response Message",
  "lang.ark.fed.outer": "External interface",
  "lang.ark.fed.waitPoint": "Waiting point",
  "lang.ark.apiCommonCode.robotRecoverFailed": "Robot recovery failed",
  "lang.ark.fed.menu.exceptionHandling": "Robot troubleshooting",
  "lang.ark.workflow.arriveOrientation": "Shelf demand orientation",
  "lang.ark.fed.movingShelvesToWorkstations": "Move shelves to workstations",
  "lang.ark.fed.teakDetail": "Task details",
  "lang.ark.fed.shelfAttribute.PNAE": "PNAE",
  "lang.ark.fed.maximumSpeed": "Maximum speed",
  "lang.ark.fed.english": "English",
  "lang.ark.fed.unconnectedNodeExist": "Operation failed. Unconnected node exists!",
  "lang.ark.fed.pickUpTheTask": "Receive task",
  "lang.ark.fed.parameterGrouping": "Parameter grouping",
  "lang.gles.baseData.baseGoodsPosition": "Shelf bin",
  "lang.mwms.fed.wcs": "Control",
  "lang.ark.fed.targetPoint": "Destination location",
  "lang.ark.fed.areaName": "Area name",
  "lang.ark.workflow.task.status.node.undoing": "Withdrawing",
  "lang.ark.fed.taskStage": "Task stage",
  "lang.ark.fed.default": "Default",
  "lang.ark.workflow.task.status.moving": "Robot on the way",
  "lang.ark.workflow.manulChoice": "Manually select subsequent tasks",
  "lang.ark.fed.automatic": "Automation",
  "lang.ark.deliverOrder.positiveSequence": "Forward order of production line processes",
  "lang.ark.apiRobotTaskCode.robotTaskIdNotEmpty": "Robot task ID cannot be empty",
  "lang.ark.fed.screen.systemConfig.commonGroup": "System configuration",
  "lang.ark.fed.openAll": "Enable all",
  "lang.ark.fed.condition": "Conditions",
  "lang.ark.fed.pullDownTheCargoPosition": "Normal/locked/task occupation",
  "lang.ark.fed.speed": "Speed",
  "lang.ark.fed.oneway": "One-way",
  "lang.ark.fed.passingPoint": "Passing point",
  "lang.ark.workflow.autoReleaseFactoryNullError": "The manufacturer that automatically releases robot is empty. Please fill in the manufacturer",
  "lang.ark.fed.networkTimeout": "Network timeout",
  "lang.ark.fed.backgroundMapEditing": "Background image editing",
  "lang.ark.fed.arrivelExternalInteraction": "External interaction upon arrival",
  "lang.ark.fed.isStopAllQueue": "Do you want to disable queuing for all",
  "lang.ark.robot.classfy.forklift": "Forklift",
  "lang.ark.workflow.queue.noAvailableShelf": "No shelf points available ",
  "lang.ark.warehouse.shelfDifferent": "The current shelf at the point is inconsistent with the shelf when requiring material",
  "lang.ark.fed.theWorkstationnodeidDidNotSelectAStopPoint": "The workstation {nodeId} did not select a docking point;",
  "lang.ark.button.operation.command.removeContainer": "Container exit",
  "lang.ark.fed.screen.flowNodeConfig.checkMsg.rule": "Saving failed. Please select instruction rules.",
  "lang.ark.fed.takeAway": "Withdraw",
  "lang.ark.interface.apiAddContainer": "Container Entry",
  "lang.ark.interface.apiPriorityAdjust": "Workflow instance priority adjusted",
  "lang.ark.fed.pleaseSelectTheAlarmLightPortNumber": "Please select the port number of the alarm light",
  "lang.ark.apiStationCode.stationNotExists": "Workstation does not exist",
  "lang.ark.fed.noPointToDeleteWasSelected": "No points to be deleted are selected",
  "lang.mwms.monitorRobotMsg.config": "Process configuration exception. Please check process configuration.",
  "lang.ark.fed.offsetInstructionMustSpecify": "A component must be specified for the offset command",
  "lang.ark.warehouse.waveStatusCannotbyCancel": "Failed to execute cancelled wave picking",
  "lang.ark.interface.businessCode": "Response code",
  "lang.ark.fed.excel.cellNotExist": "Point in line {0} does not exist in the system. Please make changes before uploading.",
  "lang.ark.fed.containerBinFree": "Normal",
  "lang.ark.fed.hoursLater": "In? hours",
  "lang.ark.fed.taskRunDetails": "Task execution details",
  "lang.ark.fed.sendMaterial": "Delivery destination",
  "lang.ark.warehouse.taskHasExecute": "The task has been executed. Please do not execute it repeatedly",
  "lang.ark.fed.overAlarmType": "Early warning type",
  "lang.gles.StockInStore": "Inventory in warehouse",
  "lang.ark.fed.switchToSuccess": "Switch to {val} successfully",
  "lang.authManage.web.common.disabled": "Disable",
  "lang.ark.workflowTriggerMonitorStatus.executing": "Executing",
  "lang.ark.action.interface.boolean": "bool",
  "lang.ark.fed.flowCategory": "Workflow category",
  "lang.ark.warehouse.deliveryStationLine": "Distribution station",
  "lang.ark.action.interface.taskCode": "taskCode",
  "lang.mwms.monitorRobotMsg21100": "Component receipt exception",
  "lang.ark.fed.configCustomParams": "Configure custom parameters",
  "lang.ark.workflow.targetNode": "Destination location",
  "lang.ark.fed.back": "Back",
  "lang.ark.workflow.task.status.completed": "Finished",
  "lang.ark.fed.load": "Load",
  "lang.ark.workflow.condition.lessThan": "Less than",
  "lang.ark.button.operation.command.start": "Start",
  "lang.ark.fed.multiplefactoryMultipleEntrances": "Multiple manufacturers with a single entrance",
  "lang.ark.fed.pleaseSelectTheParentNode": "Please select the parent node!",
  "lang.ark.fed.noInstructUnderTheAction": "No commands under this interactive action",
  "lang.ark.fed.interfaceInteraction": "Interface interaction",
  "lang.gles.materialClassify": "Material class",
  "lang.ark.workflow.task.status.wait": "Wait",
  "lang.ark.workflow.paramValueCode.extraParam8": "extraParam8",
  "lang.mwms.monitorRobotMsg21111": "Positioning lost",
  "lang.ark.workflow.paramValueCode.extraParam7": "extraParam7",
  "lang.mwms.monitorRobotMsg21110": "Weighing overload or unbalanced load overrun",
  "lang.auth.UserAPI.item1271": "Enable",
  "lang.ark.fed.threeLight": "Three-color indicator",
  "lang.ark.fed.locationOfRobotsWaitingToReceiveNewTasks": "A position that the robot can dock while waiting to receive a new task",
  "lang.ark.workflow.paramValueCode.extraParam9": "extraParam9",
  "lang.auth.UserAPI.item1270": "Disable",
  "lang.ark.workflow.paramValueCode.extraParam4": "extraParam4",
  "lang.ark.workflow.paramValueCode.extraParam3": "extraParam3",
  "lang.ark.workflow.paramValueCode.extraParam6": "extraParam6",
  "lang.ark.workflow.paramValueCode.extraParam5": "extraParam5",
  "lang.ark.workflow.paramValueCode.extraParam2": "extraParam2",
  "lang.ark.workflow.paramValueCode.extraParam1": "extraParam1",
  "lang.mwms.monitorRobotMsg21109": "Dead battery",
  "lang.ark.workflow.wareHouseSupportBusiness": "Business",
  "lang.ark.task.log.export.title.endNode.name": "Terminal point name",
  "lang.mwms.monitorRobotMsg21104": "Fisheye camera data lost",
  "lang.mwms.monitorRobotMsg21103": "Depth camera data lost",
  "lang.ark.fed.locking": "Lock",
  "lang.mwms.monitorRobotMsg21102": "Component failure",
  "lang.ark.auth.userHaveLoginOtherStation": "The current user {0} has logged in at No. {1} workstation!",
  "lang.mwms.monitorRobotMsg21101": "Component communication interrupted",
  "lang.ark.workflow.notFirstStation": '"When the workstation is in the active distribution mode, only the first node workstation can initiate a process"',
  "lang.ark.fed.makeADetour": "Bypass",
  "lang.mwms.monitorRobotMsg21108": "Press to release the band-type brake",
  "lang.ark.fed.logicAnd": "+ and operator (&)",
  "lang.mwms.monitorRobotMsg21107": "STO triggered",
  "lang.mwms.monitorRobotMsg21106": '"Driver data is missing, or device failure"',
  "lang.ark.fed.receiveGoodsNumLessThanRemain": "Stock bin {0}: up to {1} can be stocked in",
  "lang.mwms.monitorRobotMsg21105": "Network interrupted",
  "lang.ark.workflow.workflowtypeNameOrCodeRepeat": "Duplicate process classification name or code",
  "lang.ark.fed.mesInterfaceError": "MES interface return exception, exception message: {0}",
  "lang.mwms.fed.viewSet": "Page configuration",
  "lang.mwms.fed.inWarehouse": "Stock in daily report",
  "lang.ark.hitStrategy.stack": "Last-in First-out",
  "lang.ark.fed.detail": "Details",
  "lang.ark.fed.closeAllTabs": "Close all",
  "lang.ark.workflow.action.command.robot.FBShift": "Front and rear offset",
  "lang.ark.fed.morePicking": "Received by multiple AGVs",
  "lang.ark.fed.column": "Column",
  "lang.ark.fed.orderSourceNum": "Original order number",
  "lang.ark.fed.noRobotsHaveBeenAssignedYetPleaseClickToAdd": "No robots have been distributed, please click to add!",
  "lang.ark.fed.ruleConfig": "Rule configuration",
  "geekplus.moving.uic.elTableWrapperVue2.actionBtn.confirmPopup.btnCancel": "Cancel",
  "lang.ark.fed.prompt": "Prompt",
  "lang.mwms.fed.supplier": "Supplier management",
  "lang.ark.warehouse.estimateUseTimeUnit.minute": "Minute",
  "lang.ark.fed.exportFile": "Downloading",
  "lang.ark.fed.selectRobot": "Select a robot",
  "lang.ark.fed.workflowId": "Workflow number:",
  "lang.ark.warehouse.wave": "Wave picking",
  "lang.ark.record.task.over": "Task completed",
  "lang.ark.stationCodeExist": "Workstation code can’t be repeated",
  "lang.ark.fed.arriveEndCellCodeTime": "Time of arrival at node",
  "lang.ark.workflow.template.validate.equipmentNodeNotFoundData": "Device node fails to find corresponding data. Please examine device data.",
  "lang.ark.fed.occupied": "Whether the device is occupied!",
  "lang.ark.fed.releaseFull": "Release",
  "lang.ark.fed.numberOfButtons": "Number of buttons",
  "lang.ark.fed.screen.hybridRobot.pointInfo": "Point information",
  "lang.ark.workflow.areaNotHaveIdlePoint": "There are no available locations in area {}.",
  "lang.ark.base.license.exceptionForLicenseValidating": "Certificate verification exception!",
  "lang.ark.fed.cancelWait": "Cancel waiting",
  "lang.ark.fed.selectAll": "Select all",
  "lang.ark.fed.missionEndpoint": "Task end point",
  "lang.ark.trafficControl.stopFunction": "Emergency stop area function",
  "lang.ark.fed.pleaseSaveEditTable": "Please check whether there is any empty or unsaved data in the current list",
  "lang.ark.fed.menu.heartbeat": "Heartbeat management",
  "lang.ark.fed.facilityType": "Facility type:",
  "lang.ark.fed.screen.area.containGroup": "Include grouping",
  "lang.mwms.fed.operate": "Map operation",
  "lang.ark.fed.dashboardSetting": "Display board settings",
  "lang.ark.taskRecord.param.notblank": "Task number cannot be empty",
  "lang.ark.operation.workflow.pauseWorkflow": "Recovery task",
  "lang.ark.workflow.paramValueCode.export": "export",
  "lang.ark.fed.cancellationProcess": "Cancel process",
  "lang.ark.fed.conButtonLogSocketPort": "Port number",
  "lang.ark.pda.function.transport.task": "Moving task",
  "lang.mwms.fed.package": "Package",
  "lang.ark.apiCommonCode.systemStopFailed": "System emergency stop failed",
  "lang.ark.interface.interfaceDesc.phaseTypeDesc": "Description: Format of parameter value returned to the upstream when the field value is an enumerated value. For example, the taskPhase field has several enumerated value states, and the upstream might need to return 10, 20, and 30 to correspond to different states, or return English to correspond to different states CREATE, EXECUTION, and COMPLETED. This setting is valid for all enumerated value fields of this interface.",
  "lang.ark.trafficControl.queueFunction": "Queuing area function",
  "lang.ark.fed.appointMentLock": "Reserved locking",
  "lang.ark.fed.executeDetail": "Execution details",
  "lang.ark.fed.twoway": "Two-way",
  "lang.ark.fed.stageOfCompletion": "Completion",
  "lang.ark.fed.ForkliftTray": "Forklift pallet",
  "lang.ark.fed.jackUpLayDown": "Jack up/down",
  "lang.ark.fed.screen.flowTemplate.specialNode": "Special point",
  "lang.ark.workflow.notSupportAgvClass": "Unsupported robot type",
  "lang.ark.interface.interfaceDesc.detail": "Interface details",
  "lang.ark.interface.callbackAddress": "Callback address",
  "lang.ark.fed.flowCreate": "Process creation",
  "lang.ark.fed.editMap": "Map Editing",
  "lang.ark.fed.addProcess": "Add workflow",
  "lang.mwms.fed.userManage": "User management",
  "lang.ark.fed.zoom": "Zoom",
  "lang.ark.workflow.template.validate.templateCannotSelectBranchOrderNode": "The dynamic template cannot select branch nodes based on the wiring method",
  "lang.ark.fed.className": "Category name",
  "lang.ark.executedNum": "Executed times:",
  "lang.ark.waveTaskStatus.cancel": "Cancel",
  "lang.gles.receipt.tallyList.tallyList": "Tally order",
  "lang.ark.fed.loadGoods": "Feeding",
  "lang.ark.warehouse.shelfNotFree": "No free shelf at the current point",
  "lang.ark.fed.triggerOtherSettings": "Trigger other settings",
  "lang.ark.fed.robotLessQueue": "There is a task queue when the robot is insufficient",
  "lang.ark.warehouse.hasSameProductionLineCode": "The production line code already exists",
  "lang.gles.logisticsConfig.workTemplate": "Operation template",
  "lang.ark.fed.goodsSend": "Distributing",
  "lang.ark.fed.userAndRole": "User/Role",
  "lang.authManage.web.others.addNewIp": "Add IP and Port",
  "lang.authManage.fed.effectiveDate": "Start date",
  "lang.ark.fed.robotManagement": "Robot management",
  "lang.ark.fed.clearLock": "Unlock",
  "lang.ark.fed.typeMustBeDockPointWorkstationRackPointArea": "Types must be: docking points, workstations, shelf points, areas",
  "lang.ark.task.log.export.title.start.time": "Start time",
  "lang.ark.fed.taskLog": "Log",
  "lang.ark.fed.menu.hybridRobot": "Cobot configuration",
  "lang.ark.workflow.task.status.ready": "Ready",
  "lang.ark.fed.east": "East",
  "lang.mwms.monitorRobotMsg91001": "Emergency stop button disconnected",
  "lang.ark.fed.confirmCharging": "Are you sure to charge?",
  "lang.ark.fed.homeViewUpload": "Home page upload",
  "lang.mwms.monitorRobotMsg91000": "Emergency stop button triggered",
  "lang.ark.interface.interfaceType": "Interface type",
  "lang.ark.fed.loadingPoint": "Feeding point",
  "lang.ark.fed.screen.flowNodeConfig.executeByCondition": "Execute after judgment based on conditions",
  "lang.ark.fed.serialLessMount": "The quantity of materials cannot be less than 0",
  "lang.ark.fed.orderPassPriority": "First come first pass (priority first)",
  "lang.ark.fed.nomsgIsAvailableDoYouWantToContinueGeneratingTasks": "No {msg} available, do you want to continue generating tasks?",
  "lang.ark.fed.callStation": "Material calling workstation",
  "lang.ark.fed.purpose": "Usage",
  "lang.ark.fed.returnDistance": "Return",
  "lang.ark.fed.everyMonth": "{0} monthly",
  "lang.ark.interface.requestSource": "Request body",
  "lang.ark.fed.taskBoardWaringSetting": "Kanban Alert Setting",
  "lang.mwms.fed.skuOutInConfig": "Materials stock in/out configuration",
  "lang.ark.fed.pleaseEnter": "Please enter",
  "lang.mwms.fed.printSet": "Print settings",
  "lang.ark.workflow.recycleTypeNoSupportManual": "Manual selection of return location upon cancellation is not supported in the interface cancellation process",
  "lang.ark.fed.revokeButton": "Revoke button",
  "lang.ark.action.interface.paramName": "Parameter name",
  "lang.ark.fed.generationTime": "Generation time",
  "lang.ark.fed.triggerTiming": "Trigger timing",
  "lang.ark.action.interface.assignType": "Assignment mode",
  "lang.ark.logType.waitPointTaskLog": "Task log of waiting point",
  "lang.ark.hand.push": "Push manually to",
  "lang.ark.singleCellStation.canNotDelete": "Failed to delete. Data can be synchronized after deletion in map edit",
  "lang.ark.fed.pleaseSelectRobot": "Please select robot",
  "lang.ark.fed.selected.cellCode": '"Select point position, area, grouping, container code"',
  "lang.ark.loadCarrier.loadCarrierParamsErr": "Container parameter encountered error.",
  "lang.mwms.rf.outbound": "Stock out",
  "lang.ark.fed.batteryCurrent": "Battery current",
  "lang.ark.workflowConfig.nodeActionDoNotExists": "Nodes not associated with interactions",
  "lang.ark.fed.includePoints": "Contained points",
  "lang.ark.fed.isEnabledTrigger": "Do you want to enable this trigger?",
  "lang.ark.fed.narrow": "Zoom out",
  "lang.ark.fed.allowLiftUp": "{0}A lifting command can be configured only once",
  "lang.ark.fed.abnormalComplete": "Completed abnormally",
  "lang.ark.workflow.wareHouseWorkflowType": "Process type",
  "lang.ark.fed.orderReAssign": "Reallocate",
  "lang.ark.fed.thereIsNoWorkflowAtThisDockPointPosition": "No process at this point",
  "lang.ark.mechanical.arm.place": "Placed by mechanical arm",
  "lang.authManage.fed.screen.login.pwdExpireTipTitle": "Password expiry reminder",
  "lang.ark.fed.abnormalCancel": "Abnormal cancellation",
  "lang.ark.fed.oneClickExecution": "Oneclickgo",
  "lang.ark.fed.robotCode": "Robot coding",
  "lang.ark.workflowCode.exists": "The workflow coding cannot be repeated",
  "lang.mwms.fed.stationControl": "Workstation monitoring",
  "lang.ark.station.rollerStation": "Roller station",
  "lang.ark.fed.extraParam18": "extraParam18",
  "lang.ark.fed.extraParam17": "extraParam17",
  "lang.ark.fed.extraParam19": "extraParam19",
  "lang.ark.hitStrategy.default": "Default strategy",
  "lang.ark.fed.extraParam20": "extraParam20",
  "lang.ark.workflow.invalidWorkflowConfig": "Invalid workflow configuration",
  "lang.ark.fed.deleteWaveRow": "Confirm to delete the strategy?",
  "lang.ark.fed.notAllow": "Unselected",
  "lang.ark.taskStatusCannotOperate": "The task is in {0} status and no operation allowed!",
  "lang.ark.fed.unloadOrderDesc": "Material discharging for the whole order: material discharging in the workstation requires all materials off shelf by default, without strict requirements on the materials and stock bins.",
  "lang.ark.plugin.pluginType.returnContainer.way.full": "Deliver full container",
  "lang.ark.fed.extraParam10": "extraParam10",
  "lang.ark.fed.menu.systemConfiguration": "System parameters",
  "lang.ark.fed.extraParam12": "extraParam12",
  "lang.ark.fed.extraParam11": "extraParam11",
  "lang.ark.fed.extraParam14": "extraParam14",
  "lang.ark.record.createTask": "Create task",
  "lang.ark.fed.extraParam13": "extraParam13",
  "lang.ark.fed.taskNumber": "Task number",
  "lang.ark.fed.extraParam16": "extraParam16",
  "lang.ark.fed.extraParam15": "extraParam15",
  "lang.authManage.fed.effectiveDays": "Days of valid period",
  "lang.ark.fed.screen.area.tipForEmptyGroup": "Cannot save because grouping is not added",
  "lang.gles.receipt.warehousingExternalOrder": "External putaway order",
  "lang.ark.fed.deleteFlow": "Delete workflows",
  "lang.ark.fed.numberOfCategories": "Number of categories",
  "lang.ark.fed.screen.LoginLog.realName": "name",
  "lang.ark.fed.mediaPlay": "Voice play",
  "lang.ark.fed.nearTheWorkstationRobotsCanBeParkedAndWaitedForAPosition": "A position near a workstation where robots can dock and wait for personnel to operate",
  "lang.ark.apiContainerCode.containerCategoryNotExists": "Container type does not exist",
  "lang.authManage.fed.screen.auth.cardNo": "The work card number",
  "lang.ark.areaCode.not.exist": "All or some area codes do not exist",
  "lang.ark.fed.processProcessNew": "Process",
  "lang.ark.workflow.action.command.robot.goTurnOfSide": "Rotate by plane",
  "lang.ark.task.log.export.title.task.number": "Task number",
  "lang.ark.fed.taskNumberNew": "Task order number",
  "lang.ark.shelfType.referenceByShelf": "The container type is associated with a container",
  "lang.ark.fed.updateLocation": "Update position",
  "lang.gles.stockInStore.factoryPositionStock": "Inventory in storage position",
  "lang.ark.fed.executiveInstruction": "Execution command",
  "lang.ark.fed.baseInfo": "Basic information",
  "lang.ark.fed.endPointName": "Terminal point name",
  "lang.ark.interface.requestId": "Message No.",
  "lang.ark.fed.newTask": "Create a task",
  "lang.ark.fed.areYouSureToDeleteThisNodeWorkflow": "Do you want to delete this node {nodeId} and all of its child nodes?",
  "lang.ark.fed.sending": "Distributing",
  "lang.authManage.web.common.roleDelSuc": "The role was deleted successfully!",
  "lang.ark.fed.finish": "Finished",
  "lang.ark.apiContainerCong.modifyAngleFail": "Failed to modify the container angle",
  "lang.ark.fed.pinking": "Feeding",
  "lang.ark.workflow.noAvailableAreaOrShelfPoint": "No reception areas or shelf points were found",
  "lang.ark.robot.unbind.device": "The robot is not bound to a upper structure",
  "lang.ark.fed.menu.containerEditor": "Container management",
  "lang.gles.planTask": "Planned task",
  "lang.ark.containerNotExists": "Container does not exist!",
  "lang.ark.warehouse.TriggerCellCodeCanNotFindUnWaveOrder": '"No document available, and wave grouping failed"',
  "lang.ark.loadCarrier.loadCarrierModelIdIsEmpty": "Container model cannot be empty.",
  "lang.ark.workflow.lastTaskArrivedSimple": "Previous step",
  "lang.ark.container.inUsing": "Container {} is in use. Please try again later",
  "lang.ark.dynamicTemplate.previousNode": "Previous point",
  "lang.ark.workflow.wareHouseWorkflowTemplate": "Process template",
  "lang.ark.fed.priorityStickTop": "Sticky priority",
  "lang.ark.fed.nodeDeviceInfo": "Point device information",
  "lang.ark.common.importExcelFile": "Import template",
  "lang.mwms.fed.accountManage": "Account management",
  "lang.ark.fed.containerAmount": "Container quantity",
  "lang.ark.workflow.workflowInstanceNotExists": "The workflow instance does not exist",
  "lang.ark.workflow.action.command.paramSourceType.clientAssign": "Sent from upstream",
  "lang.ark.fed.stopPointIdExistent": "The current point already exists. Please switch the point",
  "lang.ark.fed.specialAreaName": "Special area name",
  "lang.ark.fed.materialNum": "Material quantity",
  "lang.ark.fed.removeRack": "Remove the shelves",
  "lang.ark.fed.menu.templateManage": "Import external task",
  "lang.ark.fed.noWorkflowNodePleaseReedit": ",no workflow nodes, please edit again",
  "lang.ark.fed.preStep": "Previous step",
  "lang.ark.fed.height": "Height",
  "lang.ark.existsInUsedContainer": "Container already entered exist",
  "lang.ark.warehouse.buttonExistsButNoMatch": '"The button has been configured with another operating command, and the point or container number must be consistent with it"',
  "lang.ark.fed.menu.areaEditController": "Area configuration",
  "lang.ark.fed.batchImport": "Bulk import",
  "lang.ark.canNotSubmitRepeat": "Do not click repeatedly",
  "lang.ark.fed.parentWorkflowContainThis": "Process {0} contains this process. It has been changed into an abnormal status!",
  "lang.ark.fed.workstationName": "Workstation name",
  "lang.ark.fed.cellCodeLock": "Point lock",
  "lang.ark.workflow.manulChoiceSimple": "Manual selection",
  "lang.ark.fed.interfaceAccessType": "Interface address obtaining mode",
  "lang.ark.shelf.addShelfFailed": "Failed to add container",
  "lang.ark.workflow.mutiBeginOrEnd": "There are many start nodes or end nodes",
  "lang.ark.fed.pleaseSelectOrder": "Please select a document",
  "lang.ark.fed.inTheTask": "In the task",
  "lang.ark.fed.endOfProcess": "End of workflow",
  "lang.ark.fed.drawingAMap": "Draw a map",
  "lang.ark.waveTriggerCondition.all": "All",
  "lang.mwms.fed.replenishmentWorkCreate": "Replenishment is generated",
  "lang.mwms.fed.pickWork": "Picking work",
  "lang.ark.interface.apiAreaList": "Area query",
  "lang.gles.systemManage.systemManage": "System management",
  "lang.ark.fed.loading": "The system is loading like crazy...",
  "lang.gles.baseData.workshop": "Workshop",
  "lang.ark.fed.updateTimeInterval": "Update interval",
  "lang.ark.button.code.already.exist": "This controller button already exists, and cannot be added repeatedly",
  "lang.ark.workflow.area.occupiedTime": "Upper limit of continuous occupation time",
  "lang.ark.fed.errorBins": "Incorrect stock bin. Please confirm the barcode",
  "lang.ark.fed.save": "Save",
  "lang.ark.workflow.action.command.paramSourceType.manualConfiguration": "Manual configuration",
  "lang.ark.fed.menu.robotManagement": "Robot",
  "lang.ark.fed.facility": "Facility:",
  "lang.ark.trafficControl.enterType.multiFactorySingleEnter": "Multiple manufacturers with a single entrance",
  "lang.mwms.fed.strategyOrderAllocate": "Order hit strategy",
  "lang.ark.fed.nodeName": "Node name",
  "lang.ark.fed.openMoreGoodsInfo": "View more material information",
  "lang.ark.fed.codeValue": "Code value",
  "lang.ark.api.template.startNodeNotExist": "Template start point does not exist",
  "lang.ark.fed.typeName": "Type name",
  "lang.ark.fed.processMode": "Workflow mode",
  "lang.ark.fed.common.placeholder.input": "Please enter",
  "lang.ark.apiRobotTaskCode.robotTaskNotExists": "Robot task does not exist",
  "lang.ark.fed.trigger": "Trigger",
  "lang.ark.fed.menu.editMap": "Map configuration",
  "lang.authManage.web.common.loading": "Loading...",
  "lang.ark.apiStationCode.stationStopPointIsEmpty": "Information of docking point in the workstation is empty",
  "lang.ark.fed.ruleConfiguration": "Rule configuration",
  "lang.ark.fed.synchronizeAllRacks": "Synchronize all shelves",
  "lang.ark.fed.singleCellStation": "Single-point Station",
  "lang.ark.apiContainerCode.containerCategoryNotMatch": "containerCategory:{0} does not match the container type in the system",
  "lang.ark.sameQueuePriorityNeedSameAndallowQueueRobotNumNeedSame": "The same queuing point requires the same queuing quantity and priority",
  "lang.mwms.fed.wave": "Wave picking management",
  "lang.ark.fed.triggerLocking": "External triggering locking",
  "lang.ark.fed.robotDashboard": "Robot display board",
  "lang.ark.fed.west": "West",
  "lang.ark.workflow.shelfExistsAndOccupied": "There is a shelf in use at the current point",
  "geekplus.moving.uic.elTableWrapperVue2.actionBtn.confirmPopup.btnConfirm": "OK",
  "lang.ark.fed.lingdan": "Material requisition timeout",
  "lang.ark.fed.dataStatus": "Data status",
  "lang.ark.archiveType.interfaceRecord": "Interface Log",
  "lang.ark.fed.havingSeparateBusinessAttributesAPhysicalOrLogical": "A physically or logically partitioned position with dependent business properties",
  "lang.ark.fed.robotStat": "Robot status",
  "lang.ark.fed.typeMustBeDockPointWorkstationRackPointArea1": "The type must be: point, workstation, shelf point, area ",
  "lang.ark.fed.shelfFlow": "Load mode",
  "lang.ark.fed.areYouSureYouWantToRedrawTheMap": "Are you sure to redraw the map?",
  "lang.ark.warehouse.goods": "Delivery of materials",
  "lang.ark.interface.interfaceDesc.phaseType": "Callback format of enumerated value field",
  "lang.ark.fed.productModel": "Product model",
  "lang.gles.receipt.tallyList.externalTallyList": "External tally order",
  "lang.mwms.fed.monitorAll": "Whole warehouse monitoring",
  "lang.ark.fed.manualConfig": "Manual configuration",
  "lang.ark.fed.redistributionSure": "Add a record of the same document for re-distribution. Confirm?",
  "lang.ark.workflowAction.default": "Default",
  "lang.ark.workflow.workflowNodeConfigNotExists": "The workflow node configuration does not exist",
  "lang.ark.lang.ark.controlNodeType.point": "Point",
  "lang.ark.workflow.subflowCancellationNotAllowed": "Cancellation is not allowed for sub-processes.",
  "lang.mwms.fed.outManage": "Stock out management",
  "lang.ark.workflowTrigger.logType.operation": "Operation log",
  "lang.ark.fed.recoveryToInitialStatus": "Return to the initial state",
  "lang.ark.fed.callStationTaskDetails": "Material calling task order details",
  "lang.ark.fed.containerEntry": "Entry",
  "lang.ark.warehouse.stationLineDeliveryPriority": "Distribution priority",
  "lang.ark.fed.pleaseSelectFlow": "Please select a process!",
  "lang.ark.workflow.manul": "Manual triggering of automatic branch judgment",
  "lang.ark.fed.estimatedTime": "; this trip is expected to take",
  "lang.ark.fed.childFlow": "Sub-process",
  "lang.ark.fed.parameterType": "Parameter Type",
  "lang.ark.fed.addRackPoints": "Add shelf points",
  "lang.ark.fed.controlArea": "Control area",
  "lang.mwms.fed.inventoryManage": "Inventory management",
  "lang.ark.rollerStation.canNotEdit": "Roller station cannot be edited",
  "lang.ark.workflow.extendRobotTrue": "Yes",
  "lang.ark.workflow.workflowDropNotSupport": "WORKFLOW_DROP instruction does not support roller and composite types, Please use WORKFLOW_CANCEL",
  "lang.ark.fed.setMiddlePoint": "Set as passing point",
  "lang.ark.fed.lockingSure": "Confirm to lock the stock bin",
  "lang.ark.fed.screen.flowNodeConfig.judgingByTask": "Task",
  "lang.authManage.fed.preAlertDays": "Early warning days",
  "lang.ark.workflowConfig.cellFunctions.rest": "Stop",
  "lang.ark.workflow.workflowNotExists": "Cancellation failed. The process does not exist",
  "lang.ark.curNodeExitsShelf": "There are shelves at the current point, and the no shelf mode cannot be used",
  "lang.ark.action.interface.retry": "Try again",
  "lang.ark.loadCarrier.loadCarrierCodeGenerateErr": "Container ID generation encountered error.",
  "lang.mwms.fed.exception": "Exception handling",
  "lang.ark.fed.signalDisplay": "Signal display",
  "lang.ark.fed.set": "Settings",
  "lang.ark.warehouse.materialPreparePointType": "Node type of feeding point",
  "lang.ark.fed.pleaseChooseRack": "Please select shelf",
  "lang.ark.fed.start": "Start",
  "lang.ark.shelfTypeRefWorkflowNodeAction": "Failed to delete. Used by interaction configuration! Interaction configuration name: {0}",
  "lang.mwms.fed.allocationRule": "Picking rule",
  "lang.ark.fed.businessModel": "Business mode",
  "lang.ark.fed.operationInstruction": "Operational instruction",
  "lang.ark.fed.wirelessCallModule": "Wireless calling module",
  "lang.ark.base.license.nolicense": "Please import the certificate!",
  "lang.gles.receipt.upAndDownMaterialOrder": "Feeding and discharging order",
  "lang.gles.baseData.baseContainerArchives": "Container profile",
  "lang.ark.fed.forkLiftSetCommponent": "A component action must be configured in the command list of the forklift",
  "lang.ark.action.interface.retrySize": "upon",
  "lang.ark.fed.setTriggerTime": "Set trigger time",
  "lang.ark.workflow.recycleTypeNoConfigAction": "No interaction action configured for returning goods to their original position upon cancellation",
  "lang.ark.fed.sendMaterialRepertory": "What materials can be distributed by the feeding point",
  "lang.ark.fed.addPointPosition": "Add point",
  "lang.ark.plugin.pluginType.fetchContainer.way.empty": "Fetch empty container",
  "lang.ark.fed.empty": "Empty",
  "lang.ark.fed.bussinessModel": "Business mode",
  "lang.ark.workflow.area.ContinuousPassage": "One-by-one pass/continuous pass",
  "lang.ark.workflow.area.end": "Tail queue",
  "lang.ark.action.interface.responseParamName": "Name of returned parameter",
  "lang.ark.fed.screen.area.groupCode": "Grouping code",
  "lang.ark.fed.brushSize": "Stroke size",
  "lang.authManage.web.common.surePassword": "Confirm password",
  "lang.ark.fed.startDeliveryTime": "Start time of distribution",
  "lang.ark.fed.operationFlow": "Operation flow",
  "lang.ark.fed.workFlow": "Workflow",
  "lang.ark.fed.workflowTriggerMonitor": "Trigger log",
  "lang.ark.fed.leaved": "Left",
  "lang.ark.fed.pleaseFlowNode": "Please add a process node",
  "lang.ark.fed.cacheNodeActions": "Temporary storage area interaction",
  "lang.ark.fed.reconnectToTheSystem": "Connect to the system again",
  "lang.ark.fed.simulationButton": "Button trigger",
  "lang.ark.trafficControl.enterStrategy.byTimePriority": "First come first pass (priority first)",
  "lang.ark.workflow.area.increase": "Fill the vacancy in order",
  "lang.ark.fed.firstLetterUppercasePlus6Digits": "Capitalize the first letter plus 6 digits!",
  "lang.ark.fed.common.btn.detail": "Details",
  "lang.ark.fed.earlyWarningMode": "Early warning mode",
  "lang.ark.fed.menu.containerLog": "Container log",
  "lang.ark.robot.classfy.roller": "Roller robot",
  "lang.ark.fed.carModel": "Vehicle model",
  "lang.ark.fed.youCanRemotelySelectTheRobot": "You can have a remote control of the selected robot",
  "lang.ark.fed.strategy": "Strategy",
  "lang.ark.fed.dockPointName": "Docking point name",
  "lang.ark.roller.docking.bating": "Roller butt blanking",
  "lang.mwms.fed.arrangeMoveNote": "Movement sheet management",
  "lang.ark.externalDevice.instructionRule": "Command rule",
  "lang.ark.fed.workingNotAllowClickCell": "There are tasks being executed. Do not switch the cell",
  "lang.ark.fed.canAddshelfFlag": "Add a shelf",
  "lang.ark.fed.export": "Export",
  "lang.ark.fed.selectLocation": "Select a stock bin",
  "lang.ark.workflow.area.factoryControl": "Manufacturer that is not allowed to enter by following others",
  "lang.ark.fed.externalNumber": "External document number",
  "lang.ark.fed.update": "Update",
  "lang.ark.fed.operationLog": "Operation log",
  "lang.ark.workflowConfig.cellFunctions.dropCell": "DROP_CELL",
  "lang.ark.fed.equipmentNode": "Device node",
  "lang.ark.externalDevice.device_own_type": "Equipment type",
  "lang.ark.fed.templateDownloading": "download",
  "lang.ark.fed.contents.flowConfig.recycleType.autoRecoveryRule.add": "add preset location",
  "lang.ark.fed.menu.instance": "Device example",
  "lang.ark.workflow.task.status.suspension": "Suspend",
  "lang.ark.workflow.denseStorageTemplateAreaFull": "The end point is in a dense storage area that is already fully occupied.",
  "lang.ark.groupStrategy": "Group selection strategy",
  "lang.ark.fed.allValuesOrRelationshipsWithinTheCollection": "All values in the collection have an OR relation",
  "lang.ark.rpc.syncNodeErr": "Failed to get node information!",
  "lang.ark.fed.commonMaterials": "Common materials",
  "lang.ark.fed.eitherOrRobotAndType": "Select either robot model or specified robot",
  "lang.ark.fed.importFileFormatError": "Failed to import to template. Please download specified template and upload.",
  "lang.ark.fed.productPolice": "Foolproof alarm",
  "lang.mwms.monitorRobotMsg.other": "Other exception",
  "lang.ark.fed.deleteAuthWorkStation": "Confirm to delete the permission?",
  "lang.ark.fed.generalNode": "Map node",
  "lang.ark.base.license.licenseExpiredErrorMsg": "Certificate expired. You cannot use the system!",
  "lang.ark.fed.stationConfig": "Station configuration",
  "lang.ark.fed.screen.equipmentAssociatedInfo.labelEquipType": "Device type",
  "lang.ark.container.containerBatchAddPartSuccessful": "{} containers are added successfully and {} containers failed (container code {} already exists)",
  "lang.ark.fireStop.areaCodeAlreadyExistPleaseCheck": "The area code {0} already exists. Please check!",
  "lang.ark.apiRobotTaskCode.robotTaskNotOnlyOne": "The unique robotTaskId:{} not matched",
  "lang.ark.fed.conButtonLogMessage": "Message content",
  "lang.ark.fed.sacnFailAndCancelTask": "Failed to recognize the container code. Task canceled",
  "lang.ark.workflow.workflowNotFound": "Process start point not found",
  "lang.ark.immediate.bating": "Direct blanking",
  "lang.gles.receipt.receiptOutWarehouseOrder": "Outbound order",
  "lang.ark.waveTaskStatus.finished": "Finished",
  "lang.ark.fed.component.workflow.tooltip.specifyNodeType": '"When ""Not specify"" is selected, all node types are included by default. Selecting ""Not specify"" is recommended for the situation where only one external device exists."',
  "lang.authManage.web.common.password": "Password",
  "lang.ark.fed.deviceAccessType": "Device name obtaining mode",
  "lang.ark.fed.waitingTime": "Waiting time",
  "lang.ark.fed.queueOrder": "Queuing order",
  "lang.ark.fed.deliveryMaterial": "Material distribution",
  "lang.ark.base.license.ipStrIsNull": "IP port number is null!",
  "lang.ark.workflowConfig.cellFunctions.firePass": "Fire control",
  "lang.ark.loadCarrier.loadCarrierModelCodeDuplicated": "Container model ID is repeated.",
  "lang.mwms.fed.efficiency": "Efficiency management",
  "lang.ark.fed.workingNotAllowChangeFloor": "There are tasks being executed. Do not switch the floor",
  "lang.ark.interface.containerNo": "Container No.",
  "lang.ark.fed.businessCode": "Business code",
  "lang.ark.fed.createManually": "Manual creation",
  "lang.ark.fed.speedLimit": "Speed limit",
  "lang.ark.fed.picking": "Receiving materials",
  "lang.gles.workflow.receipt": "Order management",
  "lang.mwms.fed.inventoryStockshot": "Inventory account checking",
  "lang.authManage.web.menu.roleList": "Role profile",
  "lang.ark.record.robotCallback.turnOfSide": "Robot turning surface {0}",
  "lang.ark.fed.commandCode": "Command code",
  "lang.ark.fed.commandName":"Command Name",
  "lang.ark.fed.conButtonLogExecuteContent": "Execution content",
  "lang.ark.fed.stationPoint": "Node code of station",
  "lang.ark.fed.upTaskNumber": "External task number",
  "lang.ark.fed.queueRobotNum": "Number of queuing robots",
  "lang.ark.api.areaNodeExit": "Shelf area does not exist",
  "lang.ark.workflow.area.accessWay": "Access mode",
  "lang.ark.fed.pleaseSelectShelfType": "Please select a shelf type or enter the shelf code",
  "lang.ark.fed.currentWorkflow": "Current workflow",
  "lang.ark.fed.showContents": "Display content",
  "lang.ark.workflow.task.status.cancelExecuting": "Canceling",
  "lang.ark.workflow.template.validate.templateCodeIsExist": "Template code already exists.",
  "lang.ark.fed.rotate": "Rotate",
  "lang.ark.api.template.finishNodeNotExist": "Template end point does not exist",
  "lang.ark.fed.orderNum": "Document No.",
  "lang.ark.fed.setTop": "Top",
  "lang.ark.fed.parameterClassification": "Parameter classification",
  "lang.ark.fed.operator": "Operator",
  "lang.ark.fed.addDockPoints": "Add docking points",
  "lang.ark.fed.selectImage": "Select picture",
  "lang.ark.fed.clickUpload": "Click upload",
  "lang.ark.fed.menu.nodeDeviceInfo": "Point device info",
  "lang.ark.fed.lineCoding": "Production line code",
  "lang.ark.fed.screen.flowNodeConfig.judgingByRule": "Rules",
  "lang.ark.fed.month": "Month",
  "lang.ark.fed.electricQuantity": "Electric quantity",
  "lang.ark.workflow.area.order": "Order queue",
  "lang.ark.fed.userEnquiry": "User inquiry",
  "lang.ark.fed.idle": "Idle",
  "lang.gles.interface.interfaceManager": "Interface management",
  "lang.ark.fed.week": "Week",
  "lang.ark.fed.forbid": "Disable",
  "lang.ark.workflow.cancelLocationCodeInvalid": "Invalid location type for cancellation",
  "lang.ark.apiContainerCode.numberLessThanZero": "The value of numberOfContainer must be greater than 0",
  "lang.ark.workflow.successHandler": "Workflow the logic after success",
  "lang.ark.warehouse.warehouseHaveNoBinInfo": "No available inventory",
  "lang.ark.fed.alarmInfo": "Alarm information",
  "lang.ark.fed.shelfAttribute.PNAC": "PNAC",
  "lang.authManage.web.common.newItem": "New",
  "lang.ark.fed.serialExcessMount": "The quantity of materials cannot exceed {0}. Please switch the material",
  "lang.ark.warehouse.preparationEnableFailed": "The material of the feeding point or the distribution destination is empty. Failed to enable!",
  "lang.ark.fed.screen.hybridRobot.robotBody": "Robot body",
  "lang.ark.workflow.notFoundDenseStorageTemplate": "The start point or end point is in a dense storage area. Failed to find the configured dynamic points template for dense storage.",
  "lang.ark.fed.waveTaskDashboard": "Display board of wave picking task",
  "lang.ark.fed.syncInform": "Information Synchronization",
  "lang.ark.fed.extendDevice": "External equipment",
  "lang.ark.workflow.chooseStrategy.normal": "Completed normally",
  "lang.ark.fed.rotationAngle": "Rotation angle",
  "lang.ark.fed.happensEveryOnceInAwhile": "Once every {0} days",
  "lang.ark.fed.triggerEntity": "Trigger instance",
  "lang.ark.workflowTrigger.logType.robot": "Robot log",
  "lang.ark.fed.makeSureToDeleteTheCurrentRule": "Confirm to delete the current rule",
  "lang.ark.fed.abnormalCompletion": "Completed abnormally",
  "lang.ark.fed.left": "Left",
  "lang.ark.fed.stationCoding": "External code of station",
  "lang.ark.fed.screen.flowNodeConfig.instructExeCondition": "Conditions for execution of instruction",
  "lang.ark.fed.closeLeftTabs": "Close the left",
  "lang.ark.fed.deviceCode": "Device ID",
  "lang.ark.workflow.area.stopRange": "Emergency stop area",
  "lang.ark.fed.triggerStopPointId": "Start station",
  "lang.ark.element.has.boundNodeAction": "This element has been bound to interaction configuration",
  "lang.ark.workflow.stopPointNotExists": "Point does not exist",
  "lang.ark.fed.unloadingPoint": "Blanking point",
  "lang.ark.pda.function.container.list": "Container list",
  "lang.authManage.web.common.save": "Save",
  "lang.gles.baseData.factory": "Factory",
  "lang.ark.warehouse.doubleContainerSide": "Two sides",
  "lang.ark.fed.handlingRack": "Move shelves",
  "lang.ark.fed.chromeScanSetting": "1. Enter chrome://flags in the browser address bar, search for the  unsafely-treat-insecure-origin-as-secure  option, and set it as Enabled; add the domain name or IP for which the camera needs to be enabled in the option input box, and restart the browser after modification.",
  "lang.ark.interface.apiPause": "Workflow paused",
  "lang.ark.base.license.getHarewareFail": "Failed to obtain hardware information. Please check IP configuration and jar package start!",
  "lang.ark.fed.taskJiXu": "Click to resume the suspended task and the process continues",
  "lang.ark.apiCallbackReg.all": "All",
  "lang.ark.fed.ruleOpreator": "Operator",
  "lang.ark.fed.noItemSelected": "No selected items",
  "lang.ark.fed.emergencyStopSuccess": "System emergency stop succeeded!",
  "lang.ark.fed.interfaceAccessType.fixation": "Fixed address",
  "lang.ark.workflow.noAvailableNode": "No available nodes were found",
  "lang.ark.fed.clearWay": "Clear mode",
  "lang.ark.fed.menu.vens.dmpInstance": "Device instance",
  "lang.ark.fed.return": "Back",
  "lang.ark.fed.menu.deviceModel": "Device model",
  "lang.ark.workflow.recoveryAreaType.appointCell": "Designated recycling area",
  "lang.ark.apiStationCode.stationTypeNotBlank": "Workstation type cannot be empty",
  "lang.ark.workflow.failure": "Failed",
  "lang.ark.fed.screen.flowNodeConfig.checkMsg2": "Saving failed. Please select task.",
  "lang.ark.operation.workflow.deleteWorkflow": "Delete task {0}",
  "lang.ark.fed.screen.flowNodeConfig.checkMsg0": "Saving failed. Please enter point ID.",
  "lang.ark.fed.screen.flowNodeConfig.checkMsg1": "Saving failed. Please select path as the judgment condition.",
  "lang.ark.fed.workstation.loading.logout": "Logging out",
  "lang.ark.button.command.pressDown": "Press",
  "lang.ark.fed.asc": "Ascending order",
  "lang.ark.workflow.template.validate.dynamicUnitTemplateFormatError": "Dynamic cell task style error",
  "lang.ark.fed.taskStatus": "Task status",
  "lang.gles.logisticsConfig.tallyConfig": "Logistics configuration for tally",
  "lang.ark.fed.once": "Once",
  "lang.ark.workflow.dataArchiving": "Data archiving",
  "lang.ark.fed.monitoringAndManagement": "Monitoring management",
  "lang.ark.fed.floor": "Floor",
  "lang.ark.fed.menu.robotInformation": "Robot monitoring",
  "lang.ark.fed.workstationstationid": "No. {stationId} workstation ",
  "lang.ark.workflow.deviceTaskNotExistOrUnException": "Retrying is not allowed for device tasks that do not exist or are not in an abnormal state.",
  "lang.ark.fed.runMode.unload": "No-load",
  "lang.ark.trafficControl.noStayRange": "Traffic control area",
  "lang.ark.button.operation.command.fetch": "Fetch",
  "lang.mwms.api.menu.item0001": "RHINO-WEB",
  "lang.ark.fed.desc": "Descending order",
  "lang.authManage.web.common.reset": "Reset",
  "lang.ark.fed.screen.flowNodeConfig.sourceTurnSide": "Rotation plane source",
  "lang.ark.workflow.wareHouseConfigurationEnable": "Enable configuration",
  "lang.ark.workflow.notAllowBreak": "No interruption is allowed during robot picking and placing operation",
  "lang.authManage.web.common.phone": "Telephone",
  "lang.ark.workflow.taskSplit": "Task split",
  "lang.ark.fed.configName": "Configuration name",
  "lang.gles.batchProperty": "Batch attribute",
  "lang.ark.fed.airRunProcess": "Empty running workflow",
  "lang.ark.fed.operatorPositionInProductionLine": "A position (station) where the operator performs production operations on the production line",
  "lang.ark.fed.screen.flowNodeConfig.deviceInstruct": "Device instruction",
  "lang.ark.fed.all": "All",
  "lang.ark.workflow.paramValueCode.binCode": "binCode",
  "lang.ark.workflow.paramValueCode.offsetX": "offsetX",
  "lang.ark.apiNodeActionCode.successHandlerIsAuto": "Success processing logic of node interaction configuration is automatically triggered",
  "lang.ark.workflow.noAvailableRobot": "No robots available",
  "lang.ark.waveTriggerCondition.workstation": "Workstation ",
  "lang.ark.workflow.arrive.action.robotGoTurnOfAngle": "Robot self rotation by angle",
  "lang.ark.fed.startCharging": "Start charging",
  "lang.ark.fed.theSaveSourceOnlySameTriggerHandle": '"When connection lines have the same start point, configure only one trigger timing!"',
  "lang.ark.warehouse.policyNumberExists": "The wave picking strategy code already exists",
  "lang.ark.workflowConfig.cellFunctions.shelfCell": "SHELF_CELL",
  "lang.ark.fed.emergencyStop": "Emergency stop completed",
  "lang.ark.fed.screen.flowNodeConfig.SourceOffsetValue": "Offset value source",
  "lang.ark.fed.sourceDocuments": "Document source",
  "lang.mwms.fed.seedRule": "Put wall specification maintenance",
  "lang.ark.fed.nodeWaiting": "Arrival at the workstation",
  "lang.ark.apiContainerCode.locationAndContainerAreEmpty": "Both containerCode and locationCode are empty. Either must be specified",
  "lang.ark.fed.emptyItAll": "Clear all",
  "lang.ark.fed.operation": "Operation",
  "lang.ark.api.workflow.idOrTaskCodeIsNull": "Task code or workflow ID cannot be empty",
  "lang.ark.plugin.pluginType.returnContainer.way.manual": "Manually select empty or full",
  "lang.ark.fed.menu.vens.dmpTemplateInstance": "Example model",
  "lang.mwms.fed.workStationCharts": "Station efficiency management",
  "lang.ark.fed.theLogicalArea": "Logic area",
  "lang.ark.fed.flowEdgeType": "Flow type",
  "lang.ark.warehouse.goodsNoMatchZagvdbm": "No feeding point matched. Material code: {0}",
  "lang.ark.workflow.initiateNextTask": "Initiate the next task",
  "lang.ark.fed.road": "Path",
  "lang.ark.fed.receiveSure": "Confirm to receive",
  "lang.ark.workflow.childNodeListIsEmpty": "The child node collection is empty",
  "lang.ark.workflow.denseStorageEndPointTaskUpperLimit": '"The end point is the dense storage area, which has reached the maximum number of accepted tasks"',
  "lang.gles.systemManage.systemParam": "System parameters",
  "lang.ark.workflowConfig.status.deleted": "Deleted",
  "lang.ark.fed.waveSetting": "Wave picking generation",
  "lang.ark.fed.waveNum": "Wave picking ID",
  "lang.ark.interface.config.field.locationToDesc": "Target point code, if the workstation (workstation) has only one point, the \nworkstation number can also be passed in.\nMatching order: map node -workstation - area",
  "lang.ark.interface.config.taskCallback.field.msgTypeDesc": "Message type: This business function must be passed: MovingCallbackMsg",
  "lang.ark.interface.config.taskCallback.field.exceptionFailReasonDesc": "Reasons for failure to handle workflow error",
  "lang.ark.sys.config.values.show": "Display",
  "lang.ark.sys.config.values.rpcStop": 'Use "RPC" to implement an emergency stop of the system',
  "lang.ark.sys.config.values.sync": "Synchronization",
  "lang.ark.externalDevice.instructionRule7": "The double lifting robot executes when it is non-empty full exchange task",
  "lang.ark.externalDevice.instructionRule6": "The double lifting robot executes when it is unloading task",
  "lang.ark.externalDevice.instructionRule5": "The cantilever robot executes when it is picking task",
  "lang.ark.interface.config.taskCallbackDesc": '1. In addition to the callbacks generated by the tasks initiated by the request interface, the tasks initiated by the workstation-side operation page will also generate callbacks.\n2. The callback task phase is divided into three levels: the first level workflowPhase, the second level taskPhase, the third level robotPhase.\n3. Note: In the task execution, "interrupt waiting" and "instruction execution" in taskPhase are non-robot-triggered task phase changes, so robotPhase is empty at this time. If there is a list of events that need to be executed after the interrupt wait, the status will be changed to instruction execution, and the robotPhase callback will be generated according to the change of the robot execution instruction phase.\nNote: This interface is a callback interface, it is a GMS call to the host system, this interface needs to be developed by the host system according to the corresponding format.',
  "lang.ark.sys.config.rmsQueryCurrentMapAddress": "The address used to query the current map of rms(temporary solution, to be replaced with RPC interface later)",
  "lang.ark.interface.config.taskCallback.field.parentInstanceIdDesc": "Parent workflow instance number",
  "lang.ark.sys.config.values.noneStop": "Do not use either",
  "lang.ark.sys.config.callbackMessageRetryInterval": "In Socket mode, when calling back to the upstream system, the retry time interval: unit (seconds), default is 10 seconds.",
  "lang.ark.interface.config.field.containerCodeDesc": "Container number.",
  "lang.ark.fed.uploadFile": "Upload file",
  "lang.ark.sys.config.denseStorageTemplateForAreaToArea": "the code for dynamic point template in dense storage area-to-area tasks.",
  "lang.ark.sys.config.simpHost": "SIMP server host",
  "lang.ark.interface.config.movingMulti.field.destsDesc": "Collection of target points",
  "lang.ark.fed.excel.data.null": "Incomplete data in row ({0}). Please complete the data and upload it again.",
  "lang.ark.task.nodeDevice.export.param6.value": "Parameter 6: Parameter Value",
  "lang.ark.interface.config.field.priorityDesc": "The priority of executing the task, the smaller the number, the higher the priority. If not filled in, the priority from the template will be used; if filled in, the entered priority will be used.",
  "lang.ark.interface.config.taskCallback.field.robotTaskIdDesc": "Robot task number",
  "lang.ark.binStopPoint.deviceType": "Equipment type",
  "lang.ark.task.nodeDevice.export.param1.value": "Parameter 1: Parameter Value",
  "lang.ark.task.plugin.take.fullContainer": "Take full container",
  "lang.ark.sys.config.values.oldFormat": "Old format",
  "lang.ark.sys.config.strictOrderMode": "Should tasks be executed strictly in order: If executed strictly in order, the next task of the same type will only be executed after the previous task is successfully completed; if not executed strictly in order, the next task can be executed once the previous task is finished, regardless of success or failure.",
  "lang.ark.sys.config.buttonFilterTime": "Within a certain period, pressing the physical button multiple times will only trigger it once. This time can be set (unit: seconds).",
  "lang.ark.sys.config.callbackMessageRetryTimes": "In Socket mode, the number of retries when calling back to the upstream system: default is 360 times.",
  "lang.ark.sys.config.values.close": "turn off",
  "lang.ark.sys.config.rmsWsAddress": "RMS websocket address",
  "lang.ark.task.plugin.take.emptyContainer": "Take empty container",
  "lang.ark.interface.config.taskCallback.field.instancePriorityDesc": "The priority of the workflow instance (task), the smaller the value the higher the priority, the highest priority is 1",
  "lang.ark.fed.containerBinStatus": "Slot status",
  "lang.ark.sys.config.rmsQueryAreaAddress": "The address for obtaining various regional data from the RMS system",
  "lang.ark.fed.binStopPoint.file.excel.name": "locationBaseInfo",
  "lang.ark.sys.config.modbusEnable": "Physical button function switch: when turned on, the physical button function is enabled; when turned off, the physical button function is disabled (even if configured in the physical button configuration interface, it will not take effect).",
  "lang.ark.task.exception.startPoint.containerCode.mustOne": "Either starting point code or container code must be filled in line {}.",
  "lang.ark.interface.config.movingMulti": "Point-to-Multipoint Moving Task",
  "lang.ark.interface.config.field.containerCategoryDesc": "Container type code.",
  "lang.ark.interface.config.taskCallback.field.waitNextLocationDesc": "Waiting point for the next point",
  "lang.ark.sys.config.robotMediaApi": "Default API for multimedia broadcasting",
  "lang.ark.task.exception.templateTask.empty": "The result of querying the template information is empty.",
  "lang.ark.sys.config.stationNoticeCycle": "The time interval for workstation loop reminders, unit: seconds",
  "lang.ark.interface.config.taskCallback.field.robotDesc": "Robot number",
  "lang.ark.sys.config.values.exactMatch": "Exact match",
  "lang.ark.interface.config.taskCallback.field.instanceIdDesc": "Workflow instance Number",
  "lang.ark.task.nodeDevice.export.param8.name": "Parameter 8: Parameter Name",
  "lang.ark.sys.config.denseStorageTemplateForAreaToPoint": "the code for dynamic point template in dense storage area -to-point tasks.",
  "lang.ark.sys.config.overtimeTaskIntervalHours": "Task timeout threshold: If the current time minus the task creation time is greater than the configured threshold, the task is considered as timed out.",
  "lang.ark.sys.config.values.accountLogin": "Account login",
  "lang.ark.sys.config.values.async": "Asynchronous",
  "lang.ark.interface.config.field.needTimeDesc": "Task requirements completion time.",
  "lang.ark.sys.config.stationConfig": "Workstation type",
  "lang.ark.task.nodeDevice.export.interfaceOrDevice": "Interface address / Device ID",
  "lang.ark.interface.config.taskCallback.field.robotPhaseDesc": "Robot task phase",
  "lang.ark.sys.config.denseStorageTemplateForPointToArea": "the code for dynamic point template in dense storage point-to-area tasks.",
  "lang.ark.fed.frontend": "Front-end",
  "lang.ark.fed.updateUser": "Updated by",
  "lang.ark.interface.config.taskCallback.field.workflowCodeDesc": "Workflow external coding",
  "lang.ark.interface.config.field.requestTimeDesc": "Time of request sent in the format yyyy-MM-dd hh:mm:ss",
  "lang.ark.workflow.noAvailableOnlineDevices": "No available online devices were found. Please check if the device is in offline mode.",
  "lang.ark.sys.config.filterChannelButton": "Should the callback message be sent only to the task-sending channel, or to all channels?",
  "lang.ark.sys.config.showExceptionTab": "Whether to display the exception page",
  "lang.ark.interface.config.movingMultiDesc": "Start from one location and go to multiple locations in succession. It is possible to go to the target location in ascending order by the ID of the target location or in the order of sending.",
  "lang.ark.sys.config.modbusPort": "The port used when GMS interacts with external devices (such as physical buttons) through Modbus",
  "lang.ark.fed.excel.data.binCodeAndOrderError": "The shelf bin code and shelf bin SN in row ({0}) must either be filled with values or be empty at the same time. Please modify them and import again.",
  "lang.ark.sys.config.loginUrl": "Permission system login address",
  "lang.ark.fed.updateTime": "Update time",
  "lang.ark.sys.config.shelfOnShare": "Can the shelf be shared among multiple workflow?",
  "lang.ark.task.nodeDevice.export.param3.name": "Parameter 3: Parameter Name",
  "lang.ark.sys.config.values.version2": "Version 2.x",
  "lang.ark.sys.config.values.version1": "Version 1.x",
  "lang.ark.fed.containerColumn": "Which column in the container",
  "lang.ark.task.nodeDevice.export.param5.value": "Parameter 5: Parameter Value",
  "lang.ark.task.exception.endpoint.empty": "The destination code in line {} is empty.",
  "lang.ark.task.exception.templateCode.empty": "Template code in line {} is empty.",
  "lang.ark.sys.config.warehousePointRelatedScheduled": "The switch for updating the loading point configuration data in the warehouse system. If it is turned on, updates are allowed; if it is turned off, updates are not allowed.",
  "lang.ark.sys.config.values.withoutFloor": "Do not determine by floor",
  "lang.ark.sys.config.httpTimeOut": "HTTP connection timeout duration, default is 2000ms.",
  "lang.ark.sys.config.values.adaptive": "Adaptive to different devices",
  "lang.ark.sys.config.authUrl": "Authentication center URL",
  "lang.ark.interface.config.movingDesc": "Moving containers (shelves) between two points, or running the robot with no load between two points.\nScenario:\n1. When the host system manages the shelves and needs to move the specified shelves, you can input the shelf number and the destination code, and the system will move the shelves to the destination.\n2. The host system does not manage the shelves, but manages the location where the shelves are located. At this time, you can pass in the starting point code and the destination point code, and the system will carry the shelves from the starting point to the destination point.\n3. The host system manages the point or station and wants the robot to go from one position to another with no load, and can pass in the starting point code and the destination point code.\nUsage:\n1. Configure the robot interaction action of the node in the GMS system, and the GMS will automatically generate the workflow and execute the task according to the incoming points from the host system.\n2. Workflows can be created in GMS in advance, and GMS will match the existing workflows and execute them according to the incoming points from the host system.",
  "lang.ark.sys.config.callbackMessageRetryAndConfirm": "In Socket mode, whether to enable the confirmation and retry mechanism when calling back to the upstream. By default, it is disabled, and there will be no retries in Socket mode. If enabled, the system will confirm whether the upstream has received the callback; if not, it will retry. (Note that in Socket mode, enabling retries requires upstream system development. Upon receiving the callback from the GMS system, the upstream should inform GMS that the callback has been received.)",
  "lang.ark.sys.config.values.doNotSplitTheRollerFetchCommand": "Do not split the roller fetch command",
  "lang.ark.interface.config.field.languageDesc": "Contained in the message header, the language setting for the response message.\nOnce this field is set, RMS will select the language of the returned response message based on this field.\nSimplified Chinese (zh_cn), English (en_us), Japanese (ja_jp) and Traditional Chinese (zh_hk/zh_tw) are currently supported.\nIf the language value is not uploaded, it defaults to Simplified Chinese (zh_cn)",
  "lang.ark.fed.containerLayer": "Which layer in the container",
  "lang.ark.interface.config.dynamicUnitMoving.field.robotIdDesc": "The robot ID that executes the process task of reaching the target point. It must be passed when specifying a robot to execute the task. If the template has configured both robot type and ID, the passed-in value takes precedence.",
  "lang.ark.sys.config.values.mapStation": "Map version workstation",
  "lang.ark.sys.config.clientId": "mars websocket client Id",
  "lang.ark.sys.config.asynCount": "Number of interface logs for asynchronous requests inserted into the database each time.",
  "lang.ark.sys.config.rmsUrl": "mars websocket address",
  "lang.ark.fed.excel.data.binCodeExist": "The shelf bin point code and shelf bin SN in row ({0}) already exist. Do not add them repeatedly. Please modify them and import again.",
  "lang.ark.flowNodeConfig.rollerFetchCommand.checkMsg": "'Start picking up' and 'Finished picking up' must be configured together and cannot be used separately.",
  "lang.ark.interface.config.taskCallback.field.nodeCodeToDesc": "Task endpoint - node code configured in the workflow",
  "lang.ark.sys.config.smpAlterIp": "SMP (Security Management Platform) receives the warning message's IP address.",
  "lang.ark.task.nodeDevice.export.param5.name": "Parameter 5: Parameter Name",
  "lang.ark.interface.config.taskCallback.field.locationToDesc": "The actual target point code",
  "lang.ark.task.plugin.deliver.manuallyChoose": "When delivering a container, manually choose whether to deliver an empty one or a full one.",
  "lang.ark.sys.config.values.notLeave": "Container does not leave the scene",
  "lang.ark.interface.config.movingMulti.field.msgTypeDesc": "Message type: the value must be MultiMovingRequestMsg",
  "lang.ark.sys.config.ningdeHttpSoapUserName": "The username for accessing the Ningde SOAP request address",
  "lang.ark.interface.config.dynamicUnitMoving.field.msgTypeDesc": "Message type: the value must be DynamicUnitMovingRequestMsg",
  "lang.ark.fed.templateTask": "Template Task",
  "lang.ark.sys.config.isSynInterfaceRecord": "Method of inserting interface logs into the database: synchronous or asynchronous, default is synchronous.",
  "lang.ark.binStopPoint.name": "Name",
  "lang.ark.task.exception.robotType.empty": "Robot model in line {} is empty.",
  "lang.ark.fed.excel.data.deviceType.nonNumericFormat": "The device type in row ({0}) must be numbers. Please modify it and import again.",
  "lang.ark.interface.config.field.taskCodeDesc": "External task number/bill number, etc., will be passed back upstream in \nresponse or callback",
  "lang.ark.sys.config.values.leaveByTaskDest": "Determine whether to remove the container from the scene based on the interaction at the task's current node.",
  "lang.ark.interface.config.moving": "Point-to-Point Moving Task",
  "lang.ark.interface.config.taskCallback.field.scanningInformationDesc": "New field added, reason for code sweep error ",
  "lang.ark.task.plugin.deliver.fullContainer": "Deliver full container",
  "lang.ark.interface.config.field.requestIdDesc": "Contained in the message header and used as a unique identifier \nfor a request and its response.\nThe value of this field is defined by the host system and the \nresponse message should have the same value.\nThe GMS needs to guarantee idempotency based on this ID, so \nit needs to be globally unique and it is recommended that the \nUUID is used.",
  "lang.ark.task.exception.priority.gt": "The priority in line {} can only be a positive integer.",
  "lang.ark.sys.config.values.pc": "Always display the content according to the PC layout and presentation.",
  "lang.ark.interface.interfaceDesc.button": "Operation",
  "lang.ark.task.nodeDevice.export.param7.value": "Parameter 7: Parameter Value",
  "lang.ark.interface.config.moving.field.msgTypeDesc": "Message type: the value must be \nMovingRequestMsg",
  "lang.ark.sys.config.robotMediaCatalogue": "Local directory for storing multimedia files",
  "lang.ark.sys.config.canBackTaskFlag": "Does the workstation display a return button?",
  "lang.ark.sys.config.trafficControlInterfaceType": "Traffic control interface access method: synchronous or asynchronous",
  "lang.ark.sys.config.values.cancelInPlace": "After cancellation, stay in the original location",
  "lang.ark.binStopPoint.dropHeight": "Placement height",
  "lang.ark.sys.config.waitPointTaskShowNum": "The number of tasks displayed per page in the waiting point task list.",
  "lang.ark.task.nodeDevice.export.param3.value": "Parameter 3: Parameter Value",
  "lang.ark.interface.config.taskCallback.field.robotErrorDesc": "Robot mission failure",
  "lang.ark.sys.config.values.notStrictOrder": "Do not execute strictly in order",
  "lang.ark.interface.config.dynamicUnitMoving.field.robotProductDesc": "Specify the robot model. If a robot ID is also specified, the robot ID takes precedence.",
  "lang.ark.apiCommonCode.name.overLengthError": "The name exceeds 30 characters.",
  "lang.ark.sys.config.robotDefaultPort": "Robot port",
  "lang.ark.sys.config.rmsRetryCode": "When issuing a task to RMS fails, retries are needed for specific error codes. Configure the error codes to be retried here, separated by commas.",
  "lang.ark.interface.config.field.channelIdDesc": "Included in the message header, identification of the link \nchannel",
  "lang.ark.sys.config.checkFreeRobotFlag": "When assigning tasks, do you need to determine the idle status of the robot? (Determine: Tasks cannot be assigned when there are no idle robots; Do not determine: Task assignment is not affected by whether there are idle robots or not.)",
  "lang.ark.interface.config.dynamicUnitMoving.field.containerCategoryDesc": "Container type code\nUsage scenario: When the first target node (initial node of the process) is an area, and there are more than two container types within the area, a container type needs to be passed when initiating the task.",
  "lang.ark.task.nodeDevice.export.param4.name": "Parameter 4: Parameter Name",
  "lang.ark.sys.config.canCancelDoneTaskFlag": "Does the workstation display a cancel complete button?",
  "lang.ark.sys.config.arkRoot": "ark root directory",
  "lang.ark.interface.config.field.clientCodeDesc": "Contained in the message header. The clientCode, is an \nidentification of the upstream client.\nThis ID is issued by the GMS to the upstream client and the \nsame clientCode is used within the same project service.",
  "lang.ark.task.exception.startPoint.notMatch": "The imported starting point code in line {} does not match the starting point code maintained in the system.",
  "lang.ark.sys.config.callbackMsgTransFlag": 'For point-to-point tasks, interfaces such as "container exit," "container entry," "cancel task," "task resume," and "task callback" can be switched between version 2.x and version 1.x. By default, version 2.x is used. If the switch is enabled, version 1.x will be used.',
  "lang.ark.interface.config.taskCallback.field.scanCodeDesc": "Pallet code",
  "lang.ark.fed.excel.data.name.overLength": "The name in row ({0}) exceeds 30 characters. Please modify it and import again.",
  "lang.ark.sys.config.movingApiResponsePattern": 'Automatically match the format of the "process send" and "process cancel" interfaces, 1: standard format, 0: old format (with header and body).',
  "lang.ark.fed.flowNodeConfig.beforeTaskArrived.destDistance.label": "The time to execute the instruction",
  "lang.ark.interface.config.dynamicUnitMovingDesc": 'You can update the task without any limitations. The process will only end after "isEnd" is submitted.',
  "lang.ark.sys.config.ningdeHttpSoapPassword": "The password for the Ningde SOAP request address",
  "lang.ark.binStopPoint.pickHeight": "Fetching height",
  "lang.ark.task.rule.saveFloor": "Twisting strategy for the same floor",
  "lang.ark.fed.binStopPoint.file.excel.sheet1.name": "locationBaseInfo",
  "lang.ark.interface.config.dynamicUnitMoving.field.isEndDesc": "When the target point is the endpoint of the process, the process is considered complete upon creation, and it will automatically end after execution.",
  "lang.ark.interface.config.taskCallback.field.locationFromDesc": "Actual start point code",
  "lang.ark.sys.config.taskScheduleInterval": "Task scheduling",
  "lang.ark.task.rule.default": "Completed normally",
  "lang.ark.sys.config.stationDisplayMode": "The workstation's display performance on various types of terminals.",
  "lang.ark.sys.config.masterKey": "Key for storing the current running server hostname in Redis.",
  "lang.ark.sys.config.marsRoot": "mars root directory",
  "lang.ark.interface.config.field.taskTypeDesc": "Match the workflow template code in the workflow template",
  "lang.ark.interface.config.dynamicUnitMoving": "Dynamic Unit Moving Task",
  "lang.ark.sys.config.stationNoticeTimes": "The number of times the notification is played on the workstation",
  "lang.ark.sys.config.values.strictOrder": "Execute strictly in order",
  "lang.ark.sys.config.isSyncHandleApi": "Method of processing interface requests: synchronous or asynchronous, default is synchronous.",
  "lang.ark.task.nodeDevice.export.param10.name": "Parameter 10: Parameter Name",
  "lang.ark.sys.config.modbusIp": "The IP used when GMS interacts with external devices (such as physical buttons) through Modbus",
  "lang.ark.interface.config.field.instanceIdDesc": "Instance (task) number",
  "lang.ark.task.plugin.name.take.emptyContainer": "Take empty container",
  "lang.ark.sys.config.values.fullStation": "FULL version workstation",
  "lang.ark.sys.config.rmsHttpAddress": "RMS HTTP request address",
  "lang.ark.interface.config.taskCallback.field.exceptionStateDesc": "Workflow error handling status",
  "lang.ark.sys.config.ningdeHttpSoap": "Ningde SOAP request address",
  "lang.ark.task.nodeDevice.export.param4.value": "Parameter 4: Parameter Value",
  "lang.ark.workflow.action.commandExecutePhase.beforeTaskArrived": "Before the task arrives",
  "lang.ark.interface.config.dynamicUnitMoving.field.toBinTypeDesc": "Target bin location type: 0, 1, 2, 3, etc. If empty, the default is 0. Values other than 0 should be passed according to the project agreement.",
  "lang.ark.task.nodeDevice.export.param9.value": "Parameter 9: Parameter Value",
  "lang.ark.task.plugin.deliver.emptyContainer": "Deliver empty container",
  "lang.ark.task.nodeDevice.export.param9.name": "Parameter 9: Parameter Name",
  "lang.ark.sys.config.loginType": "Login method",
  "lang.ark.task.plugin.name.take.fullContainer": "Take full container",
  "lang.ark.task.nodeDevice.export.param2.name": "Parameter 2: Parameter Name",
  "lang.ark.fed.menu.loginLog": "Login Log",
  "lang.ark.binStopPoint.dockingHeight": "Docking height",
  "lang.ark.interface.config.taskCallback.field.taskPhaseDesc": "Mission Phase",
  "lang.ark.task.nodeDevice.export.param6.name": "Parameter 6: Parameter Name",
  "lang.ark.sys.config.matchWorkflowStrategy": "Process matching strategy: fuzzy matching or precise matching",
  "lang.ark.fed.excel.nodeCodeNotExists": "Node code in row ({0}) does not exist. Please make changes before uploading.",
  "lang.ark.sys.config.authNoPerUrl": "403 page",
  "lang.ark.workflow.startPickUp": "Start picking up",
  "lang.ark.fed.excel.data.stopPointCodeNotExist": "Location code in row ({0}) does not exist in the system. Please modify it and import again.",
  "lang.ark.sys.config.changepwUrl": 'The address of the "Change Password" page in the permission system',
  "lang.ark.interface.config.taskCallback": "Task callback",
  "lang.ark.interface.config.taskCallback.field.workflowExceptionCodeDesc": "Workflow exception code",
  "lang.ark.interface.config.dynamicMovingDesc": "The task flow/step is determined, and the exact position of each step is not determined. Input \nthe start and end location Ids in a single step in turn to complete the work workflow in progress or \ninitiate a new work workflow. \nUsage: \n1. Configure the robot interaction action of the node in the GMS system, and the GMS will \nautomatically generate the workflow and execute the task according to the incoming points from \nthe host system.\n2. GMS will either match the executing workflow instance to the current workflow or \nautomatically generate the workflow and execute the task based on the points incoming from the \nhost system.",
  "lang.ark.sys.config.values.splitTheRollerFetchCommand": "Split the roller fetch command",
  "lang.ark.interface.config.movingMulti.field.flowStrategyDesc": "Flow strategy",
  "lang.ark.task.plugin.name.deliver.emptyContainer": "Deliver empty container",
  "lang.ark.trigger.logClearDesc": "default trigger (clearing all logs)",
  "lang.ark.sys.config.values.callbackToTaskChannel": "Only call back the channel that sent the task",
  "lang.ark.sys.config.authCode": "The Code used by the GMS system when accessing the permission system, making it easier for the permission system to distinguish which system's access it is.",
  "lang.ark.interface.config.dynamicUnitMoving.field.robotSideDesc": "The facing direction of the robot at the target point.\nThis takes effect when the robot's facing direction in the interaction configuration is assigned by the interface.",
  "lang.ark.fed.excel.data.nonNumericFormat": "The docking height, placement height, fetching height, and shelf bin SN in row ({0}) must be numbers. Please modify them and import again.",
  "lang.ark.sys.config.dmpSetContainerAngle": "Switch for whether the container angle needs to be set when entering a container through a DMP (Device Management Platform). If the switch is turned on, the container angle must be set; if turned off, there is no need to set the angle.",
  "lang.ark.sys.config.canDeleteTaskFlag": "Does the workstation remove the workflow return button?",
  "lang.ark.interface.config.dynamicUnitMoving.field.toBinOrderDesc": "Target location sequence number",
  "lang.ark.interface.config.field.scanCodeDesc": "Plallet/shelf code (from upstream system)",
  "lang.ark.task.plugin.name.deliver.fullContainer": "Deliver full container",
  "lang.ark.sys.config.stationService": "workstation websocket address",
  "lang.ark.sys.config.language": "language",
  "lang.ark.sys.config.authId": "The Id used by the GMS system when accessing the permission system, making it easier for the permission system to distinguish which system's access it is.",
  "lang.ark.sys.config.globalCanDeleteTaskFlag": "Does the task monitoring page remove the workflow return button?",
  "lang.ark.sys.config.values.disable": "Close",
  "lang.ark.sys.config.robotReceiveUrl": "Directory where the robot simulator receives files",
  "lang.ark.interface.config.dynamicUnitMoving.field.robotRuleDesc": "If a robot ID is specified and needs to be executed forcibly, this field is required; otherwise, it does not need to be passed, and the default is to prioritize.",
  "lang.ark.sys.config.values.notShow": "Do not display",
  "lang.ark.task.exception.taskType.empty": "Task type in line {} is empty.",
  "lang.ark.fed.arkVersion": "The version of ARK",
  "lang.ark.sys.config.recycleFunctionSwitch": "Recycle bin function switch: when turned on, the recycle bin function can be used (but it also needs to be configured in the process template/page first); when turned off, the recycle bin function cannot be used (there is no recycle bin function available for selection in the process template and process configuration pages).",
  "lang.ark.trigger.dataArchivingDesc": "default trigger (data archiving)",
  "lang.ark.sys.config.rollerFetchCommandSplitConfiguration": "Should the roller robot fetch be split into two commands: start picking up and finished picking up?Note: 1. GMS splitting the pick-up command depends on the callback of the upper assembly starting to roll, and both the robot and RMS need to be upgraded to the version with this callback; 2. After changing to split, the drum interaction pick-up command needs to be reconfigured.",
  "lang.ark.sys.config.values.open": "turn on",
  "lang.ark.trigger.logClear": "clearing all logs",
  "lang.ark.sys.config.rmsChannelId": "The RPC channel ID passed when RMS calls back to GMS",
  "lang.ark.task.exception.priority.empty": "Priority in line {} is empty.",
  "lang.ark.sys.config.dataArchiveMaxNum": "The frequency of data archiving, archiving once for every certain number of data records generated.",
  "lang.ark.sys.config.shelfMovingCallbackMsgFlag": "Switch for whether container changes require callbacks. When turned on, there will be callbacks after changing the container; when turned off, there will be no callbacks.",
  "lang.ark.task.exception.templateCode.notEqual": "Template code in line {} does not match the template code in the system.",
  "lang.ark.apiCommonCode.stopPointCodeExistError": "The location code already exists.",
  "lang.ark.sys.config.ningdeHttpLes": "Ningde LES request address",
  "lang.ark.sys.config.newShelfCodePre": "Automatically generate container code prefix when adding a new shelf",
  "lang.ark.fed.excel.data.stopPointCodeExist": "The shelf bin SN and map point code in row ({0}) already exist. Do not add them repeatedly. Please modify them and import again.",
  "lang.ark.sys.config.isFloorRobot": "When issuing tasks, should the system determine the availability of robots based on the floor: If determined by the floor, the absence of available robots on a specific floor will not affect the task issuance on other floors.",
  "lang.ark.fed.flowNodeConfig.beforeTaskArrived.destDistance.checkMsg": 'Please configure the instructions execution timing for "before the task arrives.": Only numbers greater than 0 can be entered, supporting two decimal places.',
  "lang.ark.sys.config.values.mobile": "Always display the content according to the mobile device's layout and presentation",
  "lang.ark.sys.config.systemStatusConfig": "Interface used when triggering an emergency stop in the RMS (Robot Management System).",
  "lang.ark.task.rule.diffFloor": "Twisting strategy for different floors",
  "lang.ark.sys.config.values.withFloor": "Determine by floor",
  "lang.ark.sys.config.callbackMsgOvertime": "Timeout duration for storing UUID of RPC callback request (in milliseconds)",
  "lang.ark.interface.config.taskCallback.field.taskIdDesc": "Subtask number",
  "lang.ark.task.plugin.take.manuallyChoose": "When taking a container, manually choose whether you need an empty one or a full one.",
  "lang.ark.fed.flowNodeConfig.beforeTaskArrived.destDistance.action": "away from the current node.",
  "lang.ark.sys.config.isFrklift": "Is it a forklift?",
  "lang.ark.fed.flowNodeConfig.beforeTaskArrived.destDistance.condition": "The robot will execute the following instructions when it is",
  "lang.ark.task.exception.endpoint.notMatch": "The imported destination code in line {} does not match the destination code maintained in the system.",
  "lang.ark.interface.config.taskCallback.field.workflowExceptionDesc": "Workflow exception description",
  "lang.ark.fed.backend": "Back-end",
  "lang.ark.task.nodeDevice.export.desc": 'Filling instructions:\n1. Point code: Fill in the map location code or external code for the position.\n2. Device interaction mode: Fill in "device_command" or "interface_command" .',
  "lang.ark.sys.config.values.enable": "Open",
  "lang.ark.interface.config.taskCallback.field.waitLocationDesc": "Waiting point code",
  "lang.ark.apiCommonCode.binCodeAndOrderError": "The shelf bin code and shelf bin SN must either be filled with values or be empty at the same time.",
  "lang.ark.interface.config.dynamicMoving.field.msgTypeDesc": "Message type: the value must be DynamicMovingRequestMsg\nMatch the workflow template code in the workflow template",
  "lang.ark.sys.config.values.cardLogin": "Card swipe login",
  "lang.ark.task.nodeDevice.export.param8.value": "Parameter 8: Parameter Value",
  "lang.ark.interface.config.taskCallback.field.waitDirDesc": "Waiting point robot orientation",
  "lang.ark.task.plugin.deliver.autoReturn": "Empty box automatically returned (used for end nodes)",
  "lang.ark.binStopPoint.stopPointCode": "Location code",
  "lang.ark.sys.config.callbackMessageRetryAndConfirmFilter": "In Socket mode, when retries are enabled, the resend callback filter condition is set to only resend the configured robotPhase and taskPhase status values (multiple groups connected by the | symbol). By default, it is empty (no callbacks will be resent). The format is as follows: ARRIVED,WAITING_NODE|SHELF_ARRIVED,COMPLETED|SHELF_ARRIVED,WAITING_NODE",
  "lang.ark.sys.config.values.forceDelete": "Force cancel",
  "lang.ark.fed.excel.nodeTypeError": "Wrong node type in row ({0}). Please make changes before uploading.",
  "lang.ark.sys.config.deleteTaskIfNeedRemoveShelf": "After canceling the task, should the container leave the site?",
  "lang.ark.sys.config.imageUploadPath": "Default directory for image uploads",
  "lang.ark.sys.config.canUndoTaskFlag": "Does the workstation display an undo button?",
  "lang.ark.task.nodeDevice.export.param2.value": "Parameter 2: Parameter Value",
  "lang.ark.sys.config.iniChainDefinitionTimeout": "The timeout period for the GMS system to query menus within the permission system (unit: milliseconds)",
  "lang.ark.interface.config.taskCallback.field.workflowPhaseDesc": "Workflow Status",
  "lang.ark.trigger.dataArchiving": "data archiving",
  "lang.ark.sys.config.vensVersion": "DMP version",
  "lang.ark.fed.inventoryInfo": "Inventory information",
  "lang.ark.task.nodeDevice.export.param10.value": "Parameter 10: Parameter Value",
  "lang.ark.fed.bundleDate": "Packing date",
  "lang.ark.interface.config.field.locationFromDesc": "Starting point code, if the workstation (workstation) has only one point, the \nworkstation number can also be passed in.\nMatching order: map node -workstation - area",
  "lang.ark.interface.config.taskCallback.field.nodeCodeFromDesc": "Task starting point - node code configured in the workflow",
  "lang.ark.interface.config.dynamicMoving": "Dynamic points Moving Task",
  "lang.ark.binStopPoint.binOrder": "Shelf bin SN",
  "lang.ark.task.nodeDevice.export.param1.name": "Parameter 1: Parameter Name",
  "lang.ark.sys.config.wAreaStation": 'Is it a workstation of "W area"?',
  "lang.ark.sys.config.loginByCardNoUrl": "Link for swipe card login",
  "lang.ark.interface.config.dynamicUnitMoving.field.instructionDesc": "Execute instruction",
  "lang.ark.record.robotCallback.action.completed": "Receive the completion command from the robot.",
  "lang.ark.sys.config.forceCancelUseAthenaInstruction": "The RMS interface called when deleting tasks in the GMS system",
  "lang.ark.sys.config.authUrlServer": "The address for the GMS system to query user information within the permission system",
  "lang.ark.binStopPoint.binCode": "Shelf bin point code",
  "lang.ark.sys.config.values.fuzzyMatch": "Fuzzy match",
  "lang.ark.sys.config.values.callbackToAll": "All channels are called back",
  "lang.ark.sys.config.values.liteStation": "Lite version workstation",
  "lang.ark.sys.config.stationUnique": "Workstation unique login switch, when turned on, the workstation function is enabled; when turned off, the workstation cannot be used (even if the workstation function is configured in the workstation configuration page, it will not take effect).",
  "lang.ark.task.nodeDevice.export.param7.name": "Parameter 7: Parameter Name",
  "lang.ark.task.exception.containerCode.notMatch": "Container code in line {} does not match.",
  "lang.ark.sys.config.values.leaveByTaskLast": "Determine whether to remove the container from the scene based on the interaction at the task's endpoint.",
  "lang.ark.workflow.pickupCompleted": "Finished picking up",
  "lang.ark.sys.config.values.standardFormat": "Standard format",
  "lang.ark.sys.config.values.websocketStop": 'Use "WebSocket interface" to implement an emergency stop of the system',
  "lang.ark.equipment.error.usedByWorkflow": "The hoist is already in use by the workflow/workflow template. Please uninstall the workflow/workflow template first and then modify the feed gate configuration of the hoist.",
  "lang.ark.fed.useBeforeTask": "Query the previous task or not",
  "lang.ark.fed.cellType": "Location type",
  "lang.ark.fed.menu.collapseMenu": "Collapsible sidebar",
  "lang.ark.fed.contents.flowConfig.recycleType.autoRecoveryRule.alert.api": "Tip: This method is applied to the scenario where the upstream system calls the interface to cancel the task provided that the upstream system unloads the return location of goods.",
  "lang.ark.fed.contents.flowConfig.recycleType.autoRecoveryRule.alert.auto": "Tip: Multiple preset locations can be set for matching by the system according to the priority.",
  "lang.ark.fed.contents.flowConfig.recycleType.autoRecoveryRule.alert.manual": "Tip: This method is used to manually cancel the task under monitoring, but manual selection of the return location is unavailable for the scenario where the upstream system calls the interface to cancel the task.",
  "lang.ark.fed.contents.flowConfig.recycleType.autoRecoveryRule.add.required": "Please add a preset location.",
  "lang.ark.fed.contents.flowConfig.recycleType.recycleActionId.add.required": "Please select an interactive action.",
  "lang.ark.fed.menu.testCase": "Task debugging",
  "lang.ark.fed.excel.data.binCode.overLength": "Shelf bin point code in row ({0}) exceeds 30 characters. Please modify it and import again.",
  "lang.ark.fed.excel.data.binOrder.overLength": "Shelf bin SN in row ({0}) exceeds 30 characters. Please modify it and import again.",
  "lang.ark.apiCommonCode.binCode.overLengthError": "Shelf bin point code exceeds 30 characters.",
  "lang.ark.apiCommonCode.binOrder.overLengthError": "Shelf bin SN exceeds 30 characters.",
  "lang.ark.binStopPoint.offsetX": "Upper assembly X-axis offset",
  "lang.ark.fed.excel.data.binOrder.offsetXNumericError": "The X-axis offset of the upper assembly on line {0} cannot be negative, please modify and re-import",
  "lang.ark.sys.config.publicApiVersion": "External interface version",
  'lang.ark.fed.excel.data.binOrder.ButtonBoxInformationWasNotObtainedAccordingToTheEncoding':"Failed to retrieve button box information based on the code",
  "lang.ark.fed.menu.callerConfiguration":"Caller",
  "lang.ark.fed.excel.data.binOrder.theEncodingAlreadyExists": "The encoding already exists",
}
