<template>
  <gms-dialog
    :visible="visible"
    width="50%"
    :is-need-footer="false"
    :title="$t('geekplus.gms.client.screen.container.form.viewExample')"
    @closed="$emit('update:visible', false)"
  >
    <div :class="cn.dialog_body">
      <section :class="cn.section_wrapper">
        <h3>{{ $t("geekplus.gms.client.screen.bracket.example.groundBracketExplain") }}</h3>
        <img :src="groundDiagram" alt="ground-diagram" />
      </section>
      <section :class="cn.section_wrapper">
        <h3>
          {{ $t("geekplus.gms.client.screen.bracket.example.groundBracketModel") }}
        </h3>
        <div :class="cn.red_tips">
          <p>{{ $t("geekplus.gms.client.screen.bracket.example.special.tips") }}</p>
          <p>{{ $t("geekplus.gms.client.screen.bracket.example.special.tips1") }}</p>
          <p>{{ $t("geekplus.gms.client.screen.bracket.example.special.tips2") }}</p>
          <p>{{ $t("geekplus.gms.client.screen.bracket.example.special.tips3") }}</p>
        </div>
      </section>
      <section :class="cn.section_wrapper" class="tw-flex tw-flex-col tw-gap-1">
        <h3>{{ $t("geekplus.gms.client.screen.bracket.example.standardBracketModel") }}</h3>
        <p>{{ $t("geekplus.gms.client.screen.bracket.example.standardBracketExplain") }}</p>
        <img :src="standImg" alt="ground-diagram" class="tw-w-96 tw-self-center" />
      </section>
      <section :class="cn.section_wrapper">
        <h3>{{ $t("geekplus.gms.client.screen.bracket.example.specialBracketModel") }}</h3>
        <p>{{ $t("geekplus.gms.client.screen.bracket.example.specialBracketExplain") }}</p>
      </section>
      <section :class="cn.section_wrapper">
        <img :src="specialImg1" alt="" class="tw-w-80 tw-mr-2" />
        <img :src="specialImg2" alt="" class="tw-w-80" />
      </section>
    </div>
  </gms-dialog>
</template>

<script>
import { defineComponent } from "vue";
import getContainerImage from "@/utils/containerImages";
const groundDiagram = getContainerImage("ground-bracket.png");
import standImg from "@/gms/assets/images/container/standard-bracket.png";
import specialImg1 from "@/gms/assets/images/container/special-bracket1.png";
import specialImg2 from "@/gms/assets/images/container/special-bracket2.png";

export default defineComponent({
  props: {
    visible: { type: Boolean, default: false },
  },
  setup() {
    return {
      groundDiagram,
      standImg,
      specialImg1,
      specialImg2,
    };
  },
});
</script>
<style lang="scss" module="cn">
.dialog_body {
  .section_wrapper {
    margin: 30px 20px;
    &:first-child {
      margin-top: 0;
    }
    .red_tips {
      color: #cb2421;
    }
    .text_indent {
      text-indent: 2.5em;
    }
  }
}
h3,
.title {
  font-size: 14px;
  font-weight: 700;
  line-height: 2;
}
.big_title {
  font-size: 18px;
}
</style>
