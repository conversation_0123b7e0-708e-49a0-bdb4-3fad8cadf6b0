<template>
  <gms-dialog
    :destroy-on-close="true"
    :visible="visible"
    width="65%"
    :title="dialogTitle"
    @closed="$emit('update:visible', false)"
  >
    <div :key="formKey" class="container">
      <form-section :title="t('geekplus.gms.client.screen.areaEditIndex.basicInfo')" :columns="3">
        <!-- 区域编码 -->
        <form-item v-bind="formGroup.hostCellCode" :disabled="mode === 'view'" />
        <!-- 区域名称 -->
        <form-item v-bind="formGroup.areaName" :disabled="mode === 'view'" />
        <!-- 存储区类型 -->
        <form-item v-bind="formGroup.storageType" :disabled="mode === 'view'" />
      </form-section>
      <form-section class="tw-mt-7" :title="t('geekplus.gms.client.screen.areaEditIndex.scopeAndConfig')">
        <!-- 点位编码 -->
        <template v-if="isNormalStorage">
          <div style="max-width: calc((100% - 64px) / 3)" class="tw-mt-1">
            <label class="code_label">{{ t("geekplus.gms.client.screen.areaEditIndex.locationCode") }}:</label>
            <PositionSelect
              ref="positionSelectRef"
              v-model="selectedPoints"
              :groups="positionSelectGroups"
              :disable-filter="(item) => normalStorageDisableFilter(item, selectedPoints)"
              multiple
              filterable
              :overlay-z-index="9999"
              :mode="mode === 'view' ? 'view' : 'edit'"
              :class="mode === 'view' ? 'viewclass' : ''"
              :show-all-levels="false"
              :show-parent-checkbox="false"
              @change="handlePositionSelectChange"
            />
            <div v-show="requiredFieldFlag" class="error_msg">
              {{ t("geekplus.gms.client.screen.areaEditIndex.pleaseFillRequiredFields") }}
            </div>
          </div>
          <!-- 存储策略 -->
          <form-item
            v-bind="hitStrategyForm.hitStrategy"
            style="max-width: calc((100% - 64px) / 3)"
            :disabled="mode === 'view'"
          />
        </template>
        <!--港道数量  和港道命中策略  -->
        <template v-if="isDenseStorage">
          <div class="tw-flex tw-flex-col tw-gap-8">
            <div style="max-width: calc(100% * 2 / 3); display: flex; gap: 32px">
              <form-item v-bind="formGroup.portChannelCount" style="flex: 1" :disabled="mode === 'view'" />
              <form-item v-bind="groupStrategyForm.groupStrategy" style="flex: 1" :disabled="mode === 'view'" />
            </div>
            <div v-if="dataReady" style="width: 100%; display: flex; flex-direction: column">
              <label>{{ t("geekplus.gms.client.screen.areaEditIndex.aisleInfo") }}</label>
              <table-wrapper style="width: 100%" v-bind="tableState">
                <template #codeSlot="{ row }">
                  <gp-input
                    v-model="row.hostCellCode"
                    :placeholder="t('geekplus.gms.client.screen.areaEditIndex.enterAisleCode')"
                    :disabled="mode === 'view'"
                  />
                </template>
                <template #decisionPointSlot="{ row }">
                  <PositionSelect
                    v-model="row.decisionPoint"
                    :groups="onlyGeneralPointGroup"
                    :overlay-z-index="9999"
                    :disable-filter="(item) => alreadySelectedFilter(item, row)"
                    :show-all-levels="false"
                    :mode="mode === 'view' ? 'view' : 'edit'"
                    :class="mode === 'view' ? 'viewclass' : ''"
                    @change="(val) => handleDecisionPointChange(row, val)"
                  />
                </template>
                <template #containPointsSlot="{ row }">
                  <PositionSelect
                    v-model="row.selectedPoints"
                    :groups="positionSelectGroups"
                    :multiple="true"
                    :show-parent-checkbox="false"
                    :mode="mode === 'view' ? 'view' : 'edit'"
                    :overlay-z-index="9999"
                    :show-all-levels="false"
                    :class="mode === 'view' ? 'viewclass' : ''"
                    :disable-filter="(item) => mutualDisableFilter(item, row)"
                    @change="(val) => handleContainPointsChange(row, val)"
                  />
                </template>
                <template #maxTaskCountSlot="{ row }">
                  <gp-input
                    v-model="row.maxTaskCount"
                    :placeholder="t('geekplus.gms.client.screen.areaEditIndex.enterMaxTaskNumber')"
                    :disabled="mode === 'view'"
                  />
                </template>
              </table-wrapper>
            </div>
          </div>
        </template>
      </form-section>
    </div>

    <template #footer>
      <gp-button @click="$emit('update:visible', false)">
        {{ $t("lang.ark.waveTaskStatus.disCanceled") }}
      </gp-button>
      <gp-button v-show="mode !== 'view'" type="primary" @click="handleConfirm">
        {{ $t("geekplus.gms.client.commons.btn.confirm") }}
      </gp-button>
    </template>
  </gms-dialog>
</template>
<script>
import * as R from "ramda";
import fp from "lodash/fp";
import { defineComponent, ref, reactive, toRefs, computed, watch, onMounted } from "vue";
import {
  createFormItemGroup,
  FormItem,
  formItemTypes,
  FormSection,
  validators,
} from "@srpe-fe/uic-el-form-wrapper-vue2";
import { getNodesWithPoints, AreaNodeTypeDict, createChildrenNodeLabel } from "../common.js";
import PositionSelect from "gms-components/business/position-select";
import TableWrapper, { useTableState, columnTypes } from "@srpe-fe/uic-el-table-wrapper-vue2";
import { saveAreaInfo } from "gms-apis/area";
import { AREA_FUNC_TYPE } from "gms-constants";
import { queryCellCodeList, checkWhenDeleteNode } from "gms-apis/worksite";
import { getGenerateCode, checkWhenUnbind } from "gms-apis/commons";
import { getPalletCodeList, getAreaUsedPalletCode } from "gms-apis/container";
import { Message } from "geekplus-ui";
import { useI18n } from "gms-hooks";
export default defineComponent({
  name: "DialogAddStorage",
  components: {
    FormSection,
    FormItem,
    createFormItemGroup,
    PositionSelect,
    TableWrapper,
  },
  props: {
    visible: { type: Boolean, default: false },
    initOptions: { type: Object, default: () => ({}) },
    mode: { type: String, default: "add" },
    record: { type: Object, default: () => ({}) },
    options: { type: Object, default: () => ({}) },
    disabledStationCellCode: { type: Array, default: () => [] },
    palletCodeHashmap: { type: Object, default: () => ({}) },
    selectedCellCodes: { type: Array, default: () => [] },
  },
  emits: ["update:visible", "save-success"],
  setup(props, { emit }) {
    const t = useI18n();
    const { initOptions, record, options, disabledStationCellCode, selectedCellCodes } = toRefs(props);
    const getStorageTypesList = [
      {
        value: 10,
        label: t("geekplus.gms.client.screen.areaEditIndex.regularStorageArea"),
      },
      {
        value: 20,
        label: t("geekplus.gms.client.screen.areaEditIndex.denseStorageArea"),
      },
    ];
    // 控制必填的变量
    const requiredFieldFlag = ref(false);
    // 数据是否准备就绪
    const dataReady = ref(false);
    const settingFacility = reactive({});
    // 获取节点了列表
    const cellCodeList = reactive([]); // 节点列表
    // 存储策略的数据
    const cscl = ref([]);
    // 港道命中策略的数据
    const portChannelHitStrategy = ref([]);

    // 添加 formKey 用于强制重建
    const formKey = ref(Date.now());
    const hitStrategyForm = computed(() => {
      return createFormItemGroup({
        hitStrategy: {
          type: "el-select",
          labelText: t("geekplus.gms.client.screen.areaEditIndex.storageStrategy"),
          placeholder: t("geekplus.gms.client.screen.areaEditIndex.pleaseSelect"),
          labelWidth: "110px",
          options: cscl,
          validators: [validators.required],
          span: 8,
          labelPosition: "top",
        },
      });
    });

    // 同样，groupStrategy 也可以改为计算属性
    const groupStrategyForm = computed(() => {
      return createFormItemGroup({
        groupStrategy: {
          type: "el-select",
          labelText: t("geekplus.gms.client.screen.areaEditIndex.aisleHitStrategy"),
          placeholder: t("geekplus.gms.client.screen.areaEditIndex.pleaseSelect"),
          labelWidth: "110px",
          options: portChannelHitStrategy,
          span: 8,
          labelPosition: "top",
          validators: [validators.required],
        },
      });
    });

    // 创建 formGroup 的函数
    const createFormGroup = () => {
      return createFormItemGroup({
        hostCellCode: {
          type: "el-input",
          labelText: t("geekplus.gms.client.screen.areaEditIndex.areaCode"),
          placeholder: t("geekplus.gms.client.screen.areaEditIndex.enterAreaCode"),
          labelWidth: "110px",
          labelPosition: "top",
          validators: [validators.required],
          value: initOptions.value.hostCellCode,
        },
        areaName: {
          type: "el-input",
          labelText: t("geekplus.gms.client.screen.areaEditIndex.areaName"),
          placeholder: t("geekplus.gms.client.screen.areaEditIndex.enterAreaName"),
          labelWidth: "110px",
          validators: [validators.required],
          labelPosition: "top",
        },
        storageType: {
          type: "el-select",
          labelText: t("geekplus.gms.client.screen.areaEditIndex.storageAreaType"),
          placeholder: t("geekplus.gms.client.screen.areaEditIndex.enterStorageAreaType"),
          labelPosition: "top",
          options: getStorageTypesList,
          value: record.value.storageType || 10,
        },
        portChannelCount: {
          type: formItemTypes.EL_INPUT_NUMBER,
          labelText: t("geekplus.gms.client.screen.areaEditIndex.aisleQuantity"),
          precision: 0,
          value: 1,
          min: 1,
          max: 10000,
          labelPosition: "top",
          controlsPosition: "default",
        },
      });
    };

    // 将 formGroup 改为 ref
    const formGroup = ref(createFormGroup());

    // 显示点位和托盘位
    const positionSelectGroups = ref(["GENERAL_POINT", "PALLET_POINT"]);
    // 只显示点位
    const onlyGeneralPointGroup = ref(["GENERAL_POINT"]);
    // 记录当前选择的点位类型
    const currentSelectedType = ref(null);
    // 托盘位数据的字典
    const palletCodeDict = ref([]);
    const allOption = ref({});
    const areaUsePalletCodes = ref([]); //区域中已被使用的托盘位
    const generateUniqueGroupId = () => fp.uniqueId("group_");
    const groupTableData = ref([
      {
        hostCellCode: "",
        cellType: "CHILD_AREA_CELL",
        childrenNode: [], // 存储点位
        palletChildrenNode: [], // 存储托盘位
        selectedPoints: [], // 用于 PositionSelect 组件的显示
        hitStrategy: "", // 存储区类型
        reflectCell: "", // 决策点位
        decisionPoint: null,
        maxTaskCount: 1, //同时最大任务数
        nodeType: "base", // 这里是区别托盘位还是点位的
        trafficFunctionType: "密集存储区", // 密集存储固定为存储区类型
        functionType: "20", // 密集存储固定为存储区类型
        __key: generateUniqueGroupId(),
      },
    ]);

    // 点位过滤函数
    // 这个先留着到了后期会用到
    const disableGroupFilter = (item) => {
      // 如果未选任何类型，不禁用
      if (!currentSelectedType.value) return false;
      // 只要不是当前类型的都禁用
      return item.$group !== currentSelectedType.value;
    };
    // 生成唯一标识
    // 是否为密集存储区
    const isDenseStorage = computed(() => formGroup.value.storageType.value == 20); // 20为密集存储区
    // 是否为普通存储区
    const isNormalStorage = computed(() => formGroup.value.storageType.value == 10);
    const dialogTitle = computed(() => {
      if (props.mode === "add") {
        return t("geekplus.gms.client.screen.areaEditIndex.addStorageArea");
      } else if (props.mode === "edit") {
        return t("geekplus.gms.client.screen.areaEditIndex.editStorageArea");
      } else if (props.mode === "view") {
        return t("geekplus.gms.client.screen.areaEditIndex.storageAreaDetails");
      }
      return "";
    });
    // 选中的点位数据
    const selectedPoints = ref([]);
    // 这个是在新增或编辑普通存储区域的时候，用来收集所有选中的点位和托盘位
    const allNodeList = ref([]);
    // 非表单字段统一用 reactive 管理
    const extraData = reactive({
      nodeType: "",
      childrenNode: [],
      palletChildrenNode: [],
    });
    // 区域中使用已使用的托盘位置
    const areaUsedPalletCodes = ref([]);
    //存储表格数据中所有的点位信息和托盘位信息,在新增或编辑的时候我们对已选的托盘位和点位信息进行收集进而进行禁用
    const allSelectedPoints = ref([]);
    // 获取节点编码
    // 这是就是 老版的allOptions

    const allOptions = computed(() => {
      return {
        ...props.options,
        palletCodeDict: palletCodeDict.value,
      };
    });
    const getCellCode = async () => {
      try {
        // 获取点位节点
        // const { childrenNode } = settingFacility;
        const data = await queryCellCodeList();
        cellCodeList.push(...data);
        // 使用 myTransform.arrToStringOptions 转换数据格式
        // cellCodeList.value = myTransform.arrToStringOptions(data, "hostCellCode", "cellCode");
      } catch (error) {
        console.error("获取节点编码失败:", error);
      }
    };
    // 区域中已使用的托盘位置
    const getAreaUsedPalletCodes = async () => {
      const data = await getAreaUsedPalletCode();

      areaUsedPalletCodes.value = data || [];
    };
    // 普通存储区的禁用过滤函数
    const normalStorageDisableFilter = (item, selectedPoints) => {
      const filteredSelectedCellCodes = selectedCellCodes.value.filter((code) => !allNodeList.value.includes(code));

      // 如果 item.$value 在过滤后的 selectedCellCodes 中，则禁用
      if (filteredSelectedCellCodes.includes(item.$value)) {
        return true;
      }
      if (!selectedPoints || selectedPoints.length === 0) {
        return false;
      }

      // 获取第一个选中项的类型
      const firstSelected = selectedPoints[0];
      const selectedType = Array.isArray(firstSelected) ? firstSelected[0] : firstSelected.$group;
      const currentType = item.$group;

      // 如果类型不同，则禁用
      return selectedType !== currentType;
    };

    // 密集存储区的禁用决策点位的数据逻辑
    const alreadySelectedFilter = (item, row) => {
      if (row.reflectCell === item.$value) {
        return false;
      }

      // 检查是否在 selectedCellCodes 中
      if (selectedCellCodes.value.includes(item.$value)) {
        return true;
      }

      // 检查是否在其他表格行的 childrenNode 中
      if (allTableChildrenNodes.value.includes(item.$value)) {
        return true;
      }

      return false;
    };
    // 初始化港道表格数据
    onMounted(async () => {
      if (groupTableData.value.length > 0) {
        for (let i = 0; i < groupTableData.value.length; i++) {
          if (!groupTableData.value[i].hostCellCode) {
            try {
              const code = await getGenerateCode("P");
              groupTableData.value[i].hostCellCode = code;
            } catch (error) {
              const defaultCode = formGroup.value.hostCellCode.value
                ? `${formGroup.value.hostCellCode.value}${i + 1}`
                : `A${i + 1}`;
              groupTableData.value[i].hostCellCode = defaultCode;
            }
          }
          // 初始化childrenNodeLabel
          const row = groupTableData.value[i];
          if (row.nodeType === "base" && row.childrenNode?.length) {
            row.childrenNodeLabel = row.childrenNode.join(",");
          } else if (row.nodeType === "pallet" && row.palletChildrenNode?.length) {
            row.childrenNodeLabel = row.palletChildrenNode.map((code) => palletCodeDict.value[code] || code).join(",");
          } else {
            row.childrenNodeLabel = "";
          }
        }
        tableState.data = groupTableData.value;
      }
      getCellCode();
      getAreaUsedPalletCodes();
      // 区域中占有的所有点位信息
      getServiceCode();
      dataReady.value = true;
    });
    // 这个是老版获取服务点位 的逻辑 这里我主要是对 palletCodeDict 进行初始化
    const getServiceCode = async () => {
      const datas = await getPalletCodeList();
      const { palletChildrenNode } = settingFacility;

      // 托盘位
      palletCodeDict.value = datas.map((item) => {
        return {
          cellName: item.palletLatticeCode,
          cellType: 100,
          cellTypeName: "",
          hostCellCode: item.palletLatticeHostCode || item.palletLatticeCode,
          id: item.palletLatticeCode,
          label: item.palletLatticeHostCode || item.palletLatticeCode,
          value: item.palletLatticeCode,
          disabled:
            areaUsePalletCodes.value.includes(item.palletLatticeCode) &&
            !palletChildrenNode.includes(item.palletLatticeCode),
        };
      });
    };

    // 处理点位选择变化
    const handlePositionSelectChange = (selected, items) => {
      if (!items.length) {
        // 控制必填提示的
        requiredFieldFlag.value = true;
        selectedPoints.value = [];
        currentSelectedType.value = null;
        extraData.nodeType = "";
        extraData.childrenNode = [];
        extraData.palletChildrenNode = [];
        // allNodeList.value = []; // 清空已选点
        return;
      }
      requiredFieldFlag.value = false;
      const firstType = items[0].$group;
      // const isAllSameType = items.every((item) => item.$group === firstType);
      // if (!isAllSameType) {
      //   Message.error(t("geekplus.gms.client.screen.areaEditIndex.pleaseSelectOneDataType"));
      //   return;
      // }
      selectedPoints.value = items.map((item) => [item.$group, item.$value]);
      currentSelectedType.value = firstType;
      if (firstType === "GENERAL_POINT") {
        extraData.nodeType = AreaNodeTypeDict.BASE;
        extraData.childrenNode = items.map((item) => item.$value);
        extraData.palletChildrenNode = [];
        // allNodeList.value = [...extraData.childrenNode]; // 更新已选点位
      } else {
        extraData.nodeType = AreaNodeTypeDict.PALLET;
        extraData.palletChildrenNode = items.map((item) => item.$value);
        extraData.childrenNode = [];
        // allNodeList.value = [...extraData.palletChildrenNode]; // 更新已选托盘位
      }
    };
    // 判断是托盘位还是点位
    const getNodeType = (isPallet) => {
      return isPallet ? AreaNodeTypeDict.PALLET : AreaNodeTypeDict.BASE;
    };
    // 将回显的方法给提取出来
    const fillFormByInitOptions = (newVal) => {
      if (newVal) {
        const {
          areaName,
          hostCellCode,
          childrenType = null,
          childrenNode,
          hitStrategy,
          increaseStrategy,
          queueStrategy,
          containerTypes,
          palletChildNodes,
          ynGroup,
          childrenAreas,
        } = newVal;
        // 判断是否托盘位
        const isPallet = palletChildNodes && palletChildNodes.length;

        // 使用 Object.assign 更新 settingFacility
        Object.assign(settingFacility, {
          ...newVal,
          nodeType: getNodeType(isPallet),
          areaName,
          hostCellCode: String(hostCellCode || ""),
          childrenType,
          hitStrategy: hitStrategy ? String(hitStrategy) : "",
          increaseStrategy: increaseStrategy ? String(increaseStrategy) : "",
          queueStrategy: queueStrategy ? String(queueStrategy) : "",
          containerTypeCodes: containerTypes ? R.map(R.prop("containerTypeCode"), containerTypes) : [],
          containerTypes: [], // 后台需要通过这个字段记录容器类型
          palletChildrenNode: palletChildNodes ? palletChildNodes.split(",") : [],
          // 由于后端的 childrenNode 没有区分托盘位和点位类型
          // 前端需要先判断出是否托盘类型，再修正后端的 childrenNode 字段
          childrenNode: isPallet ? [] : childrenNode,
        });
        cscl.value = settingFacility.hitStrategyDict;
        // 主表单数据的回显
        formGroup.value.$setData(
          {
            areaName: settingFacility.areaName || "",
            hostCellCode: settingFacility.hostCellCode || "",
            storageType: settingFacility.storageType || 10,
            // hitStrategy: settingFacility.hitStrategy || "",
            portChannelCount: settingFacility?.childrenAreas?.length || 1,
            // groupStrategy: settingFacility.groupStrategy ? String(settingFacility.groupStrategy) : "",
          },
          { skipValidation: true }
        );
        // 存储策略表单的回显
        hitStrategyForm.value.$setData(
          {
            hitStrategy: settingFacility.hitStrategy || "1",
          },
          { skipValidation: true }
        );
        // 港道命中策略的的数据回显
        groupStrategyForm.value.$setData(
          {
            groupStrategy: settingFacility.groupStrategy ? String(settingFacility.groupStrategy) : "",
          },
          { skipValidation: true }
        );
        // 这是港道命中数量的数据
        portChannelHitStrategy.value = settingFacility.groupStrategyDict;
        // 回显表单数据
        // selectedPoints.value = initSelectedPoints.value;
        // console.log("initSelectedPoints", initSelectedPoints.value);
        currentSelectedType.value = settingFacility.nodeType === "pallet" ? "PALLET_POINT" : "GENERAL_POINT";
        // 在新增或者编辑的时候收集普通区域的点位和托盘位的值
        allNodeList.value = [...settingFacility.childrenNode, ...settingFacility.palletChildrenNode];
        if (settingFacility.nodeType === "pallet") {
          // 这两个是回显初始化的值的多选点位的值的
          extraData.palletChildrenNode = settingFacility.palletChildrenNode;
          extraData.nodeType = "pallet";
          // 托盘位回显
          selectedPoints.value = settingFacility.palletChildrenNode.map((code) => {
            return ["PALLET_POINT", code];
          });

          currentSelectedType.value = "PALLET_POINT";
        } else {
          selectedPoints.value = settingFacility.childrenNode.map((code) => {
            return ["GENERAL_POINT", code, { name: code }];
          });
          currentSelectedType.value = "GENERAL_POINT";
          // 这两个是回显初始化的值的多选托盘位的值的
          extraData.childrenNode = settingFacility.childrenNode;
          extraData.nodeType = "base";
        }

        // 回显港道信息的表格
        if (childrenAreas && childrenAreas.length > 0) {
          const groupTableDataFromServer = getGroupTableDataFromServer(childrenAreas);
          groupTableData.value = groupTableDataFromServer;
          tableState.data = groupTableDataFromServer;
        }
      }
    };

    // 监听 initOptions 变化
    watch(
      () => props.initOptions,
      (newVal) => {
        if (props.visible) {
          fillFormByInitOptions(newVal);
        }
      },
      { immediate: true, deep: true }
    );
    //监听options变化
    watch(
      [() => options.value, () => disabledStationCellCode.value, () => selectedCellCodes.value],
      ([newOptions, newDisabled, newSelected]) => {
        allOption.value = {
          ...newOptions,
          disabledStationCellCode: newDisabled || [],
          selectedCellCodes: newSelected || [],
        };
      },
      { immediate: true, deep: true }
    );
    // 监听弹窗的显示状态
    watch(
      () => props.visible,
      (val) => {
        if (val) {
          // 1. 先重置所有内部状态
          resetAllData();
          dataReady.value = false;

          // 2. 创建新的 formGroup
          formGroup.value = createFormGroup();

          // 更新 formKey 强制重建
          formKey.value = Date.now();

          // 3. 主动回显 props.initOptions
          fillFormByInitOptions(props.initOptions);

          dataReady.value = true;
        } else {
          // 关闭时只重置
          resetAllData();
          dataReady.value = false;
        }
      }
    );

    const initSelectedPoints = computed(() => {
      if (!settingFacility) return [];

      // 根据 nodeType 决定使用哪个数据源
      const sourceData =
        settingFacility.nodeType === "pallet" ? settingFacility.palletChildrenNode : settingFacility.childrenNode;

      // 转换数据格式
      return sourceData.map((code) => ({
        $group: settingFacility.nodeType === "pallet" ? "PALLET_POINT" : "GENERAL_POINT",
        $value: code,
        $label: code,
        $key: code,
        hostCode: code,
      }));
    });
    // 巷道信息表格列配置
    const groupTableColumns = computed(() => [
      { type: "index", label: t("geekplus.gms.client.screen.areaEditIndex.serialNumber") },
      {
        type: columnTypes.GEEK_CUSTOMIZED,
        prop: "hostCellCode",
        label: t("geekplus.gms.client.screen.areaEditIndex.aisleCode"),
        cellSlotName: "codeSlot",
      },
      {
        type: columnTypes.GEEK_CUSTOMIZED,
        prop: "decisionPoint",
        label: t("geekplus.gms.client.screen.areaEditIndex.decisionPoint"),
        cellSlotName: "decisionPointSlot",
      },
      {
        type: columnTypes.GEEK_CUSTOMIZED,
        label: t("geekplus.gms.client.screen.areaEditIndex.includedPoint"),
        cellSlotName: "containPointsSlot",
      },
      {
        type: columnTypes.GEEK_CUSTOMIZED,
        prop: "maxTaskCount",
        label: t("geekplus.gms.client.screen.areaEditIndex.maxConcurrentTasks"),
        cellSlotName: "maxTaskCountSlot",
      },
    ]);

    const tableState = useTableState({
      columns: groupTableColumns.value,
      data: groupTableData.value,
      pagination: false,
      border: true,
      stripe: false,
      size: "mini",
    });

    const customTableColumns = computed(() => groupTableColumns.value.filter((col) => col.type !== "index"));

    // 港道数量联动表格行数
    watch(
      () => formGroup.value.portChannelCount.value,
      async (newVal) => {
        const count = Number(newVal) || 1;
        const oldCount = groupTableData.value.length;
        const newData = [...groupTableData.value];

        if (count > oldCount) {
          const addCount = count - oldCount;
          for (let i = 0; i < addCount; i++) {
            try {
              const code = await getGenerateCode("P");
              newData.push({
                hostCellCode: code,
                cellType: "CHILD_AREA_CELL",
                childrenNode: [], // 存储点位
                palletChildrenNode: [], // 存储托盘位
                selectedPoints: [], // 用于 PositionSelect 组件的显示
                //hitStrategy: formGroup.value.groupStrategy.value, // 港道命中策略
                reflectCell: "", // 决策点位
                decisionPoint: null,
                maxTaskCount: 1, //同时最大任务数
                nodeType: "base", // 这里是区别托盘位还是点位的
                trafficFunctionType: "密集存储区", // 密集存储固定为存储区类型
                functionType: "20", // 密集存储固定为存储区类型
                childrenNodeLabel: "", // 已选点位标签
                _key: `group_${generateUniqueGroupId()}`,
              });
            } catch (error) {
              console.error("获取港道编码失败:", error);
              const defaultCode = formGroup.value.hostCellCode.value
                ? `${formGroup.value.hostCellCode.value}${oldCount + i + 1}`
                : `A${oldCount + i + 1}`;
              newData.push({
                hostCellCode: defaultCode,
                cellType: "CHILD_AREA_CELL",
                childrenNode: [],
                palletChildrenNode: [],
                selectedPoints: [],
                //hitStrategy: formGroup.value.groupStrategy.value,
                reflectCell: "",
                decisionPoint: null,
                maxTaskCount: 1,
                nodeType: "base",
                trafficFunctionType: "密集存储区",
                functionType: "20",
                childrenNodeLabel: "",
                _key: `group_${generateUniqueGroupId()}`,
              });
            }
          }
        } else if (count < oldCount) {
          newData.length = count;
        }

        // 更新每一行的 childrenNodeLabel
        newData.forEach((row) => {
          row.childrenNodeLabel = createChildrenNodeLabel(
            {
              childrenNode: row.childrenNode,
              palletChildNodes: row.palletChildrenNode?.join(","),
              palletChildrenNode: row.palletChildrenNode,
            },
            props.palletCodeHashmap
          );
        });

        groupTableData.value = newData;
        tableState.data = newData;
      },
      { immediate: true }
    );
    const allTableChildrenNodes = computed(() => {
      const nodes = [];
      tableState.data.forEach((row) => {
        // 收集所有行的 childrenNode
        if (row.childrenNode && row.childrenNode.length) {
          nodes.push(...row.childrenNode);
        }
      });
      return nodes;
    });
    // 收集所有表格行的托盘位信息 点位信息还有决策点位信息
    const allTableData = computed(() => {
      const data = {
        childrenNodes: [],
        palletChildrenNodes: [],
        reflectCells: [],
      };

      tableState.data.forEach((row) => {
        // 收集点位
        if (row.childrenNode && row.childrenNode.length) {
          data.childrenNodes.push(...row.childrenNode);
        }

        // 收集托盘位
        if (row.palletChildrenNode && row.palletChildrenNode.length) {
          data.palletChildrenNodes.push(...row.palletChildrenNode);
        }

        // 收集决策点位
        if (row.reflectCell) {
          data.reflectCells.push(row.reflectCell);
        }
      });

      return data;
    });

    // 响应式表格
    watch(
      groupTableData,
      (newVal) => {
        console.log("groupTableData changed:", JSON.parse(JSON.stringify(newVal)));
      },
      { deep: true }
    );
    // 监听存储类型的变化   并且每次从新生成form 表单的实例
    watch(
      () => formGroup.value.storageType.value,
      async (newVal, oldVal) => {
        // 仅当从普通存储区切换到密集存储区时处理
        if (newVal === 20) {
          // 确保港道数量至少为1
          if (!formGroup.value.portChannelCount.value || formGroup.value.portChannelCount.value < 1) {
            formGroup.value.portChannelCount.value = 1;
          }

          // 初始化表格数据
          if (groupTableData.value.length === 0 || !groupTableData.value[0].hostCellCode) {
            try {
              const code = await getGenerateCode("P");
              groupTableData.value = [
                {
                  hostCellCode: code,
                  cellType: "CHILD_AREA_CELL",
                  childrenNode: [],
                  palletChildrenNode: [],
                  selectedPoints: [],
                  //hitStrategy: formGroup.value.groupStrategy.value || "",
                  reflectCell: "",
                  decisionPoint: null,
                  maxTaskCount: 1,
                  nodeType: "base",
                  trafficFunctionType: "密集存储区",
                  functionType: "20",
                  __key: generateUniqueGroupId(),
                },
              ];

              // 强制更新表格
              tableState.data = [...groupTableData.value];
            } catch (error) {
              // 获取编码失败时使用备用方案
              const defaultCode = formGroup.value.hostCellCode.value ? `${formGroup.value.hostCellCode.value}1` : `A1`;

              groupTableData.value = [
                {
                  hostCellCode: defaultCode,
                  // 其他字段同上...
                },
              ];

              tableState.data = [...groupTableData.value];
            }
          }
        }
      }
    );
    // 监听弹窗关闭时重置所有数据
    watch(
      () => props.visible,
      (val) => {
        if (!val) {
          // 弹窗关闭时重置所有数据
          resetAllData();
        }
      }
    );
    // 监听密集存储区变化是否加必填
    watch(
      () => isDenseStorage.value,
      (isDense) => {
        formGroup.value.groupStrategy.validators = isDense ? [validators.required] : [];
      },
      { immediate: true }
    );
    // 添加对 mode 的监听，处理详情和编辑的切换
    watch(
      () => props.mode,
      (newMode) => {
        // 更新 formKey 强制重建
        formKey.value = Date.now();

        // 重建 formGroup 以清除所有状态残留
        formGroup.value = createFormGroup();

        // 重新回显数据
        fillFormByInitOptions(props.initOptions);
      }
    );

    // 获取密集存储区的参数
    const getGroupRequestData = (model, hitStrategData, groupStrategyData) => {
      let childrenAreas = [];
      childrenAreas = tableState.data.map((item) => {
        return {
          id: item.id || "",
          hostCellCode: item.hostCellCode || "",
          areaName: item.areaName || "",
          containerTypeCodes: item.containerTypeCodes || [],
          hitStrategy: 6, //默认是密集存储
          maxTaskSize: item.maxTaskCount || 1,
          reflectCell: item.reflectCell || "",
          childrenNode: item.nodeType === AreaNodeTypeDict.PALLET ? [] : item.childrenNode,
          palletChildrenNode: item.nodeType === AreaNodeTypeDict.PALLET ? item.palletChildrenNode : [],
          cellType: item.cellType || "CHILD_AREA_CELL",
        };
      });
      const filteredModel = fp.omit(["childrenNode", "palletChildrenNode"], model);
      return {
        ...filteredModel,
        // 这个还不确定是哪里的id  最外层的区域id
        id: settingFacility.id || "",
        wallFlag: "0",
        cellType: "AREA_CELL",
        functionType: AREA_FUNC_TYPE.STORAGE,
        groupStrategy: groupStrategyData.groupStrategy || "",
        childrenAreas,
        // 这个字段在后端没有存,我在这里先不给他赋值了但是老版的赋值了
        // nodeType
        ynGroup: 1,
      };
    };

    // 收集表单数据
    //  getRequestData
    const getRequestData = (model, hitStrategData, groupStrategyData) => {
      // 这个是密集存储区获取的参数
      if (model.storageType === 20) {
        return getGroupRequestData(model, hitStrategData, groupStrategyData);
      }
      const outCellCodes = (extraData.childrenNode || [])
        .map((cellCode) => {
          const outItem = cellCodeList.find((item) => item.cellCode === cellCode);
          return outItem ? outItem.hostCellCode || outItem.cellCode : cellCode;
        })
        .join(",");

      // 过滤掉不需要的字段
      const filteredSettingFacility = Object.fromEntries(
        Object.entries(settingFacility).filter(([key]) => !["hitStrategyDict", "childrenAreas"].includes(key))
      );
      const params = {
        id: settingFacility.id || "",
        ...filteredSettingFacility,
        wallFlag: "0",
        areaName: model.areaName || "",
        hostCellCode: model.hostCellCode || "",
        storageType: model.storageType || 10,
        hitStrategy: hitStrategData.hitStrategy || "",
        ynGroup: 0,
        functionType: AREA_FUNC_TYPE.STORAGE,
        // 根据 extraData 中的 nodeType 来决定传值    这个在编辑回显的时候也要给extraData.childrenNode 和extraData.palletChildrenNode  一份数据
        childrenNode: extraData.nodeType === "base" ? extraData.childrenNode || [] : [],
        palletChildrenNode: extraData.nodeType === "pallet" ? extraData.palletChildrenNode || [] : [],
        outCellCodes,
      };
      return params;
    };
    // 重置所有的交互数据
    const resetAllData = () => {
      // 重置表单
      formGroup.value = createFormGroup();
      // 重置普通存储区的数据
      hitStrategyForm.value.$setData({
        hitStrategy: "",
      });
      // 重置密集存储区
      groupStrategyForm.value.$setData({
        groupStrategy: "",
      });

      // 重置表格
      groupTableData.value = [
        {
          hostCellCode: "",
          cellType: "CHILD_AREA_CELL",
          childrenNode: [],
          palletChildrenNode: [],
          selectedPoints: [],
          hitStrategy: "",
          reflectCell: "",
          decisionPoint: null,
          maxTaskCount: 1,
          nodeType: "base",
          trafficFunctionType: "密集存储区",
          functionType: "20",
          __key: generateUniqueGroupId(),
        },
      ];
      tableState.data = groupTableData.value;

      // 重置其他交互状态
      selectedPoints.value = [];
      currentSelectedType.value = null;
      extraData.nodeType = "";
      extraData.childrenNode = [];
      extraData.palletChildrenNode = [];
      settingFacility.value = {};
      cellCodeList.length = 0;
      cscl.value = [];
      portChannelHitStrategy.value = [];
      requiredFieldFlag.value = false;
    };

    // 确认按钮点击处理
    const handleConfirm = async () => {
      try {
        // 获取表单数据
        const formData = formGroup.value.$getData();
        const hitStrategyData = hitStrategyForm.value.$getData();
        const groupStrategyData = groupStrategyForm.value.$getData();
        const storageType = formData.storageType;

        // 根据存储类型进行不同的验证
        if (storageType === 10) {
          // 检查点位是否已选择
          const hasPoints =
            props.mode === "edit"
              ? settingFacility.childrenNode?.length > 0 ||
                settingFacility.palletChildrenNode?.length > 0 ||
                extraData.childrenNode.length > 0 ||
                extraData.palletChildrenNode.length > 0 ||
                selectedPoints.value.length > 0 // 添加对selectedPoints的检查
              : extraData.childrenNode.length > 0 ||
                extraData.palletChildrenNode.length > 0 ||
                selectedPoints.value.length > 0; // 添加对selectedPoints的检查
          // 普通存储区：验证 formGroup 和 hitStrategyForm
          const [formGroupValid, hitStrategyValid] = await Promise.all([
            formGroup.value.$validate(),
            hitStrategyForm.value.$validate(),
          ]);
          if (!hasPoints) {
            requiredFieldFlag.value = true;
            return;
          }
          if (!formGroupValid || !hitStrategyValid) {
            return;
          }

          // 验证通过，执行保存操作
          const params = getRequestData(formData, hitStrategyData, groupStrategyData);

          await saveAreaInfo(params);
          emit("update:visible", false);
          emit("save-success", params);
          resetAllData();
          return;
        } else if (storageType === 20) {
          // 密集存储区：验证 formGroup 和 groupStrategyForm
          const [formGroupValid, groupStrategyValid] = await Promise.all([
            formGroup.value.$validate(),
            groupStrategyForm.value.$validate(),
          ]);

          if (!formGroupValid || !groupStrategyValid) {
            return;
          }
          // 验证通过，执行保存操作
          const params = getRequestData(formData, hitStrategyData, groupStrategyData);
          await saveAreaInfo(params);
          emit("update:visible", false);
          emit("save-success", params);
          resetAllData();
        } else {
          // 存储类型无效
          Message.error(t("geekplus.gms.client.screen.areaEditIndex.invalidStorageType"));
          return;
        }
      } catch (error) {
        console.error("保存失败:", error);
      }
    };

    // 决策点位单选
    function handleDecisionPointChange(row, val) {
      if (Array.isArray(val) && val.length > 0) {
        row.reflectCell = val[1];
      } else {
        row.reflectCell = val;
      }
    }

    // 包含点位多选
    function handleContainPointsChange(row, val) {
      if (!val || !val.length) {
        row.nodeType = "";
        row.childrenNode = [];
        row.palletChildrenNode = [];
        row.selectedPoints = [];
        row.childrenNodeLabel = "";
        return;
      }

      // 判断类型
      const allGeneral = val.every((item) => item[0] === "GENERAL_POINT");
      const allPallet = val.every((item) => item[0] === "PALLET_POINT");

      if (allGeneral) {
        row.nodeType = "base";
        // 只存储点位编码
        row.childrenNode = val.map((item) => item[1]);
        row.palletChildrenNode = [];
        row.selectedPoints = val; // 保持 PositionSelect 的显示
        row.childrenNodeLabel = row.childrenNode.join(",");
      } else if (allPallet) {
        row.nodeType = "pallet";
        // 只存储托盘位编码
        row.palletChildrenNode = val.map((item) => item[1]);
        row.childrenNode = [];
        row.selectedPoints = val; // 保持 PositionSelect 的显示
        row.childrenNodeLabel = row.palletChildrenNode.map((code) => props.palletCodeHashmap[code] || code).join(",");
      } else {
        // 混选时，只保留最后一类
        const lastType = val[val.length - 1][0];
        const filtered = val.filter((item) => item[0] === lastType);

        if (lastType === "GENERAL_POINT") {
          row.nodeType = "base";
          row.childrenNode = filtered.map((item) => item[1]);
          row.palletChildrenNode = [];
          row.selectedPoints = filtered;
          row.childrenNodeLabel = row.childrenNode.join(",");
        } else if (lastType === "PALLET_POINT") {
          row.nodeType = "pallet";
          row.palletChildrenNode = filtered.map((item) => item[1]);
          row.childrenNode = [];
          row.selectedPoints = filtered;
          row.childrenNodeLabel = row.palletChildrenNode.map((code) => props.palletCodeHashmap[code] || code).join(",");
        } else {
          row.nodeType = "";
          row.childrenNode = [];
          row.palletChildrenNode = [];
          row.selectedPoints = [];
          row.childrenNodeLabel = "";
        }
      }
    }

    // **监听密集存储区已选点位，这个密集存储区的决策点位，当表格中的任意一样发生改变的时候这个值就会从新计算，用来禁用包含点位中地图点位
    //    当编辑的时候还要回显当前的数据
    //    **/
    const denseStorageSelectedPoints = computed(() => {
      const allPoints = [];

      // 确保能监听到数组内部的变化
      const tableData = JSON.parse(JSON.stringify(groupTableData.value));

      tableData.forEach((row) => {
        // 收集决策点位
        if (row.reflectCell) {
          allPoints.push(row.reflectCell);
        }
      });
      return allPoints;
    });
    // 深度监听 groupTableData 的变化，当港道信息的表格数据条数发生变化的时候会计算 所有表格的包含点位和决策点位,来禁用表格中的包含点位和托盘点位的信息
    watch(
      () => groupTableData.value.length,
      (newVal) => {
        const allPoints = [];
        groupTableData.value.forEach((row) => {
          // 收集普通点位
          if (row.childrenNode && row.childrenNode.length) {
            allPoints.push(...row.childrenNode);
          }
          // 收集托盘位
          if (row.palletChildrenNode && row.palletChildrenNode.length) {
            allPoints.push(...row.palletChildrenNode);
          }
        });
        allSelectedPoints.value = allPoints;
      },
      { immediate: true }
    );

    // 互斥禁用函数：点位和托盘位不能同时选
    function mutualDisableFilter(item, row) {
      // 获取点位的值
      const itemValue = item.$value;

      // 检查这个点位是否在当前行的选择数组中(UI状态)
      const isCurrentlySelected = row.selectedPoints?.some((point) =>
        Array.isArray(point) ? point[1] === itemValue : point.$value === itemValue
      );

      // 如果是当前已选中的点位，永远不禁用它
      if (isCurrentlySelected) {
        return false;
      }

      // 检查是否是当前行的决策点位值
      if (row.reflectCell === itemValue) {
        return true; // 如果是当前行的决策点位，禁用该点位
      }

      // 收集当前行的所有点位（数据状态）
      const currentRowPoints = [...(row.childrenNode || []), ...(row.palletChildrenNode || [])];

      // 收集 settingFacility 的所有点位
      const settingFacilityPoints = [
        ...(settingFacility.childrenNode || []),
        ...(settingFacility.palletChildrenNode || []),
      ];

      // 合并所有需要排除的点位
      const excludePoints = [...currentRowPoints, ...settingFacilityPoints];

      // 从 selectedCellCodes 中排除这些点位
      const filteredSelectedCellCodes = selectedCellCodes.value.filter((code) => !excludePoints.includes(code));

      if (filteredSelectedCellCodes.includes(itemValue)) return true;

      // 检查是否在其他行的点位和托盘位中（不再检查reflectCells）或者在需要排除的点位里，我们就禁用他
      if (
        (allTableData.value.childrenNodes.includes(itemValue) ||
          allTableData.value.palletChildrenNodes.includes(itemValue)) &&
        excludePoints.includes(itemValue)
      ) {
        return true;
      }

      // 类型互斥逻辑
      if (!Array.isArray(row?.selectedPoints) || row.selectedPoints.length === 0) {
        return false;
      }

      const firstSelected = row.selectedPoints[0];
      const selectedType = Array.isArray(firstSelected) ? firstSelected[0] : firstSelected.$group;
      const currentType = item.$group;
      return selectedType !== currentType;
    }

    const getSelectLabel = (value, options) => {
      if (!value || !options) return "";
      const option = options.find((item) => item.value === value);
      return option ? option.label : "";
    };

    const getGroupTableDataFromServer = (arr) => {
      if (!arr || arr.length < 1) return [];

      return arr.map((item) => {
        // 是否托盘位 - 通过 palletChildNodes 字段判断
        const isPallet = item.palletChildNodes && item.palletChildNodes.length > 0;

        // 处理决策点位数据 - 单选，始终使用 GENERAL_POINT 类型
        const decisionPoint = item.reflectCell ? ["GENERAL_POINT", item.reflectCell] : null;

        // 处理包含点位数据 - 多选，根据 isPallet 决定类型
        let selectedPoints = [];
        if (!isPallet) {
          selectedPoints = item.childrenNode.map((code) => ["GENERAL_POINT", code]);
        } else {
          selectedPoints = item.palletChildNodes.split(",").map((code) => ["PALLET_POINT", code]);
        }

        return {
          id: item.id,
          cellType: item.cellType,
          areaName: item.areaName,
          hostCellCode: item.hostCellCode || "",
          hitStrategy: item.hitStrategy || "",
          strategyLabel: getSelectLabel(item.hitStrategy, props.options.hitStrategy), // 策略文本
          trafficFunctionType: item.trafficFunctionType || "",
          functionType: item.functionType || AREA_FUNC_TYPE.STORAGE, // 固定为存储区类型
          containerTypeLabels: fp.map("containerTypeName", item.containerTypes),
          containerTypeCodes: fp.map("containerTypeCode", item.containerTypes),
          maxTaskCount: item.maxTaskSize || 1, // 修改这里，将 maxTaskSize 映射到 maxTaskCount，默认值为 1
          reflectCell: item.reflectCell || "",
          // 托盘位
          palletChildrenNode: isPallet ? item.palletChildNodes.split(",") : [],
          // 点位
          childrenNode: isPallet ? [] : item.childrenNode,
          nodeType: getNodeType(isPallet),
          mode: props.mode,
          __key: generateUniqueGroupId(),
          childrenNodeLabel: createChildrenNodeLabel(item, props.palletCodeHashmap),
          decisionPoint,
          selectedPoints,
        };
      });
    };

    return {
      formGroup,
      groupTableData,
      groupTableColumns,
      tableState,
      customTableColumns,
      portChannelHitStrategy,
      isDenseStorage,
      isNormalStorage,
      selectedPoints,
      handlePositionSelectChange,
      handleConfirm,
      positionSelectGroups,
      disableGroupFilter,
      extraData,
      settingFacility,
      getNodeType,
      cellCodeList,
      getRequestData,
      onlyGeneralPointGroup,
      handleDecisionPointChange,
      handleContainPointsChange,
      mutualDisableFilter,
      allOptions,
      palletCodeDict,
      areaUsePalletCodes,
      getServiceCode,
      getGroupTableDataFromServer,
      areaUsedPalletCodes,
      getAreaUsedPalletCodes,
      allOption,
      normalStorageDisableFilter,
      alreadySelectedFilter,
      denseStorageSelectedPoints,
      allSelectedPoints,
      allNodeList,
      allTableChildrenNodes,
      allTableData,
      t,
      dataReady,
      resetAllData,
      dialogTitle,
      formKey,
      formGroup,
      groupStrategyForm,
      hitStrategyForm,
      requiredFieldFlag,
    };
  },
});
</script>
<style scoped lang="scss">
.gp-dialog {
  height: 600px;
}
:deep(._header_lonhs_132) {
  margin-bottom: 10px;
}
.viewclass {
  background-color: var(--gp-color-line4-light) !important;
  border-color: #dcdfe6 !important;
  border: 1px solid #e5e7ea !important;
  cursor: not-allowed !important;
  padding: 0 9px;
  :deep(.gp-tag) {
    color: #c8cdd3;
    background-color: var(--gp-color-white);
  }
  :deep(._tag_box_1372r_7) {
    background-color: var(--gp-color-line4-light);
  }
  :deep(._icon_box_1372r_46) {
    display: none !important;
  }
  :deep(.gp-tag.gp-tag--info .gp-tag__close) {
    display: none !important;
  }
}
.code_label {
  width: 120px;
  &:before {
    color: gms.$colorDanger;
    content: "*";
    margin-right: 4px;
  }
}
.error_msg {
  color: #f56c6c;
  font-size: 12px;
  left: 0;
  line-height: 1;
  margin-top: 6px;
  min-height: 6px; // 设置一个固定的最小高度，确保有足够空间显示错误信息
  // padding-top: 4px;
  // position: absolute;
  // top: 20px;
}
</style>
