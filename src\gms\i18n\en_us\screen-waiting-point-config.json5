{"geekplus.gms.client.screen.waitingPointConfig.btns.add": "Add", "geekplus.gms.client.screen.waitingPointConfig.btns.edit": "Edit", "geekplus.gms.client.screen.waitingPointConfig.btns.delete": "Delete", "geekplus.gms.client.screen.waitingPointConfig.columns.cellCode": "Location code", "geekplus.gms.client.screen.waitingPointConfig.columns.cellType": "Location type", "geekplus.gms.client.screen.waitingPointConfig.columns.nodeActionName": "Interactive action", "geekplus.gms.client.screen.waitingPointConfig.columns.cleanWaitType": "Waiting status", "geekplus.gms.client.screen.waitingPointConfig.columns.creatorUsername": "Creator", "geekplus.gms.client.screen.waitingPointConfig.columns.creationTime": "Creation time", "geekplus.gms.client.screen.waitingPointConfig.columns.updatorUsername": "Editor", "geekplus.gms.client.screen.waitingPointConfig.columns.updateTime": "Edit time", "geekplus.gms.client.screen.waitingPointConfig.columns.actions": "Operation", "geekplus.gms.client.screen.waitingPointConfig.columns.cleanWaitType.auto": "Automatic clearing", "geekplus.gms.client.screen.waitingPointConfig.columns.cleanWaitType.manual": "Manual clearing", "geekplus.gms.client.screen.waitingPointConfig.tooltip.edit": "Edit", "geekplus.gms.client.screen.waitingPointConfig.tooltip.delete": "Delete", "geekplus.gms.client.screen.waitingPointConfig.dialogConfig.title.add": "Add", "geekplus.gms.client.screen.waitingPointConfig.dialogConfig.title.edit": "Edit", "geekplus.gms.client.screen.waitingPointConfig.form.label.cellType": "Location type", "geekplus.gms.client.screen.waitingPointConfig.form.label.cleanWaitType": "Waiting status", "geekplus.gms.client.screen.waitingPointConfig.form.label.cellCode": "Location code", "geekplus.gms.client.screen.waitingPointConfig.form.label.nodeActionName": "Interactive action", "geekplus.gms.client.screen.waitingPointConfig.dialogConfig.actions.save": "Save", "geekplus.gms.client.screen.waitingPointConfig.dialogConfig.actions.cancel": "Cancel", "geekplus.gms.client.screen.waitingPointConfig.dialogConfirm.actions.desc": "Are you sure to delete the data?", "geekplus.gms.client.screen.waitingPointConfig.dialogConfirm.actions.cancel": "Cancel", "geekplus.gms.client.screen.waitingPointConfig.dialogConfirm.actions.confirm": "OK", "geekplus.gms.client.screen.waitingPointConfig.dialogConfig.actions.device.title": "External device", "geekplus.gms.client.screen.waitingPointConfig.dialogConfig.actions.device.own": "Device attribution", "geekplus.gms.client.screen.waitingPointConfig.dialogConfig.actions.device.type": "Equipment type", "geekplus.gms.client.screen.waitingPointConfig.dialogConfig.actions.device.name": "Device Name", "geekplus.gms.client.screen.waitingPointConfig.dialogConfig.actions.device.command": "Device command", "geekplus.gms.client.screen.waitingPointConfig.dialogConfig.actions.device.criteria": "Conditions for execution of instruction", "geekplus.gms.client.screen.waitingPointConfig.dialogConfig.actions.interface.title": "Interface command", "geekplus.gms.client.screen.waitingPointConfig.dialogConfig.actions.interface.url": "Interface address", "geekplus.gms.client.screen.waitingPointConfig.dialogConfig.actions.interface.commandCode": "Command code", "geekplus.gms.client.screen.waitingPointConfig.dialogConfig.actions.component.title": "Component commands", "geekplus.gms.client.screen.waitingPointConfig.dialogConfig.actions.component.robotType": "Robot model", "geekplus.gms.client.screen.waitingPointConfig.dialogConfig.actions.component.actionType": "Rotate", "geekplus.gms.client.screen.waitingPointConfig.dialogConfig.actions.component.actionExtendedParameters": "Rotation value"}