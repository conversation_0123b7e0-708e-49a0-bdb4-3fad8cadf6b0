{"libsSz.key1": "Account", "libsSz.key2": "Password", "libsSz.key3": "<PERSON><PERSON>", "libsSz.key4": "Logout", "libsSz.key5": "Sorry, you are not authorized to visit this page", "libsSz.key6": "Previous", "libsSz.key7": "Sorry, the page you visited does not exist", "libsSz.key8": "Unfold", "libsSz.key9": "Fold", "libsSz.key10": "Query", "libsSz.key11": "Reset", "libsSz.key12": "SN", "libsSz.key13": "Operation", "libsSz.key14": "Cancel", "libsSz.key15": "Confirm", "libsSz.key16": "Loading...", "libsSz.key17": "Change Password", "libsSz.key18": "Role name", "libsSz.key19": "Role description", "libsSz.key20": "Permission types", "libsSz.key21": "Authorization subsystem", "libsSz.key22": "Please enter ", "libsSz.key23": "Please select", "libsSz.key24": "Page permissions", "libsSz.key25": "Associated users", "libsSz.key26": "Please enter keywords to filter", "libsSz.key27": "Name", "libsSz.key28": "Tel", "libsSz.key29": "Enable the permissions", "libsSz.key30": "Disable the permissions", "libsSz.key31": "Please enter required fields!", "libsSz.key32": "Select the subsystem for which authorization is required", "libsSz.key33": "assignment", "libsSz.key34": "Special characters are not allowed", "libsSz.key35": "Please enter required fields!", "libsSz.key36": "Maximum limit input {0} characters", "libsSz.key37": "Please enter a valid IP address", "libsSz.key38": "Please enter a positive integer", "libsSz.key39": "Save", "libsSz.key40": "Edit", "libsSz.key41": "There is an unsaved edit item, please save it in edit first!", "libsSz.key42": "Make sure to cancel the line edit", "libsSz.key43": "Please enter a natural number", "libsSz.key44": "Please enter the correct phone number", "libsSz.key45": "No Data", "libsSz.key46": "Please enter the {0} character", "libsSz.key47": "No choice", "libsSz.key48": "Have chosen", "libsSz.key49": "<PERSON><PERSON><PERSON>", "libsSz.key50": "Download", "libsSz.key51": "Image failed to load", "libsSz.key52": "Navigator", "libsSz.key53": "Undo", "libsSz.key54": "Redo", "libsSz.key55": "Delete", "libsSz.key56": "ZOOM In", "libsSz.key57": "Zoom Out", "libsSz.key58": "Zoom Auto", "libsSz.key59": "Actual size", "libsSz.key60": "Post-level", "libsSz.key61": "Pre-level", "libsSz.key62": "Multiple", "libsSz.key63": "<PERSON><PERSON>", "libsSz.key64": "Grid alignment", "libsSz.key65": "Only 1-65535 positive integers", "libsSz.key66": "Only English letters or numbers", "libsSz.key67": "Please enter a valid subnet mask", "libsSz.key68": "Please enter a valid gateway address", "libsSz.key69": "download", "libsSz.key70": "drag files here, or ", "libsSz.key71": "Click to upload", "libsSz.key72": "uploading...", "libsSz.key73": "confirm upload", "libsSz.key74": "re-upload", "libsSz.key75": "Please upload file~", "libsSz.key76": "Upload file size cannot exceed {0}", "libsSz.key77": "upload failed", "libsSz.key78": "Limit uploading of {0} files", "libsSz.key79": "In order to ensure the accuracy of barcode recognition, please crop the image to only the barcode size after uploading the image", "libsSz.key80": "Upload and crop barcodes", "libsSz.key81": "Check if the camera is turned on...", "libsSz.key82": "Back", "libsSz.key83": "1. Enter 'chrome://flags' in the browser address bar, search for the 'unsafely-treat-insecure-origin-as-secure' option, set it to Enabled, add the domain name or IP that needs to open the camera in the option input box, and restart the browser after modification.", "libsSz.key84": "2. The browser has enabled the camera to scan the code, and the camera is prohibited. Click the security warning mark in the browser address bar and follow the browser guidance to turn on the camera.", "libsSz.key85": "Select Image", "libsSz.key86": "<PERSON><PERSON><PERSON> left", "libsSz.key87": "Rotate right", "libsSz.key88": "Zoom in", "libsSz.key89": "Zoom out", "libsSz.key90": "Your browser does not enable the camera to scan the barcode, please use the uploaded image to parse the barcode", "libsSz.key91": "Please upload the correct barcode image first", "libsSz.key92": "Upload and crop barcodes", "libsSz.key93": "Upload image", "libsSz.key94": "Only numbers are supported", "libsSz.key95": "Please enter numbers, separated by commas", "libsSz.key96": "<PERSON><PERSON>", "libsSz.key97": "Add row", "libsSz.key98": "Add column", "libsSz.key99": "Add row before current row", "libsSz.key100": "Add column after current column", "libsSz.key101": "Delete row", "libsSz.key102": "Delete column", "libsSz.key103": "Delete current row", "libsSz.key104": "Delete current column", "libsSz.key105": "Merge columns"}