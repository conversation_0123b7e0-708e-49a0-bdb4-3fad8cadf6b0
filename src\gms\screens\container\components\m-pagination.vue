<template>
  <div :class="cn.pagination_container">
    <gp-pagination
      background
      layout="total, sizes, prev, pager, next, jumper"
      v-bind="$attrs"
      v-on="$listeners"
    ></gp-pagination>
  </div>
</template>
<script>
import { defineComponent } from "vue";

export default defineComponent({
  name: "MPagination",
  setup() {},
});
</script>

<style lang="scss" module="cn">
.pagination_container {
  position: absolute;
  bottom: 0px;
  left: 0px;
  right: 0px;
  height: gms.length(6);
  padding: 0px gms.length(1);
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  background-color: gms.$colorBackgroundWhite;
  box-shadow: 0px -1px 4px 0px rgba(0, 0, 0, 0.05);
  border-bottom-right-radius: gms.$borderRadius6;
  border-bottom-left-radius: gms.$borderRadius6;
  z-index: 10;
}
</style>
